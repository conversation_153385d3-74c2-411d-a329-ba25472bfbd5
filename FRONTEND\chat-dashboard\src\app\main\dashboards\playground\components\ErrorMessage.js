import React from 'react';

/**
 * Error message component
 * @param {Object} props - Component props
 * @param {Function} props.onRetry - Handler for retry action
 */
const ErrorMessage = ({ onRetry }) => (
  <div className="error-message">
    <div className="error-icon">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="8" x2="12" y2="12"></line>
        <line x1="12" y1="16" x2="12.01" y2="16"></line>
      </svg>
    </div>
    <p>Sorry, there was an error processing your request.</p>
    <button className="retry-button" onClick={onRetry}>
      Try Again
    </button>
  </div>
);

export default ErrorMessage; 