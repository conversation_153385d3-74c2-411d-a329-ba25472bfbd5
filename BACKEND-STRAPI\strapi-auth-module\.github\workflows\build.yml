name: Docker Build and Deploy

on:
  push:
    branches:
      - production
      - development

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Docker to allow insecure registries
        run: |
          echo '{ "insecure-registries": ["139.59.28.213"] }' | sudo tee /etc/docker/daemon.json
          sudo systemctl restart docker

      - name: Login to Docker Registry (Harbor)
        run: docker login -u ${{ secrets.HARBOR_USERNAME }} -p ${{ secrets.HARBOR_PASSWORD }} 139.59.28.213

      - name: Build Docker Image
        run: |
          if [[ $GITHUB_REF == refs/heads/production ]]; then
            docker build -t 139.59.28.213/strapi-auth-server/chat-masala -f Dockerfile.prod .
          else
            docker build -t 139.59.28.213/strapi-auth-server/strapi-auth-server  -f Dockerfile.dev .
          fi

      - name: Push Docker Image to Harbor
        run: | 
         if [[ $GITHUB_REF == refs/heads/production ]]; then
            docker push 139.59.28.213/strapi-auth-server/chat-masala
          else
            docker push 139.59.28.213/strapi-auth-server/strapi-auth-server 
          fi

      - name: Update Portainer Stack
        run: |
          if [[ $GITHUB_REF == refs/heads/production ]]; then
            curl -X POST \
              -H "Content-Type: application/json" \
              http://142.93.220.94:9000/api/webhooks/76e15111-f068-4335-967f-de4e80d072ec
          else
            curl -X POST \
              -H "Content-Type: application/json" \
              http://206.189.131.232:9000/api/webhooks/b8958754-ce7f-4daa-bf2b-afdbe44ed4d3
          fi
