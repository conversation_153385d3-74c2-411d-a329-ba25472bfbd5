'use strict';

/**
 * IAP custom router
 */
module.exports = {
  routes: [
    {
      method: 'POST',
      path: '/iap/verify/apple',
      handler: 'custom-controller.verifyAppleReceipt',
      config: {
        policies: [
          'global::user-details-populate',
          'global::update-org-before-api-call',
        ]
      }
    },
    {
      method: 'POST',
      path: '/iap/verify/google',
      handler: 'custom-controller.verifyGooglePurchase',
      config: {
        policies: [
          'global::user-details-populate',
          'global::update-org-before-api-call',
        ]
      }
    },
    {
      method: 'GET',
      path: '/subscription/status',
      handler: 'custom-controller.getSubscriptionStatus',
      config: {
        policies: [
          'global::user-details-populate',
          'global::update-org-before-api-call',
        ]
      }
    },
    {
      method: 'POST',
      path: '/iap/purchase/apple/one-time',
      handler: 'custom-controller.verifyAppleOneTimePurchase',
      config: {
        policies: [
          'global::user-details-populate',
          'global::update-org-before-api-call',
        ]
      }
    },
    {
      method: 'POST',
      path: '/iap/purchase/google/one-time',
      handler: 'custom-controller.verifyGoogleOneTimePurchase',
      config: {
        policies: [
          'global::user-details-populate',
          'global::update-org-before-api-call',
        ]
      }
    },
    {
      method: 'GET',
      path: '/purchase/history',
      handler: 'custom-controller.getPurchaseHistory',
      config: {
        policies: [
          'global::user-details-populate',
          'global::update-org-before-api-call',
        ]
      }
    }
  ]
}; 