'use strict';
const rateLimit = require('koa-ratelimit');

module.exports = {
  load: {
    before: ['strapi::errors', 'strapi::security', 'strapi::ratelimit'],
  },
  settings: {
    'strapi::ratelimit': {
      enabled: true,
      max: 5, // Maximum number of requests per time window
      duration: 2000, // Time window in milliseconds
      message: 'Too many requests, please try again later.', // Error message for rate limit exceeded
      id: (ctx) => ctx.ip, // Unique identifier for rate limiting (using IP address in this example)
    },
  },
};
