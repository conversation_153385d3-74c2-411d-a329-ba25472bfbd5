import React from 'react'
import <PERSON><PERSON> from 'react-lottie';
import * as animationData from './cta1.json'

export default class LottieControl extends React.Component {

  constructor(props) {
    super(props);
    this.state = {isStopped: false, isPaused: false};
  }

  render() {
    const buttonStyle = {
      display: 'block',
      margin: '10px auto'
    };

    const defaultOptions = {
      loop: true,
      autoplay: true,
      animationData: animationData,
      rendererSettings: {
        preserveAspectRatio: 'xMidYMid slice'
      }
    };

    return <div>
      <Lottie options={defaultOptions}
              height={170}
              width={170}
              isStopped={this.state.isStopped}
              isPaused={this.state.isPaused}/>

    </div>
  }
}
