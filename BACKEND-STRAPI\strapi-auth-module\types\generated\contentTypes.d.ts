import type { Schema, Attribute } from '@strapi/strapi';

export interface AdminPermission extends Schema.CollectionType {
  collectionName: 'admin_permissions';
  info: {
    name: 'Permission';
    description: '';
    singularName: 'permission';
    pluralName: 'permissions';
    displayName: 'Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    actionParameters: Attribute.JSON & Attribute.DefaultTo<{}>;
    subject: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    properties: Attribute.JSON & Attribute.DefaultTo<{}>;
    conditions: Attribute.JSON & Attribute.DefaultTo<[]>;
    role: Attribute.Relation<'admin::permission', 'manyToOne', 'admin::role'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminUser extends Schema.CollectionType {
  collectionName: 'admin_users';
  info: {
    name: 'User';
    description: '';
    singularName: 'user';
    pluralName: 'users';
    displayName: 'User';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    firstname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    username: Attribute.String;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.Private &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    resetPasswordToken: Attribute.String & Attribute.Private;
    registrationToken: Attribute.String & Attribute.Private;
    isActive: Attribute.Boolean &
      Attribute.Private &
      Attribute.DefaultTo<false>;
    roles: Attribute.Relation<'admin::user', 'manyToMany', 'admin::role'> &
      Attribute.Private;
    blocked: Attribute.Boolean & Attribute.Private & Attribute.DefaultTo<false>;
    preferedLanguage: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface AdminRole extends Schema.CollectionType {
  collectionName: 'admin_roles';
  info: {
    name: 'Role';
    description: '';
    singularName: 'role';
    pluralName: 'roles';
    displayName: 'Role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    code: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String;
    users: Attribute.Relation<'admin::role', 'manyToMany', 'admin::user'>;
    permissions: Attribute.Relation<
      'admin::role',
      'oneToMany',
      'admin::permission'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface AdminApiToken extends Schema.CollectionType {
  collectionName: 'strapi_api_tokens';
  info: {
    name: 'Api Token';
    singularName: 'api-token';
    pluralName: 'api-tokens';
    displayName: 'Api Token';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    type: Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Attribute.Required &
      Attribute.DefaultTo<'read-only'>;
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastUsedAt: Attribute.DateTime;
    permissions: Attribute.Relation<
      'admin::api-token',
      'oneToMany',
      'admin::api-token-permission'
    >;
    expiresAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_api_token_permissions';
  info: {
    name: 'API Token Permission';
    description: '';
    singularName: 'api-token-permission';
    pluralName: 'api-token-permissions';
    displayName: 'API Token Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    token: Attribute.Relation<
      'admin::api-token-permission',
      'manyToOne',
      'admin::api-token'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferToken extends Schema.CollectionType {
  collectionName: 'strapi_transfer_tokens';
  info: {
    name: 'Transfer Token';
    singularName: 'transfer-token';
    pluralName: 'transfer-tokens';
    displayName: 'Transfer Token';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastUsedAt: Attribute.DateTime;
    permissions: Attribute.Relation<
      'admin::transfer-token',
      'oneToMany',
      'admin::transfer-token-permission'
    >;
    expiresAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    name: 'Transfer Token Permission';
    description: '';
    singularName: 'transfer-token-permission';
    pluralName: 'transfer-token-permissions';
    displayName: 'Transfer Token Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    token: Attribute.Relation<
      'admin::transfer-token-permission',
      'manyToOne',
      'admin::transfer-token'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFile extends Schema.CollectionType {
  collectionName: 'files';
  info: {
    singularName: 'file';
    pluralName: 'files';
    displayName: 'File';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    alternativeText: Attribute.String;
    caption: Attribute.String;
    width: Attribute.Integer;
    height: Attribute.Integer;
    formats: Attribute.JSON;
    hash: Attribute.String & Attribute.Required;
    ext: Attribute.String;
    mime: Attribute.String & Attribute.Required;
    size: Attribute.Decimal & Attribute.Required;
    url: Attribute.String & Attribute.Required;
    previewUrl: Attribute.String;
    provider: Attribute.String & Attribute.Required;
    provider_metadata: Attribute.JSON;
    related: Attribute.Relation<'plugin::upload.file', 'morphToMany'>;
    folder: Attribute.Relation<
      'plugin::upload.file',
      'manyToOne',
      'plugin::upload.folder'
    > &
      Attribute.Private;
    folderPath: Attribute.String &
      Attribute.Required &
      Attribute.Private &
      Attribute.SetMinMax<{
        min: 1;
      }>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFolder extends Schema.CollectionType {
  collectionName: 'upload_folders';
  info: {
    singularName: 'folder';
    pluralName: 'folders';
    displayName: 'Folder';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<{
        min: 1;
      }>;
    pathId: Attribute.Integer & Attribute.Required & Attribute.Unique;
    parent: Attribute.Relation<
      'plugin::upload.folder',
      'manyToOne',
      'plugin::upload.folder'
    >;
    children: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.folder'
    >;
    files: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.file'
    >;
    path: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<{
        min: 1;
      }>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission
  extends Schema.CollectionType {
  collectionName: 'up_permissions';
  info: {
    name: 'permission';
    description: '';
    singularName: 'permission';
    pluralName: 'permissions';
    displayName: 'Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String & Attribute.Required;
    role: Attribute.Relation<
      'plugin::users-permissions.permission',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole extends Schema.CollectionType {
  collectionName: 'up_roles';
  info: {
    name: 'role';
    description: '';
    singularName: 'role';
    pluralName: 'roles';
    displayName: 'Role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    description: Attribute.String;
    type: Attribute.String & Attribute.Unique;
    permissions: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.permission'
    >;
    users: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.user'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsUser extends Schema.CollectionType {
  collectionName: 'up_users';
  info: {
    name: 'user';
    description: '';
    singularName: 'user';
    pluralName: 'users';
    displayName: 'User';
  };
  options: {
    draftAndPublish: false;
    timestamps: true;
  };
  attributes: {
    username: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    provider: Attribute.String;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    resetPasswordToken: Attribute.String & Attribute.Private;
    confirmationToken: Attribute.String & Attribute.Private;
    confirmed: Attribute.Boolean & Attribute.DefaultTo<false>;
    blocked: Attribute.Boolean & Attribute.DefaultTo<false>;
    role: Attribute.Relation<
      'plugin::users-permissions.user',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    phone: Attribute.String & Attribute.Unique;
    organization: Attribute.Relation<
      'plugin::users-permissions.user',
      'manyToOne',
      'api::organization.organization'
    >;
    acc_type: Attribute.Enumeration<['individual', 'organization']>;
    scrumboards: Attribute.Relation<
      'plugin::users-permissions.user',
      'manyToMany',
      'api::scrumboard.scrumboard'
    >;
    scrumboard_card: Attribute.Relation<
      'plugin::users-permissions.user',
      'manyToOne',
      'api::scrumboard-card.scrumboard-card'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginI18NLocale extends Schema.CollectionType {
  collectionName: 'i18n_locale';
  info: {
    singularName: 'locale';
    pluralName: 'locales';
    collectionName: 'locales';
    displayName: 'Locale';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetMinMax<{
        min: 1;
        max: 50;
      }>;
    code: Attribute.String & Attribute.Unique;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginStrapiStripeSsProduct extends Schema.CollectionType {
  collectionName: 'strapi-stripe_ss-product';
  info: {
    tableName: 'StripeProduct';
    singularName: 'ss-product';
    pluralName: 'ss-products';
    displayName: 'Product';
    description: 'Stripe Products';
    kind: 'collectionType';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    title: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<{
        min: 1;
      }>;
    slug: Attribute.UID<'plugin::strapi-stripe.ss-product', 'title'> &
      Attribute.Required &
      Attribute.Unique;
    description: Attribute.Text &
      Attribute.Required &
      Attribute.SetMinMax<{
        min: 1;
      }>;
    price: Attribute.Decimal & Attribute.Required;
    currency: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<{
        min: 1;
      }>;
    productImage: Attribute.Media & Attribute.Required;
    isSubscription: Attribute.Boolean & Attribute.DefaultTo<false>;
    interval: Attribute.String;
    trialPeriodDays: Attribute.Integer;
    stripeProductId: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<{
        min: 3;
      }>;
    stripePriceId: Attribute.String &
      Attribute.SetMinMax<{
        min: 3;
      }>;
    stripePlanId: Attribute.String &
      Attribute.SetMinMax<{
        min: 3;
      }>;
    stripePayment: Attribute.Relation<
      'plugin::strapi-stripe.ss-product',
      'oneToMany',
      'plugin::strapi-stripe.ss-payment'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::strapi-stripe.ss-product',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::strapi-stripe.ss-product',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginStrapiStripeSsPayment extends Schema.CollectionType {
  collectionName: 'strapi-stripe_ss-payment';
  info: {
    tableName: 'StripePayment';
    singularName: 'ss-payment';
    pluralName: 'ss-payments';
    displayName: 'Payment';
    description: 'Stripe Payment';
    kind: 'collectionType';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    txnDate: Attribute.DateTime & Attribute.Required;
    transactionId: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 250;
      }>;
    isTxnSuccessful: Attribute.Boolean & Attribute.DefaultTo<false>;
    txnMessage: Attribute.Text &
      Attribute.SetMinMaxLength<{
        maxLength: 5000;
      }>;
    txnErrorMessage: Attribute.String &
      Attribute.SetMinMaxLength<{
        maxLength: 250;
      }>;
    txnAmount: Attribute.Decimal & Attribute.Required;
    customerName: Attribute.String & Attribute.Required;
    customerEmail: Attribute.String & Attribute.Required;
    stripeProduct: Attribute.Relation<
      'plugin::strapi-stripe.ss-payment',
      'manyToOne',
      'plugin::strapi-stripe.ss-product'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::strapi-stripe.ss-payment',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::strapi-stripe.ss-payment',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAiTaskAiTask extends Schema.CollectionType {
  collectionName: 'ai_tasks';
  info: {
    singularName: 'ai-task';
    pluralName: 'ai-tasks';
    displayName: 'AI Task';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String;
    task_id: Attribute.Integer;
    detailsFactor: Attribute.Decimal;
    speedFactor: Attribute.Decimal;
    tokensUsageFactor: Attribute.Decimal;
    description: Attribute.Text;
    waitingTime: Attribute.String;
    organizations: Attribute.Relation<
      'api::ai-task.ai-task',
      'oneToMany',
      'api::organization.organization'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::ai-task.ai-task',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::ai-task.ai-task',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAnswerAnswer extends Schema.CollectionType {
  collectionName: 'answers';
  info: {
    singularName: 'answer';
    pluralName: 'answers';
    displayName: 'Answer';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    question: Attribute.Text & Attribute.Required;
    answer: Attribute.Text;
    answer_status: Attribute.Boolean & Attribute.DefaultTo<false>;
    sources: Attribute.JSON;
    kb_id: Attribute.Text;
    org_id: Attribute.Text;
    api_source: Attribute.String;
    session: Attribute.String;
    url_trackers: Attribute.Relation<
      'api::answer.answer',
      'oneToMany',
      'api::url-tracker.url-tracker'
    >;
    did_find_answer: Attribute.Boolean;
    knowledgebase: Attribute.Relation<
      'api::answer.answer',
      'oneToOne',
      'api::knowledgebase.knowledgebase'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::answer.answer',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::answer.answer',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiApiAccessKeyApiAccessKey extends Schema.CollectionType {
  collectionName: 'api_access_keys';
  info: {
    singularName: 'api-access-key';
    pluralName: 'api-access-keys';
    displayName: 'API Access Key';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    key: Attribute.String;
    name: Attribute.String;
    organization: Attribute.Relation<
      'api::api-access-key.api-access-key',
      'manyToOne',
      'api::organization.organization'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::api-access-key.api-access-key',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::api-access-key.api-access-key',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiChatbotStyleChatbotStyle extends Schema.CollectionType {
  collectionName: 'chatbot_styles';
  info: {
    singularName: 'chatbot-style';
    pluralName: 'chatbot-styles';
    displayName: 'chatbot-style';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    knowledgebase: Attribute.Relation<
      'api::chatbot-style.chatbot-style',
      'oneToOne',
      'api::knowledgebase.knowledgebase'
    >;
    position: Attribute.Enumeration<['right', 'left', 'center']> &
      Attribute.DefaultTo<'right'>;
    fabColor: Attribute.String & Attribute.DefaultTo<'#1A1A1A'>;
    fabTextColor: Attribute.String & Attribute.DefaultTo<'#EFEFEF'>;
    primaryFont: Attribute.String &
      Attribute.DefaultTo<'Source Sans Pro, sans-serif'>;
    secondaryFont: Attribute.String &
      Attribute.DefaultTo<'Source Sans Pro, serif'>;
    bgColor: Attribute.String & Attribute.DefaultTo<'#FDFDFD'>;
    headerColor: Attribute.String & Attribute.DefaultTo<'#1A1A1A'>;
    secondaryColor: Attribute.String & Attribute.DefaultTo<'#273747'>;
    textColor: Attribute.String & Attribute.DefaultTo<'#E9E9E9'>;
    secondaryTextColor: Attribute.String & Attribute.DefaultTo<'#7c7c7c'>;
    botBubbleColor: Attribute.String & Attribute.DefaultTo<'#EFEFEF'>;
    userBubbleColor: Attribute.String & Attribute.DefaultTo<'#F1F1F1'>;
    botBubbleTextColor: Attribute.String & Attribute.DefaultTo<'#1A1A1A'>;
    userBubbleTextColor: Attribute.String & Attribute.DefaultTo<'#1A1A1A'>;
    inputBackgroundColor: Attribute.String & Attribute.DefaultTo<'#FDFDFD'>;
    inputTextColor: Attribute.String & Attribute.DefaultTo<'#1A1A1A'>;
    linkColor: Attribute.String & Attribute.DefaultTo<'#004a91'>;
    sendButtonColor: Attribute.String & Attribute.DefaultTo<'#1A1A1A'>;
    sendButtonTextColor: Attribute.String & Attribute.DefaultTo<'#E9E9E9'>;
    tooltipBackgroundColor: Attribute.String & Attribute.DefaultTo<'#1A1A1A'>;
    tooltipTextColor: Attribute.String & Attribute.DefaultTo<'#EFEFEF'>;
    loadingColors: Attribute.JSON;
    suggested_questions: Attribute.JSON;
    initial_message: Attribute.Text;
    tooltip_message: Attribute.String;
    second_tooltip_message: Attribute.String;
    border_color: Attribute.String & Attribute.DefaultTo<'#e0e0e0'>;
    powerd_by_text: Attribute.String & Attribute.DefaultTo<'Talkbase'>;
    show_powered_by: Attribute.Boolean & Attribute.DefaultTo<true>;
    chat_bot_icon: Attribute.String;
    profile_picture: Attribute.String;
    show_profile_picture: Attribute.Boolean & Attribute.DefaultTo<true>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::chatbot-style.chatbot-style',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::chatbot-style.chatbot-style',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCodeTrainCodeTrain extends Schema.CollectionType {
  collectionName: 'code_trains';
  info: {
    singularName: 'code-train';
    pluralName: 'code-trains';
    displayName: 'Code Train';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    file: Attribute.Media;
    train_status: Attribute.Boolean & Attribute.DefaultTo<false>;
    file_name: Attribute.String;
    knowledgebase: Attribute.Relation<
      'api::code-train.code-train',
      'oneToOne',
      'api::knowledgebase.knowledgebase'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::code-train.code-train',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::code-train.code-train',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiContactBookingContactBooking extends Schema.CollectionType {
  collectionName: 'contact_bookings';
  info: {
    singularName: 'contact-booking';
    pluralName: 'contact-bookings';
    displayName: 'contact_booking';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    user_name: Attribute.String;
    email: Attribute.Email;
    timezone: Attribute.String;
    date_time: Attribute.DateTime;
    communication_channel: Attribute.Enumeration<
      ['google_meet', 'zoom', 'teams', 'call', 'email', 'whatsapp']
    >;
    phone_number: Attribute.BigInteger;
    channel_link: Attribute.String;
    query: Attribute.Relation<
      'api::contact-booking.contact-booking',
      'oneToOne',
      'api::answer.answer'
    >;
    scrumboard_card: Attribute.Relation<
      'api::contact-booking.contact-booking',
      'oneToOne',
      'api::scrumboard-card.scrumboard-card'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::contact-booking.contact-booking',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::contact-booking.contact-booking',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiFileTrainFileTrain extends Schema.CollectionType {
  collectionName: 'file_trains';
  info: {
    singularName: 'file-train';
    pluralName: 'file-trains';
    displayName: 'File Train';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    file: Attribute.Media;
    train_status: Attribute.Boolean & Attribute.DefaultTo<false>;
    file_name: Attribute.String;
    knowledgebase: Attribute.Relation<
      'api::file-train.file-train',
      'oneToOne',
      'api::knowledgebase.knowledgebase'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::file-train.file-train',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::file-train.file-train',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiKnowledgebaseKnowledgebase extends Schema.CollectionType {
  collectionName: 'knowledgebases';
  info: {
    singularName: 'knowledgebase';
    pluralName: 'knowledgebases';
    displayName: 'knowledgebase';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    kb_id: Attribute.String;
    organization: Attribute.Relation<
      'api::knowledgebase.knowledgebase',
      'oneToOne',
      'api::organization.organization'
    >;
    file_trains: Attribute.Relation<
      'api::knowledgebase.knowledgebase',
      'oneToOne',
      'api::file-train.file-train'
    >;
    trains: Attribute.Relation<
      'api::knowledgebase.knowledgebase',
      'oneToOne',
      'api::train.train'
    >;
    default_ai_task: Attribute.Relation<
      'api::knowledgebase.knowledgebase',
      'oneToOne',
      'api::ai-task.ai-task'
    >;
    shareLink: Attribute.String;
    type: Attribute.Enumeration<['datasource', 'shopify', 'assistants']> &
      Attribute.DefaultTo<'datasource'>;
    assistant_id: Attribute.String;
    whatsapp_integration_model: Attribute.Relation<
      'api::knowledgebase.knowledgebase',
      'oneToOne',
      'api::whatsapp-integration-model.whatsapp-integration-model'
    >;
    shopify_model: Attribute.Relation<
      'api::knowledgebase.knowledgebase',
      'oneToOne',
      'api::shopify-model.shopify-model'
    >;
    chatbot_style: Attribute.Relation<
      'api::knowledgebase.knowledgebase',
      'oneToOne',
      'api::chatbot-style.chatbot-style'
    >;
    prompt_prefix: Attribute.Text;
    scrumboard: Attribute.Relation<
      'api::knowledgebase.knowledgebase',
      'manyToOne',
      'api::scrumboard.scrumboard'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::knowledgebase.knowledgebase',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::knowledgebase.knowledgebase',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiLabelLabel extends Schema.CollectionType {
  collectionName: 'labels';
  info: {
    singularName: 'label';
    pluralName: 'labels';
    displayName: 'Label';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    board_ids: Attribute.Relation<
      'api::label.label',
      'manyToMany',
      'api::scrumboard.scrumboard'
    >;
    title: Attribute.String;
    scrumboard_card: Attribute.Relation<
      'api::label.label',
      'manyToOne',
      'api::scrumboard-card.scrumboard-card'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::label.label',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::label.label',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiLaunchConfigLaunchConfig extends Schema.CollectionType {
  collectionName: 'launch_configs';
  info: {
    singularName: 'launch-config';
    pluralName: 'launch-configs';
    displayName: 'launch_setting';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    version: Attribute.String;
    release_date: Attribute.String;
    feedback_url: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::launch-config.launch-config',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::launch-config.launch-config',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMonthlyUsageMonthlyUsage extends Schema.CollectionType {
  collectionName: 'monthly_usages';
  info: {
    singularName: 'monthly-usage';
    pluralName: 'monthly-usages';
    displayName: 'Monthly Usage';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    training_char_count: Attribute.Integer & Attribute.DefaultTo<0>;
    training_tokens_count: Attribute.Integer & Attribute.DefaultTo<0>;
    query_tokens_count: Attribute.Integer & Attribute.DefaultTo<0>;
    query_count: Attribute.Integer & Attribute.DefaultTo<0>;
    usage_quota: Attribute.Integer & Attribute.DefaultTo<0>;
    total_tokens_used: Attribute.Integer & Attribute.DefaultTo<0>;
    trained_count: Attribute.Integer & Attribute.DefaultTo<0>;
    kb_count: Attribute.Integer & Attribute.DefaultTo<0>;
    cost_training: Attribute.Decimal & Attribute.DefaultTo<0>;
    cost_query: Attribute.Decimal & Attribute.DefaultTo<0>;
    cost_used: Attribute.Decimal & Attribute.DefaultTo<0>;
    organization: Attribute.Relation<
      'api::monthly-usage.monthly-usage',
      'manyToOne',
      'api::organization.organization'
    >;
    period: Attribute.Enumeration<['year', 'month']> &
      Attribute.DefaultTo<'month'>;
    credits_used: Attribute.BigInteger & Attribute.DefaultTo<'0'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::monthly-usage.monthly-usage',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::monthly-usage.monthly-usage',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiOrganizationOrganization extends Schema.CollectionType {
  collectionName: 'organizations';
  info: {
    singularName: 'organization';
    pluralName: 'organizations';
    displayName: 'organization';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    name: Attribute.String;
    users_limit: Attribute.Integer & Attribute.DefaultTo<10>;
    usage_quota: Attribute.Decimal & Attribute.DefaultTo<10>;
    tokens_used: Attribute.Decimal & Attribute.DefaultTo<0>;
    paid: Attribute.Decimal & Attribute.DefaultTo<20>;
    cost_used: Attribute.Decimal & Attribute.DefaultTo<0>;
    org_id: Attribute.String;
    users: Attribute.Relation<
      'api::organization.organization',
      'oneToMany',
      'plugin::users-permissions.user'
    >;
    subscription: Attribute.Enumeration<
      ['subscribed', 'unsubscribed', 'renewPending', 'renewFail', 'trial']
    > &
      Attribute.DefaultTo<'unsubscribed'>;
    blocked: Attribute.Boolean & Attribute.DefaultTo<false>;
    subscribedOn: Attribute.DateTime;
    subscriptionUpdatedOn: Attribute.DateTime;
    subscription_orders: Attribute.Relation<
      'api::organization.organization',
      'oneToMany',
      'api::subscription-order.subscription-order'
    >;
    type: Attribute.Enumeration<['individual', 'organization']> &
      Attribute.DefaultTo<'individual'>;
    allowed_kbs: Attribute.Integer & Attribute.DefaultTo<0>;
    allowed_training_char_count: Attribute.Integer & Attribute.DefaultTo<0>;
    allowed_query_count: Attribute.Integer & Attribute.DefaultTo<0>;
    allowed_trained_count: Attribute.Integer & Attribute.DefaultTo<0>;
    allowed_query_tokens_count: Attribute.BigInteger & Attribute.DefaultTo<'0'>;
    allowed_training_tokens_count: Attribute.BigInteger &
      Attribute.DefaultTo<'0'>;
    allowed_usage_quota: Attribute.Decimal & Attribute.DefaultTo<0>;
    allowed_query_cost: Attribute.Decimal & Attribute.DefaultTo<2>;
    allowed_trained_cost: Attribute.Decimal & Attribute.DefaultTo<10>;
    training_char_count: Attribute.Integer & Attribute.DefaultTo<0>;
    monthly_usages: Attribute.Relation<
      'api::organization.organization',
      'oneToMany',
      'api::monthly-usage.monthly-usage'
    >;
    validation_type: Attribute.Enumeration<['count', 'token', 'cost']> &
      Attribute.DefaultTo<'cost'>;
    plan: Attribute.Relation<
      'api::organization.organization',
      'manyToOne',
      'api::plan.plan'
    >;
    ai_task: Attribute.Relation<
      'api::organization.organization',
      'manyToOne',
      'api::ai-task.ai-task'
    >;
    api_access_keys: Attribute.Relation<
      'api::organization.organization',
      'oneToMany',
      'api::api-access-key.api-access-key'
    > &
      Attribute.Private;
    stripe_customer: Attribute.String;
    allowed_credits: Attribute.BigInteger & Attribute.DefaultTo<'0'>;
    scrumboards: Attribute.Relation<
      'api::organization.organization',
      'oneToMany',
      'api::scrumboard.scrumboard'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::organization.organization',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::organization.organization',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiOtpRequestOtpRequest extends Schema.CollectionType {
  collectionName: 'otp_requests';
  info: {
    singularName: 'otp-request';
    pluralName: 'otp-requests';
    displayName: 'OtpRequest';
    description: '';
  };
  options: {
    privateAttributes: ['otp'];
    draftAndPublish: false;
  };
  attributes: {
    otp: Attribute.Integer & Attribute.Private;
    requestID: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.DefaultTo<'f0d8368d-85e2-54fb-73c4-2d60374295e3'>;
    timeToLive: Attribute.Integer & Attribute.DefaultTo<90>;
    phone: Attribute.String & Attribute.Required;
    failedAttemptCount: Attribute.Integer & Attribute.DefaultTo<0>;
    resendAttemptCount: Attribute.Integer & Attribute.DefaultTo<0>;
    failedAttemptTime: Attribute.DateTime;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::otp-request.otp-request',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::otp-request.otp-request',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPlanPlan extends Schema.CollectionType {
  collectionName: 'plans';
  info: {
    singularName: 'plan';
    pluralName: 'plans';
    displayName: 'Plan';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    name: Attribute.String;
    description: Attribute.String;
    stripe_prod_id: Attribute.String;
    paid: Attribute.Decimal & Attribute.DefaultTo<20>;
    allowed_usage: Attribute.Integer & Attribute.DefaultTo<5>;
    allowed_kbs: Attribute.Integer & Attribute.DefaultTo<5>;
    message_allowed: Attribute.Integer & Attribute.DefaultTo<100>;
    training_allowed: Attribute.Integer & Attribute.DefaultTo<10>;
    organizations: Attribute.Relation<
      'api::plan.plan',
      'oneToMany',
      'api::organization.organization'
    >;
    allowed_training_tokens: Attribute.Integer & Attribute.DefaultTo<100000>;
    allowed_query_tokens: Attribute.Integer & Attribute.DefaultTo<100000>;
    allowed_training_cost: Attribute.Decimal & Attribute.DefaultTo<0>;
    allowed_query_cost: Attribute.Decimal & Attribute.DefaultTo<0>;
    key_features: Attribute.JSON & Attribute.DefaultTo<[]>;
    type: Attribute.Enumeration<['monthly', 'yearly']> &
      Attribute.DefaultTo<'monthly'>;
    allowed_credits: Attribute.BigInteger;
    allowed_scrape_train_count: Attribute.Integer & Attribute.DefaultTo<5>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::plan.plan', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::plan.plan', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiProfileProfile extends Schema.CollectionType {
  collectionName: 'profiles';
  info: {
    singularName: 'profile';
    pluralName: 'profiles';
    displayName: 'profile';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    first_name: Attribute.String;
    last_name: Attribute.String;
    email: Attribute.Email;
    phone_no: Attribute.BigInteger &
      Attribute.SetMinMaxLength<{
        minLength: 10;
        maxLength: 10;
      }>;
    profile_picture: Attribute.Media;
    organization: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::profile.profile',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::profile.profile',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiScrumboardScrumboard extends Schema.CollectionType {
  collectionName: 'scrumboards';
  info: {
    singularName: 'scrumboard';
    pluralName: 'scrumboards';
    displayName: 'scrumboard';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.Text;
    icon: Attribute.String & Attribute.DefaultTo<'heroicons-outline:template'>;
    lastActivity: Attribute.DateTime;
    members: Attribute.Relation<
      'api::scrumboard.scrumboard',
      'manyToMany',
      'plugin::users-permissions.user'
    >;
    settings: Attribute.JSON &
      Attribute.DefaultTo<{
        subscribed: true;
        cardCoverImages: true;
      }>;
    scrumboard_cards: Attribute.Relation<
      'api::scrumboard.scrumboard',
      'oneToMany',
      'api::scrumboard-card.scrumboard-card'
    >;
    labels: Attribute.Relation<
      'api::scrumboard.scrumboard',
      'manyToMany',
      'api::label.label'
    >;
    lists: Attribute.Relation<
      'api::scrumboard.scrumboard',
      'oneToMany',
      'api::scrumboard-list.scrumboard-list'
    >;
    organization: Attribute.Relation<
      'api::scrumboard.scrumboard',
      'manyToOne',
      'api::organization.organization'
    >;
    knowledgebases: Attribute.Relation<
      'api::scrumboard.scrumboard',
      'oneToMany',
      'api::knowledgebase.knowledgebase'
    >;
    scrumboard_automations: Attribute.Relation<
      'api::scrumboard.scrumboard',
      'oneToMany',
      'api::scrumboard-automation.scrumboard-automation'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::scrumboard.scrumboard',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::scrumboard.scrumboard',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiScrumboardAutomationScrumboardAutomation
  extends Schema.CollectionType {
  collectionName: 'scrumboard_automations';
  info: {
    singularName: 'scrumboard-automation';
    pluralName: 'scrumboard-automations';
    displayName: 'scrumboard_automation';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    prompt: Attribute.Text &
      Attribute.Required &
      Attribute.DefaultTo<'You customer service manager. Given some conversation history you have to create a ticket json model with the following fields.  For every key, give two keys a \u201Cvalue\u201D and \u201Cfield_type\u201D. The field type can be \u201CLabel\u201D , \u201CShort Text\u201D , \u201CLong Text\u201D , \u201CNumber\u201D  or \u201CBool\u201D. Label should be one word. Just output the json and nothing else. Don\u2019t explain your answer. {     \u201Ctitle\u201D:Subject of the inquiry     \u201Cdescription\u201D:  A detailed description of the customers enquiry,     \u201Cconversation_summary\u201D:A brief overview of the conversation history,     \u201Cseverity_label\u201D:The level of severifty of the inquiry as a label. Possible value \u201CUrgent\u201D \u201CHigh\u201D \u201CLow\u201D \u201CVery Low\u201D,     \u201Carea_label\u201D:Category of the inquiry based on area of work as a Label,     \u201Cnature_label\u201D: Type of inquiry, based on nature of work as a Label,     \u201Cnew_customer\u201D: Bool indicating if the customer is new or existing. True it it is a new customer, false otherwise,     \u201Csuggested_actions\u201D: A series of recommended steps to address the customer\u2019s inquiry,     \u201Crequires_on_site_support\u201D: Indicates whether on-site support is needed,     \u201Cdid_customer_request_visit\u201D: Indicates if the customer requested an on-site visit,     \u201Ccustomer_engagement_score\u201D:  A numeric score representing the level of customer engagement out of 100",     \u201Clead_potential\u201D: Assessment of the potential for the customer to become a lead as a string a label with possible values \u201CVery High\u201D \u201CHigh\u201D \u201CMedium\u201D \u201CLow\u201D Very Low\u201D     \u201Cfollow_up_required\u201D: Indicates if follow-up action is needed,     \u201Ccustomer_segment\u201D: Classification of the customer,     \u201Cservice_interest_level\u201D: The customer\u2019s level of interest in the service as a label, possible values are \u201CVery High\u201D \u201CHigh\u201D \u201CMedium\u201D \u201CLow\u201D Very Low\u201D     \u201Cescalation_required\u201D: Indicates if the inquiry needs to be escalated,     \u201Cfeedback_requested\u201D: Indicates if feedback was requested from the customer,     \u201Cproduct_interest\u201D: The specific product the customer is interested in     \u201Cestimated_resolution_time\u201D: The estimated time to resolve the customer\u2019s inquiry }'>;
    automation_identifier: Attribute.Enumeration<['ticket_create']> &
      Attribute.DefaultTo<'ticket_create'>;
    scrumboard: Attribute.Relation<
      'api::scrumboard-automation.scrumboard-automation',
      'manyToOne',
      'api::scrumboard.scrumboard'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::scrumboard-automation.scrumboard-automation',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::scrumboard-automation.scrumboard-automation',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiScrumboardCardScrumboardCard extends Schema.CollectionType {
  collectionName: 'scrumboard_cards';
  info: {
    singularName: 'scrumboard-card';
    pluralName: 'scrumboard-cards';
    displayName: 'scrumboard_card';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.Text;
    ticketId: Attribute.String;
    ai_assesment: Attribute.Text;
    labels: Attribute.Relation<
      'api::scrumboard-card.scrumboard-card',
      'oneToMany',
      'api::label.label'
    >;
    dueDate: Attribute.DateTime;
    attachmentCoverId: Attribute.String;
    memberIds: Attribute.Relation<
      'api::scrumboard-card.scrumboard-card',
      'oneToMany',
      'plugin::users-permissions.user'
    >;
    subscribed: Attribute.Boolean & Attribute.DefaultTo<true>;
    checklists: Attribute.JSON & Attribute.DefaultTo<[]>;
    attachments: Attribute.JSON & Attribute.DefaultTo<[]>;
    activities: Attribute.Relation<
      'api::scrumboard-card.scrumboard-card',
      'oneToMany',
      'api::scrumboard-comment.scrumboard-comment'
    >;
    scrumboard: Attribute.Relation<
      'api::scrumboard-card.scrumboard-card',
      'manyToOne',
      'api::scrumboard.scrumboard'
    >;
    listId: Attribute.Relation<
      'api::scrumboard-card.scrumboard-card',
      'oneToOne',
      'api::scrumboard-list.scrumboard-list'
    >;
    contact_booking: Attribute.Relation<
      'api::scrumboard-card.scrumboard-card',
      'oneToOne',
      'api::contact-booking.contact-booking'
    >;
    ticket_info: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::scrumboard-card.scrumboard-card',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::scrumboard-card.scrumboard-card',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiScrumboardCommentScrumboardComment
  extends Schema.CollectionType {
  collectionName: 'scrumboard_comments';
  info: {
    singularName: 'scrumboard-comment';
    pluralName: 'scrumboard-comments';
    displayName: 'scrumboard_comment';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    type: Attribute.Enumeration<['comment']> & Attribute.DefaultTo<'comment'>;
    message: Attribute.String;
    time: Attribute.DateTime;
    scrumboard_card: Attribute.Relation<
      'api::scrumboard-comment.scrumboard-comment',
      'manyToOne',
      'api::scrumboard-card.scrumboard-card'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::scrumboard-comment.scrumboard-comment',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::scrumboard-comment.scrumboard-comment',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiScrumboardListScrumboardList extends Schema.CollectionType {
  collectionName: 'scrumboard_lists';
  info: {
    singularName: 'scrumboard-list';
    pluralName: 'scrumboard-lists';
    displayName: 'scrumboard_list';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    title: Attribute.String;
    boardId: Attribute.Relation<
      'api::scrumboard-list.scrumboard-list',
      'manyToOne',
      'api::scrumboard.scrumboard'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::scrumboard-list.scrumboard-list',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::scrumboard-list.scrumboard-list',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiShopifyModelShopifyModel extends Schema.CollectionType {
  collectionName: 'shopify_models';
  info: {
    singularName: 'shopify-model';
    pluralName: 'shopify-models';
    displayName: 'shopify_model';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    url: Attribute.String;
    store_front_api_key: Attribute.Text;
    knowledgebase: Attribute.Relation<
      'api::shopify-model.shopify-model',
      'oneToOne',
      'api::knowledgebase.knowledgebase'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::shopify-model.shopify-model',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::shopify-model.shopify-model',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSubscriptionOrderSubscriptionOrder
  extends Schema.CollectionType {
  collectionName: 'subscription_orders';
  info: {
    singularName: 'subscription-order';
    pluralName: 'subscription-orders';
    displayName: 'Subscription Order';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    type: Attribute.Enumeration<['subscription', 'renew']>;
    organization: Attribute.Relation<
      'api::subscription-order.subscription-order',
      'manyToOne',
      'api::organization.organization'
    >;
    isPaid: Attribute.Boolean & Attribute.DefaultTo<false>;
    stripeSessionId: Attribute.String;
    invoice: Attribute.String;
    plan: Attribute.Relation<
      'api::subscription-order.subscription-order',
      'oneToOne',
      'api::plan.plan'
    >;
    plantype: Attribute.Enumeration<['monthly', 'yearly']> &
      Attribute.DefaultTo<'monthly'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::subscription-order.subscription-order',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::subscription-order.subscription-order',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTrainTrain extends Schema.CollectionType {
  collectionName: 'trains';
  info: {
    singularName: 'train';
    pluralName: 'trains';
    displayName: 'Train';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    link: Attribute.String & Attribute.Required;
    train_status: Attribute.Boolean & Attribute.DefaultTo<false>;
    knowledgebase: Attribute.Relation<
      'api::train.train',
      'oneToOne',
      'api::knowledgebase.knowledgebase'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::train.train',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::train.train',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiUrlTrackerUrlTracker extends Schema.CollectionType {
  collectionName: 'url_trackers';
  info: {
    singularName: 'url-tracker';
    pluralName: 'url-trackers';
    displayName: 'URL Tracker';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    url: Attribute.String;
    tracking_id: Attribute.UID;
    query_id: Attribute.Relation<
      'api::url-tracker.url-tracker',
      'manyToOne',
      'api::answer.answer'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::url-tracker.url-tracker',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::url-tracker.url-tracker',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiWhatsappIntegrationModelWhatsappIntegrationModel
  extends Schema.CollectionType {
  collectionName: 'whatsapp_integration_models';
  info: {
    singularName: 'whatsapp-integration-model';
    pluralName: 'whatsapp-integration-models';
    displayName: 'whatsapp integration model';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    phone_number: Attribute.BigInteger & Attribute.Required & Attribute.Unique;
    phone_number_id: Attribute.BigInteger &
      Attribute.Required &
      Attribute.Unique;
    access_token: Attribute.Text;
    knowledgebase: Attribute.Relation<
      'api::whatsapp-integration-model.whatsapp-integration-model',
      'oneToOne',
      'api::knowledgebase.knowledgebase'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::whatsapp-integration-model.whatsapp-integration-model',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::whatsapp-integration-model.whatsapp-integration-model',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface ContentTypes {
      'admin::permission': AdminPermission;
      'admin::user': AdminUser;
      'admin::role': AdminRole;
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
      'plugin::i18n.locale': PluginI18NLocale;
      'plugin::strapi-stripe.ss-product': PluginStrapiStripeSsProduct;
      'plugin::strapi-stripe.ss-payment': PluginStrapiStripeSsPayment;
      'api::ai-task.ai-task': ApiAiTaskAiTask;
      'api::answer.answer': ApiAnswerAnswer;
      'api::api-access-key.api-access-key': ApiApiAccessKeyApiAccessKey;
      'api::chatbot-style.chatbot-style': ApiChatbotStyleChatbotStyle;
      'api::code-train.code-train': ApiCodeTrainCodeTrain;
      'api::contact-booking.contact-booking': ApiContactBookingContactBooking;
      'api::file-train.file-train': ApiFileTrainFileTrain;
      'api::knowledgebase.knowledgebase': ApiKnowledgebaseKnowledgebase;
      'api::label.label': ApiLabelLabel;
      'api::launch-config.launch-config': ApiLaunchConfigLaunchConfig;
      'api::monthly-usage.monthly-usage': ApiMonthlyUsageMonthlyUsage;
      'api::organization.organization': ApiOrganizationOrganization;
      'api::otp-request.otp-request': ApiOtpRequestOtpRequest;
      'api::plan.plan': ApiPlanPlan;
      'api::profile.profile': ApiProfileProfile;
      'api::scrumboard.scrumboard': ApiScrumboardScrumboard;
      'api::scrumboard-automation.scrumboard-automation': ApiScrumboardAutomationScrumboardAutomation;
      'api::scrumboard-card.scrumboard-card': ApiScrumboardCardScrumboardCard;
      'api::scrumboard-comment.scrumboard-comment': ApiScrumboardCommentScrumboardComment;
      'api::scrumboard-list.scrumboard-list': ApiScrumboardListScrumboardList;
      'api::shopify-model.shopify-model': ApiShopifyModelShopifyModel;
      'api::subscription-order.subscription-order': ApiSubscriptionOrderSubscriptionOrder;
      'api::train.train': ApiTrainTrain;
      'api::url-tracker.url-tracker': ApiUrlTrackerUrlTracker;
      'api::whatsapp-integration-model.whatsapp-integration-model': ApiWhatsappIntegrationModelWhatsappIntegrationModel;
    }
  }
}
