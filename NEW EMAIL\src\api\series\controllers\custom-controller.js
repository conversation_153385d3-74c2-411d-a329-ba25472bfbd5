'use strict';

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::series.series', ({ strapi }) => ({

  /**
   * Get public series with pagination and filtering
   */
  async getPublicSeries(ctx) {
    try {
      const {
        page = 1,
        pageSize = 10,
        category,
        difficulty,
        featured,
        search
      } = ctx.query;

      const filters = {
        public: true,
        publishedAt: { $notNull: true }
      };

      if (category) filters.category = category;
      if (difficulty) filters.difficulty_level = difficulty;
      if (featured !== undefined) filters.featured = featured === 'true';
      if (search) {
        filters.$or = [
          { title: { $containsi: search } },
          { description: { $containsi: search } }
        ];
      }

      const series = await strapi.entityService.findMany('api::series.series', {
        filters,
        sort: featured === 'true' ? ['featured:desc', 'createdAt:desc'] : ['createdAt:desc'],
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize)
        },
        populate: {
          series_episodes: {
            populate: {
              file_results_model: {
                fields: ['id', 'feature_image_url']
              }
            }
          }
        }
      });

      // If user is authenticated, include their progress
      const user = ctx.state.user;
      if (user && series.results?.length > 0) {
        for (let s of series.results) {
          const progress = await strapi.db.query('api::series-progress.series-progress').findOne({
            where: { user: user.id, series: s.id }
          });
          s.user_progress = progress;
        }
      }

      return {
        data: series.results || [],
        pagination: series.pagination
      };
    } catch (error) {
      strapi.log.error('Error fetching public series:', error);
      return ctx.throw(500, 'Failed to fetch series');
    }
  },

  /**
   * Get featured series
   */
  async getFeaturedSeries(ctx) {
    try {
      const { limit = 5 } = ctx.query;

      const series = await strapi.entityService.findMany('api::series.series', {
        filters: {
          public: true,
          featured: true,
          publishedAt: { $notNull: true }
        },
        sort: ['display_order:asc', 'createdAt:desc'],
        limit: parseInt(limit),
        populate: {
          series_episodes: {
                    fields: ['position', 'episode_id'],
        sort: 'position:asc'
          }
        }
      });

      return { data: series };
    } catch (error) {
      strapi.log.error('Error fetching featured series:', error);
      return ctx.throw(500, 'Failed to fetch featured series');
    }
  },

  /**
   * Add episode to series
   */
  async addEpisode(ctx) {
    try {
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized('Authentication required');
      }

      const { seriesId } = ctx.params;
      const { file_results_model_id, position, title_override, description_override, is_preview } = ctx.request.body.data;

      // Check if user owns the series
      const series = await strapi.entityService.findOne('api::series.series', seriesId, {
        populate: ['created_by_user', 'organization']
      });

      if (!series) {
        return ctx.notFound('Series not found');
      }

      if (series.created_by_user?.id !== user.id && series.organization?.id !== user.organization?.id) {
        return ctx.forbidden('You do not have permission to modify this series');
      }

      // Check if position already exists
      const existingEpisode = await strapi.db.query('api::series-episode.series-episode').findOne({
        where: { series: seriesId, position }
      });

      if (existingEpisode) {
        return ctx.badRequest(`Position ${position} already exists in this series`);
      }

      // Create the episode
      const episode = await strapi.entityService.create('api::series-episode.series-episode', {
        data: {
          series: seriesId,
          file_results_model: file_results_model_id,
          position,
          title_override,
          description_override,
          is_preview: is_preview || false
        },
        populate: {
          file_results_model: {
            fields: ['id', 'title', 'audio_url', 'feature_image_url', 'excerpt']
          }
        }
      });

      // Update series total episodes count
      await strapi.entityService.update('api::series.series', seriesId, {
        data: {
          total_episodes: series.total_episodes + 1
        }
      });

      return { data: episode };
    } catch (error) {
      strapi.log.error('Error adding episode to series:', error);
      return ctx.throw(500, 'Failed to add episode to series');
    }
  },

  /**
   * Update episode progress
   */
  async updateEpisodeProgress(ctx) {
    try {
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized('Authentication required');
      }

      const { seriesId, episodeId } = ctx.params;
      const { progress_percentage, time_spent, last_position, completed } = ctx.request.body.data;

      // Validate access to series
      const series = await strapi.entityService.findOne('api::series.series', seriesId);
      if (!series) {
        return ctx.notFound('Series not found');
      }

      if (!series.public) {
        // Check if user has access to private series
        const hasAccess = await this.checkSeriesAccess(user.id, seriesId);
        if (!hasAccess) {
          return ctx.forbidden('You do not have access to this series');
        }
      }

      // Get the episode by episode_id (UUID)
      const episode = await strapi.db.query('api::series-episode.series-episode').findOne({
        where: { series: seriesId, episode_id: episodeId },
        populate: ['file_results_model']
      });

      if (!episode) {
        return ctx.notFound('Episode not found');
      }

      // Check if user can access this episode (progressive unlocking)
      const canAccess = await strapi.service('api::content-progress.content-progress')
        .canAccessEpisode(user.id, episode.episode_id);

      if (!canAccess) {
        return ctx.forbidden('This episode is not yet unlocked');
      }

      // Update or create content progress
      let contentProgress = await strapi.db.query('api::content-progress.content-progress').findOne({
        where: { user: user.id, file_results_model: episode.file_results_model.id }
      });

      const now = new Date();
      const updateData = {
        progress_percentage: progress_percentage || contentProgress?.progress_percentage || 0,
        time_spent: (contentProgress?.time_spent || 0) + (time_spent || 0),
        last_position: last_position !== undefined ? last_position : contentProgress?.last_position || 0,
        completed: completed !== undefined ? completed : (progress_percentage >= 100),
        last_accessed_at: now
      };

      if (updateData.completed && !contentProgress?.completed) {
        updateData.completed_at = now;
      }

      if (contentProgress) {
        contentProgress = await strapi.entityService.update('api::content-progress.content-progress', contentProgress.id, {
          data: updateData
        });
      } else {
        contentProgress = await strapi.entityService.create('api::content-progress.content-progress', {
          data: {
            user: user.id,
            file_results_model: episode.file_results_model.id,
            episode_id: episode.episode_id,
            series_id: seriesId,
            started_at: now,
            ...updateData
          }
        });
      }

      // Update series progress if episode is completed
      if (updateData.completed) {
        await this.updateSeriesProgress(user.id, seriesId);
      }

      return { data: episodeProgress };
    } catch (error) {
      strapi.log.error('Error updating episode progress:', error);
      return ctx.throw(500, 'Failed to update episode progress');
    }
  },

  /**
   * Mark episode as complete
   */
  async completeEpisode(ctx) {
    try {
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized('Authentication required');
      }

      const { seriesId, episodeId } = ctx.params;

      // Use the existing updateEpisodeProgress method with completion data
      ctx.request.body.data = {
        progress_percentage: 100,
        completed: true
      };

      return await this.updateEpisodeProgress(ctx);
    } catch (error) {
      strapi.log.error('Error completing episode:', error);
      return ctx.throw(500, 'Failed to complete episode');
    }
  },

  /**
   * Get user's series progress
   */
  async getUserSeriesProgress(ctx) {
    try {
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized('Authentication required');
      }

      const {
        page = 1,
        pageSize = 10,
        status = 'all' // 'all', 'in_progress', 'completed'
      } = ctx.query;

      const filters = { user: user.id };
      
      if (status === 'in_progress') {
        filters.completion_percentage = { $lt: 100, $gt: 0 };
      } else if (status === 'completed') {
        filters.completion_percentage = 100;
      }

      const progress = await strapi.entityService.findMany('api::series-progress.series-progress', {
        filters,
        sort: ['last_accessed_at:desc'],
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize)
        },
        populate: {
          series: {
            fields: ['id', 'title', 'description', 'cover_image_url', 'total_episodes', 'category', 'difficulty_level']
          }
        }
      });

      return {
        data: progress.results || [],
        pagination: progress.pagination
      };
    } catch (error) {
      strapi.log.error('Error fetching user series progress:', error);
      return ctx.throw(500, 'Failed to fetch series progress');
    }
  },

  /**
   * Grant access to private series
   */
  async grantSeriesAccess(ctx) {
    try {
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized('Authentication required');
      }

      const { seriesId } = ctx.params;
      const { user_id, organization_id, access_type = 'granted', expires_at, notes } = ctx.request.body.data;

      // Check if user can grant access (series owner or admin)
      const series = await strapi.entityService.findOne('api::series.series', seriesId, {
        populate: ['created_by_user', 'organization']
      });

      if (!series) {
        return ctx.notFound('Series not found');
      }

      if (series.public) {
        return ctx.badRequest('Cannot grant access to public series - they are already accessible to everyone');
      }

      const canGrant = series.created_by_user?.id === user.id || 
                      series.organization?.id === user.organization?.id;
      
      if (!canGrant) {
        return ctx.forbidden('You do not have permission to grant access to this series');
      }

      // Check if access already exists
      const existingAccess = await strapi.db.query('api::series-access.series-access').findOne({
        where: {
          series: seriesId,
          ...(user_id ? { user: user_id } : {}),
          ...(organization_id ? { organization: organization_id } : {})
        }
      });

      if (existingAccess) {
        return ctx.badRequest('Access already granted');
      }

      // Grant access
      const accessGrant = await strapi.entityService.create('api::series-access.series-access', {
        data: {
          series: seriesId,
          ...(user_id ? { user: user_id } : {}),
          ...(organization_id ? { organization: organization_id } : {}),
          access_type,
          granted_at: new Date(),
          expires_at,
          granted_by: user.id,
          notes
        }
      });

      return { data: accessGrant };
    } catch (error) {
      strapi.log.error('Error granting series access:', error);
      return ctx.throw(500, 'Failed to grant series access');
    }
  },

  /**
   * Revoke access to private series
   */
  async revokeSeriesAccess(ctx) {
    try {
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized('Authentication required');
      }

      const { seriesId, accessId } = ctx.params;

      // Check if user can revoke access
      const series = await strapi.entityService.findOne('api::series.series', seriesId, {
        populate: ['created_by_user', 'organization']
      });

      if (!series) {
        return ctx.notFound('Series not found');
      }

      const canRevoke = series.created_by_user?.id === user.id || 
                       series.organization?.id === user.organization?.id;
      
      if (!canRevoke) {
        return ctx.forbidden('You do not have permission to revoke access to this series');
      }

      // Revoke access
      await strapi.entityService.delete('api::series-access.series-access', accessId);

      return { message: 'Access revoked successfully' };
    } catch (error) {
      strapi.log.error('Error revoking series access:', error);
      return ctx.throw(500, 'Failed to revoke series access');
    }
  },

  // Helper methods
  async checkSeriesAccess(userId, seriesId) {
    // Get the series to check if it's public
    const series = await strapi.entityService.findOne('api::series.series', seriesId, {
      fields: ['id', 'public', 'organization', 'access_tier'],
      populate: {
        organization: {
          fields: ['id']
        },
        created_by_user: {
          fields: ['id']
        }
      }
    });

    if (!series) {
      return false;
    }

    // If series is public, everyone has access
    if (series.public) {
      return true;
    }

    // For private series, check access hierarchy
    const user = await strapi.entityService.findOne('plugin::users-permissions.user', userId, {
      populate: {
        organization: {
          fields: ['id']
        }
      }
    });

    if (!user) {
      return false;
    }

    // Check if user is the creator
    if (series.created_by_user?.id === userId) {
      return true;
    }

    // Check if user's organization owns the series
    if (series.organization?.id === user.organization?.id) {
      return true;
    }

    // Check subscription-based access
    const hasSubscriptionAccess = await this.checkSeriesSubscriptionAccess(userId, seriesId);
    if (hasSubscriptionAccess) {
      return true;
    }

    // Check for explicit access grants (user or organization level)
    const now = new Date();
    const accessGrant = await strapi.db.query('api::series-access.series-access').findOne({
      where: {
        series: seriesId,
        $and: [
          {
            $or: [
              { user: userId },
              { organization: user.organization?.id }
            ]
          },
          {
            $or: [
              { expires_at: null }, // No expiration
              { expires_at: { $gt: now } } // Not expired
            ]
          }
        ]
      }
    });

    return !!accessGrant;
  },

  /**
   * Check series access based on subscription level
   */
  async checkSeriesSubscriptionAccess(userId, seriesId) {
    try {
      // Get user with organization and plan info
      const user = await strapi.query("plugin::users-permissions.user").findOne({
        where: { id: userId },
        populate: {
          organization: {
            populate: {
              plan: true,
              subscription_orders: {
                sort: 'createdAt:desc',
                limit: 1
              }
            }
          }
        }
      });

      if (!user?.organization) {
        return false;
      }

      const org = user.organization;
      const plan = org.plan;
      const latestSubscription = org.subscription_orders?.[0];

      // Check if subscription is active (using existing logic)
      let isActive = false;
      if (latestSubscription) {
        const now = new Date();
        const endDate = new Date(latestSubscription.current_period_end);
        isActive = (
          (latestSubscription.status === 'active' || latestSubscription.status === 'trialing') &&
          endDate > now
        );
      }

      if (!isActive || !plan) {
        return false;
      }

      // Get series access requirements
      const series = await strapi.entityService.findOne('api::series.series', seriesId, {
        fields: ['id', 'access_tier']
      });

      if (!series) {
        return false;
      }

      // Check if plan includes this series tier
      const planAccessLevel = plan.series_access_level || 'none';
      const seriesRequiredTier = series.access_tier || 'free';

      const tierHierarchy = {
        'none': 0,
        'free': 1,
        'basic': 2,
        'premium': 3,
        'enterprise': 4,
        'all': 5
      };

      return tierHierarchy[planAccessLevel] >= tierHierarchy[seriesRequiredTier];

    } catch (error) {
      strapi.log.error('Error checking series subscription access:', error);
      return false;
    }
  },

  async updateSeriesProgress(userId, seriesId) {
    try {
      // Get all episodes in the series
      const episodes = await strapi.db.query('api::series-episode.series-episode').findMany({
        where: { series: seriesId },
        orderBy: { position: 'asc' }
      });

      // Get user's content progress for this series
      const contentProgress = await strapi.db.query('api::content-progress.content-progress').findMany({
        where: { user: userId, series_id: seriesId }
      });

      const completedEpisodes = contentProgress.filter(ep => ep.completed).length;
      const totalTimeSpent = contentProgress.reduce((total, ep) => total + (ep.time_spent || 0), 0);
      const completionPercentage = episodes.length > 0 ? (completedEpisodes / episodes.length) * 100 : 0;
      
      // Find next episode to unlock
      const nextEpisodeNumber = Math.min(completedEpisodes + 1, episodes.length);
      
      // Update series progress
      const seriesProgress = await strapi.db.query('api::series-progress.series-progress').findOne({
        where: { user: userId, series: seriesId }
      });

      // Find the last accessed episode ID
      const lastAccessedProgress = contentProgress
        .sort((a, b) => new Date(b.last_accessed_at) - new Date(a.last_accessed_at))[0];

      const updateData = {
        last_episode_id: lastAccessedProgress?.episode_id,
        last_accessed_at: new Date()
      };

      if (completionPercentage >= 100 && !seriesProgress?.completed_at) {
        updateData.completed_at = new Date();
      }

      await strapi.entityService.update('api::series-progress.series-progress', seriesProgress.id, {
        data: updateData
      });

    } catch (error) {
      strapi.log.error('Error updating series progress:', error);
    }
  }

})); 