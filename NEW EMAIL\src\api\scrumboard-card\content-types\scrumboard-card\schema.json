{"kind": "collectionType", "collectionName": "scrumboard_cards", "info": {"singularName": "scrumboard-card", "pluralName": "scrumboard-cards", "displayName": "scrumboard_card", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "description": {"type": "text"}, "ticketId": {"type": "string"}, "ai_assesment": {"type": "text"}, "labels": {"type": "relation", "relation": "oneToMany", "target": "api::label.label", "mappedBy": "scrumboard_card"}, "dueDate": {"type": "datetime"}, "attachmentCoverId": {"type": "string"}, "memberIds": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user", "mappedBy": "scrumboard_card"}, "subscribed": {"type": "boolean", "default": true}, "checklists": {"type": "json", "default": []}, "attachments": {"type": "json", "default": []}, "activities": {"type": "relation", "relation": "oneToMany", "target": "api::scrumboard-comment.scrumboard-comment", "mappedBy": "scrumboard_card"}, "scrumboard": {"type": "relation", "relation": "manyToOne", "target": "api::scrumboard.scrumboard", "inversedBy": "scrumboard_cards"}, "listId": {"type": "relation", "relation": "oneToOne", "target": "api::scrumboard-list.scrumboard-list"}, "contact_booking": {"type": "relation", "relation": "oneToOne", "target": "api::contact-booking.contact-booking", "inversedBy": "scrumboard_card"}, "ticket_info": {"type": "json"}}}