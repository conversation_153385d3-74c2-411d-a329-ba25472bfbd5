'use strict';

/**
 * Answer lifecycle
 */
const _=require('lodash');
module.exports = {
	async afterCreate(event){
		console.log(event)
		//To disable pdf sources
		if(event.result.api_source !=='dashboard' && (!_.isEmpty(event.result.sources))){
			const validUrlRegex = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/;
			event.result.sources=event.result.sources.filter(source => validUrlRegex.test(source));
			console.log(event.result);
		}
	},
	async afterFindOne(event){
		//To disable pdf sources
		if(event.result.api_source !=='dashboard' && (!_.isEmpty(event.result.sources))){
			const validUrlRegex = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/;
			event.result.sources=event.result.sources.filter(source => validUrlRegex.test(source));
		}
	},

	async afterFindMany(event){
		//To disable pdf sources
		event.result=event.result.map(a=>{
		if(!_.isEmpty(a.sources)){
			const validUrlRegex = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/;
   			a.sources = a.sources.filter(source => validUrlRegex.test(source));
		} 
		})
	}

}