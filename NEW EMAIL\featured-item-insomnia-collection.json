{"_type": "export", "__export_format": 4, "__export_date": "2024-07-30T12:00:00.000Z", "__export_source": "insomnia.desktop.app:v2023.5.8", "resources": [{"_id": "wrk_featured_item", "parentId": null, "modified": 1678886400000, "created": 1678886400000, "name": "Featured Item API", "description": "", "scope": "collection", "_type": "workspace"}, {"_id": "env_base", "parentId": "wrk_featured_item", "modified": 1678886400000, "created": 1678886400000, "name": "Base Environment", "data": {"base_url": "http://localhost:1337/api"}, "dataPropertyOrder": {"&": ["base_url"]}, "color": null, "isPrivate": false, "metaSortKey": 1678886400000, "_type": "environment"}, {"_id": "req_create_featured_item", "parentId": "wrk_featured_item", "modified": 1678886400000, "created": 1678886400000, "url": "{{ _.base_url }}/featured-items", "name": "Create Featured <PERSON>em", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\"data\":{\"title\":\"New Awesome Playlist\",\"description\":\"Check out this curated playlist of hits!\",\"type\":\"Playlist\",\"cover_image\":1,\"pill_text\":\"Hot\",\"call_to_action_text\":\"Listen Now\",\"navigation_target_playlist\":1,\"display_order\":1,\"start_date\":\"2024-08-01T00:00:00.000Z\",\"end_date\":\"2024-09-01T00:00:00.000Z\"}}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}], "authentication": {}, "metaSortKey": -100, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_get_all_featured_items", "parentId": "wrk_featured_item", "modified": 1678886400000, "created": 1678886400000, "url": "{{ _.base_url }}/featured-items", "name": "Get All Featured Items", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -90, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_get_all_featured_items_paginated", "parentId": "wrk_featured_item", "modified": 1678886400000, "created": 1678886400000, "url": "{{ _.base_url }}/featured-items", "name": "Get All Featured Items (Paginated)", "description": "", "method": "GET", "body": {}, "parameters": [{"name": "pagination[page]", "value": "1"}, {"name": "pagination[pageSize]", "value": "10"}, {"name": "sort", "value": "display_order:asc"}], "headers": [], "authentication": {}, "metaSortKey": -85, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_get_featured_item_by_id", "parentId": "wrk_featured_item", "modified": 1678886400000, "created": 1678886400000, "url": "{{ _.base_url }}/featured-items/1", "name": "Get Featured Item by ID", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -80, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_update_featured_item", "parentId": "wrk_featured_item", "modified": 1678886400000, "created": 1678886400000, "url": "{{ _.base_url }}/featured-items/1", "name": "Update Featured Item", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\"data\":{\"title\":\"Updated Awesome Playlist\",\"description\":\"This playlist has been updated with even more hits!\",\"pill_text\":\"Updated Hot\"}}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}], "authentication": {}, "metaSortKey": -70, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_delete_featured_item", "parentId": "wrk_featured_item", "modified": 1678886400000, "created": 1678886400000, "url": "{{ _.base_url }}/featured-items/1", "name": "Delete Featured Item", "description": "", "method": "DELETE", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -60, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}]}