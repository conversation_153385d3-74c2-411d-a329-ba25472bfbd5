import { <PERSON><PERSON>, Divide<PERSON>, <PERSON>, Paper, Stack, Typography } from "@mui/material";

import { Fragment, useEffect, useState } from "react";
import axios from "axios";
import { useSelector } from "react-redux";
import { selectUser } from "app/store/userSlice";
import SessionItem from "./SessionItem";
import SessionsSkeleton from "./SessionsSkeleton";
import { useSearchParams } from "react-router-dom";
import EmptyLottie from "app/shared-components/empty-data/EmptyLottie";

export default function SessionsSidebar() {
  const user = useSelector(selectUser);
  const [sessions, setSessions] = useState([]);
  const [pagination, setPagination] = useState({});
  const [loading, setLoading] = useState(true);
  const [searchParams, setSearchParams] = useSearchParams();

  async function fetchSessions(page) {
    setLoading(true);
    try {
      const resp = await axios.get(
        `${
          process.env.REACT_APP_AUTH_BASE_URL
        }/api/recent-sessions?pageSize=10${page ? `&page=${page}` : ""}`
      );
      setSessions([...sessions, ...resp.data?.data]);
      setPagination(resp.data?.pagination);
      if (resp.data?.data?.length && !searchParams.get("sessionId")) {
        setSearchParams({ sessionId: resp.data?.data[0].session.session_id });
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchSessions(1);
    return () => {
      setLoading(false);
    };
  }, []);

  async function handleFetchMore() {
    fetchSessions(pagination.page ? pagination.page + 1 : 1);
  }

  return (
    <Paper elevation={0} className="border rounded-none h-full">
      <List
        subheader={
          <Typography variant="body2" className="font-bold p-20 text-lg">
            Sessions
          </Typography>
        }
        className="w-full"
      >
        {loading && <SessionsSkeleton />}
        {sessions?.length ? (
          sessions.map((session) => (
            <Fragment key={session.session.session_id}>
              <SessionItem
                apiSource={session.query.api_source}
                knowledgeBaseName={session.knowledgebase_name}
                question={session.query.question}
                sessionId={session.session.session_id}
                createdAt={session.session.createdAt}
              />
              <Divider variant="middle" component="li" />
            </Fragment>
          ))
        ) : (
          <EmptyLottie allowCreateAgent={false} text="No sessions found" />
        )}
        {pagination.page < pagination.totalPages && (
          <Stack direction={"row"} justifyContent={"center"}>
            <Button
              disabled={loading}
              onClick={handleFetchMore}
              variant="outlined"
              size="small"
              className="mt-4"
            >
              Load more
            </Button>
          </Stack>
        )}
      </List>
    </Paper>
  );
}
