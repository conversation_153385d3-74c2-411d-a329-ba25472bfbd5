'use strict';

/**
 * Update organization policy
 */
const axios = require('axios');
const { hasActiveSubscriptionPlan } = require('../helpers/has-subscription');
module.exports = async (policyContext, config, { strapi }) => {
	// Check if this is a password reset flow - allow unauthenticated access
	const isPasswordResetFlow = policyContext.request.url.includes('/auth/reset-password') ||
	                           policyContext.request.url.includes('/reset-password') ||
	                           policyContext.request.headers.referer?.includes('/reset-password');

	if (isPasswordResetFlow) {
		console.log('🔓 Password reset flow detected in update-org policy - skipping org update');
		return true;
	}

	if (policyContext.state.user) {
		const result = await axios.get(
			`${process.env.TALKBASE_BASE_URL}/v4/org?org_id=${policyContext.state.user.organization.org_id}`,
			{
				headers: {
				  'Content-Type': 'multipart/form-data'
				}
			}
			).catch(err=>{
				console.log(err);
			});
		if(!policyContext.state.user.organization.current_month_usage){
			

			const customError = new Error("You don't have subscription");
			customError.status = 403; // Set the desired status code
			throw customError;
			
		}
		const isActive = hasActiveSubscriptionPlan(policyContext.state.user.organization);
		if(!isActive){
			let model = await strapi.query('api::organization.organization').update({
				where: { 
					id:policyContext.state.user.organization.id,
				},
				data: {
					subscription: 'renewFail'
				}
			});
			const customError = new Error("You don't have subscription");
			customError.status = 403; // Set the desired status code
			throw customError;

		}
		// const current_month_usage= policyContext.state.user.organization.monthly_usages.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];
		let model = await strapi.query('api::monthly-usage.monthly-usage').update({
			where: { 
				id:policyContext.state.user.organization.current_month_usage.id,
			},
			data: {
				cost_used: result.data.cost,
				credits_used: result.data.credits_used,
				query_count: result.data.query_messages_count,
				query_tokens_count: result.data.query_tokens_count,
				training_tokens_count:result.data.training_tokens_count,
				training_char_count:result.data.training_chars_count,
				cost_query: result.data.cost_query,
				cost_training: result.data.cost_training,

				},
			})
		policyContext.state.user.organization.current_month_usage=model;
	}else{
		const customError = new Error("Doesn't have user info");
		customError.status = 500; // Set the desired status code
		throw customError;
	}	
  
	return true; // If you return nothing, Strapi considers you didn't want to block the request and will let it pass

};