// policies/PhoneNumberValidation.js
const { phone } = require('phone');

module.exports = (policyContext, config, { strapi }) => {
	const { PolicyError } = require("@strapi/utils").errors;
	const params = policyContext.request.body;
	const phoneNum = phone(( params.phone ).toString(), {country: 'IN'})

	if ( ! phoneNum.isValid ) {
		strapi.log.error('Invalid Phone number');
		return false;
	}

	return true;
};
