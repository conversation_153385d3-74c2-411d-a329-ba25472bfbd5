'use strict';

/**
 * Knowledgebase lifecycle
 */
const CryptoJS = require('crypto-js');
const { scrumboardList, agentTaskId } = require('../../../../helpers/defaults');
const knowledgebase = require('../../controllers/knowledgebase');
module.exports = {
	async beforeCreate(event) {
	  const { data, where, select, populate } = event.params;
	  if(data.type !="assistants"){
	  if (!data.default_ai_task) {
		let taskid;
		if(data.type==='shopify'){
			taskid=12;
		}if(data.type==='autoAgent'){
			taskid=21;
		}
		else{
			taskid=agentTaskId;
		}
		const aiTask = await strapi.query('api::ai-task.ai-task').findOne({
			where: { task_id: taskid }
		}).catch(err => {
			console.log(err);
		});
		event.params.data.default_ai_task = aiTask.id;
      }
	}
	},
	async afterCreate(event){
		const { result, params } = event;
		try{
		const kb= await strapi.query('api::knowledgebase.knowledgebase').findOne( { where: { id: result.id },populate:{organization:true,default_ai_task:true}});
		const kanban= await strapi.query('api::scrumboard.scrumboard').create({data:{title:kb.name,description:"Scrumboard to handle "+kb.name+" chatbot issues",owner:kb.organization.id}})
		scrumboardList.map(async (list)=>{
		const listRecord= await strapi.query('api::scrumboard-list.scrumboard-list').create({data:{title:list,boardId:kanban.id}});
		});
		const encryptedKb = await CryptoJS.AES.encrypt(result.kb_id, process.env.ENCRYPTION_KEY).toString();
		const encryptedOrg = await CryptoJS.AES.encrypt(kb.organization.org_id, process.env.ENCRYPTION_KEY).toString();
		const shareLink=`${process.env.TALKBASE_SHARE_URL}?chatbotId=${encryptedKb}&orgId=${encryptedOrg}`;
		event.params.data.shareLink = shareLink;
		const updatedModelEntry = await strapi.query('api::knowledgebase.knowledgebase').update({
			where:{ id: result.id },
			data:{ shareLink: shareLink,
				scrumboard:kanban.id } 
		} );
		const style= await strapi.query('api::chatbot-style.chatbot-style').create({data:{knowledgebase:kb.id}});
		
		}catch(e){
			console.log(e);
		}
	}

  };
