{"kind": "collectionType", "collectionName": "otp_requests", "info": {"singularName": "otp-request", "pluralName": "otp-requests", "displayName": "OtpRequest", "description": ""}, "options": {"privateAttributes": ["otp"], "draftAndPublish": false}, "pluginOptions": {}, "attributes": {"otp": {"type": "integer", "configurable": false, "private": true}, "requestID": {"type": "string", "unique": true, "required": true, "default": "f0d8368d-85e2-54fb-73c4-2d60374295e3"}, "timeToLive": {"type": "integer", "default": 90}, "phone": {"type": "string", "required": true}, "failedAttemptCount": {"type": "integer", "default": 0}, "resendAttemptCount": {"type": "integer", "default": 0}, "failedAttemptTime": {"type": "datetime"}}}