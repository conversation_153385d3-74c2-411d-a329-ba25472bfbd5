import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import axios from 'axios';

// Async thunk to fetch home data
export const getHomeData = createAsyncThunk(
  'homeDashboardApp/homeData/getHomeData',
  async (arg, { rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/usage?range=7`
      );
      // Adjust this if your API nests data under `data`
      return response.data?.data || response.data;
    } catch (error) {
      console.error("Error fetching home data:", error);
      return rejectWithValue(error.response?.data || {});
    }
  }
);

// Corrected initial state: should be an object, not an array
const initialState = {};

// Create the slice
const homeSlice = createSlice({
  name: 'homeDashboardApp/homeData',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(getHomeData.fulfilled, (state, action) => {
      return action.payload;
    });
  },
});

// Selector to access home data from the Redux state
export const selectHomeData = ({ homeDashboardApp }) =>
  homeDashboardApp.homeData;

// Export the reducer
export default homeSlice.reducer;
