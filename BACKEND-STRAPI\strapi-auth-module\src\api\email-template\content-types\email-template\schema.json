{"kind": "collectionType", "collectionName": "email_templates", "info": {"singularName": "email-template", "pluralName": "email-templates", "displayName": "<PERSON>ail Te<PERSON>late", "description": "Manage email templates for different providers and use cases including ticket notifications"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "unique": true, "description": "Unique template identifier (e.g., 'ticket-notification', 'welcome-email')"}, "display_name": {"type": "string", "description": "Human-readable template name for admin interface"}, "description": {"type": "text", "description": "Template description and usage notes"}, "provider": {"type": "enumeration", "enum": ["postmark", "sendgrid", "mailgun", "ses"], "default": "postmark", "required": true, "description": "Email provider this template is designed for"}, "template_id": {"type": "string", "required": false, "description": "Provider-specific template ID (for provider_template type)"}, "html_content": {"type": "text", "description": "HTML email content with template variables"}, "text_content": {"type": "text", "description": "Plain text email content with template variables"}, "template_type": {"type": "enumeration", "enum": ["provider_template", "html_content", "hybrid"], "default": "html_content", "required": true, "description": "Template rendering type: provider_template (use provider's template), html_content (custom HTML), hybrid (both)"}, "category": {"type": "enumeration", "enum": ["welcome", "verification", "reset-password", "notification", "ticket-notification", "marketing", "transactional", "system"], "default": "transactional", "description": "Template category for organization and filtering"}, "subject": {"type": "string", "description": "Email subject line with template variables"}, "default_from_email": {"type": "email", "description": "Default sender email address"}, "default_from_name": {"type": "string", "description": "Default sender display name"}, "variables": {"type": "json", "default": {}, "description": "Template variables schema and default values"}, "required_variables": {"type": "json", "default": [], "description": "List of required template variables"}, "is_active": {"type": "boolean", "default": true, "description": "Whether this template is active and available for use"}, "scope": {"type": "enumeration", "enum": ["global", "system"], "default": "global", "required": true, "description": "Template scope: global (everyone), system (admin only)"}, "parent_template": {"type": "relation", "relation": "manyToOne", "target": "api::email-template.email-template", "description": "Parent template for inheritance/overrides"}, "usage_count": {"type": "integer", "default": 0, "description": "Number of times this template has been used"}, "last_used": {"type": "datetime", "description": "Timestamp when template was last used"}, "tags": {"type": "json", "default": [], "description": "Tags for categorizing and searching templates"}, "version": {"type": "string", "default": "1.0.0", "description": "Template version for tracking changes"}, "created_by_user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "description": "User who created this template"}, "organization": {"type": "relation", "relation": "manyToOne", "target": "api::organization.organization", "description": "Organization this template belongs to (null for global templates)"}, "email_logs": {"type": "relation", "relation": "oneToMany", "target": "api::email-log.email-log", "mappedBy": "email_template", "description": "Email logs using this template"}, "preview_data": {"type": "json", "default": {}, "description": "Sample data for template preview in admin interface"}, "css_styles": {"type": "text", "description": "Custom CSS styles for email template"}, "responsive_design": {"type": "boolean", "default": true, "description": "Whether template is optimized for mobile devices"}, "analytics_enabled": {"type": "boolean", "default": true, "description": "Whether to track opens, clicks, and other analytics"}}}