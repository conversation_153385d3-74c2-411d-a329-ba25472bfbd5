{"_type": "export", "__export_format": 4, "__export_date": "2025-06-02T14:00:00.000Z", "__export_source": "strapi-gemini-copilot", "resources": [{"_id": "wrk_couponsystem001", "parentId": null, "modified": 1678886400000, "created": 1678886400000, "name": "Coupon System API", "description": "", "scope": "collection", "_type": "workspace"}, {"_id": "env_basecouponenv001", "parentId": "wrk_couponsystem001", "modified": 1678886400000, "created": 1678886400000, "name": "Base Environment", "data": {"base_url": "http://localhost:1337/api"}, "dataPropertyOrder": {"&": ["base_url"]}, "color": null, "isPrivate": false, "metaSortKey": 1678886400000, "_type": "environment"}, {"_id": "grp_coupons001", "parentId": "wrk_couponsystem001", "modified": 1678886400010, "created": 1678886400010, "name": "Coupons", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -100, "_type": "request_group"}, {"_id": "req_coupon_create001", "parentId": "grp_coupons001", "modified": 1678886400020, "created": 1678886400020, "url": "{{ _.base_url }}/coupons", "name": "Create Coupon", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"data\": {\n    \"code\": \"NEWYEAR25\",\n    \"description\": \"New Year 25% Off\",\n    \"credits_awarded\": 100,\n    \"is_active\": true,\n    \"valid_from\": \"2025-01-01T00:00:00.000Z\",\n    \"valid_until\": \"2025-01-31T23:59:59.000Z\",\n    \"max_redemptions_per_organization\": 1,\n    \"max_total_redemptions\": 1000\n  }\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}], "authentication": {}, "metaSortKey": -1000, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_coupon_list001", "parentId": "grp_coupons001", "modified": 1678886400030, "created": 1678886400030, "url": "{{ _.base_url }}/coupons", "name": "List Coupons", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -900, "_type": "request"}, {"_id": "req_coupon_get001", "parentId": "grp_coupons001", "modified": 1678886400040, "created": 1678886400040, "url": "{{ _.base_url }}/coupons/:couponId", "name": "Get Coupon by ID", "description": "", "method": "GET", "body": {}, "parameters": [{"id": "pv_couponid01", "name": "couponId", "value": "1", "description": "", "disabled": false}], "headers": [], "authentication": {}, "metaSortKey": -800, "_type": "request"}, {"_id": "req_coupon_update001", "parentId": "grp_coupons001", "modified": 1678886400050, "created": 1678886400050, "url": "{{ _.base_url }}/coupons/:couponId", "name": "Update Coupon", "description": "", "method": "PUT", "body": {"mimeType": "application/json", "text": "{\n  \"data\": {\n    \"description\": \"Updated New Year 25% Off Super Deal\",\n    \"is_active\": false\n  }\n}"}, "parameters": [{"id": "pv_couponid02", "name": "couponId", "value": "1", "description": "", "disabled": false}], "headers": [{"name": "Content-Type", "value": "application/json"}], "authentication": {}, "metaSortKey": -700, "_type": "request"}, {"_id": "req_coupon_delete001", "parentId": "grp_coupons001", "modified": 1678886400060, "created": 1678886400060, "url": "{{ _.base_url }}/coupons/:couponId", "name": "Delete Coupon", "description": "", "method": "DELETE", "body": {}, "parameters": [{"id": "pv_couponid03", "name": "couponId", "value": "1", "description": "", "disabled": false}], "headers": [], "authentication": {}, "metaSortKey": -600, "_type": "request"}, {"_id": "req_coupon_redeem001", "parentId": "grp_coupons001", "modified": 1678886400070, "created": 1678886400070, "url": "{{ _.base_url }}/coupons/redeem", "name": "Redeem Coupon", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n  \"couponCode\": \"NEWYEAR25\",\n  \"organizationId\": 1\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}], "authentication": {}, "metaSortKey": -500, "_type": "request"}, {"_id": "grp_couponlogs001", "parentId": "wrk_couponsystem001", "modified": 1678886400080, "created": 1678886400080, "name": "Coupon Redemption Logs", "description": "Endpoints for viewing coupon redemption history (Admin)", "environment": {}, "metaSortKey": -90, "_type": "request_group"}, {"_id": "req_couponlog_list001", "parentId": "grp_couponlogs001", "modified": 1678886400090, "created": 1678886400090, "url": "{{ _.base_url }}/coupon-redemption-logs", "name": "List Redemption Logs", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -100, "_type": "request"}, {"_id": "req_couponlog_get001", "parentId": "grp_couponlogs001", "modified": 1678886400100, "created": 1678886400100, "url": "{{ _.base_url }}/coupon-redemption-logs/:logId", "name": "Get Redemption Log by ID", "description": "", "method": "GET", "body": {}, "parameters": [{"id": "pv_logid01", "name": "logId", "value": "1", "description": "", "disabled": false}], "headers": [], "authentication": {}, "metaSortKey": -90, "_type": "request"}]}