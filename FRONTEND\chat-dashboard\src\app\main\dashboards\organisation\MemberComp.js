import * as React from 'react';
import { Chip } from '@mui/material';
import { useSelector } from 'react-redux';
import { selectUser } from 'app/store/userSlice';
import './organisation.css';

export default function MemberComp({ name, email }) {
  const currentUser = useSelector(selectUser);
  const isCurrentUser = currentUser.data.email === email;

  return (
    <div className="flex items-start space-x-4">
      <div className="member-avatar text-black bg-gray-200 rounded-full w-10 h-10 flex items-center justify-center">
        {name.charAt(0).toUpperCase()}
      </div>
      <div className="flex flex-col">
        <div className="flex items-center space-x-2">
          <span className="member-name mx-8 text-black font-semibold">
            {name.charAt(0).toUpperCase() + name.slice(1)}
          </span>
          {isCurrentUser && (
            <Chip
              label="You"
              size="small"
              className="current-user-chip"
            />
          )}
        </div>
        <span className="text-black ml-8 mt-4">
          {email}
        </span>
      </div>
    </div>
  );
}
