{"kind": "collectionType", "collectionName": "email_integrations", "info": {"singularName": "email-integration", "pluralName": "email-integrations", "displayName": "Email integration", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"users": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user"}, "knowledgebase": {"type": "relation", "relation": "oneToOne", "target": "api::knowledgebase.knowledgebase", "inversedBy": "email_integration"}}}