import React from "react";
import <PERSON><PERSON> from "react-lottie";
import * as animationData from "./empty.json";

export default class EmptyLottie extends React.Component {
  constructor(props) {
    super(props);
    this.state = { isStopped: false, isPaused: false };
  }

  render() {
    const { allowCreateAgent = true } = this.props;
    const buttonStyle = {
      display: "block",
      margin: "10px auto",
    };

    const defaultOptions = {
      loop: true,
      autoplay: true,
      animationData: animationData,
      rendererSettings: {
        preserveAspectRatio: "xMidYMid slice",
      },
    };

    return (
      <div className="flex flex-col items-center">
        <Lottie
          options={defaultOptions}
          height={220}
          width={220}
          isStopped={this.state.isStopped}
          isPaused={this.state.isPaused}
        />
        <p className="text-lg font-medium text-center mt-24">
          {this.props.text}
        </p>
        {allowCreateAgent && (
          <button
            className="mt-16 shadow-md bg-base-purple text-white text-l rounded p-8 w-[170px]
                             transition duration-300 ease-in-out
                             hover:shadow-[0_0_10px_rgba(255,255,255,0.8)] hover:brightness-110
                             active:shadow-inner active:transform active:scale-95"
            onClick={() => {
              window.location.href = "/dashboards/knowledge_base";
            }}
          >
            Create your agent
          </button>
        )}
      </div>
    );
  }
}

EmptyLottie.defaultProps = {
  text: "No data found",
  buttonText: "Create Agent",
};
