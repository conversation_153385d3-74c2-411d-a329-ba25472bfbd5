"use strict";

/**
 * answer router
 */

module.exports = {
  routes: [
    {
      method: "POST",
      path: "/whatsapp-webhooks",
      handler: "whatsapp.webhook",
      // config: {
      // 	policies: [
      // 		// point to a registered policy
      // 		'global::user-details-populate',
      // 		'global::update-org-before-api-call',
      // 	]
      // }
    },
    {
      method: "GET",
      path: "/whatsapp-webhooks",
      handler: "whatsapp.getwebhook",
      // config: {
      // 	policies: [
      // 		// point to a registered policy
      // 		'global::user-details-populate',
      // 		'global::update-org-before-api-call',
      // 	]
      // }
    },
    {
      method: "POST",
      path: "/whatsapp-send-message",
      handler: "whatsapp.sendMessage",
      // config: {
      // 	policies: [
      // 		// point to a registered policy
      // 		'global::user-details-populate',
      // 		'global::update-org-before-api-call',
      // 	]
      // }
    },
  ],
};
