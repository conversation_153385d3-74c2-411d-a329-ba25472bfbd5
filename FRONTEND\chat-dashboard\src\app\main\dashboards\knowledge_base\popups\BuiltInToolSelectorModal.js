import React from "react";
import * as PropTypes from "prop-types";
import { <PERSON><PERSON><PERSON>onte<PERSON>, Button, DialogActions } from "@mui/material";
import RadioOptionCardGrid from "app/shared-components/cards/radioOptionCardGrid";
import { useState, useEffect } from "react";
import axios from "axios";
import BaseDialog from "app/shared-components/dialog/base-dialog";
import qs from "qs";
import CircularProgress from "@mui/material/CircularProgress";
import EmptyData from "app/shared-components/empty-data/empty-data";


function BuiltInToolSelectorModal(props) {
  const [selectedOption, setSelectedOption] = useState(0);
  const [inBuiltTools, setInBuiltTools] = useState([]);
  const [loading, setLoading] = useState(false);
  const itemsPerPage = 5;

  const onOptionSelect = (option) => {
    setSelectedOption(option);
  };

  const toolOptions = [
    {
      icon: "https://talkbase-chatbot-images.s3.ap-south-1.amazonaws.com/open-meto.png",
      title: "Open Meto Weather Tool",
      value:  {},
      subType: "weather_tool",
      isBeta: false,
      isDisabled: false,
      description:
        "Useful when you want to get the weather information of a particular location or city.",
    },
    {
      icon: "https://talkbase-chatbot-images.s3.ap-south-1.amazonaws.com/booking-icon.png",
      title: "Ajentic Bookings Tool",
      value: {
        booking_base_url: "https://meet.talkbase.ai",
      },
      subType: "booking_link_tool",
      isBeta: false,
      isDisabled: false,
      description:
        "Useful when you want to generate a booking or meeting link within conversation. ",
    },
  ];

  useEffect(() => {
    setInBuiltTools(toolOptions);
    getAddedTools(1);
  }, [props.kbId]);

  const onAddClick = () => {
    const createdToolData = {
      data: {
        identifier: inBuiltTools[selectedOption].subType,
        name: inBuiltTools[selectedOption].title,
        type: "inbuilt",
        description: inBuiltTools[selectedOption].description,
        enabled: true,
        tool_image_url: inBuiltTools[selectedOption].icon,
        tool_data: inBuiltTools[selectedOption].value,
        knowledgebases: {
          connect: [{ id: props.kbId }],
        },
      },
    };

    axios
      .post(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/inbuilt-tools`,
        createdToolData
      )
      .then((res) => {
        props.setSuccessMessage("Tool added successfully");
        props.handleClose();
        props.setSuccessMessage("");
        props.onComplete();

      })
      .catch((e) => {
        props.setFailureMessage("Failed to add tool");
        props.handleClose();
        props.setFailureMessage("");
        props.onFailure();
      });
  };

  const getAddedTools = async (page) => {
    setLoading(true);
    try {
      const query = {
        populate: {
          inBuiltTools: {
            sort: "createdAt:asc",
            limit: itemsPerPage,
            start: (page - 1) * itemsPerPage,
          },
        },
      };
      const queryToString = qs.stringify(query, { encode: false });
      const kb = await axios.get(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/knowledgebases/${props.kbId}?${queryToString}`
      );

      const diffTools = toolOptions.filter((option) => {
        return !kb.data.data.attributes.inBuiltTools.data.some(
          (tool) => tool.attributes.identifier === option.subType
        );
      });
      setInBuiltTools(diffTools);
      setLoading(false);
    } catch (e) {
      console.log(e);
      setLoading(false);
    }
  };

  return (
    <BaseDialog
      open={props.open}
      title="Select Tool"
      sx={{
        "& .MuiDialog-container": {
          "& .MuiPaper-root": {
            width: "100%",
            maxWidth: "800px",
            maxHeight: "90%",
            borderRadius: "12px",
          },
        },
      }}
    >
      <DialogContent
        className="mx-0 mb-16"
        style={{
          padding: "0px",
          margin: "0px",
        }}
      >
        {inBuiltTools.length > 0 ? (
          <div className="py-16 mx-16">
            <RadioOptionCardGrid
              data={inBuiltTools}
              onOptionSelect={onOptionSelect}
              initialSelectedOption={selectedOption}
            />
          </div>
        ) : (
          <div className="flex flex-row mt-32 align-center items-center justify-center">
            <EmptyData message="No tools available to add" />
          </div>
        )}
      </DialogContent>
      <DialogActions className="m-8">
        <Button
          variant="outlined"
          className="shine-button border border-[#7F56D9] rounded-lg text-base-purple max-w-200 min-h-32 max-h-48 py-0"
          onClick={props.handleClose}
        >
          Cancel
        </Button>
        {props.loading ? (
          <CircularProgress thickness={5} size={30} color="secondary" />
        ) : (
          <Button
            variant="contained"
            className="shine-button ml-16 press-button text-white max-w-200 bg-base-purple rounded-lg min-h-32 max-h-48 hover:bg-[#7042d3]"
            onClick={onAddClick}
          >
            Submit
          </Button>
        )}
      </DialogActions>
    </BaseDialog>
  );
}

BuiltInToolSelectorModal.propTypes = {
  open: PropTypes.bool,
  handleClose: PropTypes.func,
  handleSubmit: PropTypes.func,
  loading: PropTypes.bool,
  successMessage: PropTypes.string,
  failureMessage: PropTypes.string,
  setSuccessMessage: PropTypes.func,
  setFailureMessage: PropTypes.func,
  kbId: PropTypes.string,
  onComplete : PropTypes.func,
  onFailure : PropTypes.func
};

export default BuiltInToolSelectorModal;
