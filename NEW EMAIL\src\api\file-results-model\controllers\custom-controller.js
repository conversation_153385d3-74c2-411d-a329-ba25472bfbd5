const { createCoreController } = require("@strapi/strapi").factories;


module.exports = createCoreController(
  "api::file-results-model.file-results-model",
  ({ strapi }) => ({
    async getFeaturedFileResultsModel(ctx) {
      try {
        const limit = parseInt(ctx.query.limit) || 10;
        const skip = parseInt(ctx.query.skip) || 0;

        // Get total count first
        const totalCount = await strapi.query("api::file-results-model.file-results-model").count({
          where: {
            playlist: {
              public: true
            }
          }
        });

        // Get paginated results
        const results = await strapi.query("api::file-results-model.file-results-model").findMany({
          where: {
            playlist: {
              public: true
            }
          },
          offset: skip,
          limit: limit,
          orderBy: { createdAt: 'desc' },
          select: ['start_time', 'end_time', 'created_at', 'id', 'audio_url', 'feature_image_url', 'excerpt', 'title', 'color' , 'status']
        });

        return {
          data: results,
          meta: {
            total: totalCount,
            returned: results.length
          }
        };
      } catch (err) {
        return ctx.throw(500, "Server error");
      }
    },

    async getRandomPublicFileResultsModel(ctx) {
      try {
        // Get pagination parameters from query, default to 10 items
        const limit = parseInt(ctx.query.limit) || 10;

        // Get all public file results
        const results = await strapi.query("api::file-results-model.file-results-model").findMany({
          where: {
            playlist: {
              public: true
            }
          },
        });

        // If no results found, return empty
        if (!results.length) {
          return { data: [] };
        }

        // Shuffle the array using Fisher-Yates algorithm
        const shuffledResults = [...results];
        for (let i = shuffledResults.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [shuffledResults[i], shuffledResults[j]] = [shuffledResults[j], shuffledResults[i]];
        }

        // Return the first 'limit' number of results
        return {
          data: shuffledResults.slice(0, limit),
          meta: {
            total: results.length,
            returned: Math.min(limit, results.length)
          }
        };

      } catch (err) {
        return ctx.throw(500, "Server error");
      }
    },
    async getRandomPublicFileResultsModelPreview(ctx) {
      try {
        // Get pagination parameters from query, default to 10 items
        const limit = parseInt(ctx.query.limit) || 10;

        // Get all public file results with selected fields only
        const results = await strapi.query("api::file-results-model.file-results-model").findMany({
          where: {
            playlist: {
              public: true
            }
          },
          select: ['title', 'feature_image_url', 'excerpt'],
        });

        // If no results found, return empty
        if (!results.length) {
          return { data: [] };
        }

        // Shuffle the array using Fisher-Yates algorithm
        const shuffledResults = [...results];
        for (let i = shuffledResults.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [shuffledResults[i], shuffledResults[j]] = [shuffledResults[j], shuffledResults[i]];
        }

        // Return the first 'limit' number of results
        return {
          data: shuffledResults.slice(0, limit),
          meta: {
            total: results.length,
            returned: Math.min(limit, results.length)
          }
        };

      } catch (err) {
        return ctx.throw(500, "Server error");
      }
    },
    async shareTrack(ctx) {
      try {
        const { id } = ctx.params;

        if (!id) {
          return ctx.badRequest('File Result ID is required');
        }

        // Get the specific file result with only the required fields
        const result = await strapi.query("api::file-results-model.file-results-model").findOne({
          where: { id },
          select: ['title', 'excerpt', 'feature_image_url']
        });

        if (!result) {
          return ctx.notFound('File Result not found');
        }

        // Return the data with mapped field names
        return {
          data: {
            name: result.title,
            description: result.excerpt,
            feature_image_url: result.feature_image_url
          }
        };

      } catch (err) {
        return ctx.throw(500, "Server error");
      }
    }
  })
);
