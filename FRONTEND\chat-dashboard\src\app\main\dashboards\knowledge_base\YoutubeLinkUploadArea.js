import React, { useState } from 'react';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import BaseDialog from 'app/shared-components/dialog/base-dialog';

export default function YoutubeLinkUploadArea({
	open,
	handleClose,
	handleSubmit,
	loading,
	successMessage,
	failureMessage,
	setSuccessMessage,
	setFailureMessage,
}) {
	const [link, setLink] = useState('');

	const handleFormSubmit = (event) => {
		event.preventDefault();
		handleSubmit(link);
		setLink('');
	};

	return (
		<BaseDialog
			open={open}
			handleClose={handleClose}
			title="Add Youtube URL"
		>
			<div className="border border-black p-10 flex rounded-md w-full ">
				<FuseSvgIcon
					className="text-48"
					size={24}
					color="action"
				>
					heroicons-solid:link
				</FuseSvgIcon>
				<input
					type="text"
					value={link}
					onChange={(event) => {
						setLink(event.target.value);
						if (successMessage != '' || failureMessage != '') {
							setSuccessMessage('');
							setFailureMessage('');
						}
					}}
					className="mx-6 flex-grow"
					placeholder="Add through webpage"
				/>
			</div>
			<div className="border border-black p-10 flex rounded-md w-full ">
				Add any public youtube video. Ajentic ingests the text scripts
				in the video and adds it to your knowledgebase. The contents in
				the page willl NOT be ingested, only the text scripts from the
				video.
			</div>
			{successMessage && (
				<div className="text-green-600 text-center mt-20">
					{successMessage}
				</div>
			)}
			{failureMessage && (
				<div className="text-red-600 text-center mt-20">
					{failureMessage}
				</div>
			)}
		</BaseDialog>
	);
}
