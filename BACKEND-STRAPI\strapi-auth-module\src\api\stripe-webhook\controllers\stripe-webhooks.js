// api/stripe-webhooks/controllers/stripe-webhooks.js
"use strict";
const _ = require("lodash");

const {
  hasSubscription,
  hasActiveSubscriptionPlan,
} = require("../../../helpers/has-subscription");
const { updateOrgWithPlan } = require("../../../helpers/update-org-with-plan");

const stripe = require("stripe")(process.env.STRIPE_SECRETE_KEY);

module.exports = {
  async webhook(ctx, next) {
    console.log("Stripe webhook trigger");
    console.log(ctx.request.body.type);
    try {
      const { type, data } = ctx.request.body;

      // Handle different event types
      switch (type) {
        // case 'customer.subscription.created':
        //   // await handleSubscriptionCreated(stripeEvent.data.object);
        //   break;
        case "invoice.payment_succeeded":
          try {
            const customer = await stripe.customers.retrieve(
              data.object.customer
            );
            const user = await strapi
              .query("plugin::users-permissions.user")
              .findOne({
                where: { email: customer.email },
                populate: {
                  organization: {
                    populate: {
                      plan: true,
                      monthly_usages: true,
                      subscription_orders: true,
                    },
                  },
                },
              });
            try {
              strapi
                .plugin("measurement-protocol")
                .service("gtag")
                .send({
                  name: "Transaction",
                  params: {
                    status: "Success",
                    timestamp: Date().toString(),
                    user: user.email,
                  }, // Event Value (you can change this as needed)
                });
            } catch (e) {
              console.log(e);
            }
            console.log(data.object.lines.data[0].plan?.product);
            console.log("--------product id-------");
            try {
              const order = _.maxBy(
                user.organization.subscription_orders,
                "createdAt"
              );

              // Update the subscription model with payment status and invoice
              await strapi
                .query("api::subscription-order.subscription-order")
                .update({
                  where: { id: order.id },
                  data: {
                    isPaid: true,
                    invoice: data.object.id,
                  },
                });
            } catch (e) {
              console.log("Update subscription order failed", e);
            }
            const plan = await strapi.query("api::plan.plan").findOne({
              where: {
                stripe_prod_id: data.object.lines.data[0].plan?.product,
              },
            });
            updateOrgWithPlan(user.organization, plan);
          } catch (e) {
            console.log(e);
          }
          break;
        // case 'customer.subscription.updated':
        //   try {
        //     const customer = await stripe.customers.retrieve(
        //       data.object.customer
        //     );
        //     const user = await strapi
        //       .query("plugin::users-permissions.user")
        //       .findOne({
        //         where: { email: customer.email },
        //         populate: {
        //           organization: { populate: {
        //             plan: true,
        //             monthly_usages: true,
        //             subscription_orders: true,
        //             },
        //           },
        //         }
        //       });
        //       ///Need to handle
        //   const isval = await hasSubscription(user.organization);
        //   console.log(isval);
        //     }catch(e){
        //       console.log(e);
        //     }
        //   // await handleSubscriptionUpdated(stripeEvent.data.object);
        //   break;
        case "invoice.payment_failed":
          try {
            const customer = await stripe.customers.retrieve(
              data.object.customer
            );
            const user = await strapi
              .query("plugin::users-permissions.user")
              .findOne({
                where: { email: customer.email },
                populate: {
                  organization: {
                    populate: {
                      plan: true,
                      monthly_usages: true,
                      subscription_orders: true,
                    },
                  },
                },
              });
            try {
              strapi
                .plugin("measurement-protocol")
                .service("gtag")
                .send({
                  name: "Transaction",
                  params: {
                    status: "failed",
                    timestamp: Date().toString(),
                    user: user.email,
                  }, // Event Value (you can change this as needed)
                });
            } catch (e) {
              console.log(e);
            }
            const activePlan = hasActiveSubscriptionPlan(user.organization);
            if (!activePlan) {
              await strapi.query("api::organization.organization").update({
                where: { id: user.organization?.id },
                data: {
                  subscription: "renewFail",
                },
              });
            }
          } catch (e) {
            console.log(e);
          }
          break;
        case "customer.subscription.deleted":
          try {
            const customer = await stripe.customers.retrieve(
              data.object.customer
            );
            const user = await strapi
              .query("plugin::users-permissions.user")
              .findOne({
                where: { email: customer.email },
                populate: {
                  organization: {
                    populate: {
                      plan: true,
                      monthly_usages: true,
                      subscription_orders: true,
                    },
                  },
                },
              });
            try {
              strapi
                .plugin("measurement-protocol")
                .service("gtag")
                .send({
                  name: "Subscription",
                  params: {
                    status: "deleted",
                    timestamp: Date().toString(),
                    user: user.email,
                  }, // Event Value (you can change this as needed)
                });
            } catch (e) {
              console.log(e);
            }
            await strapi.query("api::organization.organization").update({
              where: { id: user.organization.id },
              data: {
                subscription: "unsubscribed",
              },
            });
          } catch (e) {
            console.log(e);
          }
          break;
        // Add more cases for other events you want to handle
        default:
          console.log(`Unhandled event type: ${type}`);
      }
      return "Stripe webhook success";
    } catch (e) {
      console.error("Error handling webhook event:", e);
      return "Stripe webhook error";
    }
  },
  // async webhook(ctx, next) {
  //   console.log("Stripe webhook trigger");
  //   console.log(ctx.request.body.type);
  //   const { type, data } = ctx.request.body;
  //   switch (type) {
  //     case "checkout.session.completed":
  //       if (data.object.payment_status == "paid") {
  //         try {
  //           const user = await strapi
  //             .query("plugin::users-permissions.user")
  //             .findOne({
  //               where: {
  //                 email:
  //                   data.object.customer_email ??
  //                   data.object.customer_details.email,
  //               },
  //               populate: {
  // 				organization: { populate: {
  // 				  plan: true,
  // 				  monthly_usages: true,
  // 				  subscription_orders: { populate: { plan:true }},
  // 				},
  // 			  },
  // 			}
  //             });

  //           console.log("----User Fetch Done----");
  //           try {
  //             const order = user.organization.subscription_orders.sort(
  //               (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
  //             )[0];
  //             // Update the subscription model with payment status and invoice
  //             await strapi
  //               .query("api::subscription-order.subscription-order")
  //               .update({
  //                 where: { id: order.id },
  //                 data: {
  //                   isPaid: true,
  //                   invoice: data.object.id,
  //                 },
  //               });
  //           } catch (e) {
  //             console.log("Update subscription order failed",e);
  //           }

  //           console.log("----Subscription Fetch Done-----");
  //           console.log("----Subscription-----");
  //           let plan;
  //           try {

  //               // const order = user.organization.subscription_orders.sort(
  //               //   (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
  //               // )[0];
  //               // Fetch the plan associated with the subscription
  //               plan = await strapi.query("api::plan.plan").findOne({ where: {
  // 				stripe_prod_id: data.object.lines?.data[0]?.plan?.product }
  //               });

  //           } catch (e) {
  // 			console.log("plan fetch error",e);
  //             const order = user.organization.subscription_orders?.sort(
  //               (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
  //             )[0];
  // 		  if(order?.plan){
  // 			console.log("plan id",order.plan.id);
  //             // Fetch the plan associated with the subscription
  //             plan = await strapi
  //               .query("api::plan.plan")
  //               .findOne({ where: { id: order.plan.id } });
  // 		  }
  //           }

  //           console.log("----Plan fetch Done-----", plan );
  //           try {
  //             // Update organization with the plan details
  //             await strapi.query("api::organization.organization").update({
  //               where: { id: user.organization.id },
  //               data: {
  //                 subscription: "subscribed",
  //                 paid: plan.paid,
  //                 plan: plan.id,
  //                 allowed_query_tokens_count:
  //                   parseInt(user.organization.allowed_query_tokens_count) +
  //                   parseInt(plan.allowed_query_tokens),
  //                 allowed_training_tokens_count:
  //                   parseInt(user.organization.allowed_training_tokens_count) +
  //                   parseInt(plan.allowed_training_tokens),
  //                 allowed_trained_count:
  //                   user.organization.allowed_trained_count +
  //                   plan.message_allowed,
  //                 allowed_query_count:
  //                   user.organization.allowed_query_count +
  //                   plan.training_allowed,
  //                 allowed_usage_quota:
  //                   user.organization.allowed_usage_quota + plan.allowed_usage,
  //                 allowed_query_cost:
  //                   user.organization.allowed_query_cost +
  //                   plan.allowed_query_cost,
  //                 allowed_trained_cost:
  //                   user.organization.allowed_trained_cost +
  //                   plan.allowed_training_cost,
  //                 allowed_kbs: user.organization.allowed_kbs + plan.allowed_kbs,
  //               },
  //             });
  //           } catch (e) {
  //             console.log("Org Update failed", e);
  //           }

  //           console.log("----org update Done-----");

  //           // Create a new monthly usage record
  //           await strapi
  //             .query("api::monthly-usage.monthly-usage")
  //             .create({ data: { organization: user.organization.id } });

  //           console.log("----usage create Done-----");
  //         } catch (err) {
  //           console.log("Error processing webhook event:", err);
  //         }
  //       }
  //       break;
  //     case "invoice.payment_succeeded":
  //       console.log("--------Payment succeeded-------");
  //       console.log("--------invoice id-------");
  //       console.log(data.object.id);
  //       console.log("--------invoice id-------");

  //       try {
  //         const user = await strapi
  //           .query("plugin::users-permissions.user")
  //           .findOne({
  //             where: { email: data.object.customer_email },
  //             populate: {
  //               organization: { populate: {
  //                 plan: true,
  //                 monthly_usages: true,
  //                 subscription_orders: true,
  //               },
  //             },
  // 		}
  //           });
  //         console.log("--------product id-------");
  //         console.log(data.object.lines.data[0].plan?.product);
  //         console.log("--------product id-------");
  //         if (
  //           user.organization.plan?.stripe_prod_id ==
  //           data.object.lines.data[0].plan?.product
  //         ) {
  //           const currentMonthUsage = user.organization.monthly_usages.sort(
  //             (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
  //           )[0];
  //           const currentDate = new Date();
  //           const createdDate = new Date(currentMonthUsage.createdAt);
  //           if (
  //             createdDate.getMonth() === currentDate.getMonth() &&
  //             createdDate.getFullYear() === currentDate.getFullYear()
  //           ) {
  //             return;
  //           }
  //           return;
  //         }

  //         console.log("----User Fetch Done----");
  //         try {
  //           const order = user.organization.subscription_orders.sort(
  //             (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
  //           )[0];
  //           // Update the subscription model with payment status and invoice
  //           await strapi
  //             .query("api::subscription-order.subscription-order")
  //             .update({
  //               where: { id: order.id },
  //               data: {
  //                 isPaid: true,
  //                 invoice: data.object.id,
  //               },
  //             });
  //         } catch (e) {
  //           console.log("Update subscription order failed",e);
  //         }

  //         console.log("----Subscription Fetch Done-----");
  //         console.log("----Subscription-----");
  // 	  console.log("Product",data.object.lines.data[0].plan.product,)
  // 	  let plan;
  // 	try{
  // 		console.log("Product 193 line",data.object.lines.data[0].plan.product,)
  //         // Fetch the plan associated with the subscription
  //         plan = await strapi.query("api::plan.plan").findOne({where: {
  //           stripe_prod_id: data.object.lines.data[0].plan.product,}
  //         });
  // 	}
  // 	catch(e){
  // 		console.log("Plan fetch fail",e);
  // 	}
  //         // if(!plan){
  //         // 	plan = await strapi.query("api::plan.plan").findOne({
  //         // 		stripe_prod_id: data.lines.data[0].plan.product,
  //         // 	  });
  //         // }

  //         console.log("----Plan fetch Done-----",plan);
  //         try {
  //           // Update organization with the plan details
  //           await strapi.query("api::organization.organization").update({
  //             where: { id: user.organization.id },
  //             data: {
  //               subscription: "subscribed",
  //               paid: plan.paid,
  //               plan: plan.id,
  //               allowed_query_tokens_count: plan.allowed_query_tokens,
  //               allowed_training_tokens_count: plan.allowed_training_tokens,
  //               allowed_trained_count: plan.message_allowed,
  //               allowed_query_count: plan.training_allowed,
  //               allowed_usage_quota: plan.allowed_usage,
  //               allowed_query_cost: plan.allowed_query_cost,
  //               allowed_trained_cost: plan.allowed_training_cost,
  //               allowed_kbs: plan.allowed_kbs,
  //             },
  //           });
  //         } catch (e) {
  //           console.log("Org Update failed", e);
  //         }

  //         console.log("----org update Done-----");

  //         // Create a new monthly usage record
  //         await strapi
  //           .query("api::monthly-usage.monthly-usage")
  //           .create({ data: { organization: user.organization.id } });

  //         console.log("----usage create Done-----");
  //       } catch (err) {
  //         console.log("Error processing webhook event:", err);
  //       }
  //       break;

  //     case "invoice.payment_failed":
  //       const user = await strapi
  //         .query("plugin::users-permissions.user")
  //         .findOne({
  //           where: { email: data.object.customer_email },
  // 		populate: {
  //               organization: { populate: {
  //                 plan: true,
  //                 monthly_usages: true,
  //                 subscription_orders: true,
  //               },
  //             },
  // 		}
  //         });
  //       if (user.organization.monthly_usages) {
  //         const currentMonthUsage = user.organization.monthly_usages?.sort(
  //           (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
  //         )[0];
  //         const currentDate = new Date();
  //         const createdDate = new Date(currentMonthUsage.createdAt);
  //         if (
  //           createdDate.getMonth() === currentDate.getMonth() &&
  //           createdDate.getFullYear() === currentDate.getFullYear()
  //         ) {
  //           return;
  //         }
  //       }
  //       await strapi.query("api::organization.organization").update({
  //         where: { id: user.organization.id },
  //         data: {
  //           subscription: "renewFail",
  //         },
  //       });
  //       break;

  //     case "customer.subscription.updated":
  //       // Handle subscription updates
  //       try {
  //         const customer = await stripe.customers.retrieve(
  //           data.object.customer
  //         );
  //         const user = await strapi
  //           .query("plugin::users-permissions.user")
  //           .findOne({
  //             where: { email: customer.email },
  // 		  populate: {
  //               organization: { populate: {
  //                 plan: true,
  //                 monthly_usages: true,
  //                 subscription_orders: true,
  //               },
  //             },
  // 		}
  //           });
  //         if (
  //           user.organization.plan?.stripe_prod_id == data.object.plan.product
  //         ) {
  //           return;
  //         }
  //         let plan = await strapi.query("api::plan.plan").findOne({ where: {
  //           stripe_prod_id: data.object.plan.product,
  // 	  }});
  //         await strapi.query("api::organization.organization").update({
  //           where: { id: user.organization.id },
  //           data: {
  //             subscription: "subscribed",
  //             paid: plan.paid,
  //             plan: plan.id,
  //             allowed_query_tokens_count:
  //               parseInt(user.organization.allowed_query_tokens_count) +
  //               parseInt(plan.allowed_query_tokens),
  //             allowed_training_tokens_count:
  //               parseInt(user.organization.allowed_training_tokens_count) +
  //               parseInt(plan.allowed_training_tokens),
  //             allowed_trained_count:
  //               user.organization.allowed_trained_count + plan.message_allowed,
  //             allowed_query_count:
  //               user.organization.allowed_query_count + plan.training_allowed,
  //             allowed_usage_quota:
  //               user.organization.allowed_usage_quota + plan.allowed_usage,
  //             allowed_query_cost:
  //               user.organization.allowed_query_cost + plan.allowed_query_cost,
  //             allowed_trained_cost:
  //               user.organization.allowed_trained_cost +
  //               plan.allowed_training_cost,
  //             allowed_kbs: user.organization.allowed_kbs + plan.allowed_kbs,
  //           },
  //         });
  //       } catch (e) {
  //         console.log(e);
  //       }
  //       break;
  //     case "invoice.created":
  //       // Fetch the customer and check if it's within the trial period
  //       const customer = await stripe.customers.retrieve(data.object.customer);
  //       const isTrial =
  //         customer &&
  //         customer.subscription?.trial_end &&
  //         customer.subscription.trial_end > Math.floor(Date.now() / 1000);

  //       if (isTrial) {
  //         console.log("trial mode");
  //         // If the customer is still in the trial period, you can handle trial usage here.

  //         const user = await strapi
  //           .query("plugin::users-permissions.user")
  //           .findOne({
  //             where: { email: customer.email },
  //             populate: {
  //               organization: { populate: {
  //                 plan: true,
  //                 monthly_usages: true,
  //                 subscription_orders: true,
  //               },
  //             },
  // 		}
  //           });
  //         if (user.organization.subscription == "subscribed") {
  //           return;
  //         }
  //         // Fetch the plan associated with the subscription
  //         const plan = await strapi.query("api::plan.plan").findOne({where: {
  //           name: "Trial",
  // 	  }
  //         });

  //         console.log("----User Fetch Done----");

  //         // Update organization's subscription status to reflect the trial period
  //         await strapi.query("api::organization.organization").update({
  //           where: { id: user.organization.id },
  //           data: {
  //             subscription: "trial",
  //             paid: plan.paid,
  //             allowed_query_tokens_count:
  //               parseInt(user.organization.allowed_query_tokens_count) +
  //               parseInt(plan.allowed_query_tokens),
  //             allowed_training_tokens_count:
  //               parseInt(user.organization.allowed_training_tokens_count) +
  //               parseInt(plan.allowed_training_tokens),
  //             allowed_trained_count:
  //               user.organization.allowed_trained_count + plan.message_allowed,
  //             allowed_query_count:
  //               user.organization.allowed_query_count + plan.training_allowed,
  //             allowed_usage_quota:
  //               user.organization.allowed_usage_quota + plan.allowed_usage,
  //             allowed_query_cost:
  //               user.organization.allowed_query_cost + plan.allowed_query_cost,
  //             allowed_trained_cost:
  //               user.organization.allowed_trained_cost +
  //               plan.allowed_training_cost,
  //             allowed_kbs: user.organization.allowed_kbs + plan.allowed_kbs,
  //           },
  //         });

  //         console.log("----org update Done-----");

  //         // Create a new monthly usage record for trial usage
  //         await strapi
  //           .query("api::monthly-usage.monthly-usage")
  //           .create({
  //             data: { organization: user.organization.id, period: 10 },
  //           });

  //         console.log("----usage create Done-----");
  //       }
  //       break;

  //     case "customer.subscription.deleted":
  //       try {
  //         const user = await strapi
  //           .query("plugin::users-permissions.user")
  //           .findOne({
  //             where: { email: customer.email },
  // 		  populate: {
  //               organization: { populate: {
  //                 plan: true,
  //                 monthly_usages: true,
  //                 subscription_orders: true,
  //               },
  //             },
  // 		}
  //           });
  //         await strapi.query("api::organization.organization").update({
  //           where: { id: user.organization.id },
  //           data: {
  //             subscription: "unsubscribed",
  //           },
  //         });
  //       } catch (e) {
  //         console.log(e);
  //       }
  //       break;
  //     default:
  //       console.log(`Unhandled event type: ${type}`);
  //       break;
  //   }

  //   return "Stripe webhook";
  // },
};
