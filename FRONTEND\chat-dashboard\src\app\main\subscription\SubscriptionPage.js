import Typography from "@mui/material/Typography";
import React, { useState, useEffect } from "react";
import axios from "axios";
import { loadStripe } from "@stripe/stripe-js";
import ToggleButton from "@mui/material/ToggleButton";
import ToggleButtonGroup from "@mui/material/ToggleButtonGroup";
import Grid from "@mui/material/Grid";
import { showMessage } from "app/store/fuse/messageSlice";
import { useSelector, useDispatch } from "react-redux";
import "./subscription.css";
import PlanCard from "app/shared-components/plan-card/plan-card";
import { selectUser } from "app/store/userSlice";
import { useLocation } from "react-router-dom";
import LoadingSpinner from "app/shared-components/loading-spinner/loading-spinner";
import FusePageSimple from "@fuse/core/FusePageSimple";
import SubscriptionPageHeader from "./SubscriptionPageHeader";
import { styled } from '@mui/material/styles';
import Chip from '@mui/material/Chip';
import { Stack } from "immutable";

// Add this styled component near the top of your file, after the imports
const StyledToggleButtonGroup = styled(ToggleButtonGroup)(({ theme }) => ({
  display: 'flex !important',
  border: 'none',
  height: '44px !important',
  borderRadius: '28px !important',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.20) !important',
  backgroundColor: 'transparent',
  '& .MuiToggleButtonGroup-grouped': {
    margin: 0,
    border: 0,
    '&:not(:first-of-type)': {
      borderRadius: '24px',
    },
    '&:first-of-type': {
      borderRadius: '24px',
    },
  },
}));

const StyledToggleButton = styled(ToggleButton)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginRight: "12px",
  marginLeft: '12px',
  borderRadius: '24px !important',
  padding: '14px 24px !important',
  fontSize: '14px !important',
  fontWeight: 600,
  height: '36px !important',
  minWidth: '100px',
  textTransform: 'none',
  border: `1px solid ${theme.palette.divider}`,
  whiteSpace: 'nowrap',
  '&.Mui-selected': {
    backgroundColor: "#7C53FE !important",
    color: "#ffffff !important",

    '&:hover': {
      backgroundColor: "#7C53FE !important",
    }
  },
  // Remove all hover effects and transitions
  '&:hover': {
    backgroundColor: '#eeeeee !important',
    transform: 'none !important',
  },
  transition: 'none',
}));

const SaveChip = styled(Chip)(({ theme }) => ({
  position: 'absolute',
  top: '100%',
  left: '50%',
  transform: 'translateX(50%) translateY(-80%) !important',
  marginTop: '12px',
  backgroundColor: '#00C681 !important',
  color: '#ffffff !important',
  fontWeight: 600,
  fontSize: '10px !important',
  zIndex: 1,
  borderRadius: '6px !important',
}));

function SubscriptionPage() {
  const standardPlanBgColor = "#ffffff"
  const standardPlanTextColor = "#1e2227"
  const standardPlanButtonColor = "#ffffff"
  const standardPlanButtonTextColor = "#1e2227"
  const standardPlanButtonBorderColor = "#C5C5C5"
  const standardPlanBorderColor = "#ebebeb"
  const standardPlanBorderWidth = "1px"

  const popularPlanBgColor = "#7C53FE10"
  const popularPlanTextColor = "#1e2227"
  const popularPlanButtonColor = "#7C53FE"
  const popularPlanButtonTextColor = "#ffffff"
  const popularPlanButtonBorderColor = "#7C53FE"
  const popularPlanBorderColor = "#7C53FE"
  const popularPlanBorderWidth = "2px"


  const [plans, setPlans] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [term, setTerm] = useState("monthly");
  const user = useSelector(selectUser);
  const location = useLocation();
  const stripePromise = loadStripe(
    process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY
  );
  const dispatch = useDispatch();

  useEffect(() => {
    fetchPlans();
  }, [term]);

  const handleTerm = (event, newTerm) => {
    if(newTerm !== null){
      setTerm(newTerm);
    }
  };

  async function fetchPlans() {
    try {
      setIsLoading(true);
      const response = await axios
        .get(
          `${process.env.REACT_APP_AUTH_BASE_URL}/api/plans?filters[type][$eq]=${term}`
        )
        .catch((error) => {
          console.log(error);
        });
      var plans = response.data.data;

      plans.sort((a, b) => a.attributes.paid - b.attributes.paid);

      // Check if the user has subscribed to any enterprise plan
      const hasEnterprisePlan = user.data.organization?.plan?.name.toLowerCase().includes('enterprise');
      const hasFreePlan = user.data.organization?.plan?.name.toLowerCase().includes('trial');

      if(!hasFreePlan){
        plans = plans.filter(plan => !plan.attributes.name.toLowerCase().includes('trial'));
      }

      // If the user doesn't have an enterprise plan, remove all enterprise plans from the plans array
      if (!hasEnterprisePlan) {
        plans = plans.filter(plan => !plan.attributes.name.toLowerCase().includes('enterprise'));
      }

      setPlans(plans);
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }

  const handlePaymnent = async (value) => {
    try {
      if (value?.attributes.name === "Trial") {
        return;
      }
      setIsLoading(true);
      if (
        user.data.organization?.plan?.id === value.id &&
        user.data.organization?.current_month_usage
      ) {
        const createdDate = new Date(
          user.data.organization?.current_month_usage?.createdAt
        );
        const endDate = new Date(createdDate);
        if (organization.plan.type === "monthly") {
          endDate.setMonth(createdDate.getMonth() + 1);
        } else {
          endDate.setFullYear(createdDate.getFullYear() + 1);
        }
        if (currentDate <= endDate) {
          dispatch(
            showMessage({ message: "Currently you are on the same plan" })
          );
          return;
        }
      }
      const stripe = await stripePromise;
      const response = await axios
        .post(
          `${process.env.REACT_APP_AUTH_BASE_URL}/api/subscription-orders`,
          { type: "subscription", plan: value.id }
        )
        .catch((error) => {
          console.log(error);
          dispatch(
            showMessage({
              message: "Something went worng. Try after some time",
            })
          );
        });
      const res = await stripe.redirectToCheckout({
        sessionId: response.data.stripeSession.id,
      });
    } catch (e) {
      console.log(e);
      dispatch(
        showMessage({ message: "Something went worng. Try after some time" })
      );
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <FusePageSimple
      header={<SubscriptionPageHeader />}
      content={
        <div className="mt-24 flex h-full w-full flex-col">
          {!isLoading && (
            <div className="flex w-full justify-center">
              <div style={{ position: 'relative' }}>
                <StyledToggleButtonGroup
                  value={term}
                  exclusive
                  onChange={handleTerm}
                  aria-label="Subscription term"
                >
                  <StyledToggleButton value="monthly" aria-label="Monthly subscription">
                    Monthly Billing
                  </StyledToggleButton>
                  <StyledToggleButton value="yearly" aria-label="Yearly subscription">
                    Yearly Billing
                  </StyledToggleButton>
                </StyledToggleButtonGroup>
                { (
                  <SaveChip label="Save 10%" size="small" />
                )}
              </div>
            </div>
          )}

          {isLoading ? (
            <LoadingSpinner className="w-56 mt-32 self-center"></LoadingSpinner>
          ) : (
            <div className="mt-48 mx-24">
              <Grid
                container
                justifyContent="center"
                spacing={4}
                sx={{
                  '& > .MuiGrid-item': {
                    maxWidth: { xs: '100%', sm: '50%', md: '33.333%', lg: '30%', xl: '30%'  },
                  },
                }}
              >
                {plans?.map((value, index) => {
                  const isCurrentPlan = user.data.organization?.plan?.id === value.id
                  const currentPlanPrice = user.data.organization.plan.name === "Trial" ? 0 : user.data.organization?.plan?.attributes?.paid
                  const isPopular =  ((term === "monthly" && currentPlanPrice < 50) || (term === "yearly" && currentPlanPrice < 500)) && (value.attributes.paid === 49 || value.attributes.paid === 529)

                  return (
                    <Grid key={value.id} item xs={12} sm={6} md={6} lg={3} xl={4}>
                      <PlanCard
                        key={value.id}
                        plan={value}
                        color={isPopular ? popularPlanBgColor : standardPlanBgColor}
                        textColor={isPopular ? popularPlanTextColor : standardPlanTextColor}
                        buttonColor={isPopular ? popularPlanButtonColor : standardPlanButtonColor}
                        buttonTextColor={isPopular ? popularPlanButtonTextColor : standardPlanButtonTextColor}
                        buttonBorderColor={isPopular ? popularPlanButtonBorderColor : standardPlanButtonBorderColor}
                        borderColor={isPopular ? popularPlanBorderColor : standardPlanBorderColor}
                        borderWidth={isPopular ? popularPlanBorderWidth : standardPlanBorderWidth}
                        isPopular={isPopular}
                        isCurrentPlan={isCurrentPlan}
                        onClick={(e) => handlePaymnent(e)}
                        style={{
                          width: "100%",
                          maxWidth: "350px",
                          margin: "0 auto",
                        }}
                        buttonText={
                          isCurrentPlan
                            ? "Renew"
                            : location.state === "update"
                            ? "Change Plan"
                            : "Subscribe"
                        }
                      ></PlanCard>
                    </Grid>
                  );
                })}
              </Grid>
            </div>
          )}
        </div>
      }
    />
  );
}

export default SubscriptionPage;
