import React, { useState } from 'react';


const TruncateText = ({
		text,
		maxLines=4,
	  }) =>{
  const [showFullText, setShowFullText] = useState(false);

  const toggleText = () => {
    setShowFullText(!showFullText);
  };

  const textStyles = {
    display: '-webkit-box',
    WebkitLineClamp: maxLines,
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
  };

  return (
    <div className="mt-14 text-[#667085] text-base">
      <div style={showFullText ? {} : textStyles}>{text}</div>
      {answer.split('\n').length > maxLines && (
        <button onClick={toggleText} className="text-blue-500">
          {showFullText ? 'See Less' : 'See More'}
        </button>
      )}
    </div>
  );
}
export const TruncatedText = ({
	text,
	maxLines=4,
  }) =>{
const [showFullText, setShowFullText] = useState(false);

const toggleText = () => {
setShowFullText(!showFullText);
};

const textStyles = {
display: '-webkit-box',
WebkitLineClamp: maxLines,
WebkitBoxOrient: 'vertical',
overflow: 'hidden',
};

return (
<div className="mt-14 text-[#667085] text-base">
  <div style={ textStyles}>{text}</div>
  {text?.split('\n').length > maxLines && (
	<button className="text-[#667085 font-bold">
	   See More
	</button>
  )}
</div>
);
}

export default TruncateText;
