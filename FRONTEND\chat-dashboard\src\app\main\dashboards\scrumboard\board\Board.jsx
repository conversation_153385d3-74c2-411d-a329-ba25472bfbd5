import React, { useState, useEffect } from "react";
import { DragDrop<PERSON>ontext, Droppable, DropResult } from "react-beautiful-dnd";
import { useAppDispatch, useAppSelector } from "app/store";
import { useParams } from "react-router-dom";
import withRouter from "@fuse/core/withRouter";
import { useDeepCompareEffect } from "@fuse/hooks";
import FusePageSimple from "@fuse/core/FusePageSimple";
import useThemeMediaQuery from "@fuse/hooks/useThemeMediaQuery";
import {
  getBoard,
  reorderCard,
  reorderList,
  resetBoard,
  selectBoard,
} from "../store/boardSlice";
import BoardAddList from "./board-list/BoardAddList";
import BoardList from "./board-list/BoardList";
import BoardCardDialog from "./dialogs/card/BoardCardDialog";
import BoardSettingsSidebar from "./sidebars/settings/BoardSettingsSidebar";
import { getCards } from "../store/cardsSlice";
import { getLists } from "../store/listsSlice";
import { getLabels } from "../store/labelsSlice";
import BoardHeader from "./BoardHeader";
import TicketCard from "app/shared-components/cards/ticketCard";
import { TaskManagement } from "../taskManagement/taskManagement";

function Board() {
  const dispatch = useAppDispatch();
  const { data: board } = useAppSelector(selectBoard);
  const isMobile = useThemeMediaQuery((theme) => theme.breakpoints.down("lg"));
  const [kanbanBoard, setKanbanBoard] = useState(false);
  const routeParams = useParams();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { boardId } = routeParams;

  function collectCards(tasksArray) {
    // Check if the input is an array
    if (!Array.isArray(tasksArray)) {
      throw new Error("Invalid input: expected an array of tasks");
    }

    // Use flatMap to collect all cards into a single array
    const allCards = tasksArray.flatMap((task) => {
      // Check if the task has the expected structure
      if (task && task.attributes && Array.isArray(task.attributes.cards)) {
        return task.attributes.cards;
      }
      // If the structure is not as expected, return an empty array for this task
      return [];
    });

    // Sort the cards by created date
    return allCards.sort((a, b) => {
      const dateA = new Date(a.createdAt);
      const dateB = new Date(b.createdAt);
      return dateB - dateA; // Sort in descending order (newest first)
    });
  }

  const handleTaskUpdate = (task) => {
    dispatch(getBoard(boardId));
    dispatch(getCards(boardId));
    dispatch(getLists(boardId));
    dispatch(getLabels(boardId));
  };

  useDeepCompareEffect(() => {
    dispatch(getBoard(boardId));
    dispatch(getCards(boardId));
    dispatch(getLists(boardId));
    dispatch(getLabels(boardId));

    return () => {
      dispatch(resetBoard());
    };
  }, [dispatch, routeParams]);

  function onKanbanBoard() {
    setKanbanBoard(!kanbanBoard);
  }

  function onDragEnd(result) {
    const { source, destination } = result;

    if (!destination) {
      return;
    }

    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) {
      return;
    }

    if (result.type === "list") {
      ///TODO:List/flow reorder disabled
      // dispatch(reorderList(result));
    }

    if (result.type === "card") {
      dispatch(reorderCard(result));
    }
  }

  if (!board) {
    return null;
  }

  return (
    <>
      <FusePageSimple
        header={
          <BoardHeader
            onSetSidebarOpen={setSidebarOpen}
            onKanbanBoard={onKanbanBoard}
          />
        }
        content={
          board?.attributes.lists.data && (
            <div className="flex flex-1 overflow-x-auto overflow-y-hidden h-full">
              {!kanbanBoard ? (
                <TaskManagement
                  tasks={collectCards(board?.attributes.lists.data)}
                  boardId={board.id}
                  onTaskUpdate={handleTaskUpdate}
                />
              ) : (
                <DragDropContext onDragEnd={onDragEnd}>
                  <Droppable
                    droppableId="list"
                    type="list"
                    direction="horizontal"
                  >
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        className="flex py-16 md:py-24 px-8 md:px-12"
                      >
                        {board?.attributes.lists.data.map((list, index) => (
                          <BoardList
                            key={list.id}
                            listId={list.id}
                            cardIds={list.attributes.cards}
                            index={index}
                          />
                        ))}

                        {provided.placeholder}

                        <BoardAddList />
                      </div>
                    )}
                  </Droppable>
                </DragDropContext>
              )}
            </div>
          )
        }
        rightSidebarOpen={sidebarOpen}
        rightSidebarContent={
          <BoardSettingsSidebar onSetSidebarOpen={setSidebarOpen} />
        }
        rightSidebarOnClose={() => setSidebarOpen(false)}
        scroll={isMobile ? "normal" : "content"}
        rightSidebarWidth={320}
      />
      <BoardCardDialog />
    </>
  );
}

export default withRouter(Board);
