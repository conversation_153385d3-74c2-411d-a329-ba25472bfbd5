'use strict';

/**
 * Email Connector Service
 * Handles internal email sending with template support
 */
module.exports = ({ strapi }) => ({
  
  /**
   * Send email using template (internal method)
   */
  async sendTemplateEmail({ templateName, to, templateData = {}, provider = null }) {
    try {
      // Find the template (same logic as controller)
      let template = await strapi.query('api::email-template.email-template').findOne({
        where: { 
          name: templateName, 
          scope: 'global',
          is_active: true
        }
      });

      // Fallback to system template if no global template found
      if (!template) {
        template = await strapi.query('api::email-template.email-template').findOne({
          where: { 
            name: templateName, 
            scope: 'system',
            is_active: true
          }
        });
      }

      if (!template) {
        throw new Error(`Email template '${templateName}' not found or inactive`);
      }

      // Debug template lookup
      strapi.log.info('🔍 Template found (service):', {
        name: template.name,
        template_type: template.template_type,
        provider: template.provider,
        has_html_content: !!template.html_content,
        html_content_length: template.html_content?.length || 0
      });

      // Prepare email data using template
      let emailData = {};
      const { emailProviderFactory } = require('../../../providers/email-provider-factory');

      if (template.template_type === 'provider_template') {
        // Use provider template
        emailData = {
          to,
          templateId: template.template_id,
          templateData: {
            ...template.variables,
            ...templateData
          },
          from: template.default_from_email,
          subject: template.subject
        };
      } else if (template.template_type === 'html_content') {
        // Use HTML content with template rendering
        const TemplateRenderer = require('../../../services/template-renderer');
        
        // Debug template variables
        const mergedVariables = { ...template.variables, ...templateData };
        strapi.log.info('🎨 Rendering HTML template (service):', {
          template_name: template.name,
          template_variables_count: Object.keys(template.variables || {}).length,
          provided_data_count: Object.keys(templateData).length,
          merged_variables_count: Object.keys(mergedVariables).length,
          sample_variables: Object.keys(mergedVariables).slice(0, 5)
        });
        
        const renderedHtml = TemplateRenderer.renderAdvancedTemplate(
          template.html_content,
          mergedVariables
        );
        const renderedText = template.text_content ? 
          TemplateRenderer.renderTemplate(template.text_content, mergedVariables) : null;

        strapi.log.info('📧 Rendered email content (service):', {
          html_length: renderedHtml?.length || 0,
          text_length: renderedText?.length || 0,
          has_html: !!renderedHtml,
          has_text: !!renderedText
        });

        emailData = {
          to,
          html: renderedHtml,
          text: renderedText,
          from: template.default_from_email,
          subject: template.subject
        };
      } else if (template.template_type === 'hybrid') {
        // Try provider template first, fallback to HTML if needed
        emailData = {
          to,
          templateId: template.template_id,
          templateData: {
            ...template.variables,
            ...templateData
          },
          from: template.default_from_email,
          subject: template.subject
        };
      }

      // Send email using the template's provider or specified provider
      const templateProvider = provider || template.provider;
      
      // Debug email data being sent to provider
      strapi.log.info('📤 Sending email via provider (service):', {
        provider: templateProvider,
        to: emailData.to,
        has_html: !!emailData.html,
        has_text: !!emailData.text,
        has_templateId: !!emailData.templateId,
        from: emailData.from,
        subject: emailData.subject
      });
      
      const result = await emailProviderFactory.sendEmail(emailData, templateProvider);

      // Update template usage
      await strapi.query('api::email-template.email-template').update({
        where: { id: template.id },
        data: {
          usage_count: template.usage_count + 1,
          last_used: new Date()
        }
      });

      return result;

    } catch (error) {
      strapi.log.error('Email template service error:', error);
      throw error;
    }
  }

});