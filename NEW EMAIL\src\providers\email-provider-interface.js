/**
 * Abstract Email Provider Interface
 * Defines the contract that all email providers must implement
 */
class EmailProviderInterface {
  constructor(config = {}) {
    this.config = config;
  }

  /**
   * Initialize the email provider with configuration
   * @param {Object} config - Provider-specific configuration
   */
  async initialize(config) {
    throw new Error('Method "initialize" must be implemented by email provider');
  }

  /**
   * Send a single email
   * @param {Object} emailData - Email data
   * @param {string|Array} emailData.to - Recipient email(s)
   * @param {string} emailData.from - Sender email
   * @param {string} emailData.subject - Email subject
   * @param {string} [emailData.text] - Plain text content
   * @param {string} [emailData.html] - HTML content
   * @param {string} [emailData.templateId] - Template ID for template-based emails
   * @param {Object} [emailData.templateData] - Dynamic data for template
   * @param {Array} [emailData.attachments] - Email attachments
   * @returns {Promise<Object>} - Send result
   */
  async sendEmail(emailData) {
    throw new Error('Method "sendEmail" must be implemented by email provider');
  }

  /**
   * Send bulk emails
   * @param {Array} emails - Array of email data objects
   * @returns {Promise<Object>} - Bulk send result
   */
  async sendBulkEmails(emails) {
    throw new Error('Method "sendBulkEmails" must be implemented by email provider');
  }

  /**
   * Get email delivery status
   * @param {string} messageId - Message ID returned from send operation
   * @returns {Promise<Object>} - Delivery status
   */
  async getDeliveryStatus(messageId) {
    throw new Error('Method "getDeliveryStatus" must be implemented by email provider');
  }

  /**
   * Validate email template
   * @param {string} templateId - Template ID to validate
   * @returns {Promise<boolean>} - Template validity
   */
  async validateTemplate(templateId) {
    throw new Error('Method "validateTemplate" must be implemented by email provider');
  }

  /**
   * Get provider name
   * @returns {string} - Provider name
   */
  getProviderName() {
    throw new Error('Method "getProviderName" must be implemented by email provider');
  }

  /**
   * Test connection to email provider
   * @returns {Promise<boolean>} - Connection test result
   */
  async testConnection() {
    throw new Error('Method "testConnection" must be implemented by email provider');
  }
}

module.exports = EmailProviderInterface; 