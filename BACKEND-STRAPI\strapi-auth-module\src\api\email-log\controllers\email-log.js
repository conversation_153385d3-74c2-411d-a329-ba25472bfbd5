'use strict';

/**
 * Email Log Controller
 * Handles API endpoints for email logging and analytics
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::email-log.email-log', ({ strapi }) => ({
  /**
   * Get organization email analytics
   */
  async getOrganizationAnalytics(ctx) {
    try {
      const { organizationId } = ctx.params;
      const { start, end } = ctx.query;
      
      const dateRange = {};
      if (start) dateRange.start = new Date(start);
      if (end) dateRange.end = new Date(end);
      
      const analytics = await strapi.service('api::email-log.email-log').getEmailAnalytics(
        organizationId, 
        dateRange
      );
      
      ctx.body = {
        success: true,
        organizationId,
        dateRange,
        analytics
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Get email status by message ID
   */
  async getEmailStatus(ctx) {
    try {
      const { messageId } = ctx.params;
      
      const emailLog = await strapi.entityService.findMany('api::email-log.email-log', {
        filters: { message_id: messageId },
        populate: ['organization', 'user', 'email_template'],
        limit: 1
      });
      
      if (!emailLog || emailLog.length === 0) {
        ctx.status = 404;
        ctx.body = {
          success: false,
          error: 'Email log not found'
        };
        return;
      }
      
      ctx.body = {
        success: true,
        email: emailLog[0]
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Update email status
   */
  async updateEmailStatus(ctx) {
    try {
      const { messageId } = ctx.params;
      const { status, additionalData = {} } = ctx.request.body;
      
      const updatedLog = await strapi.service('api::email-log.email-log').updateEmailStatus(
        messageId, 
        status, 
        additionalData
      );
      
      if (!updatedLog) {
        ctx.status = 404;
        ctx.body = {
          success: false,
          error: 'Email log not found'
        };
        return;
      }
      
      ctx.body = {
        success: true,
        message: 'Email status updated successfully',
        email: updatedLog
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Clean up old email logs
   */
  async cleanupOldLogs(ctx) {
    try {
      const { daysToKeep = 90 } = ctx.request.body;
      
      const deletedCount = await strapi.service('api::email-log.email-log').cleanupOldLogs(daysToKeep);
      
      ctx.body = {
        success: true,
        message: `Cleaned up ${deletedCount} old email logs`,
        deletedCount,
        daysToKeep
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Get all emails related to a specific ticket
   */
  async getTicketEmails(ctx) {
    try {
      const { ticketId } = ctx.params;
      
      const emailLogs = await strapi.entityService.findMany('api::email-log.email-log', {
        filters: { ticket_id: ticketId },
        populate: ['organization', 'user', 'email_template'],
        sort: { createdAt: 'desc' }
      });
      
      // Calculate ticket email statistics
      const stats = {
        total: emailLogs.length,
        sent: emailLogs.filter(log => log.status === 'sent').length,
        delivered: emailLogs.filter(log => log.status === 'delivered').length,
        opened: emailLogs.filter(log => log.status === 'opened').length,
        failed: emailLogs.filter(log => log.status === 'failed').length,
        unique_recipients: [...new Set(emailLogs.flatMap(log => log.to_emails))].length
      };
      
      ctx.body = {
        success: true,
        ticketId,
        emails: emailLogs,
        statistics: stats
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Get email delivery trends
   */
  async getDeliveryTrends(ctx) {
    try {
      const { organizationId } = ctx.params;
      const { days = 30 } = ctx.query;
      
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - parseInt(days));
      
      const emailLogs = await strapi.entityService.findMany('api::email-log.email-log', {
        filters: {
          organization: organizationId,
          createdAt: { $gte: startDate }
        },
        sort: { createdAt: 'asc' }
      });
      
      // Group by date
      const trends = {};
      emailLogs.forEach(log => {
        const date = log.createdAt.split('T')[0]; // Get date part only
        if (!trends[date]) {
          trends[date] = {
            total: 0,
            sent: 0,
            delivered: 0,
            opened: 0,
            failed: 0
          };
        }
        
        trends[date].total++;
        if (log.status === 'sent') trends[date].sent++;
        if (log.status === 'delivered') trends[date].delivered++;
        if (log.status === 'opened') trends[date].opened++;
        if (log.status === 'failed') trends[date].failed++;
      });
      
      ctx.body = {
        success: true,
        organizationId,
        period: `${days} days`,
        trends
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: error.message
      };
    }
  }
}));
