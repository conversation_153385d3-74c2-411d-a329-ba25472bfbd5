{"kind": "collectionType", "collectionName": "datasources", "info": {"singularName": "datasource", "pluralName": "datasources", "displayName": "Datasource", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "type": {"type": "enumeration", "required": true, "enum": ["vectorstore"], "default": "vectorstore"}, "knowledgebase": {"type": "relation", "required": true, "relation": "manyToOne", "target": "api::knowledgebase.knowledgebase", "inversedBy": "datasource"}, "documents": {"type": "relation", "relation": "oneToMany", "target": "api::document.document", "mappedBy": "datasource"}}}