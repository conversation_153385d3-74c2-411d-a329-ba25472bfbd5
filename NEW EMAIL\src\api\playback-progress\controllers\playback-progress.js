"use strict";

module.exports = {
  async updatePosition(ctx) {
    try {
      const userId = ctx.state.user.id;
      const trackId = ctx.params.id;
      const { positionSec, durationSec, deviceId } = ctx.request.body;

      const data = await strapi
        .service("api::playback-progress.playback-progress")
        .updatePosition(userId, trackId, positionSec, durationSec, deviceId);

      ctx.send(data);
    } catch (err) {
      ctx.status = 500;
      ctx.send({ error: err.message });
    }
  },

  async listByUser(ctx) {
    try {
      const userId = ctx.state.user.id;
      const result = await strapi
        .service("api::playback-progress.playback-progress")
        .listByUser(userId, ctx.query);

      ctx.send({
        results: result.results,
        pagination: result.pagination,
      });
    } catch (err) {
      ctx.status = 500;
      ctx.send({ error: err.message });
    }
  },

  async clear(ctx) {
    try {
      const userId = ctx.state.user.id;
      const trackId = ctx.params.id;

      await strapi
        .service("api::playback-progress.playback-progress")
        .clear(userId, trackId);

      ctx.send({ ok: true });
    } catch (err) {
      ctx.status = 404;
      ctx.send({ error: err.message });
    }
  },
};
