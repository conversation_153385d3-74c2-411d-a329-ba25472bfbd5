.whatsapp-container {
    display: flex;
    align-items: center;
    width: 95%;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #26CE65;
    background-color: #E7F8EF;
    margin-bottom:48px !important; 
}

.grid-container {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr; /* Adjust as needed */
    gap: 16px; /* Adjust the gap between columns as needed */
  }

  .card table {
    width: 100%; /* Ensure the table takes up the full width of its container */
    border-collapse: collapse; /* Remove any spacing between table cells */
  }
  


  


  
  .item-text {
    text-align: left;
  }
  
  .item-icon {
    justify-self: center;
    width: 15px;
    height: 15px; /* Center the icon in its grid cell */
  }
  

.leading-icon {
    width: 50px; /* Adjust as needed */
    height: 50px; /* Adjust as needed */
    margin-right: 20px;
}

.text-container {
    display: flex;
    flex-direction: column;
}

.title {
    margin: 0;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 500; /* Adjust as needed */
}

.description {
    color : #4C4E54;
    font-size: 14px; 
    font-weight: 300;/* Adjust as needed */
}
