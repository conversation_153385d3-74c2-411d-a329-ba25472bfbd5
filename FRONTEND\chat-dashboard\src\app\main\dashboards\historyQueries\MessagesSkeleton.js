import { Skeleton, Stack } from "@mui/material";

export default function MessagesSkeleton() {
  return (
    <Stack spacing={4} className="w-full">
      {[...new Array(3)].map((i) => (
        <Stack key={i} spacing={2}>
          <Stack direction={"row"} spacing={2}>
            <Skeleton
              variant="circular"
              width={28}
              height={28}
              className="flex-none"
            />
            <Skeleton variant="rounded" className="flex-1 h-40" />
          </Stack>
          <Stack direction={"row"} spacing={2}>
            <Skeleton
              variant="circular"
              width={28}
              height={28}
              className="flex-none"
            />
            <Skeleton variant="rounded" className="flex-1  h-80" />
          </Stack>
        </Stack>
      ))}
    </Stack>
  );
}
