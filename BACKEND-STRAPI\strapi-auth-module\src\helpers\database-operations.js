const _ = require('lodash');

module.exports = {
  async add_answer_data_to_db(answerData) {
    try {
      console.log('Adding answer data to database:', answerData);
      
      // Validate required fields
      if (!answerData.question || !answerData.kb_id || !answerData.org_id) {
        throw new Error('Missing required fields: question, kb_id, or org_id');
      }

      // Find the knowledgebase
      const kb = await strapi.query('api::knowledgebase.knowledgebase').findOne({
        where: { kb_id: answerData.kb_id }
      });

      if (!kb) {
        throw new Error(`Knowledgebase not found for kb_id: ${answerData.kb_id}`);
      }

      // Create the answer record
      const answer = await strapi.query('api::answer.answer').create({
        data: {
          question: answerData.question,
          answer: answerData.answer || '',
          kb_id: answerData.kb_id,
          org_id: answerData.org_id,
          session: answerData.session || '',
          api_source: answerData.api_source || 'celery_task',
          answer_status: answerData.answer_status !== undefined ? answerData.answer_status : true,
          did_find_answer: answerData.did_find_answer !== undefined ? answerData.did_find_answer : true,
          sources: answerData.sources || [],
          knowledgebase: kb.id,
          sessionModel: answerData.sessionModel || null
        }
      });

      console.log('Answer data saved successfully:', answer.id);
      return answer;
    } catch (error) {
      console.error('Error adding answer data to database:', {
        message: error.message,
        answerData: answerData
      });
      throw error;
    }
  },

  async update_monthly_usage(orgId, queryCount = 1) {
    try {
      // Find the organization's current month usage
      const org = await strapi.query('api::organization.organization').findOne({
        where: { org_id: orgId },
        populate: { current_month_usage: true }
      });

      if (org && org.current_month_usage) {
        await strapi.query('api::monthly-usage.monthly-usage').update({
          where: { id: org.current_month_usage.id },
          data: {
            query_count: org.current_month_usage.query_count + queryCount
          }
        });
        console.log(`Updated monthly usage for org ${orgId}: +${queryCount} queries`);
      }
    } catch (error) {
      console.error('Error updating monthly usage:', error.message);
      // Don't throw error to prevent main operation from failing
    }
  }
};
