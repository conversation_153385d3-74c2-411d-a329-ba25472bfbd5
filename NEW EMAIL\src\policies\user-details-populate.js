"use strict";

/**
 * user populate policy
 */

module.exports = async (policyContext, config, { strapi }) => {
  if (policyContext.state.user) {
    const populatedUser = await strapi
      .query("plugin::users-permissions.user")
      .findOne({
        where: { id: policyContext.state.user.id },
        populate: {
          organization: {
            populate: {
              monthly_usages: true,
              plan: true,
              credit_balance: true,
            },
            users: true,
          },
        },
      })
      .catch((err) => {
        console.log(err);
      });
    if (!populatedUser.organization) {
      console.log("User doesn't have organization");
      const customError = new Error("User doesn't have organization");
      customError.status = 400; // Set the desired status code
      throw customError;
    }
    policyContext.state.user.organization = populatedUser.organization;
    const current_month_usage =
      policyContext.state.user.organization.monthly_usages.sort(
        (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
      )[0];
    policyContext.state.user.organization.current_month_usage =
      current_month_usage;
  } else {
    if (policyContext.request?.body?.org_id) {
      const org = await strapi
        .query("api::organization.organization")
        .findOne({
          where: { org_id: policyContext.request?.body?.org_id },
          populate: { users: true },
        });
      const populatedUser = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: { id: org.users[0].id },
          populate: {
            organization: {
              populate: {
                monthly_usages: true,
                plan: true,
              },
              users: true,
            },
          },
        });
      policyContext.state.user = populatedUser;
      const current_month_usage =
        policyContext.state.user.organization.monthly_usages.sort(
          (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
        )[0];
      policyContext.state.user.organization.current_month_usage =
        current_month_usage;
    } else {
      console.log("Doesn't have user info");
      const customError = new Error("**user-details-populate** Doesn't have user info");
      customError.status = 403; // Set the desired status code
      throw customError;
    }
  }

  return true; // If you return nothing, Strapi considers you didn't want to block the request and will let it pass
};
