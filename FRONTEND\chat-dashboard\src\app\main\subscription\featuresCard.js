/**
 * Renders a card containing a list of features.
 *
 * @returns {JSX.Element} The rendered FeaturesCard component.
 * TODO this is hardcoded. Need  to load this data in a better way.
 */

export default function FeaturesCard() {
  const items = [
    { text: "Train from Links", 
      planA: true, 
      planB: true, 
      planC: true 
    },
    { text: "Train from sitemap", 
      planA: true, 
      planB: true, 
      planC: true 
    },
    { text: "Train from Documents", 
      planA: true, 
      planB: true, 
      planC: true 
    },
    {
      text: "Train from Youtube videos",
      planA: true,
      planB: true,
      planC: true,
    },
    {
      text: "3 Agent Types for answering questions",
      planA: true,
      planB: true,
      planC: true,
    },
    {
      text: "Unllimted number of embedded chatbots for web",
      planA: true,
      planB: true,
      planC: true,
    },
    {
      text: "Unlimited number of data sources (within training limit)",
      planA: true,
      planB: true,
      planC: true,
    },
    { text: "Priority Support", 
      planA: false, 
      planB: true, 
      planC: true 
    },
    { text: "Agent setup support", 
      planA: false, 
      planB: false, 
      planC: true 
    },
  ];

  return (
    <div className="card mt-36">
      <table className="mt-24">
        <thead>
          <tr>
            <th>Features</th>
            <th>Spark</th>
            <th>Boost</th>
            <th>Elevate</th>
          </tr>
        </thead>
        <tbody>
          {items.map((item, index) => (
            <tr key={index}>
              <td className="features-item-text">{item.text}</td>
              <td className="pl-120">
                <img
                  className="features-item-icon"
                  src={
                    item.planA
                      ? "assets/images/check-icon.png"
                      : "assets/images/circle-x.png"
                  }
                  alt="Basic Plan Icon"
                />
              </td>
              <td className="pl-120">
                <img
                  className="features-item-icon"
                  src={
                    item.planA
                      ? "assets/images/check-icon.png"
                      : "assets/images/circle-x.png"
                  }
                  alt="Pro Plan Icon"
                />
              </td>
              <td className="pl-120">
                <img
                  className="features-item-icon"
                  src={
                    item.planC
                      ? "assets/images/check-icon.png"
                      : "assets/images/circle-x.png"
                  }
                  alt="Elite Plan Icon"
                />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
