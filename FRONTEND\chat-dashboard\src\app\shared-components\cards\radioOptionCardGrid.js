import React from 'react';
import { motion } from 'framer-motion';
import Pill from 'app/shared-components/pill/pill';
import { Body, H6 } from '../text/texts';
import Check from '@mui/icons-material/Check';

const RadioOptionCardGrid = ({
  data,
  onOptionSelect,
  initialSelectedOption = 0,
  hideCheckIcon = false
}) => {
  const handleOptionSelect = (index) => {
    if (onOptionSelect) {
      onOptionSelect(index);
    }
  };

  return (
    <div className="flex grid grid-cols-1 gap-y-16">
      {data
        .filter((item) => !item.isDisabled)
        .map((item, index) => (
          <motion.div
            key={index}
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.98 }}
            className="relative px-16"
          >
            <input
              type="radio"
              id={`option-${index}`}
              className="sr-only"
              name="chatbot-type-group"
              onChange={() => handleOptionSelect(index)}
              checked={index === initialSelectedOption}
            />
            <label htmlFor={`option-${index}`} className="cursor-pointer block h-full">
              <div className="rounded-lg shadow-md p-4 bg-white border-2 transition-all duration-300 hover:shadow-lg h-full flex flex-col">
                <div className="flex items-center mb-3">
                  {!hideCheckIcon && (
                    index === initialSelectedOption ? (
                      <div className="absolute top-[22px] right-[28px] bg-black rounded-sm p-0.5">
                        <Check className="w-16 h-16 text-white" />
                      </div>
                    ) : (
                      <div className="absolute top-[22px] right-[28px] bg-grey-300 rounded-sm p-0.5">
                        <Check className="w-16 h-16 text-grey-400" />
                      </div>
                    )
                  )}
                  <div className="relative w-36 h-36 mr-3 m-8 flex-shrink-0">
                    <img
                      className="w-full h-full object-contain p-4 bg-base-purple rounded-md"
                      src={item.icon}
                      alt={item.title}
                    />
                  </div>
                  <div className="flex flex-row">
                    <H6 className="font-medium text-base pl-8">{item.title}</H6>
                    {item.isBeta && (
                      <div className="-mt-1 ml-8">
                        <Pill text="Beta" />
                      </div>
                    )}
                  </div>
                </div>
                <Body className="m-8 px-8 flex-grow text-grey-800">{item.description}</Body>
              </div>
            </label>
          </motion.div>
        ))}
    </div>
  );
};

export default RadioOptionCardGrid;
