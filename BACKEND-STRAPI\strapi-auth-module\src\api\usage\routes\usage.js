'use strict';

/**
 * usage router
 */


module.exports = {
	routes: [

	  {
		method: 'GET',
		path: '/usage',
		handler: 'usage.tokenusage',
		config: {
			policies: [
			'global::user-details-populate',
			'global::update-org-before-api-call',
		]
		  }
	  },
	  {
		method: 'GET',
		path: '/usage/tickets',
		handler: 'usage.getRecentScrumboardCards',
		config: {
			policies: [
			'global::user-details-populate',
			'global::update-org-before-api-call',
		]
	  }
	}
  ]
}
