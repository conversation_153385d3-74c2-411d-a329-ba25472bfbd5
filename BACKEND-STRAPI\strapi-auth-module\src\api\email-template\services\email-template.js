'use strict';

/**
 * Email Template Service
 * Manages dynamic email templates and migration from JSON files
 */

const { createCoreService } = require('@strapi/strapi').factories;
const fs = require('fs');
const path = require('path');

module.exports = createCoreService('api::email-template.email-template', ({ strapi }) => ({
  /**
   * Migrate JSON templates to database
   */
  async migrateJsonTemplates() {
    try {
      const templatesDir = path.join(__dirname, '../../../email-templates');
      const templateFiles = fs.readdirSync(templatesDir).filter(file => file.endsWith('.json'));
      
      const migratedTemplates = [];
      
      for (const file of templateFiles) {
        const templatePath = path.join(templatesDir, file);
        const templateData = JSON.parse(fs.readFileSync(templatePath, 'utf8'));
        
        // Check if template already exists
        const existingTemplate = await strapi.entityService.findMany('api::email-template.email-template', {
          filters: { name: templateData.name },
          limit: 1
        });
        
        if (existingTemplate.length > 0) {
          strapi.log.info(`📧 Template ${templateData.name} already exists, skipping migration`);
          continue;
        }
        
        // Create template in database
        const dbTemplate = await strapi.entityService.create('api::email-template.email-template', {
          data: {
            name: templateData.name,
            display_name: templateData.description || templateData.name,
            description: templateData.description,
            provider: templateData.provider || 'postmark',
            template_id: templateData.template_id,
            html_content: templateData.html_content,
            text_content: templateData.text_content,
            template_type: templateData.template_type || 'html_content',
            category: templateData.category || 'transactional',
            subject: templateData.subject,
            default_from_email: templateData.default_from_email,
            default_from_name: templateData.default_from_name,
            variables: templateData.variables || {},
            is_active: templateData.is_active !== false,
            scope: templateData.scope || 'global',
            version: '1.0.0',
            tags: this.generateTemplateTags(templateData),
            preview_data: this.generatePreviewData(templateData),
            responsive_design: true,
            analytics_enabled: true
          }
        });
        
        migratedTemplates.push(dbTemplate);
        strapi.log.info(`✅ Migrated template: ${templateData.name}`);
      }
      
      strapi.log.info(`🎯 Migration complete: ${migratedTemplates.length} templates migrated`);
      return migratedTemplates;
    } catch (error) {
      strapi.log.error('❌ Template migration failed:', error.message);
      throw error;
    }
  },

  /**
   * Get template by name with fallback to JSON files
   */
  async getTemplateByName(templateName) {
    try {
      // First, try to get from database
      const dbTemplate = await strapi.entityService.findMany('api::email-template.email-template', {
        filters: { 
          name: templateName,
          is_active: true
        },
        limit: 1
      });
      
      if (dbTemplate.length > 0) {
        // Update usage statistics
        await this.updateUsageStats(dbTemplate[0].id);
        return this.formatTemplateForUse(dbTemplate[0]);
      }
      
      // Fallback to JSON file
      const templatesDir = path.join(__dirname, '../../../email-templates');
      const templatePath = path.join(templatesDir, `${templateName}.json`);
      
      if (fs.existsSync(templatePath)) {
        const jsonTemplate = JSON.parse(fs.readFileSync(templatePath, 'utf8'));
        strapi.log.warn(`⚠️ Using JSON fallback for template: ${templateName}`);
        return jsonTemplate;
      }
      
      throw new Error(`Template not found: ${templateName}`);
    } catch (error) {
      strapi.log.error('❌ Failed to get template:', error.message);
      throw error;
    }
  },

  /**
   * Update template usage statistics
   */
  async updateUsageStats(templateId) {
    try {
      const template = await strapi.entityService.findOne('api::email-template.email-template', templateId);
      
      await strapi.entityService.update('api::email-template.email-template', templateId, {
        data: {
          usage_count: (template.usage_count || 0) + 1,
          last_used: new Date()
        }
      });
    } catch (error) {
      strapi.log.error('❌ Failed to update usage stats:', error.message);
    }
  },

  /**
   * Format template for email service use
   */
  formatTemplateForUse(dbTemplate) {
    return {
      name: dbTemplate.name,
      description: dbTemplate.description,
      provider: dbTemplate.provider,
      template_id: dbTemplate.template_id,
      html_content: dbTemplate.html_content,
      text_content: dbTemplate.text_content,
      template_type: dbTemplate.template_type,
      category: dbTemplate.category,
      subject: dbTemplate.subject,
      default_from_email: dbTemplate.default_from_email,
      default_from_name: dbTemplate.default_from_name,
      variables: dbTemplate.variables,
      is_active: dbTemplate.is_active,
      scope: dbTemplate.scope
    };
  },

  /**
   * Generate tags for template categorization
   */
  generateTemplateTags(templateData) {
    const tags = [];
    
    if (templateData.category) tags.push(templateData.category);
    if (templateData.provider) tags.push(`provider:${templateData.provider}`);
    if (templateData.template_type) tags.push(`type:${templateData.template_type}`);
    
    // Add specific tags based on template name
    if (templateData.name.includes('ticket')) tags.push('support', 'notification');
    if (templateData.name.includes('welcome')) tags.push('onboarding');
    if (templateData.name.includes('reset')) tags.push('security');
    if (templateData.name.includes('confirmation')) tags.push('verification');
    
    return tags;
  },

  /**
   * Generate preview data for admin interface
   */
  generatePreviewData(templateData) {
    const previewData = {};
    
    // Generate sample data based on template variables
    if (templateData.variables) {
      Object.keys(templateData.variables).forEach(key => {
        switch (key) {
          case 'name':
            previewData[key] = 'John Doe';
            break;
          case 'title':
            previewData[key] = 'Sample Ticket Title';
            break;
          case 'description':
            previewData[key] = 'This is a sample ticket description for preview purposes.';
            break;
          case 'agent_name':
            previewData[key] = 'Support Agent';
            break;
          case 'customer_email':
            previewData[key] = '<EMAIL>';
            break;
          case 'ticket_url':
            previewData[key] = 'https://podycy.com/tickets/sample123';
            break;
          case 'organization_name':
            previewData[key] = 'Sample Organization';
            break;
          case 'priority':
            previewData[key] = 'High';
            break;
          case 'assignee':
            previewData[key] = 'Senior Support Engineer';
            break;
          default:
            previewData[key] = templateData.variables[key] || `Sample ${key}`;
        }
      });
    }
    
    return previewData;
  },

  /**
   * Create template from JSON data
   */
  async createFromJson(jsonData, createdByUserId = null) {
    try {
      return await strapi.entityService.create('api::email-template.email-template', {
        data: {
          ...jsonData,
          created_by_user: createdByUserId,
          tags: this.generateTemplateTags(jsonData),
          preview_data: this.generatePreviewData(jsonData),
          version: '1.0.0'
        }
      });
    } catch (error) {
      strapi.log.error('❌ Failed to create template from JSON:', error.message);
      throw error;
    }
  },

  /**
   * Export template to JSON format
   */
  async exportToJson(templateId) {
    try {
      const template = await strapi.entityService.findOne('api::email-template.email-template', templateId);
      
      if (!template) {
        throw new Error('Template not found');
      }
      
      // Format for JSON export
      const jsonTemplate = {
        name: template.name,
        description: template.description,
        provider: template.provider,
        template_id: template.template_id,
        html_content: template.html_content,
        text_content: template.text_content,
        template_type: template.template_type,
        category: template.category,
        subject: template.subject,
        default_from_email: template.default_from_email,
        default_from_name: template.default_from_name,
        variables: template.variables,
        is_active: template.is_active,
        scope: template.scope
      };
      
      return jsonTemplate;
    } catch (error) {
      strapi.log.error('❌ Failed to export template to JSON:', error.message);
      throw error;
    }
  },

  /**
   * Get templates by category
   */
  async getTemplatesByCategory(category, isActive = true) {
    try {
      return await strapi.entityService.findMany('api::email-template.email-template', {
        filters: { 
          category,
          is_active: isActive
        },
        sort: { usage_count: 'desc' }
      });
    } catch (error) {
      strapi.log.error('❌ Failed to get templates by category:', error.message);
      throw error;
    }
  }
}));
