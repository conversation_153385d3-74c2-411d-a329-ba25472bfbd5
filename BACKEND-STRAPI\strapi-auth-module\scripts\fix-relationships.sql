-- SQL script to fix analytics relationships
-- This will update the answers table to link to the proper knowledgebases

-- First, let's see what we're working with
SELECT 
    a.id as answer_id,
    a.kb_id as answer_kb_id,
    a.knowledgebase_id as current_kb_relation,
    k.id as kb_table_id,
    k.kb_id as kb_table_kb_id,
    k.name as kb_name
FROM answers a
LEFT JOIN knowledgebases k ON a.kb_id = k.kb_id
WHERE a.knowledgebase_id IS NULL
ORDER BY a.id;

-- Update answers to link to knowledgebases based on kb_id matching
UPDATE answers a
SET knowledgebase_id = (
    SELECT k.id 
    FROM knowledgebases k 
    WHERE k.kb_id = a.kb_id
    LIMIT 1
)
WHERE a.knowledgebase_id IS NULL
AND EXISTS (
    SELECT 1 
    FROM knowledgebases k 
    WHERE k.kb_id = a.kb_id
);

-- Verify the updates
SELECT 
    a.id as answer_id,
    a.kb_id as answer_kb_id,
    a.knowledgebase_id as updated_kb_relation,
    k.name as kb_name,
    o.name as org_name
FROM answers a
LEFT JOIN knowledgebases k ON a.knowledgebase_id = k.id
LEFT JOIN organizations o ON k.organization_id = o.id
WHERE a.answer LIKE '%error%'
ORDER BY a.id;

-- Check the relationship counts
SELECT 
    'Total answers' as metric,
    COUNT(*) as count
FROM answers
UNION ALL
SELECT 
    'Answers with KB relationship' as metric,
    COUNT(*) as count
FROM answers 
WHERE knowledgebase_id IS NOT NULL
UNION ALL
SELECT 
    'Answers with error containing text' as metric,
    COUNT(*) as count
FROM answers 
WHERE answer LIKE '%error%'
UNION ALL
SELECT 
    'Error answers with KB relationship' as metric,
    COUNT(*) as count
FROM answers 
WHERE answer LIKE '%error%' 
AND knowledgebase_id IS NOT NULL;
