import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { createTheme, ThemeProvider } from '@mui/material/styles';
import { memo } from 'react';
import { useSelector } from 'react-redux';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import LinearProgress from '@mui/material/LinearProgress';
import { selectUsage } from 'app/store/usageSlice';


function CostUsage(props) {
  const widgets = useSelector(selectUsage);
  const { used, usageLimit } = widgets;

  function calcProgressVal(val, limit) {
    const percentage = (val * 100) / limit;
    return  percentage > 100 ? 100 : (percentage);
  }

  // Create a custom theme with the desired color
  const theme = createTheme({
    palette: {
      primary: {
        main: '#7666CE', 
      },
    },
  });

  return (
    <Paper className="flex flex-col flex-auto p-24 shadow-none rounded-2xl overflow-hidden dashboard-shadow ">
      <Typography className="mt-0 widget-title">
        Usage : ${isNaN(used)?0:used.toFixed(3)} / ${usageLimit.toFixed(0)}
      </Typography>

      <div className="my-16 space-y-32">
        <div className="flex flex-col">
          <div className="flex items-top space-x-16">
            <div className="flex mt-2 items-center justify-center w-48 h-48 rounded bg-base-purple text-white dark:bg-black-600 dark:text-black-50">
            <div className="light-purple-square-container">
              <img src="/assets/images/usage_icon.png" alt="Usage Icon" className="styled-image"/>
            </div>
            </div>
            <div className="flex-auto leading-none">
              <Typography className="widget-label-tiny">
                Used Quota
              </Typography>
              <div className="mt-6 widget-label-large">
             $ {isNaN(used)?0:used.toFixed(3)}
              </div>
              <ThemeProvider theme={theme}>
                <LinearProgress
                  variant="determinate"
                  className="mt-20"
                  color="primary"
                  value={calcProgressVal(used, usageLimit)}
                />
              </ThemeProvider>
            </div>
            <div className="flex items-center justify-center min-w-72 mt-auto green-icon-pill p-4 space-x-4">
            <img src="/assets/images/arrow_up_green.png" alt="Usage Icon" className="styled-image"/>
              <div className="green-pill-fg-text leading-none">{ isNaN(calcProgressVal(used, usageLimit).toFixed(3)) ? 0 : calcProgressVal(used, usageLimit).toFixed(3) }%</div>
             
            </div>
          </div>
        </div>
      </div>
    </Paper>
  );
}

export default memo(CostUsage);
