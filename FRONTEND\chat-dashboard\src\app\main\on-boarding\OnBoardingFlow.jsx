import React, { useState, useEffect } from "react";
import { useParams, useHistory } from "react-router-dom";
import FormStep from "./FormStep";

//Experimental for later use
const OnboardingFlow = () => {
  const { step } = useParams();
  const history = useHistory();
  const [currentStep, setCurrentStep] = useState(Number(step) || 1);
  const [overallFormData, setOverallFormData] = useState(() => {
    // Retrieve saved data from localStorage
    const savedData = localStorage.getItem("onboardingData");
    return savedData ? JSON.parse(savedData) : {};
  });

  useEffect(() => {
    if (Number(step) !== currentStep) {
      setCurrentStep(Number(step));
    }
  }, [step]);

  useEffect(() => {
    // Save progress to localStorage whenever the form data changes
    localStorage.setItem("onboardingData", JSON.stringify(overallFormData));
    localStorage.setItem("onboardingStep", currentStep);
  }, [overallFormData, currentStep]);

  useEffect(() => {
    // On mount, check if there's a saved step
    const savedStep = localStorage.getItem("onboardingStep");
    if (savedStep && !step) {
      history.replace(`/onboarding/${savedStep}`);
    }
  }, []);

  useEffect(() => {
    // Redirect user if they try to access a step beyond their current progress
    const savedStep = Number(localStorage.getItem("onboardingStep")) || 1;
    if (currentStep > savedStep) {
      history.replace(`/onboarding/${savedStep}`);
    }
  }, [currentStep]);

  const nextStep = () => {
    const next = currentStep + 1;
    setCurrentStep(next);
    history.push(`/onboarding/${next}`);
  };

  const prevStep = () => {
    const prev = currentStep - 1;
    setCurrentStep(prev);
    history.push(`/onboarding/${prev}`);
  };

  const updateOverallFormData = (newData) => {
    setOverallFormData((prevData) => ({
      ...prevData,
      ...newData,
    }));
  };

  const steps = [
    <FormStep
      key={1}
      nextStep={nextStep}
      updateOverallFormData={updateOverallFormData}
      data={overallFormData}
      renderForm={({ formState, handleChange }) => (
        <>
          <h1>Step 1</h1>
          <input
            type="text"
            name="name"
            value={formState.name || ""}
            onChange={handleChange}
          />
        </>
      )}
    />,
    <FormStep
      key={2}
      nextStep={nextStep}
      prevStep={prevStep}
      updateOverallFormData={updateOverallFormData}
      data={overallFormData}
      renderForm={({ formState, handleChange }) => (
        <>
          <h1>Step 2</h1>
          <input
            type="text"
            name="name"
            value={formState.name || ""}
            onChange={handleChange}
          />
        </>
      )}
    />,
    <FormStep
      key={3}
      prevStep={prevStep}
      updateOverallFormData={updateOverallFormData}
      data={overallFormData}
      renderForm={({ formState, handleChange }) => (
        <>
          <h1>Step 3</h1>
          <input
            type="text"
            name="name"
            value={formState.name || ""}
            onChange={handleChange}
          />
        </>
      )}
    />,
  ];

  // Ensure currentStep is within bounds
  if (currentStep < 1 || currentStep > steps.length) {
    return <Redirect to="/onboarding/1" />;
  }

  return <div className="container mx-auto">{steps[currentStep - 1]}</div>;
};

export default OnboardingFlow;
