import * as PropTypes from 'prop-types';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Popper from '@mui/material/Popper';
import PopupState, { bindToggle, bindPopper } from 'material-ui-popup-state';
import Fade from '@mui/material/Fade';
import Paper from '@mui/material/Paper';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon/FuseSvgIcon';
import { ReactSVG } from 'react-svg';
import { Divider } from '@mui/material';
import clipboardCopy from 'clipboard-copy';
import './question-answer.css';

export default function AnswerSourcePopper(props) {
	return (
		<PopupState
			variant="popper"
			popupId="demo-popup-popper"
		>
			{(popupState) => (
				<div>
					<Button
						variant="outlined"
						className="min-h-32 max-h-32 font-normal"
						{...bindToggle(popupState)}
						startIcon={
							<ReactSVG src="assets/images/icons/referrals.svg" />
						}
						endIcon={
							popupState.isOpen ? (
								<FuseSvgIcon
									className="text-48"
									size={24}
									color="action"
								>
									heroicons-outline:chevron-up
								</FuseSvgIcon>
							) : (
								<FuseSvgIcon
									className="text-48"
									size={24}
									color="action"
								>
									heroicons-outline:chevron-down
								</FuseSvgIcon>
							)
						}
					>
						Referrals
					</Button>
					<Popper
						className="border border-gray-200 rounded-lg source-popper !mt-2"
						{...bindPopper(popupState)}
						transition
						placement="bottom-end"
					>
						{({ TransitionProps }) => (
							<Fade
								{...TransitionProps}
								timeout={350}
							>
								<Paper className="p-20">
									<Typography>Sources</Typography>
									<Divider />
									{props.sources.map((source) => (
										<div key={source}>
											<div
												className="flex flex-row  py-10"
											>
												<span className="link-source text-ellipsis text-[#269CDB] overflow-hidden">
													{source}
												</span>
												<ReactSVG
													className="cursor-pointer"
													onClick={() => {
														clipboardCopy(source);
													}}
													src="assets/images/icons/copy.svg"
												/>
											</div>
											<Divider />
										</div>
									))}
								</Paper>
							</Fade>
						)}
					</Popper>
				</div>
			)}
		</PopupState>
	);
}
AnswerSourcePopper.propTypes = {
	sources: PropTypes.array,
};
AnswerSourcePopper.defaultProps = {
	sources: [],
};
