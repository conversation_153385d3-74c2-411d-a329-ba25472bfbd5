
    
const openMailClient = (email , subject) => {
      const mailtoLink = `mailto:${email}?subject=${subject}`;
      const gmailLink = `https://mail.google.com/mail/?view=cm&fs=1&to=${email}&su=${subject}`;
  
      // Attempt to open the mail client
      window.location.href = mailtoLink;
  
      // Check if the user is still on the page after a short delay
      setTimeout(() => {
        if (document.hasFocus()) {
          // If the user is still on the page, try to open Gmail
          window.location.href = gmailLink;
  
          // Check again after another delay
          setTimeout(() => {
            if (document.hasFocus()) {
              // If the user is still on the page, show a toast
              showToast("Failed to open email client. Please copy the email address and send manually.");
            }
          }, 500);
        }
      }, 500);
    };

    const showToast = (message) => {
        // Basic toast implementation. You can replace this with your preferred toast library or method.
        const toast = document.createElement("div");
        toast.innerText = message;
        toast.style.position = "fixed";
        toast.style.bottom = "20px";
        toast.style.left = "50%";
        toast.style.transform = "translateX(-50%)";
        toast.style.background = "black";
        toast.style.color = "white";
        toast.style.padding = "10px 20px";
        toast.style.borderRadius = "5px";
        toast.style.zIndex = "1000";
        document.body.appendChild(toast);
    
        setTimeout(() => {
          document.body.removeChild(toast);
        }, 3000);
      };

export default openMailClient;
