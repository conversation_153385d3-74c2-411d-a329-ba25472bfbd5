import React, { useState } from "react";
import { Dialog, DialogTitle, DialogContent } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import './aitaskmodal.css';

export default function AiTaskModal({
	open, 
	handleClose,
	AiTaskData,
	handleRowSelect
    }) {

	const [selectedRowIds, setSelectedRowIds] = useState([2]);
	const handleRowSelected = (params) => {
		handleRowSelect(params.row);
	};

	const renderCircles = (value, heading) => {
		const filledCircles = Math.round(value * 5);
  		const emptyCircles = 5 - filledCircles;

		return (
			<div className="circle-container">
				<div className="label-heading">{heading} : </div>
				<div className="circles">
					{[...Array(filledCircles)].map((_, index) => (
						<div key={index} className="circle filled"></div>
					))}
					{[...Array(emptyCircles)].map((_, index) => (
						<div key={index} className="circle"></div>
					))}
				</div>
			</div>
		);
	};

    return(
        <Dialog className="modal-dialog" open={open} onClose={handleClose} fullWidth>
			<div className="flex" style={{alignItems: "center", justifyContent: "space-between"}}>
				<DialogTitle className="flex-grow-1">Select AI Task Details</DialogTitle>
				<div onClick={handleClose} className="close-button"><CloseIcon style={{color: "#E71D36"}}/></div>
			</div>
			<DialogContent>
				<div className="ai-task-conatiner p-8 flex rounded-md w-full">
					{AiTaskData.map((row) => (
						<div className="row" key={row.id} onClick={() => {
								const newSelectedRowIds = selectedRowIds.includes(row.id)
									? selectedRowIds.filter((id) => id !== row.id)
									: [...selectedRowIds, row.id];
								setSelectedRowIds(newSelectedRowIds);
								handleRowSelected({ row });
								}}>
								<div className="left-column">
									<div className="name">{row.attributes.name}</div>
									<div className="description">{row.attributes.description}</div>
								</div>
								<div className="right-column">
									{renderCircles(row.attributes.detailsFactor, "Details")}
									{renderCircles(row.attributes.speedFactor, "Speed")}
									{renderCircles(row.attributes.tokensUsageFactor, "Tokens")}
								</div>
						</div>
					))}
				</div>
			</DialogContent>
      </Dialog>
    );
}
