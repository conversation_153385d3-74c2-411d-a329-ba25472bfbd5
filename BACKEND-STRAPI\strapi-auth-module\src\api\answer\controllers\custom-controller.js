"use strict";

/**
 * answer controller
 */

const { createCoreController } = require("@strapi/strapi").factories;
const axios = require("axios");
const { head, get } = require("lodash");

module.exports = createCoreController("api::answer.answer", ({ strapi }) => ({
  async askquestion(ctx) {
    try {
      const kb = await strapi
        .query("api::knowledgebase.knowledgebase")
        .findOne({
          where: { kb_id: ctx.request.body.kb_id },
          populate: {
            default_ai_task: true,
            shopify_model: true,
            ai_agent: true,
            apiTools: true,
            inBuiltTools: true,
          },
        });
      let session;

      session = await strapi.query("api::session.session").findOne({
        where: {
          session_id: ctx.request.body.session_id,
        },
        populate: {
          browserInfo: true,
          customerInfo: true,
          machineInfo: true,
        },
      });

      const isWhatsapp =
        ctx.request.body?.api_source &&
        ctx.request.body.api_source.toLowerCase().startsWith("whatsapp");

      if (!session) {
        const sessionData = {
          session_id: ctx.request.body.session_id,
          knowledgebase: kb.id,
        };

        if (ctx.request.body.userInfo) {
          if (isWhatsapp) {
            sessionData.customerInfo = {
              userId: ctx.request.body.session_id.replace("whatsapp_", ""),
            };
          } else {
            sessionData.customerInfo = ctx.request.body.customerInfo;
          }
          const newCustomerInfo = await strapi
            .query("api::customer-info.customer-info")
            .create({
              data: {
                email: ctx.request.body.userInfo.userEmail,
                userId: ctx.request.body.userInfo.userId,
              },
            });
          sessionData.customerInfo = newCustomerInfo.id;
        }
        if (ctx.request.body.sessionInfo?.machineInfo) {
          const newMachineInfoRecord = await strapi
            .query("api::machine-info.machine-info")
            .create({
              data: ctx.request.body.sessionInfo.machineInfo,
            });
          sessionData.machineInfo = newMachineInfoRecord.id;
        }
        if (ctx.request.body.sessionInfo?.browserInfo) {
          const newbrowInfoRecord = await strapi
            .query("api::browser-info.browser-info")
            .create({
              data: ctx.request.body.sessionInfo.browserInfo,
            });
          sessionData.browserInfo = newbrowInfoRecord.id;
        }

        session = await strapi.query("api::session.session").create({
          data: sessionData,
        });

        if (ctx.request.body.sessionInfo?.machineInfo) {
          await strapi.query("api::machine-info.machine-info").update({
            where: { id: sessionData.machineInfo },
            data: { session: session.id },
          });
        }
        if (ctx.request.body.sessionInfo?.browserInfo) {
          await strapi.query("api::browser-info.browser-info").update({
            where: { id: sessionData.browserInfo },
            data: { session: session.id },
          });
        }
        if (ctx.request.body?.userInfo) {
          await strapi.query("api::customer-info.customer-info").update({
            where: { id: sessionData.customerInfo },
            data: { session: session.id },
          });
        }
      }

      ctx.state.session = session;

      let promptQuestion;
      if (kb.prompt_prefix) {
        promptQuestion = `Use these instructions to answer the question
        Instructions :${kb.prompt_prefix}
        Question :${ctx.request.body.question}`;
      } else {
        promptQuestion = ctx.request.body.question;
      }
      var status = true;
      let answerResult;

      strapi.log.info(`The Request Body: ${JSON.stringify(ctx.request.body, null, 2)}`);
      answerResult = await this.doLgAgent(ctx, kb);
      strapi.log.info(`The Answer Result Data: ${JSON.stringify(answerResult.data, null, 2)}`);
      const data = {
        question: ctx.request.body.question,
        kb_id: ctx.request.body.kb_id,
        org_id: ctx.request.body.org_id,
        answer: get(answerResult, "data.answer", ""),
        sources: get(answerResult, "data.sources", []),
        did_find_answer:
          get(answerResult, "data.did_find_answer", "true") !== "false",
        sessionModel: session.id,
        session: ctx.request.body.session_id || "",
        answer_status: status,
        knowledgebase: kb.id,
        api_source: ctx.request.body.api_source || "chatBot",
       
      };

      strapi.log.info(`The Data: ${JSON.stringify(data, null, 2)}`);

    
      
      ctx.request.body.data = data;
     
      const response = await super.create(ctx);
      const savedAnswer = await strapi.query("api::answer.answer").findOne({
        where: { id: response.data.id },
        populate: {
          sessionModel: true,
        },
      });
      strapi.log.info(`The Saved Answer: ${JSON.stringify(savedAnswer, null, 2)}`);
     
     

      if (answerResult.data?.follow_up_questions !== null && 
        answerResult.data?.follow_up_questions !== undefined && 
        answerResult.data?.follow_up_questions.length > 0) {
        response.data.attributes.follow_up_questions = answerResult.data?.follow_up_questions;
      }
     

      try {
        if (isWhatsapp) {
          response.data.attributes.sources =
            response.data?.attributes?.sources.map(
              (e) =>
                `${process.env.TALKBASE_TRACKER_URL}?url=${e}&query_id=${response.data.id}`
            );
          switch (kb.collect_leads) {
            case "Always":
              response.data.attributes["collect_lead"] = true;
              var message;
              if (answerResult.data?.did_find_answer === "false") {
                message = `We couldn't find the answer to this question, our team is committed to helping you out! You can request for further assistance here…\n${process.env.MEETING_SCHEDULE_URL
                  }?queryId=${response?.data?.id}&sessionId=${response?.data?.attributes.session
                  }${isWhatsapp ? "&isWhatsapp=true" : ""}`;
              } else {
                message = `${response.data.attributes.answer
                  }\n\nHope that helped! You can ask us any other question and we will help out. If you require additional help from a human, you can raise a request here :${process.env.MEETING_SCHEDULE_URL
                  }?queryId=${response?.data?.id}&sessionId=${response?.data?.attributes.session
                  }${isWhatsapp ? "&isWhatsapp=true" : ""}`;
              }
              response.data.attributes.answer = message;

              break;
            case "TriggeredWhenUnanswered":
              if (answerResult.data?.did_find_answer === "false") {
                response.data.attributes["collect_lead"] = true;
                var message;
                if (answerResult.data?.did_find_answer === "false") {
                  message = `We couldn't find the answer to this question, our team is committed to helping you out! You can request for further assistance here…\n${process.env.MEETING_SCHEDULE_URL
                    }?queryId=${response?.data?.id}&sessionId=${response?.data?.attributes.session
                    }${isWhatsapp ? "&isWhatsapp=true" : ""}`;
                } else {
                  message = `${response.data.attributes.answer}\n${process.env.MEETING_SCHEDULE_URL
                    }?queryId=${response?.data?.id}&sessionId=${response?.data?.attributes.session
                    }${isWhatsapp ? "&isWhatsapp=true" : ""}`;
                }
                response.data.attributes.answer = message;
              }
              break;
            case "NoLeads":
              break;

            default:
              break;
          }
        } else {
          switch (kb.collect_leads) {
            case "Always":
              response.data.attributes["collect_lead"] = true;

              break;
            case "TriggeredWhenUnanswered":
              if (answerResult.data?.did_find_answer === "false") {
                response.data.attributes["collect_lead"] = true;
              }
              break;
            case "NoLeads":
              break;

            default:
              break;
          }
        }
      } catch (e) {
        console.log(e);
      }
      return response;
    } catch (err) {
      console.log(err);
      return ctx.throw(500, "Server error");
    }
  },
  async runanswer(ctx) {
    try {
      let answerResult = await axios
        .post(
          `${process.env.TALKBASE_BASE_URL}/v4/run_answer`,
          {
            kb_id: ctx.request.body.kb_id,
            org_id: ctx.request.body.org_id,
            question: ctx.request.body.question,
          },
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        )
        .catch((err) => {
          console.log(err);
        });
      return answerResult.data;
    } catch (err) {
      return ctx.throw(500, "Server error");
    }
  },

  async sessionanswer(ctx) {
    try {
      // const sessions = await strapi.db.connection
      // .table('answers')
      // .select('session')
      // .distinct('session');

      const answers = await strapi.db.query("api::answer.answer").findMany({
        where: {
          org_id: ctx.state.user.organization.org_id,
        },
        populate: {
          knowledgebase: true,
        },
        sort: { createdAt: "desc" },
      });
      const groupedAnswers = answers.reduce((acc, answer) => {
        if (!acc[answer.session]) {
          acc[answer.session] = [];
        }
        acc[answer.session].push(answer);
        return acc;
      }, {});
      const page = ctx.query.page || 1;
      const pageSize = ctx.query.pageSize || 10;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      // Slice the groupedAnswers object to get paginated results
      const paginatedAnswers = Object.keys(groupedAnswers)
        .slice(startIndex, endIndex)
        .reduce((acc, key) => {
          acc[key] = groupedAnswers[key];
          return acc;
        }, {});

      return paginatedAnswers;
    } catch (e) {
      strapi.log.error(`The Error: ${e}`);
      return ctx.throw(500, "Server error");
    }
  },
  async multiagent(ctx) {
    try {
      const kb = await strapi
        .query("api::knowledgebase.knowledgebase")
        .findOne({
          where: { id: ctx.request.body.kb_id },
          populate: {
            default_ai_task: true,
          },
        });
      const body = {
        org_id: ctx.request.body.org_id,
        kb_id: kb.kb_id,
        objective: ctx.request.body.objective,
        max_sections: ctx.request.body.max_sections,
        guidelines: ctx.request.body.guidelines,
        publish_markdown: true,
        publish_pdf: true,
        publish_docx: true,
        follow_guidelines: true,
        ai_model_name: kb.ai_model_name,
        agent_id: 30,
        session_id: ctx.request.body.session_id,
        search_type: kb.search_type,
        k_similarity: kb.k_similarity,
        fetch_k: kb.fetch_k,
        lambda_mul_mmr: kb.lambda_mul_mmr,
        similarity_score_threshold: kb.similarity_score_threshold,
      };
      const answerResult = await axios
        .post(`${process.env.TALKBASE_BASE_URL}/v4/long_agent`, body, {
          timeout: 6000000,
        })
        .catch((err) => {
          console.log(err);
          ctx.badRequest(err);
        });
      return answerResult;
    } catch (err) {
      console.log(err);
      return ctx.throw(500, "Server error");
    }
  },

  async doLgAgent(ctx, kb) {
    try {
      const body = {
        question: ctx.request.body.question,
        kb_id: kb.kb_id,
        org_id: ctx.request.body.org_id,
        tone: kb?.persona ?? "Make the answer friendly and professional",
        prefix_prompt: kb.prompt_prefix ?? "",
        user_info: ctx.request.body.userInfo,
        tools: {
          vector_store_tool: {
            type: "custom_tool",
            enabled: true,
            search_type: kb?.search_type ?? "similarity",
            k_similarity: kb?.k_similarity ?? 3,
            fetch_k: kb?.fetch_k ?? 20,
            similarity_score_threshold: kb?.similarity_score_threshold ?? 50,
            lambda_mul_mmr: kb?.lambda_mul_mmr ?? 60,
            index_name: kb.vs_table_name,
            namespace_prefix: "namespace_",
          },
        },
        session_id: ctx.request.body.session_id,
        agent_name: kb.ai_agent.agent_name,
        agent_role: kb.ai_agent.agent_role,
        company_name: kb.ai_agent.company_name,
        company_busiess: kb.ai_agent.company_business,
        company_values: kb.ai_agent.company_values,
        conversation_purpose: kb.ai_agent.conversation_purpose,
        ai_model_name: kb.ai_model_name,
        generate_follow_up_questions: kb.generate_follow_up_questions ?? false,
      };
      console.log("THE  MAIN BODY ", body);
      for (const [key, value] of Object.entries(kb.apiTools)) {
        body.tools[value.name] = {
          type: value.type,
          enabled: value.enabled,
          api_docs: value.api_docs,
          supports_post_request: value.supports_post_request ?? false,
          description: value.description,
          base_url: value.base_url,
          headers: value?.headers ?? {},
        };
      }
      for (const [key, value] of Object.entries(kb.inBuiltTools)) {
        body.tools[value.identifier] = {
          type: value.type,
          enabled: value.enabled,
          description: value.description,
          tool_data: value.tool_data,
        };
      }

      const answerResult = await axios
        .post(`${process.env.TALKBASE_BASE_URL}/v4/ajent`, body, {
          timeout: 6000000,
          headers: {
            "Content-Type": "application/json",
          },
        })
        .catch((err) => {
          console.log(err);
          ctx.badRequest(err);
        });
      return answerResult;
    } catch (err) {
      console.log(err);
      return ctx.throw(500, "Server error");
    }
  },

  async lgAgent(ctx) {
    const kb = await strapi.query("api::knowledgebase.knowledgebase").findOne({
      where: { kb_id: ctx.request.body.kb_id },
      populate: {
        default_ai_task: true,
        apiTools: true,
        ai_agent: true,
        inBuiltTools: true,
      },
    });
    return this.doLgAgent(ctx, kb);
  },

  async assessment(ctx) {
    return this.doAssessment(ctx);
  },

  async doAssessment(ctx) {
    try {
      console.log("THE CONTEXT ", ctx);
      const kb = await strapi.query("api::knowledgebase.knowledgebase").findOne({
        where: { kb_id: ctx.request.body.kb_id },
      });
      const body = {
        question: ctx.request.body.question,
        agent_id: ctx.request.body.agent_id,
        session_id: ctx.request.body.session_id,
        search_type: ctx.request.body.search_type,
        k_similarity: ctx.request.body.k_similarity,
        fetch_k: ctx.request.body.fetch_k,
        lamba_mul_mmr: ctx.request.body.lamba_mul_mmr,
        similarity_score_threshold: ctx.request.body.similarity_score_threshold,
        ai_model_name: ctx.request.body.ai_model_name,
        agent_name: ctx.request.body.agent_name,
        agent_role: ctx.request.body.agent_role,
        company_name: ctx.request.body.company_name,
        company_business: ctx.request.body.company_business,
        company_values: ctx.request.body.company_values,
        compulsory_questions: ctx.request.body.compulsory_questions,
        conversation_purpose: ctx.request.body.conversation_purpose,
        conversation_history: ctx.request.body.conversation_history,
        conversation_type: ctx.request.body.conversation_type,
        conversation_stage: ctx.request.body.conversation_stage,
        use_tools: ctx.request.body.use_tools,
        booking_link: ctx.request.body.booking_link,
        agent_tone: ctx.request.body.agent_tone,
        agent_constraints: ctx.request.body.agent_constraints,
        agent_type_description: ctx.request.body.agent_type_description,
        agent_first_message_is_goal: ctx.request.body.agent_first_message_is_goal,
        agent_first_message: ctx.request.body.agent_first_message,
        name: ctx.request.body.name,
        current_role: ctx.request.body.current_role,
        years_of_experience: ctx.request.body.years_of_experience,
        target_role: ctx.request.body.target_role,
        assessment_type: ctx.request.body.assessment_type
      };

      const assessmentResult = await axios
        .post(`${process.env.TALKBASE_BASE_URL}/v4/assesment`, body, {
          timeout: 6000000,
          headers: {
            "Content-Type": "application/json",
          },
        })
        .catch((err) => {
          console.log(err);
          ctx.badRequest(err);
        });
      return assessmentResult.data;
    } catch (err) {
      console.log(err);
      return ctx.throw(500, "Server error");
    }
  },

}));
