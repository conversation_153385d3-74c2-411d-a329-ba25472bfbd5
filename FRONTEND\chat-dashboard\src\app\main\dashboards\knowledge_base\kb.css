.kb-table {
  border: 1px solid #e4e7ec;
  border-radius: 8px;
  padding-right: 16px;
  padding-left: 16px;
  padding-bottom: 16px;
}
.chatbot-logo {
  text-align: center;
  padding: 12px;
  height: 40px !important;
  width: 40px !important;
  border-radius: 46px;
  text-transform: capitalize;
  margin-right: 20px;
  display: "flex";
  align-items: "center";
  justify-content: "center";
}
.kb-table .MuiDataGrid-columnHeaders {
  font-size: 13px;
  border-bottom: 1px solid #e4e7ec;
  color: #1d1e22;
  text-align: center;
}
.kb-table .MuiDataGrid-columnHeaderTitle {
  font-weight: 600 !important;

}
.kb-table .MuiDataGrid-row:hover {
  cursor: pointer;
  background-color: #eaf3f9 !important;
}

.kb-table .MuiDataGrid-row {
  padding: 0px 15px;
  max-height: 82px !important;
  min-height: 82px !important;
  align-items: center;
  background-color: #ffffff  !important;
}





.agent-pill {
  background-color: #1d1c20;
  border-radius: 31px;
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 400;
  color: #ebe7f7;
}
.kb-table .MuiDataGrid-cell {
  padding: 0px;
  border-bottom: none;
}

.kb-table .MuiDataGrid-cell:focus-within {
  outline: transparent !important;
  outline-width: 0 !important;
  outline-offset: 0 !important;
}

.table-container .MuiDataGrid-columnHeaders {
  border-bottom: 1px solid #e4e7ec;
  color: #667085;
}
.table-container .MuiDataGrid-columnHeaderTitle {
  font-weight: 400 !important;
  font-family: "Inter var";
}
.table-container .MuiDataGrid-row:hover {
  cursor: pointer;
}
.table-container .MuiDataGrid-row {
  padding: 0px 15px;
  max-height: 82px !important;
  min-height: 82px !important;
  align-items: center;
}
.table-container .MuiDataGrid-row:nth-child(odd) {
  background-color: #f9fafb !important;
}

.table-container .MuiDataGrid-cell {
  padding: 0px;
  border-bottom: none;
}

.table-container .MuiDataGrid-cell:focus-within {
  outline: transparent !important;
  outline-width: 0 !important;
  outline-offset: 0 !important;
}

.playground-container {
  background-color: white;
}

.MuiDataGrid-root .MuiDataGrid-row:nth-child(odd) {
  background-color: #f9fafc;
}

.MuiDataGrid-root .MuiDataGrid-row:hover {
  background-color: #e0e8f8;
}

.create-chat-icon {
  color: #7666ce;
}

.MuiDataGrid-root .MuiDataGrid-row:hover > .create-chat-icon {
  color: white !important;
}

.item-heading {
  justify-content: space-between;
  align-items: center;

}

.completed .MuiDataGrid-cellContent {
  background-color: rgba(32, 229, 0, 0.12);
  color: #2eab33;
  border-radius: 30px;
  padding: 5px 15px;
  min-width: 80px;
  text-align: center;
  font-size: 12px;
}

.pending .MuiDataGrid-cellContent {
  background-color: #ffc1074d;
  color: #bb9215;
  border-radius: 30px;
  padding: 5px 15px;
  min-width: 80px;
  text-align: center;
  font-size: 12px;
}

.processing .MuiDataGrid-cellContent {
  background-color: rgba(227, 190, 0, 0.12);
  color: #d9b600;
  border-radius: 30px;
  padding: 5px 15px;
  min-width: 80px;
  text-align: center;
  font-size: 12px;
}

.failed .MuiDataGrid-cellContent {
  background-color: rgba(255, 0, 0, 0.12);
  color: #ec1f1f;
  border-radius: 30px;
  padding: 5px 15px;
  min-width: 80px;
  text-align: center;
  font-size: 12px;
}

.purple-styled-button {
  color: white;
  background-color: #7f56d9;
  border-radius: 8px;
  margin-bottom: 10px;
}

.purple-styled-button:hover {
  border-color: black;
  background-color: #7d4ee3;
  color: white;
}
.purple-outlined-button {
  border: 1px solod rgba(127, 86, 217, 1);
  color: rgba(127, 86, 217, 1);
  border-radius: 8px;
}

.styled-button {
  color: white;
  background-color: #7C53FE;
  border-radius: 8px;
  max-width: fit-content;
  padding: 22px 12px;
  place-content: flex-center;
  font-weight: 400;
}

.styled-button:hover {
  color: white;
  background-color: #101828;
  border-radius: 8px;
  border-color: #8974BACC;
}

@media (max-width: 912px) {
  .item-heading {
    flex-direction: column;
    padding: 0;
  }

  .item-heading span {
    font-size: 12px;
  }
}

.element {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.left-side {
  display: flex;
  flex-direction: column;
  align-items: left;
  text-align: left;
  width: 90%;
}

.right-side {
  display: flex;
  flex-direction: column;
  align-items: start;
  vertical-align: center;
  text-align: left;
  width: 100%;
}

.title {
  font-weight: bold;
  margin-top: 30px;
}

.description {
  margin-top: 5px;
}

.text-field {
  margin-left: 10px; /* Adjust the margin as needed */
}

.options-card {
  border: 1px solid #e6e6e6;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.options-card-title {
  font-size: 14px;
  font-weight: 400;
  color: #101828;
}

.options-card-body {
  font-size: 13px;
  font-weight: "regular";
  color: #667085;
  text-align: left;
}
