# Series Feature - Product Requirements Document & Engineering Design

## Table of Contents
1. [Product Requirements Document](#product-requirements-document)
2. [Engineering Design](#engineering-design)
3. [Database Schema](#database-schema)
4. [API Specifications](#api-specifications)
5. [Implementation Plan](#implementation-plan)
6. [Access Control Strategy](#access-control-strategy)

---

## Product Requirements Document

### 1. Overview

**Series** is a new content organization feature that allows the platform to group related podcast episodes, tracks, or file result models into sequential learning paths. Users can progress through series linearly, with each episode unlocking only after completing the previous one.

### 2. Business Objectives

- **Increase User Engagement**: Provide structured learning paths to keep users engaged longer
- **Monetization Opportunity**: Future premium series for paid subscribers
- **Content Organization**: Better categorization and discovery of related content
- **Progressive Learning**: Enable step-by-step educational experiences

### 3. User Stories

#### 3.1 Content Creators/Admins
- As an admin, I want to create series with multiple episodes so that I can provide structured learning paths
- As an admin, I want to set the order of episodes in a series so that users follow the intended sequence
- As an admin, I want to mark series as public or private for future monetization
- As an admin, I want to see analytics on series completion rates

#### 3.2 End Users
- As a user, I want to discover available series so that I can find structured learning content
- As a user, I want to see my progress through a series so that I know how much I've completed
- As a user, I want episodes to unlock progressively so that I follow the intended learning path
- As a user, I want to bookmark series so that I can return to them later
- As a user, I want to see how much time is required to complete a series

### 4. Functional Requirements

#### 4.1 Series Management
- Create, read, update, delete series
- Add/remove episodes (file-results-models) to/from series
- Define episode order within series
- Set series metadata (title, description, cover image, estimated duration)
- Mark series as public/private
- Set prerequisite series (future enhancement)

#### 4.2 Progress Tracking
- Track user progress through series episodes
- Lock/unlock episodes based on completion
- Calculate completion percentage
- Track time spent on each episode
- Resume functionality for partially completed episodes

#### 4.3 Access Control
- Public series (free access for all users)
- Private series (reserved for future paid access)
- Organization-level access control
- User-level permissions and enrollment

#### 4.4 Discovery & Navigation
- Browse public series
- Search series by title, description, tags
- Filter series by category, difficulty, duration
- Featured series promotion
- Related series recommendations

### 5. Non-Functional Requirements

#### 5.1 Performance
- Series listing should load within 2 seconds
- Progress updates should be real-time
- Support for 10,000+ concurrent users viewing series

#### 5.2 Scalability
- Support for 1000+ series
- Support for 100+ episodes per series
- Efficient pagination for large series

#### 5.3 Security
- Secure progress tracking (no client-side manipulation)
- Proper access control enforcement
- Data privacy for user progress

---

## Engineering Design

### 1. System Architecture

The Series feature integrates with the existing Strapi-based architecture:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Strapi API    │    │   Database      │
│                 │    │                 │    │                 │
│ - Series List   │◄──►│ - Series CRUD   │◄──►│ - Series        │
│ - Progress UI   │    │ - Progress API  │    │ - Episodes      │
│ - Episode View  │    │ - Access Control│    │ - Progress      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. Core Components

#### 2.1 Series Entity
- Main container for grouped content
- Metadata and access control settings
- Relationship to episodes and progress tracking

#### 2.2 Series Episode Entity
- Junction table between Series and File-Results-Model
- Defines order and metadata for episodes within series
- Stores episode-specific settings

#### 2.3 Series Progress Entity
- Tracks user progress through series
- Episode-level completion tracking
- Time tracking and resume points

#### 2.4 Series Access Entity
- Manages user/organization access to series
- Future-proofing for paid access control
- Enrollment and permission management

### 3. Data Flow

#### 3.1 Series Discovery
```
User Request → Authentication → Public Series Filter → Pagination → Response
```

#### 3.2 Episode Access
```
Episode Request → Authentication → Series Access Check → Episode Unlock Check → Content Delivery
```

#### 3.3 Progress Tracking
```
Progress Update → Validation → Database Update → Next Episode Unlock Check → Notification
```

---

## Database Schema

### 1. Series Table
```sql
CREATE TABLE series (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  cover_image_url TEXT,
  estimated_duration INTEGER, -- in minutes
  difficulty_level ENUM('beginner', 'intermediate', 'advanced'),
  category VARCHAR(100),
  tags JSON,
  public BOOLEAN DEFAULT TRUE,
  featured BOOLEAN DEFAULT FALSE,
  display_order INTEGER,
  total_episodes INTEGER DEFAULT 0,
  average_rating DECIMAL(3,2),
  total_ratings INTEGER DEFAULT 0,
  organization_id INTEGER REFERENCES organizations(id),
  created_by INTEGER REFERENCES up_users(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  published_at TIMESTAMP
);
```

### 2. Series Episodes Table
```sql
CREATE TABLE series_episodes (
  id SERIAL PRIMARY KEY,
  series_id INTEGER REFERENCES series(id) ON DELETE CASCADE,
  file_results_model_id INTEGER REFERENCES file_results_models(id) ON DELETE CASCADE,
  episode_number INTEGER NOT NULL,
  title VARCHAR(255), -- optional override title
  description TEXT, -- optional episode description
  is_preview BOOLEAN DEFAULT FALSE, -- for marketing previews
  estimated_duration INTEGER, -- in minutes, override from file_results_model
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(series_id, episode_number),
  UNIQUE(series_id, file_results_model_id)
);
```

### 3. Series Progress Table
```sql
CREATE TABLE series_progress (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES up_users(id) ON DELETE CASCADE,
  series_id INTEGER REFERENCES series(id) ON DELETE CASCADE,
  current_episode_number INTEGER DEFAULT 1,
  completed_episodes INTEGER DEFAULT 0,
  total_time_spent INTEGER DEFAULT 0, -- in seconds
  completion_percentage DECIMAL(5,2) DEFAULT 0.00,
  last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, series_id)
);
```

### 4. Episode Progress Table
```sql
CREATE TABLE episode_progress (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES up_users(id) ON DELETE CASCADE,
  series_id INTEGER REFERENCES series(id) ON DELETE CASCADE,
  episode_number INTEGER NOT NULL,
  file_results_model_id INTEGER REFERENCES file_results_models(id) ON DELETE CASCADE,
  progress_percentage DECIMAL(5,2) DEFAULT 0.00,
  time_spent INTEGER DEFAULT 0, -- in seconds
  last_position INTEGER DEFAULT 0, -- for resume functionality, in seconds
  completed BOOLEAN DEFAULT FALSE,
  started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,
  last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, series_id, episode_number)
);
```

### 5. Series Access Table (Future-proofing)
```sql
CREATE TABLE series_access (
  id SERIAL PRIMARY KEY,
  series_id INTEGER REFERENCES series(id) ON DELETE CASCADE,
  user_id INTEGER REFERENCES up_users(id) ON DELETE CASCADE,
  organization_id INTEGER REFERENCES organizations(id) ON DELETE CASCADE,
  access_type ENUM('free', 'paid', 'trial', 'admin') DEFAULT 'free',
  granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NULL,
  granted_by INTEGER REFERENCES up_users(id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(series_id, user_id),
  UNIQUE(series_id, organization_id)
);
```

---

## API Specifications

### 1. Series Management APIs

#### 1.1 Create Series
```http
POST /api/series
Authorization: Bearer {token}
Content-Type: application/json

{
  "data": {
    "title": "Introduction to AI",
    "description": "A comprehensive series on AI fundamentals",
    "cover_image_url": "https://example.com/cover.jpg",
    "estimated_duration": 180,
    "difficulty_level": "beginner",
    "category": "Technology",
    "tags": ["AI", "Machine Learning", "Beginner"],
    "public": true,
    "featured": false
  }
}
```

#### 1.2 Add Episode to Series
```http
POST /api/series/{seriesId}/episodes
Authorization: Bearer {token}
Content-Type: application/json

{
  "data": {
    "file_results_model_id": 123,
    "episode_number": 1,
    "title": "What is Artificial Intelligence?",
    "description": "An introduction to AI concepts",
    "is_preview": false
  }
}
```

#### 1.3 Get Public Series (Paginated)
```http
GET /api/series/public?page=1&pageSize=10&category=Technology&difficulty=beginner
```

Response:
```json
{
  "data": [
    {
      "id": 1,
      "title": "Introduction to AI",
      "description": "A comprehensive series on AI fundamentals",
      "cover_image_url": "https://example.com/cover.jpg",
      "estimated_duration": 180,
      "difficulty_level": "beginner",
      "category": "Technology",
      "total_episodes": 5,
      "average_rating": 4.5,
      "featured": true,
      "user_progress": {
        "completed_episodes": 2,
        "completion_percentage": 40.0,
        "current_episode_number": 3
      }
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 10,
    "total": 25,
    "pageCount": 3
  }
}
```

#### 1.4 Get Series Details with Episodes
```http
GET /api/series/{seriesId}
Authorization: Bearer {token}
```

Response:
```json
{
  "data": {
    "id": 1,
    "title": "Introduction to AI",
    "description": "A comprehensive series on AI fundamentals",
    "episodes": [
      {
        "id": 1,
        "episode_number": 1,
        "title": "What is AI?",
        "file_results_model": {
          "id": 123,
          "title": "AI Fundamentals",
          "audio_url": "https://example.com/episode1.mp3",
          "feature_image_url": "https://example.com/ep1.jpg"
        },
        "unlocked": true,
        "completed": true,
        "progress_percentage": 100.0
      },
      {
        "id": 2,
        "episode_number": 2,
        "title": "Machine Learning Basics",
        "file_results_model": {
          "id": 124,
          "title": "ML Introduction"
        },
        "unlocked": true,
        "completed": false,
        "progress_percentage": 25.0
      },
      {
        "id": 3,
        "episode_number": 3,
        "title": "Neural Networks",
        "unlocked": false,
        "completed": false,
        "progress_percentage": 0.0
      }
    ],
    "user_progress": {
      "current_episode_number": 2,
      "completion_percentage": 40.0,
      "total_time_spent": 3600
    }
  }
}
```

### 2. Progress Tracking APIs

#### 2.1 Update Episode Progress
```http
PUT /api/series/{seriesId}/episodes/{episodeNumber}/progress
Authorization: Bearer {token}
Content-Type: application/json

{
  "data": {
    "progress_percentage": 75.5,
    "time_spent": 1800,
    "last_position": 1350,
    "completed": false
  }
}
```

#### 2.2 Mark Episode as Complete
```http
POST /api/series/{seriesId}/episodes/{episodeNumber}/complete
Authorization: Bearer {token}
```

#### 2.3 Get User's Series Progress
```http
GET /api/users/me/series-progress?page=1&pageSize=10
Authorization: Bearer {token}
```

### 3. Discovery & Search APIs

#### 3.1 Featured Series
```http
GET /api/series/featured?limit=5
```

#### 3.2 Search Series
```http
GET /api/series/search?q=artificial intelligence&category=Technology&difficulty=beginner
```

#### 3.3 Related Series
```http
GET /api/series/{seriesId}/related?limit=5
```

---

## Implementation Plan

### Phase 1: Core Infrastructure (Week 1-2)
1. **Database Schema Implementation**
   - Create migration files for all series-related tables
   - Add indexes for performance optimization
   - Set up foreign key constraints

2. **Basic CRUD Operations**
   - Series entity and controller
   - Series Episodes entity and controller
   - Basic validation and error handling

3. **Integration with Existing System**
   - Update file-results-model schema to include series relationship
   - Modify existing APIs to include series information where relevant

### Phase 2: Progress Tracking (Week 3-4)
1. **Progress Entities**
   - Series Progress and Episode Progress controllers
   - Real-time progress update mechanisms
   - Episode unlock logic implementation

2. **User Experience Features**
   - Resume functionality
   - Completion tracking
   - Time tracking implementation

### Phase 3: Access Control & Security (Week 5)
1. **Permission System**
   - Public/private series access control
   - Integration with existing organization and user permissions
   - Series access policies

2. **Security Measures**
   - Input validation and sanitization
   - Rate limiting for progress updates
   - Anti-manipulation measures for progress tracking

### Phase 4: Discovery & UI APIs (Week 6-7)
1. **Discovery Features**
   - Public series listing with filters
   - Search functionality
   - Featured series management

2. **Enhanced APIs**
   - Pagination optimization
   - Caching strategies
   - Performance monitoring

### Phase 5: Analytics & Optimization (Week 8)
1. **Analytics Integration**
   - Series completion analytics
   - User engagement metrics
   - Performance optimization based on usage patterns

2. **Testing & Documentation**
   - Comprehensive API testing
   - Load testing for high concurrency
   - API documentation updates

---

## Access Control Strategy

### 1. Current Implementation (Public Access)

All series will initially be public and free to access, following the existing pattern used for playlists and knowledgebases.

```javascript
// Public series access pattern (similar to existing public playlist logic)
const publicSeries = await strapi.db.query('api::series.series').findMany({
  where: { public: true },
  populate: ['episodes', 'episodes.file_results_model']
});
```

### 2. Future Paid Access Framework

The schema is designed to support future monetization:

#### 2.1 Access Control Levels
- **Free**: Public series accessible to all users
- **Paid**: Premium series requiring subscription or one-time purchase
- **Trial**: Limited access for trial users
- **Organization**: Series available to specific organizations

#### 2.2 Integration with Existing Billing
```javascript
// Future paid access check (following existing subscription patterns)
const hasAccess = await checkSeriesAccess(userId, seriesId);
if (!hasAccess && !series.public) {
  return ctx.throw(403, 'Premium subscription required for this series');
}
```

#### 2.3 Monetization Models
- **Subscription-based**: Access to premium series included in paid plans
- **One-time Purchase**: Individual series purchases using existing credit system
- **Freemium**: First episode free, rest require payment
- **Organization Licenses**: Bulk access for teams/organizations

### 3. Security Considerations

#### 3.1 Progress Integrity
- Server-side validation of all progress updates
- Anti-cheating measures to prevent episode skipping
- Encrypted progress tokens for tamper resistance

#### 3.2 Content Protection
- Episode unlock validation on every request
- Secure streaming URLs with time-limited tokens
- Rate limiting to prevent automated access

#### 3.3 Privacy
- User progress data encryption
- GDPR compliance for progress tracking
- Opt-out mechanisms for analytics

---

## Technical Considerations

### 1. Performance Optimizations

#### 1.1 Database Optimizations
```sql
-- Indexes for common queries
CREATE INDEX idx_series_public_featured ON series(public, featured);
CREATE INDEX idx_series_category ON series(category);
CREATE INDEX idx_series_progress_user ON series_progress(user_id);
CREATE INDEX idx_episode_progress_user_series ON episode_progress(user_id, series_id);
```

#### 1.2 Caching Strategy
- Redis caching for public series listings
- Progress data caching with short TTL
- CDN caching for series metadata

#### 1.3 Pagination Strategy
```javascript
// Cursor-based pagination for large datasets
const series = await strapi.db.query('api::series.series').findMany({
  where: {
    public: true,
    id: { $gt: lastId }
  },
  limit: pageSize,
  orderBy: { id: 'asc' }
});
```

### 2. Scalability Considerations

#### 2.1 Horizontal Scaling
- Stateless API design for load balancing
- Database sharding strategies for large user bases
- Microservice architecture preparation

#### 2.2 Data Archival
- Archived progress data for inactive users
- Series versioning for content updates
- Backup strategies for progress data

### 3. Monitoring & Analytics

#### 3.1 Key Metrics
- Series completion rates
- Average time per episode
- User drop-off points
- Popular series and episodes

#### 3.2 Error Tracking
- Progress update failures
- Access control violations
- Performance bottlenecks

---

## Migration Strategy

### 1. Existing Data Migration

#### 1.1 Playlist to Series Migration
```javascript
// Optional migration script to convert existing playlists to series
const migratePlaylistsToSeries = async () => {
  const playlists = await strapi.db.query('api::playlist.playlist').findMany({
    populate: ['file_results_models']
  });
  
  for (const playlist of playlists) {
    // Create series from playlist
    const series = await strapi.db.query('api::series.series').create({
      data: {
        title: playlist.name,
        description: playlist.description,
        public: playlist.public,
        // ... other mappings
      }
    });
    
    // Create episodes from file results models
    // ... episode creation logic
  }
};
```

### 2. Backward Compatibility

#### 2.1 API Versioning
- Maintain existing playlist APIs
- Gradual migration to series APIs
- Clear deprecation timeline

#### 2.2 Data Consistency
- Dual-write during transition period
- Data validation between old and new systems
- Rollback procedures for failed migrations

---

## Success Metrics

### 1. Technical Metrics
- API response time < 200ms for series listing
- 99.9% uptime for progress tracking
- Support for 10,000+ concurrent users

### 2. Business Metrics
- 30% increase in user session duration
- 50% increase in content completion rates
- Foundation for premium content monetization

### 3. User Experience Metrics
- Series discovery time < 30 seconds
- Progress sync reliability 99.9%
- Episode loading time < 3 seconds

---

This comprehensive documentation provides the foundation for implementing the Series feature while maintaining consistency with your existing codebase patterns and preparing for future monetization opportunities. 