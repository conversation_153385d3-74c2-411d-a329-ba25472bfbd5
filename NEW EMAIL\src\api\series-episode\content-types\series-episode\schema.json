{"kind": "collectionType", "collectionName": "series_episodes", "info": {"singularName": "series-episode", "pluralName": "series-episodes", "displayName": "Series Episode", "description": "Episodes within a series with flexible ordering"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"episode_id": {"type": "uid", "targetField": "title", "required": true, "description": "Stable UUID that never changes - used for progress tracking"}, "series": {"type": "relation", "relation": "manyToOne", "target": "api::series.series", "inversedBy": "series_episodes"}, "file_results_model": {"type": "relation", "relation": "manyToOne", "target": "api::file-results-model.file-results-model"}, "position": {"type": "decimal", "required": true, "description": "Flexible positioning - allows insertion between episodes (e.g., 1.5 between 1 and 2)"}, "title_override": {"type": "string", "maxLength": 255, "description": "Optional title override - falls back to file_results_model.title"}, "description_override": {"type": "text", "description": "Optional description override"}, "duration_override": {"type": "integer", "description": "Optional duration override in minutes"}, "is_preview": {"type": "boolean", "default": false, "description": "Preview episodes accessible without full series access"}, "is_optional": {"type": "boolean", "default": false, "description": "Optional episodes don't block progression"}, "prerequisites": {"type": "json", "default": [], "description": "Array of episode_ids that must be completed first"}}}