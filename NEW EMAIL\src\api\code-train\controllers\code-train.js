'use strict';

/**
 * code-train controller
 */

const { create<PERSON>oreController } = require('@strapi/strapi').factories;
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');


// module.exports = createCoreController('api::file-train.file-train');
module.exports = createCoreController('api::code-train.code-train', ({ strapi }) =>  ({
	async create(ctx) {

		let status = true;
		let data;
		const form = new FormData();
		const file = ctx.request.files['files.file'];
		const buffer = fs.readFileSync(file.path);
		form.append('file', buffer, {
			filename: file.name,
		});
		form.append('force_train', 'true');
		form.append('kb_id', ctx.request.body.kb_id);
		form.append('org_id',ctx.state.user.organization?.org_id);
		form.append('remaining_quota', (ctx.state.user.organization.plan.allowed_training_tokens-ctx.state.user.organization?.current_month_usage?.training_tokens_count));

		try {
			const result = await axios.post(
			`${process.env.TALKBASE_BASE_URL}/v4/code_train`,
			form,
			{
				headers: {
					...form.getHeaders()
				},
				// data: form.getBuffer(),
			}
			).catch(err=>{
				console.log(err);
			});
			data = result.data;
			if (data?.result === 'False') {
				status = false;
			}
			await strapi.query('api::monthly-usage.monthly-usage').update({
				where: { id: ctx.state.user.organization.current_month_usage.id },
				data: {
					trained_count: ctx.state.user.organization.current_month_usage.trained_count+1,
					
				  },
			  });
			if (!ctx.request.body) {
				ctx.request.body = {};
			}
			const res= {'file_name':ctx.request.files['files.file'].name,'kb_name':ctx.request.body.kb_name,'train_status':status};
			ctx.request.body['file_name']=ctx.request.files['files.file'].name;
			ctx.request.body['data'] = JSON.stringify(res);

			const response = await super.create(ctx);

			return response.data;
		} catch (error) {
			console.log(error);
			ctx.badRequest(error);
		}

	  }
}));

