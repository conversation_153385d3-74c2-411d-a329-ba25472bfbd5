import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import { TaskItem } from "../../scrumboard/taskManagement/taskItem";
import ConfirmationNumberOutlinedIcon from "@mui/icons-material/ConfirmationNumberOutlined"; // Make sure to install @iconify/react
import { useNavigate } from "react-router-dom";
import LottieControl from "src/app/main/dashboards/home/<USER>/ctaAnimation";
import CircularProgressWithLabel from "app/shared-components/indicators/circularProgressLabel";
import { CircularProgress } from "@mui/material";

function TicketsWidget({ organizationId }) {
  const navigate = useNavigate();
  const [tickets, setTickets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const pageSize = 5;
  const containerRef = useRef(null);

  useEffect(() => {
    fetchTickets();
  }, [page]);

  const fetchTickets = async () => {
    try {
      setLoading(true);
      const response = await axios.get(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/usage/tickets`,
        {
          params: {
            page: page,
            pageSize: pageSize,
          },
        }
      );
      const { data } = response.data;
      setTickets(data);
      setLoading(false);
    } catch (e) {
      setLoading(false);
      setError(e.message);
    }
  };

  const handleScroll = () => {
    if (
      containerRef.current &&
      containerRef.current.scrollTop + containerRef.current.clientHeight >=
        containerRef.current.scrollHeight - 20
    ) {
      setPage((prevPage) => prevPage + 1);
    }
  };

  const handleTicketClick = (boardId, ticketId) => {
    const baseUrl = `/dashboards/kanban/boards/${boardId}`;
    const url = ticketId ? `${baseUrl}?ticketId=${ticketId}` : baseUrl;
    navigate(url);
  };

  return (
    <div
      className="border-1 p-16 pt-20 mr-24 w-full rounded-lg shadow-base transform hover:-translate-y-0.5 hover:shadow-xl"
      style={{
        backgroundColor: tickets.length > 0 || loading ? "white" : "#7C53FE",
        height: "510px",
      }}
    >
      <div className="flex flex-row justify-between">
        <h2
          className="font-bold text-2xl"
          style={{
            color: tickets.length > 0 ? "black" : "white",
          }}
        >
          Tickets
        </h2>
        <span
          className="text-sm mr-8 cursor-pointer text-base-purple underline"
          onClick={() => {
            window.location.href = "/dashboards/kanban/boards";
          }}
        >
          View all
        </span>
      </div>

      <div
        ref={containerRef}
        onScroll={handleScroll}
        className="overflow-y-auto"
      >
        {tickets.length > 0 ? (
          <ul>
            {tickets.map((ticket) => (
              <TaskItem
                key={ticket.id}
                id={ticket.id}
                title={ticket.title}
                ticketState={ticket.listId.title}
                onClick={() => {
                  handleTicketClick(
                    ticket.scrumboard.id,
                    ticket.id
                  );
                }}
                isSelected={false}
                createdAt={ticket.createdAt}
              />
            ))}
          </ul>
        ) : (
          !loading && (
            <div className="flex flex-col items-center justify-center h-full">
              <div className="text-white text-base text-start mt-16 mb-4">
                No tickets or leads found! Have you created an agent yet? Get
                started by creating an agent to collect leads, allow customers
                to book appointments, and raise tickets effortlessly!
              </div>
              <div className="flex flex-col items-center justify-center">
                <LottieControl />
                <button
                  className="mt-16 text-base-purple shadow-md bg-white text-l rounded p-8 w-[170px]
                             transition duration-300 ease-in-out
                             hover:shadow-[0_0_10px_rgba(255,255,255,0.8)] hover:brightness-110
                             active:shadow-inner active:transform active:scale-95"
                  onClick={() => {
                    window.location.href = "/dashboards/knowledge_base";
                  }}
                >
                  Create your agent
                </button>
              </div>
            </div>
          )
        )}
        {loading && (
          <div className="flex justify-center items-center w-full h-full">
            <CircularProgress size={24} />
          </div>
        )}
        {error && <div className="text-red-500 mt-4">Error: {error}</div>}
      </div>
    </div>
  );
}

export default TicketsWidget;
