// const axios = require("axios");

// NEW APPROACH: Direct SendGrid API
const sgMail = require('@sendgrid/mail');

// Initialize SendGrid with API key once at module level
sgMail.setApiKey(process.env.SENDGRID_API_KEY);

module.exports = {
  async sendEmail({emails, name, subject, title, description, templateId, agent_name, customer_email, ticket_url}) {
    
    //  Via GraphQL microservice
    /*
    const options = {
      method: "POST",
      url: `${process.env.EMAIL_SERVICE_URL}/graphql`,
      headers: {
        "Content-Type": "application/json",
      },
      data: {
        query:
          "mutation Mutation($newEmailData: NewEmailInput!) {\n  sendEmails(newEmailData: $newEmailData) {\n    error\n    message\n  }\n}",
        operationName: "Mutation",
        variables: {
          newEmailData: {
            templateId: templateId,
            recipients: emails,
            dynamicTemplateData: {
              sendername: name,
              agent_name: agent_name,
              customer_email: customer_email,
              ticket_title: title,
              ticket_description: description,
              ticket_url: ticket_url
            },
            subject: subject,
          },
        },
      },
    };

    try {
      const response = await axios(options);
      console.log(response.data);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
    */

    // Direct SendGrid API (ACTIVE)
    try {
      // Send email using SendGrid dynamic template
      const msg = {
        to: emails, // Can be a single email or array
        from: {
          email: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
          name: 'PODYCY'
        },
        templateId: templateId,
        // Set subject directly (this overrides template subject)
        subject: subject || 'New Account verification - Podycy',
        dynamicTemplateData: {
          // Map your variables to what the template expects
          confirmation_link: ticket_url, // For confirmation emails
          reset_url: ticket_url, // For forgot password emails
          sendername: name,
          agent_name: agent_name,
          customer_email: customer_email,
          ticket_title: title,
          ticket_description: description,
          ticket_url: ticket_url,
          // Also add subject to dynamic data in case template uses it
          email_subject: subject || 'New Account verification - Podycy'
        },
      };

      const response = await sgMail.send(msg);
      console.log('✅ Email sent successfully via SendGrid:', response[0].statusCode);
      return { success: true, message: 'Email sent successfully' };
    } catch (error) {
      console.error('❌ SendGrid Error:', error);
      if (error.response) {
        console.error('SendGrid Error Body:', error.response.body);
      }
      throw error;
    }
  },
};
