import React, { useState, useEffect } from "react";
import FusePageSimple from "@fuse/core/FusePageSimple";
import history from "@history";
import axios from "axios";
import { selectUser, setUser } from "app/store/userSlice";
import { showMessage } from "app/store/fuse/messageSlice";
import { useSelector, useDispatch } from "react-redux";
import AddKnowledgebaseName from "./AddKnowledgebaseName";
import "./kb.css";
import _ from "@lodash";
import { logChatbotCreationEvent } from "src/app/utils/analytics";
import { selectUsage, hasExceededKBLimit } from "app/store/usageSlice";
import CTAButon from "app/shared-components/buttons/cta-button";
import { AgentsTable } from "./tables/AgentsTable";
import { Grid, Typography } from "@mui/material";

export default function KnowledgeBaseDashboardApp() {
  const [uploadKBNameOpen, setUploadKBName] = useState(false);
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [failureMessage, setFailureMessage] = useState("");
  const [kbDetailsFailureMessage, setkbDetailsFailureMessage] = useState("");
  const [kbmetaData, setKbmetaData] = useState([]);
  const user = useSelector(selectUser);
  const dispatch = useDispatch();
  const [currentPage, setCurrentPage] = useState(1);
  const usage = useSelector(selectUsage);

  async function fetchData() {
    setLoading(true);
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/knowledgebases`
      );
      const kbData = await response.data;
      kbData.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      setkbDetailsFailureMessage("");
      setKbmetaData(kbData);
      setLoading(false);
    } catch (error) {
      let err;
      if (error.response?.data?.error?.message) {
        err = error.response?.data?.error?.message;
      } else {
        err = "Something went wrong, Try again";
      }

      setkbDetailsFailureMessage(err);
      setLoading(false);
      console.error(error);
    }
  }

  useEffect(() => {
    fetchData();
  }, []);

  const handleUploadKBNameOpen = () => {
    setSuccessMessage("");
    setFailureMessage("");
    setUploadKBName(true);
  };

  const handleUploadKBNameClose = () => {
    setUploadKBName(false);
  };

  const handleAddChatbotFormSubmit = (kbName, selectedOption, ai_task) => {
    if (
      !(
        user.data.organization.subscription === "subscribed" ||
        user.data.organization.subscription === "trial"
      )
    ) {
      handleUploadKBNameClose();
      dispatch(
        showMessage({
          message: "You haven't subscribed. Please do subscribe",
        })
      );
      return;
    }

    if (hasExceededKBLimit(user.data.organization.plan, usage)) {
      dispatch(
        showMessage({
          message:
            "Your Agent usage limit is exceeded. Please updgrade your plan ",
        })
      );
      handleUploadKBNameClose();
    } else if (
      !kbName ||
      kbName.trim() === "" ||
      kbName.length < 3 ||
      !/^[\w-]+$/.test(kbName)
    ) {
      setFailureMessage("Add a valid chabot name");
    } else {
      setLoading(true);
      const options = {
        method: "POST",
        url: `${process.env.REACT_APP_AUTH_BASE_URL}/api/knowledgebases`,
        headers: { "Content-Type": "application/json" },
        data: {
          kb_name: kbName,
          type: selectedOption,
          default_ai_task: parseInt(ai_task),
        },
      };

      axios
        .request(options)
        .then(function (response) {
          if (response.data) {
            logChatbotCreationEvent(user.data.email, "chatbotCreate", {
              kb_name: kbName,
              type: selectedOption,
            });
            setSuccessMessage(
              "Agent created successfully. Now you can train it"
            );

            var updatedUser = JSON.parse(JSON.stringify(user)); // Shallow copy of the user object
            updatedUser.data.organization.current_month_usage.kb_count += 1;
            dispatch(setUser(updatedUser));
            fetchData();
            if (response.data.attributes.type === "multiAgent") {
              history.push(
                `/dashboards/turbo-writer?kbID=${response.data.attributes.kb_id}&kbName=${response.data.attributes.name}&kbrID=${response.data.id}`
              );
            } else {
              const url = `/dashboards/knowledge_base/items_detail_page?kbrID=${response.data.id}&kbName=${response.data.attributes.name}&kbID=${response.data.attributes.kb_id}`;
              history.push(url);
            }
          } else {
            setFailureMessage(response.data.status);
          }
        })
        .catch(function (error) {
          var err = error?.response?.data?.error?.message;
          setFailureMessage(err ?? "Agent creation failed. Try again");
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  return (
    <FusePageSimple
      content={
        <div className="w-full mt-48 pl-4 pr-8 md:px-32 pb-24">
          <Grid container className="mt-10 p-10 mx-auto ">
            <Grid item xs={12}>
              <div className="flex flex-row justify-between">
                <div className="flex flex-col ">
                  <Typography className="text-3xl font-bold tracking-tight mb-10">
                    <span className="">Agents</span>
                  </Typography>
                  <Typography className="font-regular text-lg text-gray-800 tracking-tight mt-8">
                    See & create your Agents here
                  </Typography>
                </div>
                <CTAButon
                  text="New Agent"
                  image="assets/images/add-icon.svg"
                  onPress={handleUploadKBNameOpen}
                />
              </div>
              <div className="h-full w-full mt-16 ">
                {kbDetailsFailureMessage ? (
                  <div className="text-red text-center mt-20">
                    {kbDetailsFailureMessage}
                  </div>
                ) : (
                  <AgentsTable
                    onDeleteClick={(agentId) => {
                      openKbDeleteConfirmDialog(agentId);
                    }}
                  />
                )}
              </div>
            </Grid>
          </Grid>
          {!successMessage && (
            <AddKnowledgebaseName
              open={uploadKBNameOpen}
              handleClose={handleUploadKBNameClose}
              handleSubmit={(kbName, selectedOption, ai_task) => {
                handleAddChatbotFormSubmit(kbName, selectedOption, ai_task);
              }}
              loading={loading}
              successMessage={successMessage}
              failureMessage={failureMessage}
              setSuccessMessage={setSuccessMessage}
              setFailureMessage={setFailureMessage}
            />
          )}
        </div>
      }
    />
  );
}
