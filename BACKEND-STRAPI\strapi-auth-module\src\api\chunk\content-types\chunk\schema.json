{"kind": "collectionType", "collectionName": "chunks", "info": {"singularName": "chunk", "pluralName": "chunks", "displayName": "Chunk", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"content": {"type": "text"}, "embedding_id": {"type": "string"}, "chunk_index": {"type": "integer", "required": true}, "kb_id": {"type": "string"}, "status": {"type": "enumeration", "enum": ["succeeded", "failed"], "default": "draft"}, "document": {"type": "relation", "relation": "manyToOne", "target": "api::document.document", "inversedBy": "chunks"}}}