const SendGridEmailProvider = require('./sendgrid-email-provider');
const MailgunEmailProvider = require('./mailgun-email-provider');
const PostmarkEmailProvider = require('./postmark-email-provider');

/**
 * Email Provider Factory
 * Manages email provider instances and provides a unified interface
 */
class EmailProviderFactory {
  constructor() {
    this.providers = new Map();
    this.defaultProvider = null;
    this.initialized = false;
  }

  /**
   * Register an email provider
   * @param {string} name - Provider name
   * @param {EmailProviderInterface} providerClass - Provider class
   * @param {Object} config - Provider configuration
   */
  registerProvider(name, providerClass, config = {}) {
    try {
      const provider = new providerClass(config);
      this.providers.set(name.toLowerCase(), provider);
      
      strapi.log.info(`📧 Registered email provider: ${name}`);
      return provider;
    } catch (error) {
      strapi.log.error(`❌ Failed to register email provider ${name}:`, error.message);
      throw error;
    }
  }

  /**
   * Set the default email provider
   * @param {string} providerName - Name of the provider to set as default
   */
  setDefaultProvider(providerName) {
    const provider = this.providers.get(providerName.toLowerCase());
    if (!provider) {
      throw new Error(`Email provider '${providerName}' not found`);
    }
    
    this.defaultProvider = provider;
    strapi.log.info(`📧 Default email provider set to: ${providerName}`);
  }

  /**
   * Get a specific email provider
   * @param {string} providerName - Provider name
   * @returns {EmailProviderInterface} - Email provider instance
   */
  getProvider(providerName) {
    if (!providerName) {
      return this.defaultProvider;
    }
    
    const provider = this.providers.get(providerName.toLowerCase());
    if (!provider) {
      throw new Error(`Email provider '${providerName}' not found`);
    }
    
    return provider;
  }

  /**
   * Get all registered providers
   * @returns {Array} - Array of provider names
   */
  getAvailableProviders() {
    return Array.from(this.providers.keys());
  }

  /**
   * Initialize all registered providers
   */
  async initializeProviders() {
    try {
      const initPromises = Array.from(this.providers.values()).map(async (provider) => {
        try {
          await provider.initialize();
          return { provider: provider.getProviderName(), success: true };
        } catch (error) {
          strapi.log.error(`Failed to initialize ${provider.getProviderName()}:`, error.message);
          return { provider: provider.getProviderName(), success: false, error: error.message };
        }
      });

      const results = await Promise.all(initPromises);
      this.initialized = true;
      
      strapi.log.info('📧 Email provider initialization completed:', results);
      return results;
    } catch (error) {
      strapi.log.error('❌ Email provider initialization failed:', error.message);
      throw error;
    }
  }

  /**
   * Send email using specified or default provider
   * @param {Object} emailData - Email data
   * @param {string} [providerName] - Specific provider to use
   * @returns {Promise<Object>} - Send result
   */
  async sendEmail(emailData, providerName = null) {
    try {
      const provider = this.getProvider(providerName);
      if (!provider) {
        throw new Error('No email provider available');
      }

      return await provider.sendEmail(emailData);
    } catch (error) {
      strapi.log.error('❌ Email send failed:', error.message);
      throw error;
    }
  }

  /**
   * Send bulk emails using specified or default provider
   * @param {Array} emails - Array of email data objects
   * @param {string} [providerName] - Specific provider to use
   * @returns {Promise<Object>} - Bulk send result
   */
  async sendBulkEmails(emails, providerName = null) {
    try {
      const provider = this.getProvider(providerName);
      if (!provider) {
        throw new Error('No email provider available');
      }

      return await provider.sendBulkEmails(emails);
    } catch (error) {
      strapi.log.error('❌ Bulk email send failed:', error.message);
      throw error;
    }
  }

  /**
   * Test all providers or a specific provider
   * @param {string} [providerName] - Specific provider to test
   * @returns {Promise<Object>} - Test results
   */
  async testProviders(providerName = null) {
    try {
      if (providerName) {
        const provider = this.getProvider(providerName);
        const result = await provider.testConnection();
        return {
          [providerName]: {
            success: result,
            provider: provider.getProviderName()
          }
        };
      }

      const testPromises = Array.from(this.providers.entries()).map(async ([name, provider]) => {
        try {
          const result = await provider.testConnection();
          return {
            name,
            success: result,
            provider: provider.getProviderName()
          };
        } catch (error) {
          return {
            name,
            success: false,
            error: error.message,
            provider: provider.getProviderName()
          };
        }
      });

      const results = await Promise.all(testPromises);
      return results.reduce((acc, result) => {
        acc[result.name] = {
          success: result.success,
          provider: result.provider,
          ...(result.error && { error: result.error })
        };
        return acc;
      }, {});
    } catch (error) {
      strapi.log.error('❌ Provider test failed:', error.message);
      throw error;
    }
  }
}

// Create singleton instance
const emailProviderFactory = new EmailProviderFactory();

// Register default providers
function initializeDefaultProviders() {
  try {
    // Register SendGrid provider
    const sendGridConfig = {
      apiKey: process.env.SENDGRID_API_KEY,
      defaultFrom: process.env.SENDGRID_FROM_EMAIL,
      defaultFromName: process.env.SENDGRID_FROM_NAME || 'PODYCY'
    };
    
    emailProviderFactory.registerProvider('sendgrid', SendGridEmailProvider, sendGridConfig);
    
    // Register Mailgun provider if configured
    if (process.env.MAILGUN_API_KEY && process.env.MAILGUN_DOMAIN) {
      const mailgunConfig = {
        apiKey: process.env.MAILGUN_API_KEY,
        domain: process.env.MAILGUN_DOMAIN,
        defaultFrom: process.env.MAILGUN_FROM_EMAIL,
        defaultFromName: process.env.MAILGUN_FROM_NAME || 'PODYCY'
      };
      
      emailProviderFactory.registerProvider('mailgun', MailgunEmailProvider, mailgunConfig);
    }
    
    // Register Postmark provider if configured
    if (process.env.POSTMARK_API_KEY) {
      const postmarkConfig = {
        apiKey: process.env.POSTMARK_API_KEY,
        defaultFrom: process.env.POSTMARK_FROM_EMAIL || '<EMAIL>',
        defaultFromName: process.env.POSTMARK_FROM_NAME || 'Podycy Team'
      };
      
      emailProviderFactory.registerProvider('postmark', PostmarkEmailProvider, postmarkConfig);
    }
    
    // Set default provider (prefer Postmark)
    emailProviderFactory.setDefaultProvider('postmark');

    strapi.log.info('📧 Default email providers initialized');
  } catch (error) {
    strapi.log.error('❌ Failed to initialize default email providers:', error.message);
  }
}

module.exports = {
  EmailProviderFactory,
  emailProviderFactory,
  initializeDefaultProviders
}; 