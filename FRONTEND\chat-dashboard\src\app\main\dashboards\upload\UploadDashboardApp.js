import _ from '@lodash';
import FusePageSimple from '@fuse/core/FusePageSimple';
import Typography from '@mui/material/Typography';
import UploadDashboardAppHeader from './UploadDashboardAppHeader';
import React, { useState } from "react";
import FileUploadArea from "./FileUploadArea/FileUploadArea";
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import LoadingSpinner from './LoadingSpinner/LoadingSpinner';
import axios from "axios";
import { useSelector } from 'react-redux';
import { selectUser } from 'app/store/userSlice';
import TableComponent from './TableComponent'
import { selectUsage } from 'app/store/usageSlice';
import { useDispatch } from 'react-redux';

function UploadDashboardApp() {
  const [link, setLink] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [failureMessage, setFailureMessage] = useState("");
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const user = useSelector(selectUser);
  const usage = useSelector(selectUsage);
  const dispatch = useDispatch();

  const handleUpload = (event) => {
    event.preventDefault();
    if(successMessage!= "" || failureMessage!= ""){
      setSuccessMessage("");
      setFailureMessage("");
    }

    // If link value is provided, call API with link value
    if((usage>=user.data.usage_quota)){
      dispatch(showMessage( 'Your usage limit is exceeded ' ));
      }
    else if (link) {
      setLoading(true);
      const options = {
        method: 'POST',
        url: `${process.env.REACT_APP_AUTH_BASE_URL}/api/trains`,
        headers: {'Content-Type': 'application/json'},
        data: {data:{kb_name: process.env.REACT_APP_KB_NAME, link: link,}}
      };

      axios.request(options).then(function (response) {
        if(response.data.attributes.train_status){
          setSuccessMessage("Link Train Successful !!");
          setLink("");
          }
          else{
            setFailureMessage("Link Train Failed!!");
            setLink("");
          }
       
      }).catch(function (error) {
        error.response.data.description
        setFailureMessage(error.response?.data?.description??"Link Train Failed !!")
      }).finally(() => {
        setLoading(false);
      });
    } else if (files) { 
      // If files are provided, loop through and append to form data before calling API
      setLoading(true);
      const form = new FormData();
      
      // for (let i = 0; i < files.length; i++) {
        form.append('files.file', files[0]);
        form.append('kb_name', process.env.REACT_APP_KB_NAME);
        form.append('force_train', true);
    // }

    const options = {
      method: 'POST',
      url: `${process.env.REACT_APP_AUTH_BASE_URL}/api/file-trains`,
      headers: {'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundary1tzbASF75A9d5cTB'},
      data: form
    };
   
    // fetch(options.url, {
    //   method: 'POST',
    //   headers: {
    //     'Authorization': axios.defaults.headers.common.Authorization,
    //     'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundary1tzbASF75A9d5cTB',
    //   },
    //   body: form,
    // })
    // .then((response) => {
    //   if(response.data.result=="Success"){
    //       setSuccessMessage("File Upload successful!");
    //       }
    //       else{
    //         setFailureMessage(response.data.status);
    //       }
    // })
    // .catch((error) => {
    //   setFailureMessage(error.response?.data?.description??"File Upload Failed !!");
    // }).finally(() => {
    //   setLoading(false);
    // });
    axios.post(options.url,form,{ headers: options.headers })
    .then((response) => {
      if(response.data.attributes.train_status){
        setSuccessMessage("File Upload successful!");
      }
      else{
        setFailureMessage(response.data.status);
      }
    })
    .catch((error) => {
      setFailureMessage(error.response?.data?.description??"File Upload Failed !!");
    }).finally(() => {
      setLoading(false);
    });

      }
    
    };

    const showSubmitButton = link || files.length > 0;

  return (
    <FusePageSimple
      header={<UploadDashboardAppHeader />}
      content={
        <div className="w-full px-24 md:px-32 pb-24">
          <div className="max-w-screen-sm mx-auto">
            <div className="border border-green p-10 flex rounded-md w-full ">
              <FuseSvgIcon className="text-48" size={24} color="action">
                heroicons-solid:link
              </FuseSvgIcon>
              <input
                type="text"
                value={link}
                onChange={(event) => { 
                  setLink(event.target.value);
                  if(successMessage!= "" || failureMessage!= ""){
                    setSuccessMessage("");
                    setFailureMessage("");
                  }
                } }
                className="mx-6 flex-grow"
                placeholder="Add through webpage"
                disabled={files.length > 0}
              />
            </div>
            {files.length > 0 ? <p className="text-green text-small" style={{marginTop: "5px"}}>Link field is disabled because files are uploaded for knowledge train</p> : 
            <></> }
            {link ? (
              <p className="text-green text-small" style={{marginTop: "5px"}}>File upload area is disabled because Link is set for knowledge train.</p>
            ) : (
              <FileUploadArea setFiles={setFiles}/>
            )}

            {showSubmitButton && (
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                  <button
                    className="bg-green text-white px-4 py-2 rounded-md mt-4 center"
                    onClick={handleUpload}
                    disabled={!(link || files.length)}
                    style={{ display: 'flex', justifyContent: 'center', padding: '10px', marginTop: '20px' }}
                  >
                    {loading ? (
                      <LoadingSpinner className="sticky" style={{ marginRight: '10px' }} />
                    ) : (
                      <img style={{ marginRight: '8px' }} src="assets/images/upload_knowledge.png" alt="logo" />
                    )}
                    Add Knowledge
                  </button>
              </div>
            )}
            {successMessage && (
              <div style={{ marginTop: '20px' }} className="text-green-600 text-center mt-4">{successMessage}</div>
            )}
            {failureMessage && (
              <div style={{ marginTop: '20px' }} className="text-red-600 text-center mt-4">{failureMessage}</div>
            )}
            <br/>
            <br/>
            {/* <div className='text-center'><b>Uploaded Knowledge</b></div> */}
            <Typography className="font-medium text-20 text-center">Uploaded Knowledge</Typography>
            <TableComponent />
          </div>
          
        </div>
      }
    />
  );
}

export default UploadDashboardApp;
