import React, { useState, useEffect } from "react";
import { DataGrid } from "@mui/x-data-grid";
import {
  Button,
  DialogActions,
  CircularProgress,
  IconButton,
  Switch,
  FormControlLabel,
} from "@mui/material";
import { Link } from "react-router-dom";
import FuseSvgIcon from "@fuse/core/FuseSvgIcon";
import "./upload.css";
import BaseDialog from "app/shared-components/dialog/base-dialog";

export default function ScrapedInput({
  open,
  handleClose,
  handleSubmit,
  loading,
  successMessage,
  failureMessage,
  url,
  scrapeUrls,
}) {
  const [selectedRows, setSelectedRows] = useState([]);
  const [showUrlsOnly, setShowUrlsOnly] = useState(true); // ✅ default ON

  useEffect(() => {
    const allRowIds = scrapeUrls.map((row) => row.id);
    setSelectedRows(allRowIds);
  }, [scrapeUrls]);

  const handleSelectionChange = (selectionModel) => {
    setSelectedRows(selectionModel);
  };

  // Filter to show only valid URLs (exclude media files)
  const filteredScrapeUrls = showUrlsOnly
    ? scrapeUrls.filter(
        (row) =>
          /^https?:\/\/[^\s]+$/.test(row.url) &&
          !/\.(png|jpe?g|gif|webp|bmp|tiff|mov|mp4|avi|mkv|webm)$/i.test(
            row.url) &&
          !/\/?sitemap(\.xml)?$/i.test(row.url)  
      )
    : scrapeUrls;

  // Ensure selection is valid for filtered list
  const validSelection = selectedRows.filter((id) =>
    filteredScrapeUrls.some((row) => row.id === id)
  );

  const handleFormSubmit = (event) => {
    event.preventDefault();
    // ✅ Only pass rows that are in filteredScrapeUrls and selected
    const selectedUrls = filteredScrapeUrls.filter((row) =>
      selectedRows.includes(row.id)
    );
    handleSubmit(selectedUrls);
  };

  const columns = [
    {
      field: "url",
      headerName: "URL",
      width: 670,
      renderCell: (params) => {
        const truncatedUrl =
          params.value.length > 70
            ? params.value.slice(0, 70) + "..."
            : params.value;
        return (
          <div>
            {truncatedUrl}
            <IconButton
              component={Link}
              onClick={(event) => {
                window.open(
                  `${process.env.REACT_APP_TRACKER_PAGE}?url=${params.value}`,
                  "_blank"
                );
                event.stopPropagation();
              }}
              disableTouchRipple
            >
              <FuseSvgIcon size={20} color="action">
                material-outline:call_made
              </FuseSvgIcon>
            </IconButton>
          </div>
        );
      },
      disableClickEventBubbling: true,
    },
  ];

  return (
    <BaseDialog open={open} handleClose={handleClose} title="Add Urls (Scrape)">
      {/* URL input field */}
      <div className="flex">
        <div className="bg-gray-100 p-8 flex rounded-md w-full">
          <input
            type="text"
            value={url}
            className="mx-6 flex-grow bg-gray-100 text-gray"
            placeholder="Url"
            disabled
          />
        </div>
      </div>

      {/* Filter Toggle */}
      <div className="flex justify-end items-center pr-4 pt-2">
        <FormControlLabel
          control={
            <Switch
              checked={showUrlsOnly}
              onChange={() => setShowUrlsOnly((prev) => !prev)}
              color="primary"
            />
          }
          label="URLs Only"
        />
      </div>

      {/* Data Table */}
      <div className="scraped-table">
        <DataGrid
          rows={filteredScrapeUrls}
          columns={columns}
          pageSizeOptions={[10]}
          rowsPerPageOptions={[50]}
          selectionModel={validSelection}
          onSelectionModelChange={handleSelectionChange}
          checkboxSelection
          disableColumnMenu
        />
      </div>

      {/* Feedback Messages */}
      {successMessage && (
        <div className="text-green-600 text-center mt-5">{successMessage}</div>
      )}
      {failureMessage && (
        <div className="text-red-600 text-center mt-5">{failureMessage}</div>
      )}

      {/* Action Buttons */}
      <DialogActions className="justify-end px-0 mx-0 mt-20">
        {loading ? (
          <CircularProgress
            thickness={5}
            size={30}
            color="secondary"
            className="mr-24"
          />
        ) : (
          <Button
            disabled={!showUrlsOnly || validSelection.length === 0} // ✅ disable if toggle is off
            className="shine-buton press-button bg-black min-h-32 max-h-32 mr-24 hover:bg-base-purple mb-16"
            variant="outlined"
            onClick={handleFormSubmit}
          >
            Submit
          </Button>
        )}
      </DialogActions>
    </BaseDialog>
  );
}
