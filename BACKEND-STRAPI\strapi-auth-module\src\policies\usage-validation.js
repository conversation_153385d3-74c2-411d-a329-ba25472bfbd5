"use strict";

/**
 * Validationpolicy
 */

const { errors } = require('@strapi/utils');
const { PolicyError } = errors;

module.exports = async (policyContext, config, { strapi }) => {
  try {
    switch (policyContext.routerPath) {
      case "/api/answers":
      case "/api/runanswer":
        if (
          parseInt(policyContext.state.user.organization.current_month_usage.credits_used) >=
          parseInt(policyContext.state.user.organization.plan.allowed_credits)
        ) {
          throw new PolicyError(
            "You have exceeded the query limit"
          );

        }
        break;
      case "/api/askquestion":
        if (
          parseInt(policyContext.state.user.organization.current_month_usage.credits_used) >=
          parseInt(policyContext.state.user.organization.plan.allowed_credits)
        ) {
          throw new PolicyError(
            "You have exceeded the query limit"
          );
        }
        break;
      case "/api/trains":
      case "/api/code-trains":
      case "/api/file-trains":
      case "/api/v3/train":
        if (
          parseInt(policyContext.state.user.organization.current_month_usage.training_tokens_count) >=
            parseInt(policyContext.state.user.organization.plan.allowed_training_tokens)
        ) {
          throw new PolicyError(
            "You have exceeded the usage limit"
          );
        }

        break;

      case "/api/knowledgebases":
        const [kbs, kbCount] = await strapi
          .query("api::knowledgebase.knowledgebase")
          .findWithCount({
            where: { organization: policyContext.state.user.organization.id,publishedAt:{
              $notNull: true,
            } },
          });
        if (
          kbCount >= policyContext.state.user.organization.plan.allowed_kbs
        ) {
          throw new PolicyError("You have exceeded the Chatbot limit");
        }
        break;

      default:
        break;
    }
  } catch (e) {
    throw e;
  }
  return true;
};
