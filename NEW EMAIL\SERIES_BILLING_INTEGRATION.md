# Series Billing Integration

## 💰 **How Series Ties Into Your Existing Billing System**

The Series feature seamlessly integrates with your established subscription, credit, and one-time order infrastructure.

## 🔄 **Integration Patterns**

### 1. **Subscription-Based Access** (Most Common)
```javascript
// Plan tiers automatically include series access
const plan = {
  name: "Premium",
  series_access_level: "premium", // Includes all basic + premium series
  allowed_credits: 1000
}

// User gets access based on active subscription
const hasAccess = await checkSeriesSubscriptionAccess(userId, seriesId);
```

### 2. **Credit-Based Purchases** (Per-Series)
```javascript
// Premium series costs credits to unlock
POST /api/series/123/purchase/credits
// Uses existing credit deduction logic:
// 1. One-time credits first (credit_balance.available_credits)
// 2. Then subscription credits (monthly_usage.query_count)
```

### 3. **One-Time Purchases** (Permanent Access)
```javascript
// Buy permanent access to individual series
POST /api/series/123/purchase/one-time
// Creates Stripe checkout → webhook → series_access grant
```

## 📊 **Access Control Hierarchy**

### Series Access Decision Tree:
```
1. Is series public? → ✅ Grant Access
2. Is user subscription active?
   └── Does plan tier include series tier? → ✅ Grant Access
3. Does user have explicit paid access? → ✅ Grant Access
4. Can user purchase with credits?
   └── Sufficient credits available? → 💳 Offer Purchase
5. Can user buy one-time access?
   └── Series available for purchase? → 💰 Offer Purchase
6. ❌ Deny Access
```

## 🏗 **Schema Extensions**

### Enhanced Plan Schema:
```javascript
{
  // Existing fields...
  "series_access_level": "premium",    // none, basic, premium, all
  "allowed_premium_series": 5,         // 0 = unlimited
  "series_credit_cost": 10            // Credits per premium series
}
```

### Enhanced Series Schema:
```javascript
{
  // Existing fields...
  "access_tier": "premium",           // free, basic, premium, enterprise
  "credit_cost": 50,                  // Credits to unlock
  "one_time_price": 29.99,           // One-time purchase price
  "stripe_price_id": "price_xxx"     // Stripe price ID
}
```

### Extended One-Time Orders:
```javascript
{
  // Existing fields...
  "purchase_type": "series-one-time", // New enum value
  "series_purchased": seriesId,       // Series relation
  "amount_paid": 29.99               // Payment amount
}
```

## 💳 **Credit Integration Examples**

### Purchase Series with Credits:
```javascript
// Uses your existing credit calculation logic
const userWithOrg = await strapi.query("plugin::users-permissions.user").findOne({
  where: { id: userId },
  populate: {
    organization: {
      populate: {
        credit_balance: true,      // One-time credits
        monthly_usages: true,      // Subscription credits
        plan: true
      }
    }
  }
});

// Calculate available credits (your existing pattern)
const oneTimeCredits = org.credit_balance?.available_credits || 0;
const subscriptionCredits = getCurrentPeriodRemaining(org.monthly_usages);
const totalCredits = oneTimeCredits + subscriptionCredits;

// Deduct credits (your existing pattern)
if (totalCredits >= series.credit_cost) {
  // Deduct from one-time credits first, then subscription
  await deductCredits(series.credit_cost);
  await grantSeriesAccess(userId, seriesId);
}
```

## 🔄 **Subscription Tier Access**

### Plan Configuration Examples:

#### Free Plan:
```javascript
{
  name: "Free",
  series_access_level: "free",       // Only free series
  allowed_premium_series: 0,         // No premium series
  series_credit_cost: 0              // No additional cost
}
```

#### Basic Plan:
```javascript
{
  name: "Basic",
  series_access_level: "basic",      // Free + basic series
  allowed_premium_series: 3,         // 3 premium series
  series_credit_cost: 20             // 20 credits per premium
}
```

#### Premium Plan:
```javascript
{
  name: "Premium", 
  series_access_level: "premium",    // All free, basic, premium
  allowed_premium_series: 0,         // Unlimited
  series_credit_cost: 0              // No additional cost
}
```

### Access Check Logic:
```javascript
async function checkSeriesAccess(userId, seriesId) {
  const series = await getSeries(seriesId);
  
  // 1. Public series
  if (series.public) return true;
  
  // 2. Subscription-based access
  const hasSubscriptionAccess = await checkSubscriptionAccess(userId, series.access_tier);
  if (hasSubscriptionAccess) return true;
  
  // 3. Explicit paid access
  const hasPaidAccess = await checkPaidAccess(userId, seriesId);
  if (hasPaidAccess) return true;
  
  // 4. No access
  return false;
}
```

## 🛒 **Purchase Flows**

### Credit Purchase Flow:
```javascript
// 1. User clicks "Unlock Series" 
// 2. Check credit requirements
const creditsNeeded = series.credit_cost;
const creditsAvailable = await getUserCredits(userId);

if (creditsAvailable >= creditsNeeded) {
  // 3. Deduct credits
  await deductCredits(userId, creditsNeeded);
  
  // 4. Grant access
  await createSeriesAccess({
    series: seriesId,
    user: userId,
    access_type: 'paid',
    notes: `Purchased with ${creditsNeeded} credits`
  });
}
```

### One-Time Purchase Flow:
```javascript
// 1. User clicks "Buy Series"
// 2. Create Stripe checkout
const session = await stripe.checkout.sessions.create({
  line_items: [{ price: series.stripe_price_id, quantity: 1 }],
  metadata: {
    user_id: userId,
    series_id: seriesId,
    purchase_type: 'series_one_time'
  }
});

// 3. Webhook processes payment
// 4. Grant access and create order record
```

## 📈 **Analytics & Reporting**

### Revenue Tracking:
```sql
-- Series revenue from one-time purchases
SELECT 
  s.title,
  COUNT(oto.id) as purchases,
  SUM(oto.amount_paid) as revenue
FROM series s
JOIN one_time_orders oto ON oto.series_purchased = s.id
WHERE oto.purchase_type = 'series-one-time'
GROUP BY s.id;

-- Credit consumption by series
SELECT 
  s.title,
  SUM(sa.credits_used) as total_credits_consumed
FROM series s  
JOIN series_access sa ON sa.series = s.id
WHERE sa.access_type = 'paid'
GROUP BY s.id;
```

### Subscription Impact:
```sql
-- Series access by plan tier
SELECT 
  p.name as plan_name,
  p.series_access_level,
  COUNT(DISTINCT sp.user) as active_users
FROM plans p
JOIN organizations o ON o.plan = p.id
JOIN up_users u ON u.organization = o.id
JOIN series_progress sp ON sp.user = u.id
GROUP BY p.id;
```

## 🔧 **Implementation Examples**

### Middleware for Series Access:
```javascript
// Add to existing usage-validation policy
const seriesAccess = await checkSeriesAccess(userId, seriesId);
if (!seriesAccess) {
  // Check purchase options
  const purchaseOptions = await getSeriesPurchaseOptions(userId, seriesId);
  return ctx.throw(402, 'Payment Required', { purchaseOptions });
}
```

### Webhook Integration:
```javascript
// Extend existing webhook handler
case "stripe_checkout_completed":
  const { metadata } = webhook_data;
  
  if (metadata.purchase_type === 'series_one_time') {
    await processSeriesPurchase(metadata);
  }
  // ... existing cases
  break;
```

## 💡 **Business Models Supported**

### 1. **Freemium Model**
- Free series for all users
- Premium series require subscription/credits

### 2. **Tiered Access** 
- Basic: Free + basic series
- Premium: All series included
- Enterprise: All series + organizational features

### 3. **Credit-Based**
- Users buy credits in bulk
- Spend credits to unlock premium series
- Combines with existing credit system

### 4. **Individual Purchases**
- Buy permanent access to specific series
- Good for high-value, specialized content

### 5. **Hybrid Model**
- Subscription includes most series
- Special premium series require additional payment
- Organization-wide licenses available

## 🔒 **Security & Compliance**

### Payment Security:
- All payments processed through existing Stripe integration
- No new PCI compliance requirements
- Existing webhook security maintained

### Access Control:
- Server-side validation of all access checks
- No client-side access determination
- Audit trail for all purchases and access grants

## 📋 **Migration Strategy**

### Phase 1: Schema Updates
1. Add series billing fields to plans
2. Add access tiers to series
3. Extend one-time orders for series

### Phase 2: Access Logic
1. Implement subscription-based access
2. Add credit purchase functionality  
3. Create one-time purchase flow

### Phase 3: Integration
1. Update existing policies to include series
2. Extend webhook handlers
3. Add billing analytics

This integration maintains your existing billing patterns while adding powerful monetization options for series content! 