

export const getLabelbgColorCss = (label)=>{

switch (label.toLowerCase()) {
	case "critical":
	case "urgent":
	case "bug":
		return 'bg-[#D32F2F]';
	case "high":
		return 'bg-[#F57C00]';
	case "moderate":
		return 'bg-[#FBC02D]';
	case "low":
		return 'bg-[#1976D2]';

	default:
		return 'bg-base-purple';
}
}

export function desaturateColor(hex, desaturationPercent) {
    // Convert hex to RGB
    let r = parseInt(hex.slice(1, 3), 16);
    let g = parseInt(hex.slice(3, 5), 16);
    let b = parseInt(hex.slice(5, 7), 16);

    // Convert RGB to HSL
    r /= 255;
    g /= 255;
    b /= 255;
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h, s, l = (max + min) / 2;

    if (max === min) {
        h = s = 0; // achromatic
    } else {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch (max) {
            case r: h = (g - b) / d + (g < b ? 6 : 0); break;
            case g: h = (b - r) / d + 2; break;
            case b: h = (r - g) / d + 4; break;
        }
        h /= 6;
    }

    // Desaturate
    s *= (100 - desaturationPercent) / 100;

    // Convert HSL back to RGB
    let q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    let p = 2 * l - q;

    function hue2rgb(p, q, t) {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
    }

    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);

    // Convert RGB back to hex
    r = Math.round(r * 255).toString(16);
    g = Math.round(g * 255).toString(16);
    b = Math.round(b * 255).toString(16);

    return `#${r.padStart(2, '0')}${g.padStart(2, '0')}${b.padStart(2, '0')}`;
}

export function getTicketStatePillColors(state) {
    const colors = {
      'Ready for work':{
        background: '#1181f2',
        label: '#f8f7ff',
      },
        'Smart Requests': {
          background: '#FFB3B3', // soft pastel red
          label: '#C62828', // medium red for contrast
        },
        Triage: {
          background: '#FFCCBC', // pastel orange with a hint of red
          label: '#D84315', // medium red-orange for visibility
        },
        'In Progress': {
          background: '#D1C4E9', // pastel purple with a touch of indigo
          label: '#5E35B1', // medium indigo for good contrast
        },
        Completed: {
          background: '#C8E6C9', // pastel green for a completed state
          label: '#2E7D32', // dark green for readability
        },
      };

      // Return the colors for the given state or default colors if the state is not recognized
      return colors[state] || { background: '#f8f7ff', label: '#000000' };
  }
