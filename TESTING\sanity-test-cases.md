# AJENTIC PLATFORM - SANITY TESTING SHEET
## Pre-Production Testing Checklist

### ENVIRONMENT SETUP
- [ ] Backend API running on correct port (1337)
- [ ] Frontend running on correct port (3000)
- [ ] Database connections established
- [ ] Environment variables loaded (.env file)
- [ ] All required services running (SendGrid, Amplitude, etc.)

---

## 1. AUTHENTICATION & USER MANAGEMENT

### 1.1 User Registration
- [ ] **TC001**: Register with valid email and password
- [ ] **TC002**: Register with invalid email format
- [ ] **TC003**: Register with weak password
- [ ] **TC004**: Register with existing email (should fail)
- [ ] **TC005**: Email confirmation sent successfully
- [ ] **TC006**: Email confirmation link works
- [ ] **TC007**: Google OAuth registration works
- [ ] **TC008**: Username uniqueness validation
- [ ] **TC009**: Organization auto-creation for new users

### 1.2 User Login
- [ ] **TC010**: Login with valid credentials
- [ ] **TC011**: Login with invalid credentials
- [ ] **TC012**: Login with unconfirmed email
- [ ] **TC013**: Login with blocked user account
- [ ] **TC014**: JWT token generation and validation
- [ ] **TC015**: Session persistence across browser refresh
- [ ] **TC016**: Logout functionality

### 1.3 Password Management
- [ ] **TC017**: Forgot password request
- [ ] **TC018**: Password reset email delivery
- [ ] **TC019**: Password reset link functionality
- [ ] **TC020**: Password change with valid old password
- [ ] **TC021**: Password change with invalid old password

---

## 2. ORGANIZATION MANAGEMENT

### 2.1 Organization Creation
- [ ] **TC022**: Auto-create individual org for generic emails
- [ ] **TC023**: Auto-create company org for business emails
- [ ] **TC024**: Organization name extraction from email domain
- [ ] **TC025**: User assignment to organization

### 2.2 Organization Members
- [ ] **TC026**: Add member to organization (valid email)
- [ ] **TC027**: Add member to organization (invalid email)
- [ ] **TC028**: Add existing user as member
- [ ] **TC029**: Remove member from organization
- [ ] **TC030**: View organization member list
- [ ] **TC031**: Member role assignment
- [ ] **TC032**: Member permissions validation

### 2.3 Organization Settings
- [ ] **TC033**: Update organization details
- [ ] **TC034**: Organization subscription status check
- [ ] **TC035**: Usage limits enforcement
- [ ] **TC036**: Billing information management

---

## 3. SUBSCRIPTION & BILLING

### 3.1 Plan Management
- [ ] **TC037**: View available monthly plans
- [ ] **TC038**: View available yearly plans
- [ ] **TC039**: Plan feature comparison display
- [ ] **TC040**: Upgrade to paid plan
- [ ] **TC041**: Downgrade plan (if applicable)
- [ ] **TC042**: Trial period activation
- [ ] **TC043**: Trial expiration handling

### 3.2 Usage Tracking
- [ ] **TC044**: Credits usage tracking
- [ ] **TC045**: Training tokens usage tracking
- [ ] **TC046**: Monthly usage reset
- [ ] **TC047**: Usage limit warnings
- [ ] **TC048**: Usage limit enforcement
- [ ] **TC049**: Overage handling

---

## 4. KNOWLEDGE BASE MANAGEMENT

### 4.1 Knowledge Base Creation
- [ ] **TC050**: Create new knowledge base
- [ ] **TC051**: KB limit enforcement per plan
- [ ] **TC052**: KB naming and description
- [ ] **TC053**: KB settings configuration
- [ ] **TC054**: AI model selection
- [ ] **TC055**: Vector store configuration

### 4.2 Content Management
- [ ] **TC056**: Upload documents to KB
- [ ] **TC057**: URL scraping functionality
- [ ] **TC058**: File format validation
- [ ] **TC059**: Content processing and indexing
- [ ] **TC060**: Document deletion
- [ ] **TC061**: Bulk content operations

### 4.3 KB Configuration
- [ ] **TC062**: Search type configuration (similarity/MMR)
- [ ] **TC063**: Similarity threshold settings
- [ ] **TC064**: K-similarity parameter tuning
- [ ] **TC065**: Persona/tone configuration
- [ ] **TC066**: Prompt prefix customization
- [ ] **TC067**: Lead collection settings

---

## 5. AI CHAT & ANSWERS

### 5.1 Question Answering
- [ ] **TC068**: Ask question to knowledge base
- [ ] **TC069**: Receive accurate answer with sources
- [ ] **TC070**: Handle unanswered questions
- [ ] **TC071**: Session management
- [ ] **TC072**: Answer quality validation
- [ ] **TC073**: Response time performance
- [ ] **TC074**: Multi-language support (if applicable)

### 5.2 Chat Features
- [ ] **TC075**: Chat session creation
- [ ] **TC076**: Chat history persistence
- [ ] **TC077**: Multiple concurrent sessions
- [ ] **TC078**: Session timeout handling
- [ ] **TC079**: Chat export functionality
- [ ] **TC080**: WhatsApp integration (if enabled)

### 5.3 AI Tools & Agents
- [ ] **TC081**: Built-in tools functionality
- [ ] **TC082**: Custom API tools integration
- [ ] **TC083**: AI agent configuration
- [ ] **TC084**: Tool chaining and workflows
- [ ] **TC085**: Assessment generation
- [ ] **TC086**: Code generation features

---

## 6. TRAINING & DATA MANAGEMENT

### 6.1 Training Operations
- [ ] **TC087**: Train knowledge base with new data
- [ ] **TC088**: Training token consumption tracking
- [ ] **TC089**: Training progress monitoring
- [ ] **TC090**: Training failure handling
- [ ] **TC091**: Incremental training
- [ ] **TC092**: Training history and logs

### 6.2 Data Sources
- [ ] **TC093**: File upload training
- [ ] **TC094**: URL-based training
- [ ] **TC095**: Code repository training
- [ ] **TC096**: Database integration training
- [ ] **TC097**: API data source training
- [ ] **TC098**: Data source validation

### 6.3 URL Training Specific
- [ ] **TC177**: Single URL training with valid HTTP/HTTPS URL
- [ ] **TC178**: URL scraping mode - extract multiple links from parent URL
- [ ] **TC179**: YouTube URL training with valid video link
- [ ] **TC180**: URL validation - reject invalid URL formats
- [ ] **TC181**: URL filtering - exclude media files (images, videos)
- [ ] **TC182**: URL filtering - exclude sitemap files
- [ ] **TC183**: Bulk URL training from scraped links
- [ ] **TC184**: URL training with authentication required sites
- [ ] **TC185**: URL training timeout handling for slow sites
- [ ] **TC186**: URL training with redirects handling

---

## 7. INTEGRATIONS & APIs

### 7.1 Email Integration
- [ ] **TC099**: SendGrid email delivery
- [ ] **TC100**: Email template rendering
- [ ] **TC101**: Confirmation emails
- [ ] **TC102**: Notification emails
- [ ] **TC103**: Email bounce handling
- [ ] **TC104**: Unsubscribe functionality

### 7.2 Analytics Integration
- [ ] **TC105**: Amplitude event tracking
- [ ] **TC106**: User signup tracking
- [ ] **TC107**: Usage analytics
- [ ] **TC108**: Error tracking
- [ ] **TC109**: Performance metrics
- [ ] **TC110**: Custom event tracking

### 7.3 External APIs
- [ ] **TC111**: Talkbase API integration
- [ ] **TC112**: API authentication
- [ ] **TC113**: API rate limiting
- [ ] **TC114**: API error handling
- [ ] **TC115**: API timeout handling
- [ ] **TC116**: API response validation

---

## 8. SECURITY & PERMISSIONS

### 8.1 Authentication Security
- [ ] **TC117**: JWT token expiration
- [ ] **TC118**: Token refresh mechanism
- [ ] **TC119**: Bearer token validation
- [ ] **TC120**: Session security
- [ ] **TC121**: Password encryption
- [ ] **TC122**: Rate limiting on auth endpoints

### 8.2 Authorization & Policies
- [ ] **TC123**: User details populate policy
- [ ] **TC124**: Usage validation policy
- [ ] **TC125**: Organization ownership validation
- [ ] **TC126**: API endpoint permissions
- [ ] **TC127**: Resource access control
- [ ] **TC128**: Admin vs user permissions

### 8.3 Data Security
- [ ] **TC129**: Sensitive data encryption
- [ ] **TC130**: API key protection
- [ ] **TC131**: Database security
- [ ] **TC132**: File upload security
- [ ] **TC133**: XSS protection
- [ ] **TC134**: SQL injection prevention

---

## 9. PERFORMANCE & SCALABILITY

### 9.1 Response Times
- [ ] **TC135**: API response time < 2s for standard queries
- [ ] **TC136**: AI answer generation < 30s
- [ ] **TC137**: File upload processing time
- [ ] **TC138**: Database query performance
- [ ] **TC139**: Frontend page load times
- [ ] **TC140**: Large dataset handling

### 9.2 Concurrent Users
- [ ] **TC141**: Multiple users simultaneous access
- [ ] **TC142**: Concurrent chat sessions
- [ ] **TC143**: Concurrent training operations
- [ ] **TC144**: Database connection pooling
- [ ] **TC145**: Memory usage optimization
- [ ] **TC146**: CPU usage monitoring

---

## 10. ERROR HANDLING & RECOVERY

### 10.1 Error Scenarios
- [ ] **TC147**: Network connectivity issues
- [ ] **TC148**: Database connection failures
- [ ] **TC149**: External API failures
- [ ] **TC150**: File corruption handling
- [ ] **TC151**: Invalid input handling
- [ ] **TC152**: Timeout scenarios

### 10.2 Recovery Mechanisms
- [ ] **TC153**: Automatic retry mechanisms
- [ ] **TC154**: Graceful degradation
- [ ] **TC155**: Error logging and monitoring
- [ ] **TC156**: User-friendly error messages
- [ ] **TC157**: System health checks
- [ ] **TC158**: Backup and restore procedures

---

## 11. MOBILE & CROSS-BROWSER COMPATIBILITY

### 11.1 Browser Testing
- [ ] **TC159**: Chrome compatibility
- [ ] **TC160**: Firefox compatibility
- [ ] **TC161**: Safari compatibility
- [ ] **TC162**: Edge compatibility
- [ ] **TC163**: Mobile browser testing
- [ ] **TC164**: Responsive design validation

### 11.2 Device Testing
- [ ] **TC165**: Desktop functionality
- [ ] **TC166**: Tablet functionality
- [ ] **TC167**: Mobile phone functionality
- [ ] **TC168**: Touch interface testing
- [ ] **TC169**: Screen resolution adaptation
- [ ] **TC170**: Offline functionality (if applicable)

---

## 12. DATA MIGRATION & BACKUP

### 12.1 Data Operations
- [ ] **TC171**: Database backup creation
- [ ] **TC172**: Data export functionality
- [ ] **TC173**: Data import validation
- [ ] **TC174**: Migration scripts execution
- [ ] **TC175**: Data integrity checks
- [ ] **TC176**: Rollback procedures

---

## CRITICAL PATH TESTING (Priority 1)
**Must pass before production:**
- TC001, TC010, TC022, TC050, TC068, TC087, TC099, TC117, TC135, TC147, TC177

## HIGH PRIORITY TESTING (Priority 2)
**Should pass before production:**
- TC026, TC037, TC056, TC075, TC105, TC123, TC141, TC159, TC178, TC179

## ENVIRONMENT-SPECIFIC TESTS
### Production Environment
- [ ] SSL certificate validation
- [ ] Domain configuration
- [ ] CDN functionality
- [ ] Load balancer testing
- [ ] Monitoring and alerting
- [ ] Backup systems

### Test Data Requirements
- [ ] Sample organizations created
- [ ] Test user accounts
- [ ] Sample knowledge bases
- [ ] Test documents and content
- [ ] Mock API responses
- [ ] Performance test data

---

## SIGN-OFF CRITERIA
- [ ] All Priority 1 tests pass
- [ ] 95% of Priority 2 tests pass
- [ ] No critical security vulnerabilities
- [ ] Performance benchmarks met
- [ ] Error handling validated
- [ ] Documentation updated
- [ ] Monitoring configured

**Testing Team Sign-off:** _________________ Date: _________
**Development Team Sign-off:** _____________ Date: _________
**Product Owner Sign-off:** ________________ Date: _________