import Typography from "@mui/material/Typography";
import { motion } from "framer-motion";
import { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "app/store";
import {
  getBoards,
  getLoading,
  resetBoards,
  selectBoards,
} from "../store/boardsSlice";
import BoardItem from "./BoardItem";
import LoadingSpinner from "app/shared-components/loading-spinner/loading-spinner";
import EmptyLottie from "app/shared-components/empty-data/EmptyLottie";

export const Boards = () => {
  const dispatch = useAppDispatch();
  const boards = useAppSelector(selectBoards);
  const isLoading = useAppSelector(getLoading);

  useEffect(() => {
    dispatch(getBoards());
    return () => {
      dispatch(resetBoards());
    };
  }, [dispatch]);

  const container = {
    show: {
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (isLoading) {
    return (
      <div className="flex flex-col w-full px-28 sm:px-16 items-center my-48">
        <div className="w-full items-center">
          <LoadingSpinner className="w-56 m-auto" />
        </div>
      </div>
    );
  }

  if (boards.ids.length === 0) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="flex flex-col flex-auto w-full items-center pl-24">
          <EmptyLottie text="No tasks found. Create a task so that it can capture leads, raise tickets, and book appointments." />
        </div>
      </div>
    );
  }

  return (
  <div className="flex flex-col items-center px-8 py-20 sm:px-16 md:px-32 lg:px-48 xl:px-64">
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1, transition: { delay: 0.1 } }}
      className="w-full"
    >
      <div className="w-full text-left mb-12">
        <Typography className="text-12 sm:text-3xl font-bold text-[#060708]">
          Tasks
        </Typography>
        <Typography className="text-sm sm:text-base text-[#424242] mt-4 mb-10">
          See & create your tasks here
        </Typography>
      </div>
    </motion.div>

    <motion.div
      variants={container}
      initial="hidden"
      animate="show"
      className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-12 w-full max-w-7xl"
    >
      {boards.ids?.map((board) => (
        <motion.div variants={item} key={board}>
          <BoardItem board={boards.entities[board]} />
        </motion.div>
      ))}
    </motion.div>
  </div>
);

};
