import React, { useState } from "react";
import PropTypes from "prop-types";
import { X } from "lucide-react";

export default function AddKnowledgebaseName(props) {
  const [kbName, setkbName] = useState("");

  const defaultAgentType = {
    value: "lgAgent",
    subType: "20",
  };

  const handleFormSubmit = (event) => {
    event.preventDefault();
    if (!kbName.trim()) return;
    props.handleSubmit(kbName, defaultAgentType.value, defaultAgentType.subType);
    setkbName("");
  };

  const handleClear = () => {
    setkbName("");
    props.setSuccessMessage?.("");
    props.setFailureMessage?.("");
  };

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 ${props.open ? '' : 'hidden'}`}>
      <form
        onSubmit={handleFormSubmit}
        className="bg-white rounded-2xl shadow-lg w-full max-w-2xl p-10"
      >
        <h2 className="text-2xl font-semibold text-gray-900 mb-6">Create Your Agent</h2>

        <div className="mb-6">
          <label className="block text-gray-800 font-medium mb-2">Name your Agent</label>
          <div className="relative">
            <input
              type="text"
              placeholder="e.g. SupportBot, LeadGenPro"
              value={kbName}
              onChange={(e) => {
                setkbName(e.target.value);
                props.setSuccessMessage?.("");
                props.setFailureMessage?.("");
              }}
              className="w-full border border-gray-300 rounded-xl py-2.5 px-4 pr-10 focus:outline-none focus:ring-2 focus:ring-[#7C53FE]"
            />
            {kbName && (
              <button
                type="button"
                onClick={handleClear}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
              >
                <X size={18} />
              </button>
            )}
          </div>
        </div>

        {props.successMessage && (
          <div className="mb-4 text-green-700 bg-green-100 px-4 py-2 rounded-md">
            {props.successMessage}
          </div>
        )}

        {props.failureMessage && (
          <div className="mb-4 text-red-700 bg-red-100 px-4 py-2 rounded-md">
            {"Could not create Agent: " + props.failureMessage}
          </div>
        )}

        <div className="flex justify-end gap-4 mt-6">
          <button
            type="button"
            onClick={props.handleClose}
            className="border border-[#7C53FE] text-[#7C53FE] font-medium rounded-lg px-5 py-2.5 hover:bg-[#f3efff]"
          >
            Cancel
          </button>

          <button
            type="submit"
            className={`font-medium rounded-lg px-5 py-2.5 text-white transition ${
              kbName.trim()
                ? "bg-[#7C53FE] hover:bg-[#6B47E6]"
                : "bg-black hover:bg-neutral-800"
            }`}
          >
            Create Agent
          </button>
        </div>
      </form>
    </div>
  );
}

AddKnowledgebaseName.propTypes = {
  open: PropTypes.bool.isRequired,
  handleClose: PropTypes.func.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  loading: PropTypes.bool,
  successMessage: PropTypes.string,
  failureMessage: PropTypes.string,
  setSuccessMessage: PropTypes.func,
  setFailureMessage: PropTypes.func,
};
