{"collectionName": "host_personas", "info": {"singularName": "host-persona", "pluralName": "host-personas", "displayName": "Host Person<PERSON>", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"name": {"type": "string", "required": true}, "voice_id": {"type": "string", "required": true}, "target_duration_minutes": {"type": "integer", "default": 5}, "max_sections": {"type": "integer", "default": 3}, "model_name": {"type": "string", "default": "gpt-4o-mini"}, "pov": {"type": "enumeration", "enum": ["FIRST", "SECOND", "THIRD"]}, "traits": {"type": "json"}, "physical_traits": {"type": "json"}, "catch_phrases": {"type": "json"}, "playlist": {"type": "relation", "relation": "oneToOne", "target": "api::playlist.playlist", "inversedBy": "host_persona"}}}