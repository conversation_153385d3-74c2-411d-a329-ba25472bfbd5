
const CryptoJS = require('crypto-js');
module.exports = {
	async beforeCreate(event) {
	  const { data, where, select, populate } = event.params;
	  if (data.key) {
        const encryptedValue = await CryptoJS.AES.encrypt(data.key, process.env.ENCRYPTION_KEY).toString();
        event.params.data.key = encryptedValue;
      }
	},

	// async afterFindOne(event){
	// 	if (event.result.key) {
    //         const decryptedValue =  CryptoJS.AES.decrypt(
	// 			event.result.key,
	// 			process.env.ENCRYPTION_KEY
	// 		  ).toString(CryptoJS.enc.Utf8)
    //         event.result.key = decryptedValue;
    //       }
	//   }
  
  };
