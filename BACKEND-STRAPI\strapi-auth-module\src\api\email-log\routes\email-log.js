'use strict';

/**
 * Email Log Routes
 * API routes for email logging and analytics
 */

module.exports = {
  routes: [
    // Standard CRUD routes
    {
      method: 'GET',
      path: '/email-logs',
      handler: 'email-log.find',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/email-logs/:id',
      handler: 'email-log.findOne',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/email-logs',
      handler: 'email-log.create',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'PUT',
      path: '/email-logs/:id',
      handler: 'email-log.update',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'DELETE',
      path: '/email-logs/:id',
      handler: 'email-log.delete',
      config: {
        policies: [],
        middlewares: [],
      },
    },

    // Custom routes for email analytics
    {
      method: 'GET',
      path: '/email-logs/analytics/organization/:organizationId',
      handler: 'email-log.getOrganizationAnalytics',
      config: {
        policies: [],
        middlewares: [],
        description: 'Get email analytics for an organization'
      },
    },
    {
      method: 'GET',
      path: '/email-logs/status/:messageId',
      handler: 'email-log.getEmailStatus',
      config: {
        policies: [],
        middlewares: [],
        description: 'Get email delivery status by message ID'
      },
    },
    {
      method: 'PUT',
      path: '/email-logs/status/:messageId',
      handler: 'email-log.updateEmailStatus',
      config: {
        policies: [],
        middlewares: [],
        description: 'Update email delivery status'
      },
    },
    {
      method: 'POST',
      path: '/email-logs/cleanup',
      handler: 'email-log.cleanupOldLogs',
      config: {
        policies: [],
        middlewares: [],
        description: 'Clean up old email logs'
      },
    },
    {
      method: 'GET',
      path: '/email-logs/ticket/:ticketId',
      handler: 'email-log.getTicketEmails',
      config: {
        policies: [],
        middlewares: [],
        description: 'Get all emails related to a specific ticket'
      },
    }
  ],
};
