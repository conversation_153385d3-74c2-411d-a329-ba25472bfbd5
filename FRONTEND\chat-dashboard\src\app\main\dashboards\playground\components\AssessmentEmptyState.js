import React from 'react';

/**
 * Empty state component with assessment focus
 */
const AssessmentEmptyState = () => (
  <div className="empty-chat">
    <div className="empty-chat-icon assessment-icon">
      <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round">
        <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"/>
        <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
        <path d="M9 9h.01"/>
        <path d="M15 9h.01"/>
      </svg>
    </div>
    <h2 className="empty-chat-title">Welcome to your Assessment</h2>
    <p className="empty-chat-subtitle">
      I'll be guiding you through this assessment. Please type a message below to begin the conversation.
      The assessment will start after you send your first message.
    </p>
    <div className="assessment-instructions">
      <div className="assessment-instruction-item">
        <div className="instruction-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="8" y1="12" x2="16" y2="12"></line>
            <line x1="12" y1="8" x2="12" y2="16"></line>
          </svg>
        </div>
        <div className="instruction-text">
          <h3>Take Your Time</h3>
          <p>There's no rush. Think through your answers carefully.</p>
        </div>
      </div>
      <div className="assessment-instruction-item">
        <div className="instruction-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
        </div>
        <div className="instruction-text">
          <h3>Be Specific</h3>
          <p>Provide detailed answers to showcase your knowledge.</p>
        </div>
      </div>
      <div className="assessment-instruction-item">
        <div className="instruction-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
        </div>
        <div className="instruction-text">
          <h3>Clear Communication</h3>
          <p>Express your thoughts clearly and logically.</p>
        </div>
      </div>
    </div>
  </div>
);

export default AssessmentEmptyState; 