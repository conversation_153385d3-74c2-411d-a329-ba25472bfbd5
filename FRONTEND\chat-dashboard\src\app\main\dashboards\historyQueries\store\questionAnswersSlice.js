import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { getKb } from "app/store/kbSlice";
import axios from "axios";

export const getQuestionAnswers = createAsyncThunk(
  "historyQueriesDashboardApp/questionAnswers/getQuestionAnswers",
  async (arg, { dispatch, getState }) => {
    await dispatch(getKb());
    const { user, kb } = getState();
    const orgId = user.data.organization.org_id;

    const response = await axios
      .get(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/answers?populate[0]=knowledgebase&filters[org_id][$eq]=${orgId}&publicationState=preview&sort=createdAt:desc&pagination[page]=${arg}`
      )
      .catch((error) => {
        console.log(error);
      });

    return response.data;
  }
);

export const getSessionsAndAnswers = createAsyncThunk(
  "historyQueriesDashboardApp/questionAnswers/getSessionsAndAnswers",
  async (arg, { dispatch, getState }) => {
    await dispatch(getKb());
    const { user, kb } = getState();
    const orgId = user.data.organization.id;

    const response = await axios
      .get(`${process.env.REACT_APP_AUTH_BASE_URL}/api/sessions`, {
        params: {
          "populate[knowledgebase]": true,
          "populate[answers]": true,
          "filters[knowledgebase][organization][id][$eq]": orgId,
          "sort[0]": "createdAt:desc",
          "pagination[page]": arg,
          "pagination[pageSize]": 10, // Adjust the page size as needed
        },
      })
      .catch((error) => {
        console.error("Error fetching sessions:", error);
      });

    return response.data;
  }
);

export const getSessions = createAsyncThunk(
  "historyQueriesDashboardApp/sessionsData/getSessions",
  async (arg, { dispatch, getState }) => {
    await dispatch(getKb());
    const orgId = user.data.organization.id;

    const response = await axios
      .get(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/sessions?filters[knowledgebase][organization][id][$eq]=${orgId}`
      )
      .catch((error) => {
        console.error("Error fetching sessions:", error);
      });

    return response.data;
  }
);

const initialState = { data: [], meta: {} };

const questionAnswersSlice = createSlice({
  name: "historyQueriesDashboardApp/questionAnswers",
  initialState,
  reducers: {},
  extraReducers: {
    [getQuestionAnswers.fulfilled]: (state, action) => action.payload,
    [getSessions.fulfilled]: (_, action) => action.payload,
  },
});

export const selectQuestionAnswers = ({ historyQueriesDashboardApp }) =>
  historyQueriesDashboardApp.questionAnswers;

export const selectSessions = ({ historyQueriesDashboardApp }) =>
  historyQueriesDashboardApp.sessionsData;

export default questionAnswersSlice.reducer;
