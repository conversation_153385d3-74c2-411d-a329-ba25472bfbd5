import React from "react";
import BaseDialog from "app/shared-components/dialog/base-dialog";
import CTAButon from "../buttons/cta-button";

export default function ConfirmModal({
  title,
  message,
  open,
  handleClose,
  onConfirm,
  onCancel,
}) {
  return (
    <BaseDialog
      open={open}
      handleClose={handleClose}
      title={title}
      className="w-[40%]"
    >
      <div className="p-24 flex flex-start">
        <div className="flex flex-start flex-col items-center gap-4">
          <p className="text-black text-lg">{message}</p>
          <div className="flex flex-end mt-16 w-full  gap-16">
            <CTAButon
              onPress={(event) => {

                onConfirm();
              }}
              text="Confirm"
            />
            <CTAButon onPress={onCancel} text="Cancel" />
          </div>
        </div>
      </div>
    </BaseDialog>
  );
}
