/**
 * Modern Email Service
 * Uses the new email connector system while maintaining backward compatibility
 * This service acts as a bridge between the old email service and the new provider system
 */

const { emailProviderFactory } = require('../providers/email-provider-factory');

module.exports = {
  /**
   * Send email using the modern email connector system
   * Maintains backward compatibility with the old sendEmail function
   */
  async sendEmail({
    emails, 
    name, 
    subject, 
    title, 
    description, 
    templateId, 
    agent_name, 
    customer_email, 
    ticket_url,
    provider = null,
    from = null,
    text = null,
    html = null,
    templateData = {}
  }) {
    try {
      // Normalize emails to array format
      const recipients = Array.isArray(emails) ? emails : [emails];
      
      // Prepare email data
      const emailData = {
        to: recipients,
        subject: subject,
        from: from,
        text: text,
        html: html,
        templateId: templateId,
        templateData: {
          // Backward compatibility mappings
          sendername: name,
          agent_name: agent_name,
          customer_email: customer_email,
          ticket_title: title,
          ticket_description: description,
          ticket_url: ticket_url,
          confirmation_link: ticket_url,
          reset_url: ticket_url,
          email_subject: subject || 'New Account verification - Podycy',
          // Merge any additional template data
          ...templateData
        }
      };

      // Send email using the provider factory
      const result = await emailProviderFactory.sendEmail(emailData, provider);
      
      return result;
    } catch (error) {
      strapi.log.error('❌ Modern email service error:', error);
      throw error;
    }
  },

  /**
   * Send bulk emails using the modern system
   */
  async sendBulkEmails(emails, provider = null) {
    try {
      const result = await emailProviderFactory.sendBulkEmails(emails, provider);
      return result;
    } catch (error) {
      strapi.log.error('❌ Bulk email service error:', error);
      throw error;
    }
  },

  /**
   * Send template email by name
   */
  async sendTemplateEmail({
    templateName,
    to,
    templateData = {},
    provider = null,
    organizationId = null
  }) {
    try {
      // Find the template
      const template = await strapi.query('api::email-template.email-template').findOne({
        where: { 
          name: templateName, 
          is_active: true,
          ...(organizationId && { organization: organizationId })
        }
      });

      if (!template) {
        throw new Error(`Email template '${templateName}' not found or inactive`);
      }

      // Prepare email data using template
      const emailData = {
        to,
        templateId: template.template_id,
        templateData: {
          ...template.variables,
          ...templateData
        },
        from: template.default_from_email,
        subject: template.subject
      };

      // Send email using the template's provider or specified provider
      const templateProvider = provider || template.provider;
      const result = await emailProviderFactory.sendEmail(emailData, templateProvider);

      // Update template usage
      if (result.success) {
        await strapi.query('api::email-template.email-template').update({
          where: { id: template.id },
          data: {
            usage_count: template.usage_count + 1,
            last_used: new Date()
          }
        });
      }

      return {
        ...result,
        template: {
          id: template.id,
          name: template.name,
          provider: templateProvider
        }
      };
    } catch (error) {
      strapi.log.error('❌ Template email service error:', error);
      throw error;
    }
  },

  /**
   * Get available email providers
   */
  getAvailableProviders() {
    try {
      return emailProviderFactory.getAvailableProviders();
    } catch (error) {
      strapi.log.error('❌ Get providers error:', error);
      return [];
    }
  },

  /**
   * Test email providers
   */
  async testProviders(provider = null) {
    try {
      return await emailProviderFactory.testProviders(provider);
    } catch (error) {
      strapi.log.error('❌ Test providers error:', error);
      throw error;
    }
  },

  /**
   * Get delivery status for an email
   */
  async getDeliveryStatus(messageId, provider = null) {
    try {
      const providerInstance = emailProviderFactory.getProvider(provider);
      return await providerInstance.getDeliveryStatus(messageId);
    } catch (error) {
      strapi.log.error('❌ Get delivery status error:', error);
      throw error;
    }
  },

  /**
   * Send ticket notification email using template
   */
  async sendTicketNotificationEmail({ emails, name, title, description, agent_name, customer_email, ticket_url, provider = 'postmark' }) {
    try {
      return await this.sendTemplateEmail({
        templateName: 'ticket-notification',
        to: emails,
        templateData: {
          name,
          title,
          description,
          agent_name,
          customer_email,
          ticket_url
        },
        provider
      });
    } catch (error) {
      strapi.log.error('❌ Ticket notification email error:', error);
      throw error;
    }
  }
};