'use strict';

/**
 * file-train controller
 */

const { create<PERSON>oreController } = require('@strapi/strapi').factories;
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');


// module.exports = createCoreController('api::file-train.file-train');
module.exports = createCoreController('api::file-train.file-train', ({ strapi }) =>  ({
	async create(ctx) {

		let status = true;
		let data;
		const form = new FormData();

		const file = ctx.request.files['files.file'];
		console.log("Fies" , file.name)
		const buffer = fs.readFileSync(file.path);
		form.append('file', buffer, {
			filename: file.name,
		});
		form.append('force_train', 'true');
		form.append('kb_id', ctx.request.body["kb_id"]);
		form.append('org_id',ctx.state.user.organization?.org_id);
		form.append('remaining_quota', (ctx.state.user.organization.plan.allowed_training_tokens-ctx.state.user.organization?.current_month_usage?.training_tokens_count));
		form.append('usage_id', ctx.state.user.organization.current_month_usage.id);
		console.log("Form Data" , form)
		let datasource;
      	var didCreateNewDatasource = false;
      	datasource = await strapi.query("api::datasource.datasource").findOne({
        	where: { knowledgebase: ctx.request.body.kbr_id },
      	});
      	if (datasource === null) {
        	datasource = await strapi.query("api::datasource.datasource").create({
          	data: {
            	name: "Datasource #1",
            	type: "vectorstore",
            	knowledgebase: ctx.request.body.kbr_id,
          	},
        });
        didCreateNewDatasource = true;
      	}

		let document_model = await strapi
			.query("api::document.document")
			.create({
			data: {
				document_name: file.name,
				document_type: "pdf",
				datasource: datasource.id,
				status: "processing",
			},
		});

		form.append('document_id', document_model.id);


		try {
			const result = await axios.post(
			`${process.env.TALKBASE_BASE_URL}/v4/filetrain`,
			form,
			{
				headers: {
					...form.getHeaders()
				},
				// data: form.getBuffer(),
			}
			).catch(err=>{
				console.log(err);
			});
			data = result.data;
			if (data?.result === 'False') {
				status = false;
			}
			await strapi.query('api::monthly-usage.monthly-usage').update({
				where: { id: ctx.state.user.organization.current_month_usage.id },
				data: {
					trained_count: ctx.state.user.organization.current_month_usage.trained_count+1,

				  },
			  });
			if (!ctx.request.body) {
				ctx.request.body = {};
			}


			return {
				py_result: result.data,
				document: document_model,
				did_create_datasource: didCreateNewDatasource,
				...(didCreateNewDatasource
				  ? {
					  datasource: {
						id: datasource.id,
						name: datasource.name,
						type: datasource.type,
						knowledgebase: datasource.knowledgebase,
					  },
					}
				  : {}),
			  };
		} catch (error) {
			console.log(error);
			ctx.badRequest(error);
		}

	  }
}));

