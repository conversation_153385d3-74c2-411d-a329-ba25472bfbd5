function formatNumber({ num }) {
  if (num >= 1000000) {
    const million = (num / 1000000).toFixed(num % 1 === 0 ? 0 : 1);
    return million.endsWith('.0') ? `${million.slice(0, -2)}m` : `${million}m`;
  } else if (num >= 1000) {
    const thousand = (num / 1000).toFixed(num % 1 === 0 ? 0 : 1);
    return thousand.endsWith('.0') ? `${thousand.slice(0, -2)}k` : `${thousand}k`;
  } else {
    return num.toString();
  }
}

export default formatNumber;
