{"kind": "collectionType", "collectionName": "scrumboards", "info": {"singularName": "scrumboard", "pluralName": "scrumboards", "displayName": "scrumboard", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "description": {"type": "text"}, "icon": {"type": "string", "default": "heroicons-outline:template"}, "lastActivity": {"type": "datetime"}, "members": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user", "inversedBy": "scrumboards"}, "settings": {"type": "json", "default": {"subscribed": true, "cardCoverImages": true}}, "scrumboard_cards": {"type": "relation", "relation": "oneToMany", "target": "api::scrumboard-card.scrumboard-card", "mappedBy": "scrumboard"}, "labels": {"type": "relation", "relation": "manyToMany", "target": "api::label.label", "mappedBy": "board_ids"}, "lists": {"type": "relation", "relation": "oneToMany", "target": "api::scrumboard-list.scrumboard-list", "mappedBy": "boardId"}, "knowledgebases": {"type": "relation", "relation": "oneToMany", "target": "api::knowledgebase.knowledgebase", "mappedBy": "scrumboard"}, "scrumboard_automations": {"type": "relation", "relation": "oneToMany", "target": "api::scrumboard-automation.scrumboard-automation", "mappedBy": "scrumboard"}}}