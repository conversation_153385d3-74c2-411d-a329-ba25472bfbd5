/* Container for all plans */
.plans-container {
	position: relative;
	margin-top: 0px;
	margin-bottom: 50px;
    align-items: center;
	justify-content: center;
	place-content: center;
	border-radius: 12px; /* Rounded corners */
	box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.08); /* Directional shadow towards the bottom */
	transition: all 0.3s ease; /* Smooth transition for micro-interactions */
}

/* Individual Plan Card */
.plan-card {
	padding: 25px;
	border-radius: 12px; /* Rounded corners */
	box-shadow: 0px 6px 15px rgba(0, 0, 0, 0.06); /* Directional shadow towards the bottom */
	transition: all 0.3s ease; /* Smooth transition for micro-interactions */
}



.plan-price-label {
	font-weight: medium;
	text-align: start;
	font-size: 40px;
}

.plan-currency-label {
	font-weight: medium;
	text-align: start;
	font-size: 40px;
}

.plan-button{
	align-items: center;
	border-radius: 8px;
}

/* Plan Description */
.plan-description {
	color: #535353;
	max-lines: 2 !important;
	max-width: 100% !important;
	text-overflow: ellipsis !important;
}

.plan-text{
	font-size: 12px;

}

/* Media Query for smaller screens */
@media (max-width: 576px) {
	.plans-container {
		margin-top: -60px;
	}
}

/* Micro-interactions: Hover effect for the plan card */
.plan-card:hover {
	transform: scale(1.05); /* Slightly enlarge the card */
	box-shadow: 0px 8px 18px rgba(0, 0, 0, 0.1); /* Deeper shadow on hover */
}

/* Micro-interactions: Hover effect for the plans container */
.plans-container:hover {
	box-shadow: 0px 10px 24px rgba(0, 0, 0, 0.1); /* Deeper shadow on hover */
}
