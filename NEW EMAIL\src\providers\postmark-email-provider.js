const EmailProviderInterface = require('./email-provider-interface');
const postmark = require('postmark');

/**
 * Postmark Email Provider Implementation
 * Implements the EmailProviderInterface for Postmark service
 */
class PostmarkEmailProvider extends EmailProviderInterface {
  constructor(config = {}) {
    super(config);
    this.apiKey = config.apiKey || process.env.POSTMARK_API_KEY;
    this.defaultFrom = config.defaultFrom || process.env.POSTMARK_FROM_EMAIL || '<EMAIL>';
    this.defaultFromName = config.defaultFromName || process.env.POSTMARK_FROM_NAME || 'Podycy Team';
    this.initialized = false;
    this.client = null;
  }

  /**
   * Initialize Postmark with API key
   */
  async initialize(config = {}) {
    try {
      const apiKey = config.apiKey || this.apiKey;
      
      if (!apiKey) {
        throw new Error('Postmark API key is required');
      }

      this.client = new postmark.ServerClient(apiKey);
      this.initialized = true;
      
      // Test the connection (skip for accounts under review)
      try {
        const connectionTest = await this.testConnection();
        if (!connectionTest) {
          strapi.log.warn('⚠️ Postmark connection test failed - account may be under review');
        }
      } catch (error) {
        if (error.message.includes('review') || error.message.includes('pending') || error.message.includes('disabled')) {
          strapi.log.warn('⚠️ Postmark account under review - provider registered but not functional yet');
        } else {
          throw error; // Re-throw if it's a different error
        }
      }

      strapi.log.info('✅ Postmark Email Provider initialized successfully');
      return { success: true, provider: 'Postmark' };
    } catch (error) {
      strapi.log.error('❌ Postmark initialization failed:', error.message);
      throw error;
    }
  }

  /**
   * Send a single email using Postmark
   */
  async sendEmail(emailData) {
    try {
      if (!this.initialized) {
        throw new Error('Postmark provider not initialized');
      }

      const {
        to,
        from,
        subject,
        text,
        html,
        templateId,
        templateData = {},
        attachments = [],
        replyTo,
        cc,
        bcc,
        headers = {}
      } = emailData;

      // Prepare base email message
      const message = {
        From: from || this.defaultFrom,
        To: Array.isArray(to) ? to.join(',') : to,
        ...(replyTo && { ReplyTo: replyTo }),
        ...(cc && { Cc: Array.isArray(cc) ? cc.join(',') : cc }),
        ...(bcc && { Bcc: Array.isArray(bcc) ? bcc.join(',') : bcc })
      };

      // Handle template-based emails
      if (templateId) {
        message.TemplateAlias = templateId; // or TemplateId if using numeric IDs
        message.TemplateModel = templateData;
        // Don't set Subject, HtmlBody, or TextBody for template emails
        // as the template defines these
      } else {
        // Handle regular content emails
        message.Subject = subject;
        if (html) message.HtmlBody = html;
        if (text) message.TextBody = text;
        
        // Require either HTML or text content for non-template emails
        if (!html && !text) {
          throw new Error("Either 'html' or 'text' content is required for non-template emails");
        }
      }

      // Handle attachments
      if (attachments && attachments.length > 0) {
        message.Attachments = attachments.map(attachment => ({
          Name: attachment.filename || attachment.name,
          Content: attachment.content,
          ContentType: attachment.contentType || attachment.type || 'application/octet-stream',
          ...(attachment.cid && { ContentID: `cid:${attachment.cid}` })
        }));
      }

      // Add custom headers
      if (Object.keys(headers).length > 0) {
        message.Headers = Object.entries(headers).map(([name, value]) => ({
          Name: name,
          Value: value
        }));
      }

      const result = await this.client.sendEmail(message);

      strapi.log.info('✅ Postmark email sent successfully:', {
        messageId: result.MessageID,
        to: result.To,
        subject: result.Subject || subject
      });

      return {
        success: true,
        messageId: result.MessageID,
        provider: 'postmark',
        providerResponse: result,
        to: Array.isArray(to) ? to : [to],
        from: from || this.defaultFrom,
        subject
      };

    } catch (error) {
      strapi.log.error('❌ Postmark email send failed:', error);
      
      return {
        success: false,
        error: error.message,
        provider: 'postmark',
        to: Array.isArray(emailData.to) ? emailData.to : [emailData.to],
        from: emailData.from || this.defaultFrom,
        subject: emailData.subject
      };
    }
  }

  /**
   * Send bulk emails using Postmark batch API
   */
  async sendBulkEmails(emails) {
    try {
      if (!this.initialized) {
        throw new Error('Postmark provider not initialized');
      }

      if (!Array.isArray(emails) || emails.length === 0) {
        throw new Error('Emails array is required and cannot be empty');
      }

      const messages = emails.map(email => {
        const {
          to,
          from,
          subject,
          text,
          html,
          templateId,
          templateData = {},
          attachments = [],
          replyTo,
          cc,
          bcc,
          headers = {}
        } = email;

        const message = {
          From: from || this.defaultFrom,
          To: Array.isArray(to) ? to.join(',') : to,
          Subject: subject,
          ...(replyTo && { ReplyTo: replyTo }),
          ...(cc && { Cc: Array.isArray(cc) ? cc.join(',') : cc }),
          ...(bcc && { Bcc: Array.isArray(bcc) ? bcc.join(',') : bcc })
        };

        // Handle template-based emails
        if (templateId) {
          message.TemplateAlias = templateId;
          message.TemplateModel = templateData;
        } else {
          if (html) message.HtmlBody = html;
          if (text) message.TextBody = text;
        }

        // Handle attachments
        if (attachments && attachments.length > 0) {
          message.Attachments = attachments.map(attachment => ({
            Name: attachment.filename || attachment.name,
            Content: attachment.content,
            ContentType: attachment.contentType || attachment.type || 'application/octet-stream',
            ...(attachment.cid && { ContentID: `cid:${attachment.cid}` })
          }));
        }

        // Add custom headers
        if (Object.keys(headers).length > 0) {
          message.Headers = Object.entries(headers).map(([name, value]) => ({
            Name: name,
            Value: value
          }));
        }

        return message;
      });

      const results = await this.client.sendEmailBatch(messages);

      const successCount = results.filter(result => result.ErrorCode === 0).length;
      const failureCount = results.length - successCount;

      strapi.log.info(`✅ Postmark bulk email completed: ${successCount} sent, ${failureCount} failed`);

      return {
        success: true,
        provider: 'postmark',
        totalEmails: emails.length,
        successCount,
        failureCount,
        results: results.map((result, index) => ({
          success: result.ErrorCode === 0,
          messageId: result.MessageID,
          to: emails[index].to,
          error: result.ErrorCode !== 0 ? result.Message : null,
          providerResponse: result
        }))
      };

    } catch (error) {
      strapi.log.error('❌ Postmark bulk email send failed:', error.message);
      
      return {
        success: false,
        error: error.message,
        provider: 'postmark',
        totalEmails: emails.length,
        successCount: 0,
        failureCount: emails.length
      };
    }
  }

  /**
   * Get email delivery status from Postmark
   */
  async getDeliveryStatus(messageId) {
    try {
      if (!this.initialized) {
        throw new Error('Postmark provider not initialized');
      }

      if (!messageId) {
        throw new Error('Message ID is required');
      }

      // Get outbound message details
      const messageDetails = await this.client.getOutboundMessageDetails(messageId);

      // Get message events (opens, clicks, etc.)
      const events = await this.client.getOutboundMessageClicks(messageId)
        .catch(() => []) // Clicks might not exist
        .then(clicks => clicks || []);

      const opens = await this.client.getOutboundMessageOpens(messageId)
        .catch(() => []) // Opens might not exist
        .then(opens => opens.Opens || []);

      const status = this.mapPostmarkStatus(messageDetails.Status);

      return {
        success: true,
        provider: 'postmark',
        messageId,
        status,
        deliveredAt: messageDetails.ReceivedAt ? new Date(messageDetails.ReceivedAt) : null,
        openedAt: opens.length > 0 ? new Date(opens[0].FirstOpen) : null,
        clickedAt: events.length > 0 ? new Date(events[0].ReceivedAt) : null,
        bounced: status === 'bounced',
        opens: opens.length,
        clicks: events.length,
        details: {
          to: messageDetails.To,
          from: messageDetails.From,
          subject: messageDetails.Subject,
          tag: messageDetails.Tag,
          messageStream: messageDetails.MessageStream,
          sentAt: new Date(messageDetails.SentAt),
          receivedAt: messageDetails.ReceivedAt ? new Date(messageDetails.ReceivedAt) : null
        },
        providerResponse: {
          messageDetails,
          opens,
          clicks: events
        }
      };

    } catch (error) {
      strapi.log.error('❌ Postmark delivery status check failed:', error.message);
      
      return {
        success: false,
        error: error.message,
        provider: 'postmark',
        messageId
      };
    }
  }

  /**
   * Validate Postmark template
   */
  async validateTemplate(templateId) {
    try {
      if (!this.initialized) {
        throw new Error('Postmark provider not initialized');
      }

      if (!templateId) {
        return false;
      }

      // Try to get template details
      // Check if templateId is numeric (template ID) or string (template alias)
      const isNumeric = /^\d+$/.test(templateId);
      
      let template;
      if (isNumeric) {
        template = await this.client.getTemplate(parseInt(templateId));
      } else {
        // For alias, we need to get all templates and find by alias
        const templates = await this.client.getTemplates();
        template = templates.Templates.find(t => t.Alias === templateId);
      }

      return template && template.Active === true;

    } catch (error) {
      strapi.log.warn('Postmark template validation failed:', error.message);
      return false;
    }
  }

  /**
   * Get provider name
   */
  getProviderName() {
    return 'Postmark';
  }

  /**
   * Test connection to Postmark
   */
  async testConnection() {
    try {
      if (!this.initialized || !this.client) {
        return false;
      }

      // Test by getting account info
      const account = await this.client.getAccount();
      return account && account.Name;

    } catch (error) {
      strapi.log.error('Postmark connection test failed:', error.message);
      return false;
    }
  }

  /**
   * Map Postmark status to standardized status
   * @private
   */
  mapPostmarkStatus(postmarkStatus) {
    const statusMap = {
      'Sent': 'sent',
      'Delivered': 'delivered',
      'Bounced': 'bounced',
      'Opened': 'opened',
      'Clicked': 'clicked',
      'SpamComplaint': 'spam',
      'Blocked': 'blocked'
    };

    return statusMap[postmarkStatus] || 'unknown';
  }
}

module.exports = PostmarkEmailProvider; 