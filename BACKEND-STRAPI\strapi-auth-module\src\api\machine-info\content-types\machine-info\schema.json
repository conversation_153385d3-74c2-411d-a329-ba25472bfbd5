{"kind": "collectionType", "collectionName": "machine_infos", "info": {"singularName": "machine-info", "pluralName": "machine-infos", "displayName": "Machine Info", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"userAgent": {"type": "text"}, "language": {"type": "string"}, "hardwareConcurrency": {"type": "integer"}, "deviceMemory": {"type": "float"}, "colorDepth": {"type": "integer"}, "width": {"type": "integer"}, "height": {"type": "integer"}, "machineId": {"type": "string"}, "session": {"type": "relation", "relation": "oneToOne", "target": "api::session.session", "inversedBy": "machineInfo"}}}