"use strict";

const { createCoreController } = require("@strapi/strapi").factories;

module.exports = createCoreController(
  "api::bookmark.bookmark",
  ({ strapi }) => ({
    /**
     * Create a bookmark for the logged-in user
     */
    async create(ctx) {
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized("You must be logged in to bookmark tracks.");
      }

      const { file_results_models, notes } = ctx.request.body.data;

      if (!file_results_models) {
        return ctx.badRequest("Missing required field: file_results_models.");
      }

      // Check if this bookmark already exists for this user and track
      const existing = await strapi.db.query("api::bookmark.bookmark").findOne({
        where: {
          user: user.id,
          file_results_models: file_results_models,
        },
      });

      if (existing) {
        return ctx.conflict("Track is already bookmarked.");
      }

      // Create the bookmark
      const bookmark = await strapi.entityService.create(
        "api::bookmark.bookmark",
        {
          data: {
            user: user.id,
            file_results_models,
            notes,
          },
          populate: ["file_results_models"],
        }
      );

      return { data: bookmark };
    },

    /**
     * Find all bookmarks for the logged-in user
     */
    async find(ctx) {
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized("You must be logged in to view bookmarks.");
      }

      const bookmarks = await strapi.entityService.findMany(
        "api::bookmark.bookmark",
        {
          filters: { user: user.id },
          populate: ["file_results_models"],
        }
      );

      return { data: bookmarks };
    },

    /**
     * Find a single bookmark by ID for the logged-in user
     */
    async findOne(ctx) {
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized("You must be logged in.");
      }

      const { id } = ctx.params;

      const bookmark = await strapi.entityService.findOne(
        "api::bookmark.bookmark",
        id,
        {
          populate: ["user", "file_results_models"], // Populate user to check ownership and file_results_models for details
        }
      );

      if (!bookmark) {
        return ctx.notFound("Bookmark not found.");
      }

      if (bookmark.user?.id !== user.id) {
        return ctx.forbidden("You are not allowed to view this bookmark.");
      }

      return { data: bookmark };
    },

    /**
     * Delete a bookmark by ID, only if owned by the user
     */
    async delete(ctx) {
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized("You must be logged in.");
      }

      const { id } = ctx.params;

      // Find the bookmark to check ownership
      const bookmark = await strapi.entityService.findOne(
        "api::bookmark.bookmark",
        id,
        {
          populate: ["user"],
        }
      );

      if (!bookmark) {
        return ctx.notFound("Bookmark not found.");
      }

      if (bookmark.user?.id !== user.id) {
        return ctx.forbidden("You are not allowed to delete this bookmark.");
      }

      await strapi.entityService.delete("api::bookmark.bookmark", id);

      return { data: { id } };
    },
  })
);
