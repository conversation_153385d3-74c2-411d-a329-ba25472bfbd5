const EmailProviderInterface = require('./email-provider-interface');
const formData = require('form-data');
const Mailgun = require('mailgun.js');

/**
 * Mailgun Email Provider Implementation
 * Implements the EmailProviderInterface for Mailgun service
 */
class MailgunEmailProvider extends EmailProviderInterface {
  constructor(config = {}) {
    super(config);
    this.apiKey = config.apiKey || process.env.MAILGUN_API_KEY;
    this.domain = config.domain || process.env.MAILGUN_DOMAIN;
    this.defaultFrom = config.defaultFrom || process.env.MAILGUN_FROM_EMAIL || '<EMAIL>';
    this.defaultFromName = config.defaultFromName || 'Your App';
    this.initialized = false;
    this.mailgun = null;
    this.mg = null;
  }

  /**
   * Initialize Mailgun with API key and domain
   */
  async initialize(config = {}) {
    try {
      const apiKey = config.apiKey || this.apiKey;
      const domain = config.domain || this.domain;
      
      if (!apiKey) {
        throw new Error('Mailgun API key is required');
      }

      if (!domain) {
        throw new Error('Mailgun domain is required');
      }

      this.mailgun = new Mailgun(formData);
      this.mg = this.mailgun.client({
        username: 'api',
        key: apiKey
      });

      this.initialized = true;
      
      // Test the connection
      const connectionTest = await this.testConnection();
      if (!connectionTest) {
        throw new Error('Failed to connect to Mailgun');
      }

      strapi.log.info('✅ Mailgun Email Provider initialized successfully');
      return { success: true, provider: 'Mailgun' };
    } catch (error) {
      strapi.log.error('❌ Mailgun initialization failed:', error.message);
      throw error;
    }
  }

  /**
   * Send a single email using Mailgun
   */
  async sendEmail(emailData) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const {
        to,
        from,
        subject,
        text,
        html,
        templateId,
        templateData = {},
        attachments = []
      } = emailData;

      // Validate required fields
      if (!to) {
        throw new Error('Recipient email is required');
      }

      if (!subject && !templateId) {
        throw new Error('Subject is required when not using a template');
      }

      // Prepare the message
      const messageData = {
        from: from || `${this.defaultFromName} <${this.defaultFrom}>`,
        to: Array.isArray(to) ? to.join(', ') : to,
        subject: subject
      };

      // Handle template-based emails
      if (templateId) {
        messageData.template = templateId;
        
        // Add template variables
        Object.keys(templateData).forEach(key => {
          messageData[`v:${key}`] = templateData[key];
        });
        
        // Add common variables
        messageData['v:email_subject'] = subject;
        messageData['v:sender_name'] = this.defaultFromName;
      } else {
        // Regular email content
        if (text) messageData.text = text;
        if (html) messageData.html = html;
      }

      // Handle attachments
      if (attachments && attachments.length > 0) {
        messageData.attachment = attachments;
      }

      const response = await this.mg.messages.create(this.domain, messageData);
      
      strapi.log.info(`✅ Email sent successfully via Mailgun to: ${Array.isArray(to) ? to.join(', ') : to}`);
      
      return {
        success: true,
        messageId: response.id,
        provider: 'Mailgun',
        message: response.message,
        recipients: Array.isArray(to) ? to : [to]
      };

    } catch (error) {
      strapi.log.error('❌ Mailgun send error:', error.message);
      
      // Extract useful error information
      let errorDetails = {
        success: false,
        provider: 'Mailgun',
        error: error.message
      };

      if (error.response && error.response.body) {
        errorDetails.details = error.response.body;
      }

      throw errorDetails;
    }
  }

  /**
   * Send bulk emails using Mailgun
   */
  async sendBulkEmails(emails) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const results = [];
      const batchSize = 1000; // Mailgun batch limit
      
      for (let i = 0; i < emails.length; i += batchSize) {
        const batch = emails.slice(i, i + batchSize);
        const batchPromises = batch.map(emailData => 
          this.sendEmail(emailData).catch(error => ({
            success: false,
            error: error.message || error,
            emailData
          }))
        );
        
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      }

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      strapi.log.info(`📊 Bulk email completed: ${successCount} sent, ${failureCount} failed`);

      return {
        success: true,
        totalEmails: emails.length,
        successCount,
        failureCount,
        results,
        provider: 'Mailgun'
      };

    } catch (error) {
      strapi.log.error('❌ Mailgun bulk send error:', error.message);
      throw error;
    }
  }

  /**
   * Get email delivery status from Mailgun
   */
  async getDeliveryStatus(messageId) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      // Get events for this message
      const events = await this.mg.events.get(this.domain, {
        'message-id': messageId
      });

      if (!events.items || events.items.length === 0) {
        return {
          success: true,
          messageId,
          status: 'unknown',
          provider: 'Mailgun'
        };
      }

      // Get the latest event
      const latestEvent = events.items[0];
      let status = 'unknown';

      switch (latestEvent.event) {
        case 'delivered':
          status = 'delivered';
          break;
        case 'opened':
          status = 'opened';
          break;
        case 'clicked':
          status = 'clicked';
          break;
        case 'bounced':
          status = 'bounced';
          break;
        case 'failed':
          status = 'failed';
          break;
        case 'accepted':
          status = 'sent';
          break;
        default:
          status = latestEvent.event;
      }

      return {
        success: true,
        messageId,
        status,
        provider: 'Mailgun',
        deliveredAt: latestEvent.event === 'delivered' ? new Date(latestEvent.timestamp * 1000) : null,
        openedAt: latestEvent.event === 'opened' ? new Date(latestEvent.timestamp * 1000) : null,
        clickedAt: latestEvent.event === 'clicked' ? new Date(latestEvent.timestamp * 1000) : null,
        eventData: latestEvent
      };

    } catch (error) {
      strapi.log.error('❌ Mailgun status check error:', error.message);
      throw error;
    }
  }

  /**
   * Validate Mailgun template
   */
  async validateTemplate(templateId) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      // Get template info from Mailgun
      const template = await this.mg.templates.get(this.domain, templateId);
      
      return template && template.name === templateId;
    } catch (error) {
      strapi.log.error('❌ Mailgun template validation error:', error.message);
      return false;
    }
  }

  /**
   * Get provider name
   */
  getProviderName() {
    return 'Mailgun';
  }

  /**
   * Test Mailgun connection
   */
  async testConnection() {
    try {
      if (!this.mg) {
        return false;
      }

      // Test by getting domain info
      const domainInfo = await this.mg.domains.get(this.domain);
      return domainInfo && domainInfo.name === this.domain;
    } catch (error) {
      strapi.log.error('❌ Mailgun connection test failed:', error.message);
      return false;
    }
  }
}

module.exports = MailgunEmailProvider; 