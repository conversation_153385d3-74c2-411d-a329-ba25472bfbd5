{"kind": "collectionType", "collectionName": "scrumboard_comments", "info": {"singularName": "scrumboard-comment", "pluralName": "scrumboard-comments", "displayName": "scrumboard_comment"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"type": {"type": "enumeration", "enum": ["comment"], "default": "comment"}, "message": {"type": "string"}, "time": {"type": "datetime"}, "scrumboard_card": {"type": "relation", "relation": "manyToOne", "target": "api::scrumboard-card.scrumboard-card", "inversedBy": "activities"}}}