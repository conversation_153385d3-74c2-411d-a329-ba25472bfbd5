import React from "react";
import CodeSyntaxHighlighter from 'app/shared-components/code-syntax-highlighter/CodeSyntaxHighlighter';
import Typography from '@mui/material/Typography';
import MetaLinkCard from '../MetaLinkCard/MetaLinkCard';
import './answers.css'
import FuseSvgIcon from "@fuse/core/FuseSvgIcon/FuseSvgIcon";
import clipboardCopy from "clipboard-copy";
import { showMessage } from "app/store/fuse/messageSlice";
import { useDispatch } from "react-redux";

function AnswerSourcesContainer({ answer, isLoading, waitingTime, linksArray, linkMetaArray, docArray, isFetchingMeta, queryId }) {
  const answerAsString = answer ? (answer.replace(/(<table)/g, '\n$1')) : 'No answer found';
  const dispatch = useDispatch();
  return (
    <div>
      <div className="flex flex-row">
        <Typography className="mb-2 mt-14 ml-4" variant="h6">Response </Typography>
       
      </div>
      <div className="answer-sources-conatiner">
        <div className="answer-sources">
          {isLoading && (
            <div className="info-tip flex gap-10">
              <img className="w-40" src="assets/images/logo/ajentic-logo.png" alt="logo" />
              <span>Thinking.... This task typically takes between <b>{waitingTime}</b>. Do not refresh or navigate away from this page</span>
            </div>
          )}
          <div className="fieldset">
            {isLoading ? (
              <div className="loading-div">
                <div className="line"></div>
                <div className="line"></div>
                <div className="line"></div>
                <div className="line"></div>
                <div className="line"></div>
                <div className="line"></div>
              </div>
            ) : (
              <div className="relative">
                  {answer ?<FuseSvgIcon className="text-48 absolute -right-12 -top-16 z-10 cursor-pointer" size={24} color="action" 
                  onClick={(e)=>{clipboardCopy(answer);
                    dispatch(
                      showMessage({ message: "Sharable link cpoied to clipboard" })
                    );
                  }} 
                  >feather:copy
                  </FuseSvgIcon>:<div></div>}
                {answer ? (
                  <CodeSyntaxHighlighter answer={answerAsString} />
                ) : (
                  <p>Something went wrong, check trained information.</p>
                )}
              </div>
            )}
          </div>
        </div>
        {!isLoading && ( <div className="meta-links">
            <>
            {( linkMetaArray.length > 0 || docArray.length > 0 || linksArray.length > 0 ) && <h5 className="mb-10 -mt-32">Referred Sources</h5>}
                <div className="source-links">
                  { docArray.length > 0 && docArray.map((doc, index) => (
                    <a key={index} href={doc.file} target="_blank" rel="noopener noreferrer" className="flex no-meta">
                      {doc.name}
                    </a>
                    // <div className="no-meta" key={index}><b>{doc.name}</b></div>
                  ))}
                  {!isFetchingMeta && linkMetaArray.length > 0 && (
                    linkMetaArray?.map((link, index) => (
                      <MetaLinkCard key={index} url={link?.url} title={link?.title} description={link?.description} image={link?.image} queryId={queryId} />
                    ))
                  )}
                  { linkMetaArray.length === 0 && linksArray.map((link, index) => (
                      <div key={index} className="meta-link-card-container">
                        { link && <a className="meta-link p-5" href={`${process.env.REACT_APP_TRACKER_PAGE}?url=${link}&query_id=${queryId}` } target="_blank"><b>{link}</b></a> }
                      </div>
                    ))
                  }
              </div>
            </>
        </div> )}
      </div>
    </div>
  );
}

export default AnswerSourcesContainer;
