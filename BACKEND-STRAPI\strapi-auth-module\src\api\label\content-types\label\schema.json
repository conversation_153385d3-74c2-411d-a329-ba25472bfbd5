{"kind": "collectionType", "collectionName": "labels", "info": {"singularName": "label", "pluralName": "labels", "displayName": "Label", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"board_ids": {"type": "relation", "relation": "manyToMany", "target": "api::scrumboard.scrumboard", "inversedBy": "labels"}, "title": {"type": "string"}, "scrumboard_card": {"type": "relation", "relation": "manyToOne", "target": "api::scrumboard-card.scrumboard-card", "inversedBy": "labels"}}}