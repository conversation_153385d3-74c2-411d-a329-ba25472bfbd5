"use strict";

/**
 * Scrumboard usage lifecycle
 */
const _ = require("lodash");
module.exports = {
  async afterCreate(event) {
    const { result, params } = event;
    const automation = await strapi
      .query("api::scrumboard-automation.scrumboard-automation")
      .create({ data: { scrumboard: result.id } });
  },

  async afterFindOne(event) {
    if (
      event.result.scrumboard_cards &&
      !event.result.scrumboard_cards?.count
    ) {
      // Group cards by user ID
      const groupedCards = _.groupBy(
        event.result.scrumboard_cards,
        (card) => card.listId.id
      );
      var list = event.result.lists.map((l) => ({
        id: l.id,
        cards: groupedCards[l.id] || [], // If no cards are found for a user, default to an empty array
      }));
      list = _.sortBy(list, "id");
      event.result.lists = list;
    }
  },
  // async afterFindMany(result, params, populate){

  // 	if (result.scrumboard_cards) {
  // 		// Group cards by user ID
  // 		const groupedCards = _.groupBy(result.scrumboard_cards, 'listId');
  // 		const list = result.lists.map(id => ({
  // 		id,
  // 		cards: groupedCards[id] || [] // If no cards are found for a user, default to an empty array
  // 		}));
  // 		result.lists=list;
  // 	  }

  // }
};
