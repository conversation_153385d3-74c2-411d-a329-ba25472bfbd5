# Email Connector System for Strapi

A modern, extensible email connector system for Strapi that supports multiple email providers with a unified interface and flexible template management.

## 🚀 Features

- **Multi-Provider Support**: SendGrid, Mailgun, Postmark, and easily extensible for more providers
- **Global Templates**: Shared templates available to all users and organizations
- **Multiple Template Types**: Provider templates, HTML content, and hybrid approaches
- **Email Logging**: Track email delivery status, analytics, and history
- **Provider Switching**: Switch between email providers without code changes
- **Bulk Email Support**: Send bulk emails efficiently
- **Delivery Tracking**: Monitor email delivery status and engagement
- **Backward Compatibility**: Works with existing email service implementations
- **Simple & Clean**: No complex hierarchy - just global and system templates

## 📧 Template System

### **Template Scopes**

| Scope | Description | Access | Use Case |
|-------|-------------|--------|----------|
| **Global** | Available to everyone | All users | Common templates (welcome, password reset, notifications) |
| **System** | Admin-managed templates | Admins only | Core system emails, maintenance notifications |

### **Template Resolution**

Simple priority system:
1. **Global Template** (primary)
2. **System Template** (fallback)

### **Example Usage**

```javascript
// Create global template (available to everyone)
await strapi.service('api::email-template.email-template').create({
  data: {
    name: 'welcome',
    scope: 'global',
    template_type: 'html_content',
    subject: 'Welcome to {{app_name}}!',
    html_content: '<h1>Welcome {{username}}!</h1>'
  }
});

// Send email - system automatically finds the template
await strapi.emailProviderFactory.sendEmail({
  templateName: 'welcome',
  to: '<EMAIL>',
  templateData: { username: 'John', app_name: 'My App' }
});
```

## 📋 Table of Contents

- [Installation](#installation)
- [Configuration](#configuration)
- [Template Management](#template-management)
- [Usage](#usage)
- [API Endpoints](#api-endpoints)
- [Template Types](#template-types)
- [Analytics](#analytics)
- [Adding New Providers](#adding-new-providers)
- [Examples](#examples)

## 🛠 Installation

The email connector system is built into this Strapi application. No additional installation required.

### Required Dependencies

Ensure these packages are installed:

```bash
npm install @sendgrid/mail @sendgrid/client
npm install mailgun.js form-data  # Optional, for Mailgun support
npm install postmark              # Optional, for Postmark support
```

## ⚙️ Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```env
# Postmark (Primary Provider)
POSTMARK_API_KEY=your_postmark_api_key
POSTMARK_FROM_EMAIL=<EMAIL>
POSTMARK_FROM_NAME="Podycy Team"

# SendGrid (For marketing contacts only)
SENDGRID_API_KEY=your_sendgrid_api_key

# Mailgun (Optional Secondary Provider)
MAILGUN_API_KEY=your_mailgun_api_key
MAILGUN_DOMAIN=mg.yourdomain.com
MAILGUN_FROM_EMAIL=<EMAIL>
MAILGUN_FROM_NAME="Your App Name"
```

## 📧 Template Management

### Creating Global Templates

Global templates are available to everyone in the system:

```javascript
const globalTemplate = await strapi.service('api::email-template.email-template').create({
  data: {
    name: 'password-reset',
    scope: 'global',
    template_type: 'html_content',
    category: 'reset-password',
    subject: 'Reset your password for {{app_name}}',
    html_content: `
      <h1>Password Reset Request</h1>
      <p>Hi {{username}},</p>
      <p>Click the link below to reset your password:</p>
      <a href="{{reset_url}}">Reset Password</a>
      <p>This link expires in {{expires_in}}.</p>
    `,
    variables: {
      username: '',
      app_name: 'Your App',
      reset_url: '',
      expires_in: '24 hours'
    }
  }
});
```

### Creating System Templates (Admin Only)

System templates are managed by administrators:

```javascript
const systemTemplate = await strapi.service('api::email-template.email-template').create({
  data: {
    name: 'system-maintenance',
    scope: 'system',
    template_type: 'html_content',
    subject: 'System Maintenance Notice',
    html_content: '<h1>Scheduled maintenance: {{maintenance_time}}</h1>',
    variables: { maintenance_time: '' }
  }
});
```

### Template Cloning

Clone existing templates for customization:

```javascript
// POST /api/email-template/123/clone
{
  "name": "welcome-premium",
  "overrides": {
    "subject": "Welcome to Premium!",
    "html_content": "<h1>Welcome to Premium, {{username}}!</h1>"
  }
}
```

## 🔧 Usage

### Basic Email Sending

```javascript
// Using the email connector directly
const { emailProviderFactory } = require('../providers/email-provider-factory');

const result = await emailProviderFactory.sendEmail({
  to: '<EMAIL>',
  subject: 'Welcome!',
  html: '<h1>Welcome to our platform!</h1>'
});
```

### Using Templates

```javascript
// System automatically finds the template by name
const result = await strapi.emailProviderFactory.sendEmail({
  templateName: 'welcome',
  to: '<EMAIL>',
  templateData: {
    username: 'John Doe',
    app_name: 'My App'
  }
});
```

### Template Types

#### 1. Provider Template
```javascript
// Using SendGrid template
const sendGridTemplate = {
  template_type: 'provider_template',
  template_id: 'd-1234567890abcdef',  // SendGrid template ID
  provider: 'sendgrid'
};

// Using Postmark template
const postmarkTemplate = {
  template_type: 'provider_template',
  template_id: 'welcome-template',    // Postmark template alias
  provider: 'postmark'
};
```

#### 2. HTML Content Template
```javascript
const htmlTemplate = {
  template_type: 'html_content',
  html_content: `
    <h1>Welcome {{username}}!</h1>
    {{#if premium}}
      <p>You have premium access!</p>
    {{/if}}
    {{#each features}}
      <li>{{name}}: {{description}}</li>
    {{/each}}
  `,
  text_content: 'Welcome {{username}}! Thanks for joining.',
  variables: { username: '', premium: false, features: [] }
};
```

#### 3. Hybrid Template
```javascript
const hybridTemplate = {
  template_type: 'hybrid',
  template_id: 'd-1234567890abcdef',  // Provider template
  html_content: '<h1>Fallback: Welcome {{username}}!</h1>',  // Fallback HTML
  provider: 'sendgrid'
};

// Or with Postmark
const postmarkHybridTemplate = {
  template_type: 'hybrid',
  template_id: 'welcome-template',    // Postmark template alias
  html_content: '<h1>Fallback: Welcome {{username}}!</h1>',  // Fallback HTML
  provider: 'postmark'
};
```

## 🌐 API Endpoints

### Template Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/email-templates` | List all templates (global + system) |
| POST | `/api/email-templates` | Create template |
| PUT | `/api/email-templates/:id` | Update template |
| DELETE | `/api/email-templates/:id` | Delete template |
| POST | `/api/email-template/:id/test` | Test template |
| POST | `/api/email-template/:id/preview` | Preview HTML template |
| POST | `/api/email-template/:id/clone` | Clone template |
| GET | `/api/email-template/:id/hierarchy` | Get template inheritance chain |
| GET | `/api/email-template/scopes` | Get available scopes for user |
| POST | `/api/email-template/validate-html` | Validate HTML syntax |

### Email Sending

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/email/send` | Send single email |
| POST | `/api/email/send-bulk` | Send bulk emails |
| POST | `/api/email/send-template` | Send email using template name |

### Analytics & Logs

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/email-logs` | List email logs |
| GET | `/api/email-log/analytics` | Get email analytics |
| POST | `/api/email-log/:id/retry` | Retry failed email |

### Provider Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/email/providers` | List available providers |
| GET | `/api/email/test-providers` | Test provider connections |
| GET | `/api/email/status/:messageId` | Get delivery status |

## 📝 Examples

### Example 1: Welcome Email

```javascript
// Create global welcome template
const welcomeTemplate = await strapi.service('api::email-template.email-template').create({
  data: {
    name: 'user-welcome',
    scope: 'global',
    template_type: 'html_content',
    category: 'welcome',
    subject: 'Welcome to {{app_name}}!',
    html_content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1>Welcome {{username}}!</h1>
        <p>We're excited to have you join {{app_name}}.</p>
        {{#if confirmation_link}}
          <a href="{{confirmation_link}}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
            Confirm Your Email
          </a>
        {{/if}}
        <p>Best regards,<br>The {{app_name}} Team</p>
      </div>
    `,
    variables: {
      username: '',
      app_name: 'Your App',
      confirmation_link: ''
    }
  }
});

// Send welcome email
const result = await strapi.emailProviderFactory.sendEmail({
  templateName: 'user-welcome',
  to: '<EMAIL>',
  templateData: {
    username: 'John Doe',
    app_name: 'Amazing App',
    confirmation_link: 'https://yourapp.com/confirm/abc123'
  }
});
```

### Example 2: Password Reset

```javascript
// In your controller
async forgotPassword(ctx) {
  const { email } = ctx.request.body;
  
  // Generate reset token (your logic)
  const resetToken = generateResetToken();
  
  // Send password reset email using global template
  const result = await strapi.emailProviderFactory.sendEmail({
    templateName: 'password-reset',
    to: email,
    templateData: {
      username: user.name,
      reset_url: `https://yourapp.com/reset-password?token=${resetToken}`,
      expires_in: '24 hours'
    }
  });
  
  ctx.send({ success: true, message: 'Password reset email sent' });
}
```

### Example 3: Using Specific Provider

```javascript
// Send email using Postmark specifically
const result = await strapi.emailProviderFactory.sendEmail({
  to: '<EMAIL>',
  subject: 'Welcome from Postmark!',
  html: '<h1>Hello from Postmark!</h1>',
  provider: 'postmark'  // Force use of Postmark
});

// Send template email using Postmark template
const templateResult = await strapi.emailProviderFactory.sendEmail({
  templateName: 'welcome',
  to: '<EMAIL>',
  templateData: {
    username: 'John',
    product_name: 'My App'
  },
  provider: 'postmark'  // Use Postmark provider
});
```

### Example 4: Bulk Newsletter

```javascript
async sendNewsletter(ctx) {
  const { subscriberEmails, newsletterContent } = ctx.request.body;
  
  const emails = subscriberEmails.map(email => ({
    templateName: 'newsletter',
    to: email,
    templateData: {
      ...newsletterContent,
      unsubscribe_url: `https://yourapp.com/unsubscribe?email=${email}`
    }
  }));
  
  const result = await strapi.emailProviderFactory.sendBulkEmails(emails);
  
  ctx.send({
    success: true,
    message: `Newsletter sent to ${result.successCount} subscribers`,
    details: result
  });
}
```

## 📊 Analytics

Email analytics work across all template types:

```javascript
// Get analytics for all templates
const analytics = await strapi.service('api::email-log.email-log').getAnalytics({
  startDate: '2024-01-01',
  endDate: '2024-01-31'
});

console.log(analytics.data.summary);
// {
//   totalEmails: 1000,
//   sentEmails: 950,
//   deliveredEmails: 920,
//   openedEmails: 400,
//   clickedEmails: 100,
//   deliveryRate: 96.84,
//   openRate: 43.48,
//   bounceRate: 2.1
// }
```

## 🎯 Best Practices

### Template Organization

1. **Create Global Templates** for:
   - Common workflows (welcome, password reset)
   - Standard notifications
   - Frequently used templates

2. **Use System Templates** for:
   - Critical system notifications
   - Admin-only communications
   - Maintenance alerts

### Template Naming Convention

```javascript
// Good naming patterns
'welcome'                    // Global template
'welcome-premium'            // Premium variant
'password-reset'             // Standard template
'system-maintenance'         // System template
'newsletter-weekly'          // Newsletter template
```

### Template Development Workflow

1. **Start with HTML Content**: Create templates with full HTML control
2. **Test Thoroughly**: Use preview and test endpoints
3. **Use Variables**: Make templates flexible with dynamic data
4. **Clone for Variants**: Create variations by cloning and modifying
5. **Monitor Usage**: Track which templates are most used

## 🔌 Adding New Providers

The provider system works seamlessly with all template types. Here's how to add a new email provider:

### Example: Adding AWS SES Provider

1. **Create the provider class** (`src/providers/ses-email-provider.js`):

```javascript
const EmailProviderInterface = require('./email-provider-interface');
const aws = require('aws-sdk');

class SESEmailProvider extends EmailProviderInterface {
  constructor(config = {}) {
    super(config);
    this.ses = new aws.SES({
      region: config.region || process.env.AWS_REGION || 'us-east-1',
      accessKeyId: config.accessKeyId || process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: config.secretAccessKey || process.env.AWS_SECRET_ACCESS_KEY
    });
  }

  async initialize() {
    // Implementation...
  }

  async sendEmail(emailData) {
    // Implementation...
  }

  // ... implement all required methods
}

module.exports = SESEmailProvider;
```

2. **Register in factory** (`src/providers/email-provider-factory.js`):

```javascript
const SESEmailProvider = require('./ses-email-provider');

// In initializeDefaultProviders()
if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
  emailProviderFactory.registerProvider('ses', SESEmailProvider, {
    region: process.env.AWS_REGION,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  });
}
```

3. **Update schema** to include 'ses' in provider enum.

### Included Providers

- ✅ **SendGrid** - Full featured with templates and analytics
- ✅ **Mailgun** - Reliable email delivery with good API
- ✅ **Postmark** - Transactional email specialist with excellent deliverability
- 🚧 **AWS SES** - Coming soon (follow pattern above)
- 🚧 **Resend** - Coming soon
- 🚧 **Brevo** - Coming soon

## 🛡️ Security & Permissions

### Template Access Control

- **Global Templates**: Readable/editable by all users
- **System Templates**: Readable by all, editable by admins only

### API Security

All endpoints respect user permissions and include proper authentication.

## 🔧 Troubleshooting

### Common Issues

1. **Template not found**: Ensure template exists and is active
2. **Provider errors**: Check API keys and provider configuration
3. **Template validation**: Use validation endpoint to check HTML syntax
4. **Variable issues**: Ensure all required variables are provided

### Debug Mode

Enable debug logging:

```env
LOG_LEVEL=debug
```

## 📄 License

This email connector system is part of the main application license. 