{"kind": "collectionType", "collectionName": "subscription_orders", "info": {"singularName": "subscription-order", "pluralName": "subscription-orders", "displayName": "Subscription Order", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"type": {"type": "enumeration", "enum": ["subscription", "renew"]}, "organization": {"type": "relation", "relation": "manyToOne", "target": "api::organization.organization", "inversedBy": "subscription_orders"}, "isPaid": {"type": "boolean", "default": false, "required": false}, "stripeSessionId": {"type": "string"}, "invoice": {"type": "string"}, "plan": {"type": "relation", "relation": "oneToOne", "target": "api::plan.plan"}, "plantype": {"type": "enumeration", "enum": ["monthly", "yearly"], "default": "monthly"}}}