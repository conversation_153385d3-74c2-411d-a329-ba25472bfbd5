{"kind": "collectionType", "collectionName": "playlists", "info": {"singularName": "playlist", "pluralName": "playlists", "displayName": "playlist", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "description": {"type": "text"}, "shareLink": {"type": "string"}, "public": {"type": "boolean", "default": false}, "album_art": {"type": "string"}, "type": {"type": "enumeration", "enum": ["featured", "weekly", "daily"], "default": "featured"}, "file_results_models": {"type": "relation", "relation": "oneToMany", "target": "api::file-results-model.file-results-model", "mappedBy": "playlist"}, "host_persona": {"type": "relation", "relation": "oneToOne", "target": "api::host-persona.host-persona", "mappedBy": "playlist"}, "categories": {"type": "relation", "relation": "manyToMany", "target": "api::category.category", "inversedBy": "playlists"}}}