const postmark = require('postmark');

/**
 * Strapi Postmark Email Provider
 * Custom provider for Strapi to use Postmark
 */
module.exports = {
  init: (providerOptions = {}, settings = {}) => {
    const client = new postmark.ServerClient(providerOptions.apiKey);

    return {
      send: async (options) => {
        const { from, to, cc, bcc, replyTo, subject, text, html, ...rest } = options;

        const message = {
          From: from || settings.defaultFrom,
          To: Array.isArray(to) ? to.join(',') : to,
          Subject: subject,
          ...(html && { HtmlBody: html }),
          ...(text && { TextBody: text }),
          ...(replyTo && { ReplyTo: replyTo }),
          ...(cc && { Cc: Array.isArray(cc) ? cc.join(',') : cc }),
          ...(bcc && { Bcc: Array.isArray(bcc) ? bcc.join(',') : bcc }),
        };

        try {
          const result = await client.sendEmail(message);
          
          return {
            messageId: result.MessageID,
            response: result,
          };
        } catch (error) {
          throw new Error(`Failed to send email: ${error.message}`);
        }
      },
    };
  },
};
