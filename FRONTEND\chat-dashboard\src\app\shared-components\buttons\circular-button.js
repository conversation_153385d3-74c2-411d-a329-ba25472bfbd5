export default function CircularButton({ image, onPress }) {
  return (
    <button onClick={onPress}>
      <img
        src={image}
        className="mx-auto p-8 w-36 h-36 bg-light-bg-color shadow-lg rounded-full hover:scale-110 transition-transform duration-300 hover:shadow-2x hover:saturate-200"
      ></img>
    </button>
  );
}



export function BlackCircularButton({ image, onPress }) {
  return (
    <button onClick={onPress}>
      <img
        src={image}
        className="mx-auto p-8 w-36 h-36 bg-black shadow-lg rounded-full hover:scale-110 transition-transform duration-300 hover:shadow-2x hover:saturate-200"
      ></img>
    </button>
  );
}




