/**
 * Sample chat messages for testing the chat UI
 */
const sampleMessages = [
  {
    type: 'ai',
    text: 'Hello! I\'m your AI assistant. How can I help you today?'
  },
  {
    type: 'user',
    text: 'Can you help me with some React code?'
  },
  {
    type: 'ai',
    text: 'Of course! I\'d be happy to help with your React code. What specifically would you like assistance with?'
  },
  {
    type: 'user',
    text: 'How do I create a custom hook for handling form state?'
  },
  {
    type: 'ai',
    text: `Here's how you can create a custom hook for handling form state in React:

\`\`\`jsx
import { useState } from 'react';

function useForm(initialValues) {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setValues({
      ...values,
      [name]: value
    });
  };

  const handleSubmit = (callback) => (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    callback();
  };

  const reset = () => {
    setValues(initialValues);
    setErrors({});
  };

  return {
    values,
    errors,
    isSubmitting,
    handleChange,
    handleSubmit,
    reset,
    setValues,
    setErrors
  };
}

export default useForm;
\`\`\`

And here's how you would use it in a component:

\`\`\`jsx
function SignupForm() {
  const { values, handleChange, handleSubmit } = useForm({
    email: '',
    password: ''
  });

  const submitForm = () => {
    console.log('Form submitted with:', values);
  };

  return (
    <form onSubmit={handleSubmit(submitForm)}>
      <input
        type="email"
        name="email"
        value={values.email}
        onChange={handleChange}
      />
      <input
        type="password"
        name="password"
        value={values.password}
        onChange={handleChange}
      />
      <button type="submit">Sign Up</button>
    </form>
  );
}
\`\`\`

This custom hook follows the principle of encapsulation and reusability, making your form handling cleaner and more maintainable.`
  },
  {
    type: 'user',
    text: 'That\'s great! Can you also explain how to implement a debounce function in React?'
  },
  {
    type: 'ai',
    text: `Certainly! Debouncing is a programming practice used to ensure that time-consuming tasks do not fire so often. Here's how to implement a debounce custom hook in React:

\`\`\`jsx
import { useState, useEffect, useCallback } from 'react';

function useDebounce(value, delay) {
  // State to store the debounced value
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    // Set a timeout to update the debounced value after the specified delay
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Clean up the timeout if the value changes again within the delay period
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export default useDebounce;
\`\`\`

And here's how you would use it in a search component:

\`\`\`jsx
import React, { useState, useEffect } from 'react';
import useDebounce from './useDebounce';

function SearchComponent() {
  // State for the search input
  const [searchTerm, setSearchTerm] = useState('');
  // State for search results
  const [results, setResults] = useState([]);
  // State for loading indicator
  const [isSearching, setIsSearching] = useState(false);
  
  // Debounce search term so that it only updates after 500ms of no typing
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  
  // Effect for API call
  useEffect(() => {
    if (debouncedSearchTerm) {
      setIsSearching(true);
      
      // Make API call here
      fetchSearchResults(debouncedSearchTerm)
        .then(results => {
          setIsSearching(false);
          setResults(results);
        });
    } else {
      setResults([]);
      setIsSearching(false);
    }
  }, [debouncedSearchTerm]);
  
  return (
    <div>
      <input
        type="text"
        placeholder="Search..."
        value={searchTerm}
        onChange={e => setSearchTerm(e.target.value)}
      />
      
      {isSearching && <div>Searching...</div>}
      
      <ul>
        {results.map(result => (
          <li key={result.id}>{result.name}</li>
        ))}
      </ul>
    </div>
  );
}
\`\`\`

This implementation prevents the API call from being executed on every keystroke, thus improving performance and reducing unnecessary API calls.`
  }
];

export default sampleMessages; 