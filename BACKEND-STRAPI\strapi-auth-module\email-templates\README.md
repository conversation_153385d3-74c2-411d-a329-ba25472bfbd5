# 📧 Podycy Email Templates

Professional email templates for the Podycy platform with external JSON configuration for easy maintenance.

## 🎨 Available Templates

### 1. **confirmation-email.json**
- **Purpose**: Email verification for new user registrations
- **Trigger**: When user signs up with email/password
- **Variables**: `name`, `action_url`

### 2. **welcome-email.json**
- **Purpose**: Welcome message after email confirmation
- **Trigger**: When user confirms their email address
- **Variables**: `name`, `dashboard_url`

### 3. **reset-password.json**
- **Purpose**: Password reset instructions
- **Trigger**: When user requests password reset
- **Variables**: `name`, `action_url`

### 4. **ticket-notification.json**
- **Purpose**: Ticket creation notifications for Scrumboard
- **Trigger**: When a new ticket is created via Scrumboard
- **Variables**: `name`, `title`, `description`, `agent_name`, `customer_email`, `ticket_url`

## 🔧 Template Structure

Each template JSON file contains:

```json
{
  "name": "template-name",
  "description": "Template description",
  "provider": "postmark",
  "template_id": "",
  "html_content": "Full HTML email content with {{variables}}",
  "text_content": "Plain text version with {{variables}}",
  "template_type": "html_content",
  "category": "transactional",
  "subject": "Email subject with {{variables}}",
  "default_from_email": "<EMAIL>",
  "default_from_name": "The Ajentic Team",
  "variables": {
    "variable_name": "default_value"
  },
  "is_active": true,
  "scope": "global"
}
```

## 📝 Available Variables

### Global Variables (all templates)
- `{{name}}` - User's name/username
- `{{sender_name}}` - Email sender name ("The Ajentic Team")
- `{{company_name}}` - Company name ("Ajentic Technologies")
- `{{company_address}}` - Company address
- `{{ajentic_logo_url}}` - Ajentic logo URL
- `{{product_name}}` - Product name ("Ajentic")

### Template-Specific Variables
- `{{action_url}}` - Confirmation/reset links (confirmation, reset-password)
- `{{dashboard_url}}` - Dashboard URL (welcome-email)

## 🎨 Design Guidelines

### Colors
- **Primary**: #4F46E5 (Indigo)
- **Success**: #10B981 (Green)
- **Warning**: #F59E0B (Amber)
- **Danger**: #DC2626 (Red)
- **Text**: #333333 (Dark Gray)
- **Muted**: #666666 (Gray)

### Typography
- **Font**: Arial, sans-serif
- **Headings**: 28px (H1), 18px (H2)
- **Body**: 16px
- **Small**: 14px, 12px

### Layout
- **Width**: 600px max
- **Padding**: 40px
- **Border Radius**: 8px
- **Box Shadow**: 0 2px 10px rgba(0,0,0,0.1)

## 🔄 Template Management

### Loading Templates
Templates are automatically loaded and cached by the email service:

```javascript
const emailService = require('./helpers/email-service');

// Send confirmation email
await emailService.sendConfirmationEmail({
  email: '<EMAIL>',
  name: 'John Doe',
  confirmationUrl: 'https://podycy.com/confirm?token=...'
});

// Send ticket notification email
await emailService.sendTicketNotificationEmail({
  emails: ['<EMAIL>', '<EMAIL>'],
  name: 'John Doe',
  title: 'Customer Support Request',
  description: 'Customer needs help with login issues',
  agent_name: 'Support Bot',
  customer_email: '<EMAIL>',
  ticket_url: 'https://podycy.com/tickets/123'
});
```

### Cache Management
- Templates are cached in memory for performance
- Cache is automatically cleared in development
- Use `emailService.clearTemplateCache()` to manually clear

### Hot Reloading
In development, templates are reloaded when files change. Simply edit the JSON files and the changes will be reflected immediately.

## ✏️ Editing Templates

### 1. Edit JSON Files
Modify the template JSON files directly:
- Update `html_content` for HTML version
- Update `text_content` for plain text version
- Update `subject` for email subject
- Add/modify variables in the `variables` object

### 2. Test Changes
Templates are automatically reloaded, so changes take effect immediately.

### 3. Variable Replacement
Use `{{variable_name}}` syntax in:
- HTML content
- Text content
- Email subject

## 🚀 Adding New Templates

1. Create new JSON file in `email-templates/` directory
2. Follow the template structure above
3. Add corresponding method in `email-service.js`
4. Update this README with template documentation

## 📱 Mobile Responsiveness

All templates are designed to be mobile-responsive:
- Fluid width with max-width constraints
- Scalable images
- Touch-friendly buttons
- Readable typography on small screens

## 🔒 Security

- All variables are automatically escaped
- External links use HTTPS
- Email content is sanitized
- No JavaScript execution in emails

## 📊 Analytics

Templates include tracking-friendly structure:
- UTM parameters can be added to links
- Open tracking compatible
- Click tracking compatible
- Postmark analytics integration ready
