'use strict';

/**
 * knowledgebase custom router
 */
 module.exports = {
	routes: [
	 
		{
			method: 'GET',
			path: '/kb_source',
			handler: 'custom-controller.kbSource',
			config: {
				policies: [
					
					'global::user-details-populate',
					'global::update-org-before-api-call',
				]
			}
		  },
		  {
			method: 'DELETE',
			path: '/empty_kb/:kb_id',
			handler: 'custom-controller.emptyKb',
			config: {
				policies: [
					
					'global::user-details-populate',
					'global::update-org-before-api-call',
				]
			}
		  },
		  {
			method: 'DELETE',
			path: '/delete_kb/:id',
			handler: 'custom-controller.deleteKb',
			config: {
				policies: [
					
					'global::user-details-populate',
					'global::update-org-before-api-call',
				]
			}
		  },
		  {
			method: 'POST',
			path: '/whatsapp-url',
			handler: 'custom-controller.whatsappUrl',
			// config: {
			// 	policies: [
					
			// 		'global::user-details-populate',
			// 		'global::update-org-before-api-call',
			// 	]
			// }
		  },

	]
	};
