import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";
import { createTheme } from "@mui/material/styles";
import { memo } from "react";
import { useSelector } from "react-redux";
import { selectUsage } from "app/store/usageSlice";
import formatNumber from "./FormatNumber";
import Button from "@mui/material/Button";
import CircularProgressWithLabel from "app/shared-components/indicators/circularProgressLabel";
import history from "@history";

function CreditUsage(props) {
  const usage = useSelector(selectUsage);
  const { credits_used, allowed_credits } = usage;

  function calcProgressVal(val, limit) {
    const percentage = (val * 100) / limit;
    return percentage > 100 ? 100 : percentage;
  }

  // Create a custom theme with the desired color
  const theme = createTheme({
    palette: {
      primary: {
        main: "#7666CE",
      },
    },
  });

  return (
    <Paper elevation={0} className="flex border-1 flex-col flex-auto px-24  shadow-base rounded-md overflow-hidden ">
      <Typography className="mt-24 text-base font-regular text-gray-700">
        Query Usage
      </Typography>

      {credits_used != null && <div className="my-16 space-y-32">
        <div className="flex flex-row items-center justify-around">
          <div className="w-1/3 -ml-8">
            <CircularProgressWithLabel
              value={calcProgressVal(credits_used, allowed_credits).toFixed(0)}
            />
          </div>
          <div className="ml-24  flex flex-col items-center">
            <Typography className="text-4xl font-bold">
              {formatNumber({ num: credits_used ?? 0 }) +
                "/" +
                formatNumber({ num: allowed_credits ?? 0 })}
            </Typography>
            <Typography className="text-sm text-center ml-4 text-gray-600">
              Credits used
            </Typography>
            <Button
              onClick={() => {
                history.push("/subscription", "update");
              }}
              variant="contained"
              color="secondary"
              className="press-button shine-button w-full mt-16 rounded-md bg-primary hover:bg-primary text-white"
              aria-label="Sign in"
              // disabled={_.isEmpty(dirtyFields) || !isValid}
              type="submit"
              size="small"
            >
              Upgrage
            </Button>
          </div>
        </div>
      </div>}
    </Paper>
  );
}

export default memo(CreditUsage);
