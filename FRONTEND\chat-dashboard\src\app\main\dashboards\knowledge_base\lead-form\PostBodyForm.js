import React, { useState, useEffect, useRef } from "react";
import CircularButton from "src/app/shared-components/buttons/circular-button";
import { H6 } from "src/app/shared-components/text/texts";

const PostBodyParams = ({ onChange, bodyParams, body_type, editMode = false }) => {
  const [activeTab, setActiveTab] = useState(body_type || "form-data");
  const [formData, setFormData] = useState({});
  const [rawJson, setRawJson] = useState("");
  const [newFieldName, setNewFieldName] = useState("");
  const [newFieldValue, setNewFieldValue] = useState("");
  const isInitialRender = useRef(true);

  useEffect(() => {
    if (bodyParams) {
      if (activeTab === "form-data" && typeof bodyParams === "object") {
        setFormData(bodyParams);
      } else if (activeTab === "json" && typeof bodyParams === "string") {
        setRawJson(bodyParams);
      }
    }
  }, [bodyParams, activeTab]);

  useEffect(() => {
    if (isInitialRender.current) {
      isInitialRender.current = false;
    } else if (onChange) {
      onChange({
        type: activeTab,
        data: activeTab === "form-data" ? formData : rawJson,
      });
    }
  }, [activeTab, formData, rawJson, onChange]);

  const handleNewFieldNameUpdate = (value) => {
    setNewFieldName(value);

  };

  const handleNewFieldValueUpdate = (value) => {
    setNewFieldValue(value);

  };

  const handleAddField = (e) => {
    e.preventDefault();
    if (newFieldName.trim()) {
      const updatedFormData = {
        ...formData,
        [newFieldName]: newFieldValue,
      };
      setFormData(updatedFormData);
      setNewFieldName("");
      setNewFieldValue("");
      if (onChange) {
        onChange({
          type: "form-data",
          data: updatedFormData,
        });
      }
    }
  };

  const handleFormChange = (key, value) => {
    const updatedFormData = { ...formData, [key]: value };
    setFormData(updatedFormData);
    if (onChange) {
      onChange({
        type: "form-data",
        data: updatedFormData,
      });
    }
  };

  const handleDeleteField = (keyToDelete) => {
    const updatedFormData = { ...formData };
    delete updatedFormData[keyToDelete];
    setFormData(updatedFormData);
    if (onChange) {
      onChange({
        type: "form-data",
        data: updatedFormData,
      });
    }
  };

  const handleRawJsonChange = (value) => {
    setRawJson(value);
    if (onChange) {
      onChange({
        type: "json",
        data: value,
      });
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto px-4">
      <H6 className="text-body-text-color">Body Parameters</H6>
      <div className="flex mb-8 mt-8">
        <button
          type="button"
          className={`py-4 px-16 text-base border-2 border-black ${
            activeTab === "form-data"
              ? "bg-black text-white border-b-2 border-black"
              : "bg-gray-200 text-gray-800"
          }`}
          onClick={() => setActiveTab("form-data")}
        >
          form-data
        </button>
        <button
          type="button"
          className={`py-4 px-8 text-base border-2 border-black ${
            activeTab === "json"
              ? "bg-black text-white border-b-2 border-black"
              : "bg-gray-200 text-gray-800"
          }`}
          onClick={() => setActiveTab("json")}
        >
          Raw
        </button>
      </div>
      <div className="rounded-md p-4">
        {activeTab === "form-data" ? (
          <div>
            {Object.entries(formData).map(([key, value]) => (
              <div key={key} className="mb-4 flex">
                <input
                  type="text"
                  value={key}
                  onChange={(e) => {
                    const newKey = e.target.value;
                    handleFormChange(newKey, formData[key]);
                  }}
                  className="w-1/2 h-36 mr-8 px-6 text-grey-800 py-2 mt-4 border-2 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-base-purple focus:border-2"
                  placeholder="Field name"
                />
                <input
                  type="text"
                  value={value}
                  onChange={(e) => handleFormChange(key, e.target.value)}
                  className="w-1/2 h-36 ml-8 px-6 text-grey-800 py-2 mt-4 border-2 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-base-purple focus:border-2"
                  placeholder="Value"
                />
                <div className="ml-8">
                  <CircularButton onClick={() => handleDeleteField(key)} image="assets/images/delete-icon.png" />
                </div>
              </div>
            ))}
            <div className="mb-4 flex">
              <input
                type="text"
                value={newFieldName}
                onChange={(e) => handleNewFieldNameUpdate(e.target.value)}
                className="w-1/2 h-36 mr-8 px-6 text-grey-800 py-2 mt-4 border-2 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-base-purple focus:border-2"
                placeholder="Key"
              />
              <input
                type="text"
                value={newFieldValue}
                onChange={(e) => handleNewFieldValueUpdate(e.target.value)}
                className="w-1/2 h-36 ml-8 px-6 text-grey-800 py-2 mt-4 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-base-purple focus:border-2"
                placeholder="Value"
              />
              <div className="ml-8 w-[32px]"></div>
            </div>
            <div className="flex w-full pr-36">
            <button
              type="button"
              onClick={handleAddField}
              className="w-full h-32 mt-8 py-2  border border-gray-800 rounded text-gray-800 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
               Submit
            </button>
            </div>

          </div>
        ) : (
          <textarea
            value={rawJson}
            onChange={(e) => handleRawJsonChange(e.target.value)}
            className="flex w-full min-h-[100px] max-h-[400px] p-8 border rounded"
            placeholder="Enter raw JSON here"
          />
        )}
      </div>
    </div>
  );
};

export default PostBodyParams;
