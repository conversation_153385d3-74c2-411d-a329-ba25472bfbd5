"use strict";

/**
 * Oganiztion custom router
 */
module.exports = {
  routes: [
    {
      method: "GET",
      path: "/org",
      handler: "custom-controller.org",
      config: {
        policies: [
          "global::user-details-populate",
          "global::update-org-before-api-call",
        ],
      },
    },
    {
      method: "GET",
      path: "/orgInfo",
      handler: "custom-controller.getOrganizationInfo",
      config: {
        policies: [
          "global::bearer-token-authentication",
        ],
      },
    },
  ],
};
