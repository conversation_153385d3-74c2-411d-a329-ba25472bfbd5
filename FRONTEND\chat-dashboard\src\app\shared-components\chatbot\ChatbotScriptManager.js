import React, { useEffect, useRef } from "react";

const ChatbotScriptManager = ({ user }) => {
  const isDev = process.env.REACT_APP_ENV === "development";
  const botScriptSrc = isDev
    ? "https://bot-dev.talkbase.ai/chatbot.js"
    : "https://bot.talkbase.ai/chatbot.js";
  const botCssSrc = isDev
    ? "https://bot-dev.talkbase.ai/chatbot.css"
    : "https://bot.talkbase.ai/chatbot.css";
  const orgId = isDev
    ? "U2FsdGVkX1+9oOL/SUGba/T6Z14hJs7+6VN3G1oBkJkMe26htIsGMkfys5klhONfAFDE741gYbQQi9SL9iz1FA=="
    : "U2FsdGVkX1/FLWVwEZcQMcoGDNziG2wxRe8iJOJDVcSOMnt3Aco5nOoOwdrKQNN03k7W1KhnI8Z8a2k8G/4kVg==";
  const chatbotId = isDev
    ? "U2FsdGVkX1/mh0wwwy4Ozo8l2eNOiYbuB6u2w2gg4vciOQTWupWSjL0ms1nKj7Rp++c+IU4CRr0lyFlmeIoDMA=="
    : "U2FsdGVkX19HPxptaHD9kGrktdq1RuvJVueRHl3lQ+OF7tiMimI2glinF/WHKEaoqUfrM5akGuA31j/qfe1R6g==";

  const scriptRef = useRef(null);

  const createBotScript = () => {
    const script = document.createElement("script");
    script.src = botScriptSrc;
    script.defer = true;
    script.setAttribute("orgid", orgId);
    script.setAttribute("chatbotid", chatbotId);
    script.setAttribute("position", "right");
    script.setAttribute("showclosebutton", "true");
    script.setAttribute("usethemeargs", "false");
    script.setAttribute("showTooltip", "true");
    script.setAttribute("isinitiallyopen", "false");
    // if (userInfo) {
    //   script.setAttribute("userInfo", JSON.stringify(userInfo));
    // }
    return script;
  };

  const updateOrCreateScript = () => {
    if (scriptRef.current) {
      // If script exists, update its attributes
      // if (userInfo) {
      //   scriptRef.current.setAttribute("userInfo", JSON.stringify(userInfo));
      // } else {
      //   scriptRef.current.removeAttribute("userInfo");
      // }
    } else {
      // If script doesn't exist, create and add it
      const newScript = createBotScript();
      document.body.appendChild(newScript);
      scriptRef.current = newScript;
    }
  };

  const addCssIfNeeded = () => {
    if (!document.querySelector(`link[href="${botCssSrc}"]`)) {
      const chatbotCss = document.createElement("link");
      chatbotCss.href = botCssSrc;
      chatbotCss.rel = "stylesheet";
      document.head.appendChild(chatbotCss);
    }
  };

  const addGoogleAnalytics = () => {
    if (!document.querySelector('script[src*="googletagmanager.com/gtag/js"]')) {
      const gtagScript = document.createElement("script");
      gtagScript.src = "https://www.googletagmanager.com/gtag/js?id=G-BNL97GKPK1";
      gtagScript.async = true;
      document.body.appendChild(gtagScript);
      const gtagConfigScript = document.createElement("script");
      gtagConfigScript.innerHTML = `
        window.dataLayer = window.dataLayer || [];
        function gtag(){ dataLayer.push(arguments); }
        gtag('js', new Date());
        gtag('config', 'G-BNL97GKPK1');
      `;
      document.body.appendChild(gtagConfigScript);
    }
  };

  useEffect(() => {
    addCssIfNeeded();
    addGoogleAnalytics();
    updateOrCreateScript(); // Initial script without user info

    return () => {
      if (scriptRef.current) {
        scriptRef.current.remove();
        scriptRef.current = null;
        const tbDbRoot = document.getElementById("tb-cb-root");
        if (tbDbRoot) {
          tbDbRoot.remove();
        }
      }
    };
  }, []); // Empty dependency array ensures this runs only once on mount

  useEffect(() => {
    // const userInfo = user && user.data && user.data.email && user.data.organization && user.data.organization.id
    //   ? { userId: user.data.organization.id, userEmail: user.data.email }
    //   : null;

    // updateOrCreateScript(userInfo);
    updateOrCreateScript();
  }, [user]);

  return null; // This component doesn't render anything
};

export default ChatbotScriptManager;