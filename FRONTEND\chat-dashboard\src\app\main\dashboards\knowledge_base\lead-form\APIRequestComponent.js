import React, { useState, useCallback } from "react";
import axios from "axios";
import LoadingSpinner from "app/shared-components/loading-spinner/loading-spinner";
import { H5 } from "src/app/shared-components/text/texts";
import { ErrorCard } from "app/shared-components/cards/errorCard";

const ApiRequestComponent = ({ formData, onError }) => {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(null);
  const [responseData, setResponseData] = useState("");
  const [formErrors, setFormErrors] = useState([]);
  const [url, setUrl] = useState("");

  const validateForm = useCallback(() => {
    const errors = [];

    if (!formData.name.trim()) {
      errors.push("Name is required");
    } else if (formData.name.includes(" ")) {
      errors.push("Name cannot contain spaces");
    }

    if (!formData.description.trim()) {
      errors.push("Description is required");
    }

    if (formData.url) {
      try {
        new URL(formData.url);
      } catch (_) {
        errors.push("Invalid URL");
      }
    }

    setFormErrors(errors);
    return errors.length === 0;
  }, [formData]);

  const sendRequest = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setSuccess(null);
    setResponseData("");

    const data = {
      name: formData.name,
      description: formData.description,
      base_url: formData.url,
      type: "api",
      enabled: true,
      body: formData.body,
      body_type: formData.bodyType,
      method: formData.method,
      auth_type: "bearer",
      path_params: formData.path_params,
      query_params: formData.query_params,
      headers: formData.headers,
    };

    const pathParams = data.path_params
      ? data.path_params
          .map((param) => `${param.name}/${param.value}`)
          .join("/")
      : "";
    const url = `${data.base_url}${pathParams ? `/${pathParams}` : ""}`;
    const config = {
      headers: {
        "Content-Type": data.body_type || "application/json",
        ...(data.auth_type === "bearer" && {
          Authorization: `Bearer YOUR_BEARER_TOKEN`,
        }),
        ...data.headers,
      },
      params: data.query_params,
    };
    setUrl(url);

    try {
      let response;
      switch (data.method.toLowerCase()) {
        case "get":
          response = await axios.get(url, config);
          break;
        case "post":
          response = await axios.post(url, data.body, config);
          break;
        case "put":
          response = await axios.put(url, data.body, config);
          break;
        case "delete":
          response = await axios.delete(url, config);
          break;
        case "patch":
          response = await axios.patch(url, data.body, config);
          break;
        default:
          throw new Error(`Unsupported method: ${data.method}`);
      }
      console.log("Response:", response.data);
      setResponseData(JSON.stringify(response.data, null, 2));
      setSuccess(true);
    } catch (error) {
      console.error("Error making request:", error);
      setResponseData(
        JSON.stringify(error.response?.data || error.message, null, 2)
      );
      setSuccess(false);
    } finally {
      setLoading(false);
    }
  };

  const renderButtonContent = () => {
    if (loading) {
      return <LoadingSpinner className="w-36 m-auto" />;
    }
    if (success === true) {
      return <H5 className="text-white">Success ✓</H5>;
    }
    if (success === false) {
      return <H5 className="text-white">Failed ✗</H5>;
    }
    return <H5 className="text-white">Test Request</H5>;
  };

  return (
    <div className="flex flex-col w-full justify-center items-center">
      {url && (
        <div className="w-full flex flex-col mb-4">
          <H5 className="text-grey-800">URL</H5>
          <textarea
            className="w-full p-4 border justify-center align-center border-gray-700 rounded-md bg-gray-900 text-white font-mono text-sm"
            rows="2"
            readOnly
            value={url}
          />
        </div>
      )}
      {responseData && (
        <div className="w-full flex flex-col mb-4">
          <H5 className="text-grey-800">Result</H5>
          <textarea
            className="w-full p-4 border justify-center align-center border-gray-700 rounded-md bg-gray-900 text-white font-mono text-sm"
            rows="10"
            readOnly
            value={responseData}
          />
        </div>
      )}
      <button
        className={`w-full py-8 mb-16 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-base-purple focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
          ${
            loading
              ? "bg-gray-500"
              : success === true
              ? "bg-green-500"
              : success === false
              ? "bg-red-500"
              : "bg-black"
          }
          ${loading ? "cursor-not-allowed" : "cursor-pointer"}`}
        onClick={(e) => {
          e.preventDefault();
          if (validateForm() === true) {
            sendRequest();
          } else {
            onError(formErrors);
          }
        }}
        disabled={loading}
      >
        {renderButtonContent()}
      </button>
    </div>
  );
};

export default ApiRequestComponent;
