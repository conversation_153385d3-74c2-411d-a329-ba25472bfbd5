import React, { useState } from "react";
import history from '@history';
import {
	Box,
	IconButton,
	Typography,
	Container,
	Paper,
	Card,
	CardContent,
	List,
	ListItem,
	ListItemText,
	ListItemIcon,
	Divider,
	Tabs,
	Tab,
	Tooltip
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CodeSyntaxHighlighter from 'app/shared-components/code-syntax-highlighter/CodeSyntaxHighlighter';
import { aesEncrypt } from "src/app/utils/encryptions";
import { useSelector, useDispatch } from "react-redux";
import { selectUser } from "app/store/userSlice";
import { showMessage } from "app/store/fuse/messageSlice";
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord'; // For list bullets

// Helper component for Tab Panels
function TabPanel(props) {
	const { children, value, index, ...other } = props;
	return (
		<div
			role="tabpanel"
			hidden={value !== index}
			id={`integration-tabpanel-${index}`}
			aria-labelledby={`integration-tab-${index}`}
			{...other}
		>
			{value === index && (
				<Box sx={{ py: 2, px: 0.5 }}>
					{children}
				</Box>
			)}
		</div>
	);
}

export default function ChatBotIntegration() {
	const user = useSelector(selectUser);
	const dispatch = useDispatch();
	const [activeTab, setActiveTab] = useState(0);

	const handleTabChange = (event, newValue) => {
		setActiveTab(newValue);
	};

	const handleCopyToClipboard = (text, type) => {
		navigator.clipboard.writeText(text).then(() => {
			dispatch(showMessage({ message: `${type} code copied to clipboard!`, variant: 'success' }));
		}, (err) => {
			dispatch(showMessage({ message: `Failed to copy: ${err}`, variant: 'error' }));
		});
	};

	let kbIDParam = new URLSearchParams(location.search).get('kbID');
	let encryptedKbID = '';
	let encryptedOrgID = '';

	if (!kbIDParam) {
		console.error("ChatBotIntegration: kbID is missing from URL parameters.");
	} else {
		encryptedKbID = aesEncrypt(kbIDParam, process.env.REACT_APP_ENCRYPTION_KEY);
	}

	const orgIdFromUser = user?.data?.organization?.org_id;
	if (orgIdFromUser) {
		encryptedOrgID = aesEncrypt(orgIdFromUser, process.env.REACT_APP_ENCRYPTION_KEY);
	} else {
		console.error("ChatBotIntegration: orgID is missing from user data.");
	}

	const commonScriptAttributes =
		`orgid="${encryptedOrgID}"\n` +
		`\tchatbotid="${encryptedKbID}"\n` +
		`\tusethemeargs="false"\n` +
		`\tposition="right"\n` +
		`\tshowclosebutton="true"\n` +
		`\tshowtooltip="true"\n` +
		`\tisinitiallyopen="false"`;

	const tsxBotScriptDefinition =
		'~~~tsx\n' +
		'interface BotScriptProps extends React.DetailedHTMLProps<React.ScriptHTMLAttributes<HTMLScriptElement>, HTMLScriptElement> {\n' +
		'  orgid: string;\n' +
		'  chatbotid: string;\n' +
		'  usethemeargs: string;\n' +
		'  position: string;\n' +
		'  showclosebutton: string;\n' +
		'  showtooltip: string;\n' +
		'  isinitiallyopen: string;\n' +
		'}\n\n' +
		'const BotScript = (props: BotScriptProps) => (\n' +
		'  <script src="https://bot.talkbase.ai/chatbot.js" {...props} />\n' +
		');\n~~~';

	const tsxAppIntegration =
		'~~~tsx\n' +
		'// Your React component\n' +
		'export default function App() {\n' +
		'  return (\n' +
		'    <>\n' +
		'      {/* ... existing head elements or other components ... */}\n' +
		'      <link rel="stylesheet" href="https://bot.talkbase.ai/chatbot.css" />\n' +
		'      \n' +
		'      {/* ... rest of your application ... */}\n' +
		'      \n' +
		'      <BotScript \n' +
		`        ${commonScriptAttributes}\n` +
		'      />\n' +
		'      {/* Ensure BotScript is rendered within your main application structure */}\n' +
		'    </>\n' +
		'  );\n' +
		'}\n~~~';

	const nextJsBotScriptDefinition =
		'~~~tsx\n' +
		'import Script from \'next/script\';\n\n' +
		'interface BotScriptProps {\n' +
		'  orgid: string;\n' +
		'  chatbotid: string;\n' +
		'  usethemeargs: string;\n' +
		'  position: string;\n' +
		'  showclosebutton: string;\n' +
		'  showtooltip: string;\n' +
		'  isinitiallyopen: string;\n' +
		'  // Add any other specific props Next.js Script might need or you want to pass\n' +
		'}\n\n' +
		'const BotScript = (props: BotScriptProps) => (\n' +
		'  <Script \n' +
		'    src="https://bot.talkbase.ai/chatbot.js" \n' +
		'    orgid={props.orgid} \n' +
		'    chatbotid={props.chatbotid} \n' +
		'    usethemeargs={props.usethemeargs} \n' +
		'    position={props.position} \n' +
		'    showclosebutton={props.showclosebutton} \n' +
		'    showtooltip={props.showtooltip} \n' +
		'    isinitiallyopen={props.isinitiallyopen}\n' +
		'    strategy="lazyOnload" // Next.js specific prop\n' +
		'  />\n' +
		');\n~~~';

	const nextJsPageIntegration =
		'~~~tsx\n' +
		'import Head from \'next/head\';\n' +
		'// Assuming BotScript is imported from where you defined it\n' +
		'// import BotScript from \'../components/BotScript\'; \n\n' +
		'// ... inside your Next.js page component\n' +
		'export default function MyPage() {\n' +
		'  return (\n' +
		'    <>\n' +
		'      <Head>\n' +
		'        <link rel="stylesheet" href="https://bot.talkbase.ai/chatbot.css" />\n' +
		'      </Head>\n' +
		'      \n' +
		'      {/* ... rest of your page content ... */}\n' +
		'      \n' +
		'      <BotScript \n' +
		`        ${commonScriptAttributes}\n` +
		'      />\n' +
		'    </>\n' +
		'  );\n' +
		'}\n~~~';

	const plainJsIntegration =
		'~~~html\n' +
		'<link rel="stylesheet" href="https://bot.talkbase.ai/chatbot.css" />\n' +
		'<script src="https://bot.talkbase.ai/chatbot.js"\n' +
		`  ${commonScriptAttributes}\n` +
		'  defer\n' +
		'></script>\n~~~';

	const presetupInstructions = [
		"Create an organization in Ajentic.",
		"Create a Agent under AI Agents menu.",
		"Train your Agent with Website URL or pdf documents."
	];

	const wordpressInstructions = [
		"Add new plugin call 'WPCode Lite' (formally known as 'WP Header and Footer').",
		"Now you can see new menu option Code Snippets.",
		"Go to Code Snippets -> Header and Footer.",
		"Add below code to header section:"
	];

	const scriptAttributes = [
		{ name: "src", description: "Specifies the source URL of the chatbot script." },
		{ name: "orgid", description: "Your organization's unique identifier (Required)." },
		{ name: "chatbotid", description: "The unique identifier for your chatbot (Required)." },
		{ name: "usethemeargs", description: "Choose between default and custom theme for the chatbot's color scheme." },
		{ name: "position", description: "Choose the chatbot's position on the screen, either \"right\" (default), \"left\", or \"center\"." },
		{ name: "showclosebutton", description: "Set to \"true\" (default) or \"false\" to show or hide the chatbot's close button." },
		{ name: "showtooltip", description: "Set to \"true\" (default) or \"false\" to show or hide a tooltip explaining how to open the chatbot." },
		{ name: "isinitiallyopen", description: "Set to \"true\" to have the chatbot open automatically when the page loads, or \"false\" (default) to keep it closed initially." }
	];

	return (
		<Box sx={{ width: '100%', py: { xs: 2, sm: 3 }, px: { xs: 2, md: 3 } }}>
			<Box sx={{ display: 'flex', alignItems: 'center', mb: { xs: 2, sm: 3 }, cursor: 'pointer' }} onClick={() => history.back()}>
				<IconButton sx={{ p: 0.5, mr: 0.5 }}>
					<ArrowBackIcon />
				</IconButton>
				<Typography variant="h5" component="h1" sx={{ fontWeight: 'bold' }}>
					AI Agent Integration
				</Typography>
			</Box>

			<Container maxWidth="lg" sx={{ px: { xs: 0, sm: 1 } }}>
				<Paper elevation={2} sx={{ p: { xs: 2, sm: 3 }, mb: { xs: 3, sm: 4 }, borderRadius: 2 }}>
					<Typography variant="h6" component="h3" gutterBottom sx={{ fontWeight: 500 }}>
						Prerequisites
					</Typography>
					<List dense>
						{presetupInstructions.map((text, index) => (
							<ListItem key={index} sx={{ py: 0.5, pl: 1 }}>
								<ListItemIcon sx={{ minWidth: 'auto', mr: 1.5, color: '#000000' }}>
									<FiberManualRecordIcon sx={{ fontSize: '0.5rem' }} />
								</ListItemIcon>
								<ListItemText primary={<Typography variant="body2" color="text.primary">{text}</Typography>} />
							</ListItem>
						))}
					</List>
				</Paper>

				<Typography variant="h5" component="h2" gutterBottom sx={{ mt: { xs: 3, sm: 4 }, mb: 2, fontWeight: 500 }}>
					Getting Started with Script Injection
				</Typography>

				<Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
					<Tabs value={activeTab} onChange={handleTabChange} aria-label="Integration method tabs" indicatorColor="#7C53FE" sx={{ minHeight: '40px' }}>
						<Tab label="TypeScript" id="integration-tab-0" aria-controls="integration-tabpanel-0" sx={{ color: '#000000', '&.Mui-selected': { color: '#7C53FE' }, textTransform: 'none', fontSize: '1.3rem', minHeight: '40px' }} />
						<Tab label="Next.js (SSR)" id="integration-tab-1" aria-controls="integration-tabpanel-1" sx={{ color: '#000000', '&.Mui-selected': { color: '#7C53FE' }, textTransform: 'none', fontSize: '1.3rem', minHeight: '40px' }} />
						<Tab label="JavaScript" id="integration-tab-2" aria-controls="integration-tabpanel-2" sx={{ color: '#000000', '&.Mui-selected': { color: '#7C53FE' }, textTransform: 'none', fontSize: '1.3rem', minHeight: '40px' }} />
						<Tab label="WordPress" id="integration-tab-3" aria-controls="integration-tabpanel-3" sx={{ color: '#000000', '&.Mui-selected': { color: '#7C53FE' }, textTransform: 'none', fontSize: '1.3rem', minHeight: '40px' }} />
					</Tabs>
				</Box>

				<TabPanel value={activeTab} index={0}>
					<Card variant="outlined" sx={{ borderRadius: 2, boxShadow: 'none' }}>
						<CardContent sx={{ pt: 1.5 }}>
							<Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 500, color: 'text.primary' }}>
								1. Add the BotScript component definition:
							</Typography>
							<Box sx={{ position: 'relative' }}>
								<CodeSyntaxHighlighter answer={tsxBotScriptDefinition} />
								<Tooltip title="Copy code">
									<IconButton onClick={() => handleCopyToClipboard(tsxBotScriptDefinition, 'TypeScript BotScript definition')} sx={{ position: 'absolute', top: 8, right: 8, color: 'grey.700' }}>
										<ContentCopyIcon fontSize="small" />
									</IconButton>
								</Tooltip>
							</Box>
							<Typography variant="subtitle1" sx={{ mt: 2.5, mb: 1, fontWeight: 500, color: 'text.primary' }}>
								2. Include the stylesheet and BotScript component in your application:
							</Typography>
							<Box sx={{ position: 'relative' }}>
								<CodeSyntaxHighlighter answer={tsxAppIntegration} />
								<Tooltip title="Copy code">
									<IconButton onClick={() => handleCopyToClipboard(tsxAppIntegration, 'TypeScript App integration')} sx={{ position: 'absolute', top: 8, right: 8, color: 'grey.700' }}>
										<ContentCopyIcon fontSize="small" />
									</IconButton>
								</Tooltip>
							</Box>
						</CardContent>
					</Card>
				</TabPanel>

				<TabPanel value={activeTab} index={1}>
					<Card variant="outlined" sx={{ borderRadius: 2, boxShadow: 'none' }}>
						<CardContent sx={{ pt: 1.5 }}>
							<Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 500, color: 'text.primary' }}>
								1. Add the BotScript component definition:
							</Typography>
							<Box sx={{ position: 'relative' }}>
								<CodeSyntaxHighlighter answer={nextJsBotScriptDefinition} />
								<Tooltip title="Copy code">
									<IconButton onClick={() => handleCopyToClipboard(nextJsBotScriptDefinition, 'Next.js BotScript definition')} sx={{ position: 'absolute', top: 8, right: 8, color: 'grey.700' }}>
										<ContentCopyIcon fontSize="small" />
									</IconButton>
								</Tooltip>
							</Box>
							<Typography variant="subtitle1" sx={{ mt: 2.5, mb: 1, fontWeight: 500, color: 'text.primary' }}>
								2. Include the stylesheet and BotScript component in your Next.js page or layout:
							</Typography>
							<Box sx={{ position: 'relative' }}>
								<CodeSyntaxHighlighter answer={nextJsPageIntegration} />
								<Tooltip title="Copy code">
									<IconButton onClick={() => handleCopyToClipboard(nextJsPageIntegration, 'Next.js Page integration')} sx={{ position: 'absolute', top: 8, right: 8, color: 'grey.700' }}>
										<ContentCopyIcon fontSize="small" />
									</IconButton>
								</Tooltip>
							</Box>
						</CardContent>
					</Card>
				</TabPanel>

				<TabPanel value={activeTab} index={2}>
					<Card variant="outlined" sx={{ borderRadius: 2, boxShadow: 'none' }}>
						<CardContent sx={{ pt: 1.5 }}>
							<Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 500, color: 'text.primary' }}>
								Copy and paste the following code snippet into your main HTML file, preferably before the closing <code>&lt;/body&gt;</code> tag:
							</Typography>
							<Box sx={{ position: 'relative' }}>
								<CodeSyntaxHighlighter answer={plainJsIntegration} />
								<Tooltip title="Copy code">
									<IconButton onClick={() => handleCopyToClipboard(plainJsIntegration, 'JavaScript integration')} sx={{ position: 'absolute', top: 8, right: 8, color: 'grey.700' }}>
										<ContentCopyIcon fontSize="small" />
									</IconButton>
								</Tooltip>
							</Box>
						</CardContent>
					</Card>
				</TabPanel>

				<TabPanel value={activeTab} index={3}>
					<Card variant="outlined" sx={{ borderRadius: 2, boxShadow: 'none' }}>
						<CardContent sx={{ pt: 1.5 }}>
							<List dense sx={{ mb: 1.5 }}>
								{wordpressInstructions.map((text, index) => (
									<ListItem key={index} sx={{ py: 0.25, pl: 1 }}>
										<ListItemIcon sx={{ minWidth: 'auto', mr: 1.5, color: 'text.primary' }}>
											<FiberManualRecordIcon sx={{ fontSize: '0.5rem' }} />
										</ListItemIcon>
										<ListItemText primary={<Typography variant="body2" color="text.primary">{text}</Typography>} />
									</ListItem>
								))}
							</List>
							<Box sx={{ position: 'relative' }}>
								<CodeSyntaxHighlighter answer={plainJsIntegration} />
								<Tooltip title="Copy code">
									<IconButton onClick={() => handleCopyToClipboard(plainJsIntegration, 'WordPress integration')} sx={{ position: 'absolute', top: 8, right: 8, color: 'grey.700' }}>
										<ContentCopyIcon fontSize="small" />
									</IconButton>
								</Tooltip>
							</Box>
						</CardContent>
					</Card>
				</TabPanel>

				<Divider sx={{ my: { xs: 3, sm: 4 } }} />

				<Paper elevation={0} sx={{ p: { xs: 2, sm: 3 }, mt: { xs: 3, sm: 4 }, borderRadius: 2, border: '1px solid', borderColor: 'divider' }}>
					<Typography variant="h6" component="h3" gutterBottom sx={{ fontWeight: 500 }}>
						Script Attributes Explained
					</Typography>
					<List>
						{scriptAttributes.map((attr, index) => (
							<ListItem key={index} sx={{ py: 1, px: 1, alignItems: 'flex-start' }}>
								<ListItemIcon sx={{ minWidth: 'auto', mr: 1.5, mt: 0.7, color: '#7C53FE' }}>
									<FiberManualRecordIcon sx={{ fontSize: '0.6rem' }} />
								</ListItemIcon>
								<ListItemText
									primary={<Typography component="span" sx={{ fontWeight: 'bold' }} color="text.primary">{attr.name}:</Typography>}
									secondary={<Typography component="span" variant="body2" color="text.primary" sx={{ display: 'block', mt: 0.25 }}>{attr.description}</Typography>}
								/>
							</ListItem>
						))}
					</List>
				</Paper>
			</Container>
		</Box>
	);
}
