
FROM node:16-alpine as build
# Installing libvips-dev for sharp Compatibility
RUN apk update && apk add --no-cache build-base gcc autoconf automake zlib-dev libpng-dev vips-dev && rm -rf /var/cache/apk/* > /dev/null 2>&1
ENV NODE_ENV=production
ENV STRAPI_ADMIN_BACKEND_URL=https://api.podyc.com
ENV TALKBASE_BASE_URL=https://py-api.podyc.com

WORKDIR /opt/
COPY ./package.json ./
ENV PATH /opt/node_modules/.bin:$PATH
RUN npm install --production
WORKDIR /opt/app
COPY ./ .
RUN npm run build


FROM node:16-alpine
# Installing libvips-dev for sharp Compatibility
RUN apk add vips-dev
RUN rm -rf /var/cache/apk/*
ENV NODE_ENV=production

ENV STRAPI_ADMIN_BACKEND_URL=https://api.podyc.com
ENV TALKBASE_BASE_URL=https://py-api.podyc.com

# ENV STRAPI_ADMIN_BACKEND_URL=https://auth-server-future.talkbase.ai
# ENV TALKBASE_BASE_URL=https://api-future.talkbase.ai


WORKDIR /opt/app
COPY --from=build /opt/node_modules ./node_modules
ENV PATH /opt/node_modules/.bin:$PATH
COPY --from=build /opt/app ./
EXPOSE 1337
