import React, { useState } from "react";
import FusePageSimple from "@fuse/core/FusePageSimple";
const TabView = ({ tabs }) => {
  const [activeTab, setActiveTab] = useState(0);

  return (
    <FusePageSimple
      content={
        <div className="w-full ">
          <div className="flex border-b border-gray-200">
            {tabs.map((tab, index) => (
              <button
                key={index}
                className={`flex items-center px-16 py-16 text-md font-medium ${
                  activeTab === index
                    ? "text-base-purple border-b-2 border-base-purple"
                    : "text-gray-500 hover:text-gray-700"
                }`}
                onClick={() => setActiveTab(index)}
              >
                {tab.icon && <tab.icon className="w-16 h-16 mr-8" />}
                {tab.label}
              </button>
            ))}
          </div>
          <div className="mt-4">{tabs[activeTab].content}</div>
        </div>
      }
    />
  );
};

export default TabView;
