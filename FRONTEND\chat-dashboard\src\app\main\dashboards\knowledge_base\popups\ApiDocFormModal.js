import React from "react";
import ApiDocForm from "src/app/main/dashboards/knowledge_base/lead-form/ApiDocForm";
import { Dialog } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import {  DialogContent } from "@mui/material";
function ApiDocFormModal({ kbId, apiTool, open , onClose, onComplete, editMode = false }) {

  return (
    <Dialog
      open={open}
      title="Add Source code"
      sx={{
        "& .MuiDialog-container": {
          "& .MuiPaper-root": {
            width: "100%",
            maxWidth: "800px",
            maxHeight: "90%",
            borderRadius: "12px",
          },
        },
      }}
    >
      <div className="flex flex-row w-full items-end justify-end mt-16">
        <CloseIcon
          className=" text-black mr-16 hover:cursor-pointer"
          onClick={() => {
            onClose();
          }}
        />
      </div>
      <DialogContent
        className="mx-0 mb-16"
        style={{
          padding: "0px",
          margin: "0px",
        }}
      >
        <ApiDocForm
          editMode={editMode}
          kbId={kbId}
          apiToolData={apiTool}
          onComplete={() => {
            onComplete();
          }}
          onFailure={() => {
            onClose();
          }}
        />
      </DialogContent>
    </Dialog>
  );
}

export default ApiDocFormModal;
