'use strict';

/**
 * Template Renderer Service
 * Handles rendering of HTML templates with variable substitution
 */

class TemplateRenderer {
  
  /**
   * Render template with variables
   * @param {string} template - HTML template string
   * @param {Object} variables - Variables to substitute
   * @returns {string} - Rendered HTML
   */
  static renderTemplate(template, variables = {}) {
    if (!template) return '';
    
    let rendered = template;
    
    // Handle different template syntax patterns
    // {{variable}} - Standard handlebars style
    // {variable} - Simple brackets
    // %variable% - Percentage style
    
    Object.keys(variables).forEach(key => {
      const value = variables[key] || '';
      
      // Replace various patterns
      const patterns = [
        new RegExp(`{{\\s*${key}\\s*}}`, 'g'),
        new RegExp(`{\\s*${key}\\s*}`, 'g'),
        new RegExp(`%${key}%`, 'g')
      ];
      
      patterns.forEach(pattern => {
        rendered = rendered.replace(pattern, value);
      });
    });
    
    return rendered;
  }
  
  /**
   * Render template with advanced features (conditional blocks, loops)
   * @param {string} template - HTML template string
   * @param {Object} variables - Variables to substitute
   * @returns {string} - Rendered HTML
   */
  static renderAdvancedTemplate(template, variables = {}) {
    if (!template) return '';
    
    let rendered = template;
    
    // Handle conditional blocks: {{#if variable}}content{{/if}}
    rendered = this.processConditionals(rendered, variables);
    
    // Handle loops: {{#each items}}{{name}}{{/each}}
    rendered = this.processLoops(rendered, variables);
    
    // Handle basic variable substitution
    rendered = this.renderTemplate(rendered, variables);
    
    return rendered;
  }
  
  /**
   * Process conditional blocks in template
   * @param {string} template - Template string
   * @param {Object} variables - Variables
   * @returns {string} - Processed template
   */
  static processConditionals(template, variables) {
    // Handle {{#if variable}}content{{/if}}
    const ifRegex = /{{#if\s+(\w+)}}(.*?){{\/if}}/gs;
    
    return template.replace(ifRegex, (match, variable, content) => {
      const value = variables[variable];
      return (value && value !== 'false' && value !== '0') ? content : '';
    });
  }
  
  /**
   * Process loop blocks in template
   * @param {string} template - Template string
   * @param {Object} variables - Variables
   * @returns {string} - Processed template
   */
  static processLoops(template, variables) {
    // Handle {{#each arrayVariable}}{{property}}{{/each}}
    const eachRegex = /{{#each\s+(\w+)}}(.*?){{\/each}}/gs;
    
    return template.replace(eachRegex, (match, arrayName, itemTemplate) => {
      const array = variables[arrayName];
      if (!Array.isArray(array)) return '';
      
      return array.map(item => {
        return this.renderTemplate(itemTemplate, item);
      }).join('');
    });
  }
  
  /**
   * Escape HTML characters in variables
   * @param {Object} variables - Variables to escape
   * @returns {Object} - Escaped variables
   */
  static escapeHtmlVariables(variables) {
    const escaped = {};
    
    Object.keys(variables).forEach(key => {
      const value = variables[key];
      if (typeof value === 'string') {
        escaped[key] = value
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#39;');
      } else {
        escaped[key] = value;
      }
    });
    
    return escaped;
  }
  
  /**
   * Validate template syntax
   * @param {string} template - Template to validate
   * @returns {Object} - Validation result
   */
  static validateTemplate(template) {
    if (!template) {
      return { valid: false, errors: ['Template is empty'] };
    }
    
    const errors = [];
    
    // Check for unmatched handlebars
    const openBraces = (template.match(/{{/g) || []).length;
    const closeBraces = (template.match(/}}/g) || []).length;
    
    if (openBraces !== closeBraces) {
      errors.push('Unmatched handlebars braces');
    }
    
    // Check for unmatched conditionals
    const ifBlocks = (template.match(/{{#if/g) || []).length;
    const endIfBlocks = (template.match(/{{\/if}}/g) || []).length;
    
    if (ifBlocks !== endIfBlocks) {
      errors.push('Unmatched if/endif blocks');
    }
    
    // Check for unmatched loops
    const eachBlocks = (template.match(/{{#each/g) || []).length;
    const endEachBlocks = (template.match(/{{\/each}}/g) || []).length;
    
    if (eachBlocks !== endEachBlocks) {
      errors.push('Unmatched each/endeach blocks');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Extract variables from template
   * @param {string} template - Template to analyze
   * @returns {Array} - Array of variable names found in template
   */
  static extractVariables(template) {
    if (!template) return [];
    
    const variables = new Set();
    
    // Extract {{variable}} patterns
    const handlebarsRegex = /{{(?!#|\/)\s*(\w+)\s*}}/g;
    let match;
    
    while ((match = handlebarsRegex.exec(template)) !== null) {
      variables.add(match[1]);
    }
    
    // Extract {variable} patterns
    const simpleRegex = /{(?!{)\s*(\w+)\s*}/g;
    while ((match = simpleRegex.exec(template)) !== null) {
      variables.add(match[1]);
    }
    
    // Extract %variable% patterns
    const percentRegex = /%(\w+)%/g;
    while ((match = percentRegex.exec(template)) !== null) {
      variables.add(match[1]);
    }
    
    return Array.from(variables);
  }
  
  /**
   * Preview template with sample data
   * @param {string} template - Template to preview
   * @param {Object} sampleData - Sample data for preview
   * @returns {string} - Rendered preview
   */
  static previewTemplate(template, sampleData = {}) {
    const variables = this.extractVariables(template);
    
    // Create sample data for missing variables
    const previewData = { ...sampleData };
    variables.forEach(variable => {
      if (!(variable in previewData)) {
        previewData[variable] = `[${variable}]`;
      }
    });
    
    return this.renderAdvancedTemplate(template, previewData);
  }
}

module.exports = TemplateRenderer;
