const { createCoreController } = require("@strapi/strapi").factories;
const axios = require("axios");

module.exports = createCoreController(
  "api::knowledgebase.knowledgebase",
  ({ strapi }) => ({
    async kbSource(ctx) {
      try {
        let result = await axios
          .get(
            `${process.env.TALKBASE_BASE_URL}/v4/kb_sources?page_num=${ctx.query.page_num}&page_size=${ctx.query.page_size}&kb_id=${ctx.query.kb_id}`
          )
          .catch((err) => {
            console.log(err);
          });
        return result.data;
      } catch (err) {
        return ctx.throw(500, "Server error");
      }
    },

    async deleteKbSource(ctx) {
      try {
        const data = {
          org_id: ctx.state.user.organization.id,
          kb_id: ctx.params.id,
        };
        let result = await axios
          .delete(`${process.env.TALKBASE_BASE_URL}/v4/empty_kb`, data, {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          })
          .catch((err) => {
            console.log(err);
          });
        return result.data;
      } catch (err) {
        return ctx.throw(500, "Server error");
      }
    },
    async whatsappUrl(ctx) {
      try {
        const result = await strapi.db
          .query("api::knowledgebase.knowledgebase")
          .findOne({
            where: {
              kb_id: ctx.request.body.chatbot,
            },
            populate: {
              whatsapp_integration_model: true,
            },
          });
        if (result && result.whatsapp_integration_model) {
          return `https://wa.me/${result.whatsapp_integration_model.phone_number}`;
        } else {
          return ctx.throw(
            500,
            "No whatsapp number available for this chatbot"
          );
        }
      } catch (e) {
        return ctx.throw(500, e?.message);
      }
    },

    async publicKb(ctx) {
      try {
        const {
          page = 1,
          pageSize = 10,
          populate_file_results_model = false,
        } = ctx.query;

        const queryOptions = {
          where: {
            public: true,
          },
          start: (page - 1) * pageSize,
          limit: pageSize,
        };

        // Add populate option if requested
        if (populate_file_results_model === "true") {
          queryOptions.populate = {
            file_results_models: true,
          };
        }

        const result = await strapi.db
          .query("api::knowledgebase.knowledgebase")
          .findMany(queryOptions);

        const total = await strapi.db
          .query("api::knowledgebase.knowledgebase")
          .count({
            where: {
              public: true,
            },
          });

        return {
          data: result,
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            total,
            pageCount: Math.ceil(total / pageSize),
          },
        };
      } catch (e) {
        return ctx.throw(500, e?.message);
      }
    },
	 async getFileResultsModel(ctx) {
		try {
			const {
				page = 1,
				pageSize = 10,
			} = ctx.query;

			// First check if the knowledgebase exists and is public
			const knowledgebase = await strapi.db.query('api::knowledgebase.knowledgebase').findOne({
				where: {
					id: ctx.params.id,
					public: true
				}
			});

			if (!knowledgebase) {
				return ctx.throw(404, 'Knowledgebase not found or is not public');
			}

			// Get paginated file results
			const fileResults = await strapi.db.query('api::file-results-model.file-results-model').findMany({
				where: {
					knowledgebase: ctx.params.id
				},
				start: (page - 1) * pageSize,
				limit: parseInt(pageSize),
			});

			// Get total count for pagination
			const total = await strapi.db.query('api::file-results-model.file-results-model').count({
				where: {
					knowledgebase: ctx.params.id
				}
			});

			return {
				data: fileResults,
				pagination: {
					page: parseInt(page),
					pageSize: parseInt(pageSize),
					total,
					pageCount: Math.ceil(total / pageSize),
				}
			};
		} catch (e) {
			return ctx.throw(500, e?.message);
		}
	},
  })

);
