"use strict";

/**
 * organization controller
 */

const { createCoreController } = require("@strapi/strapi").factories;
const axios = require("axios");
module.exports = createCoreController(
  "api::organization.organization",
  ({ strapi }) => ({
    async create(ctx) {
      const result = await axios
        .post(
          `${process.env.TALKBASE_BASE_URL}/v4/add_org`,
          { org_name: ctx.request.body.name },
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        )
        .catch((err) => {
          throw new Error(
            "Failed to create organization. Please try again later. " +
              err.message
          );
        });
      ctx.request.body.data = {
        ...ctx.request.body,
        type:
          ctx.request.body.name === "individual"
            ? "individual"
            : "organization",
        org_id: result.data.id,
        usage_quota: 20 / 2,
        users_limit: 10,
        tokens_used: result.data.tokens_used,
        paid: 20,
        cost_used: result.data.cost,
      };
      // some logic here
      const org = await super.create(ctx);

      // const kbCount = await strapi
      //   .query("api::knowledgebase.knowledgebase")
      //   .findMany({
      //     where: {
      //         organization: org.data.id,
      //     },
      //   });

      // if (kbCount.length > 0) {
      //   return org.data;
      // }

      // var data = {
      //   org_id: org.data.attributes.org_id,
      //   kb_type: "multiAgent",
      //   kb_name: "PlayList",
      //   openAI_assistant_id: "",
      //   vs_source: "supabase",
      //   vs_table_name: "documents",
      // };

      // // Make a POST request to the external API with the request body
      // const response = await axios
      //   .post(`${process.env.TALKBASE_BASE_URL}/v4/add_kb`, data, {
      //     headers: {
      //       "Content-Type": "application/x-www-form-urlencoded",
      //     },
      //   })
      //   .catch((e) => {
      //     console.log(e);
      //   });

      // if (response.data?.status === "failure") {
      //   return ctx.badRequest(response.data.error ?? "Something went wrong");
      // }

      // const organization = await strapi
      //   .query("api::organization.organization")
      //   .findOne({
      //     where: { id: org.data.id },
      //     populate: ["monthly_usages"],
      //   });

      // await strapi.query("api::monthly-usage.monthly-usage").update({
      //   where: {
      //     id: organization.monthly_usages[
      //       organization.monthly_usages.length - 1
      //     ].id,
      //   },
      //   data: {
      //     kb_count:
      //       organization.monthly_usages[organization.monthly_usages.length - 1]
      //         .kb_count + 1,
      //   },
      // });

      // ctx.request.body.data = {
      //   data: {
      //     name: response.data.name,
      //     organization: org.data.id,
      //     kb_id: response.data.id,
      //     type: response.data.kb_type,
      //     default_ai_task: 1,
      //     publishedAt: new Date(),
      //   },
      // };
      // const kb = await strapi
      //   .query("api::knowledgebase.knowledgebase")
      //   .create(ctx.request.body.data);

      // var finalResult = org.data;
      // finalResult["kb"] = kb;

      return org;
    },
    async find(ctx, next) {
      const response = await super.find(ctx);
      return response;
    },
  })
);
