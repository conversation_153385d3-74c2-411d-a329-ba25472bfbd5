'use strict';

/**
 * updates owner property with organisation policy
 */
module.exports = async (policyContext, config, { strapi }) => {
	console.log(policyContext);
	if(!policyContext.request.body?.data?.organization){

	if (policyContext.state.user) {
		const populatedUser = await strapi.query('plugin::users-permissions.user').findOne({
			where: { id: policyContext.state.user.id },
			populate: { organization:true},
		}).catch(err => {
			console.log(err);
		});
		if (!populatedUser.organization) {
			console.log("User doesn't have organization");
			const customError = new Error("User doesn't have organization");
			customError.status = 400; // Set the desired status code
			throw customError;
			
		}
		policyContext.state.user.organization = populatedUser.organization;
	}
	policyContext.request.body.data['organization']=policyContext.state.user.organization.id;
}
	return true;
}