"use strict";

/**
 * answer controller
 */

const { createCoreController } = require("@strapi/strapi").factories;
const axios = require("axios");
const knowledgebase = require("../../knowledgebase/controllers/knowledgebase");
const { isYouTubeUrl } = require("../../../helpers/utils");

module.exports = createCoreController("api::train.train", ({ strapi }) => ({
  async linkExtract(ctx) {
    try {
      let result = await axios
        .post(
          `${process.env.TALKBASE_BASE_URL}/v4/link_extract`,
          { url: ctx.request.body.url },
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        )
        .catch((err) => {
          console.log(err);
        });
      return result.data;
    } catch (err) {
      return ctx.throw(500, "Server error");
    }
  },
  async train(ctx) {
    try {
      let datasource;
      var didCreateNewDatasource = false;
      datasource = await strapi.query("api::datasource.datasource").findOne({
        where: { knowledgebase: ctx.request.body.kbr_id },
      });
      if (datasource === null) {
        datasource = await strapi.query("api::datasource.datasource").create({
          data: {
            name: "Datasource #1",
            type: "vectorstore",
            knowledgebase: ctx.request.body.kbr_id,
          },
        });
        didCreateNewDatasource = true;
      }
      var urls_docid_map = {};
      var document_models = [];

      for (let i = 0; i < ctx.request.body.urls.length; i++) {
        let document_model = await strapi
          .query("api::document.document")
          .create({
            data: {
              document_name: ctx.request.body.urls[i],
              document_type: isYouTubeUrl(ctx.request.body.urls[i]) ? 'youtube' : 'url',
              datasource: datasource.id,
              status: "processing",
            },
          });
        urls_docid_map[ctx.request.body.urls[i]] = document_model.id;
        document_models.push(document_model);
        console.log("document_model", document_model);
      }

      let result = await axios
        .post(
          `${process.env.TALKBASE_BASE_URL}/v4/train`,
          {
            urls_docid_map:urls_docid_map,
            kb_id: ctx.request.body.kb_id,
            org_id: ctx.state.user.organization?.org_id,
			      usage_id : ctx.state.user.organization.current_month_usage.id,
            remaining_quota:
              ctx.state.user.organization.plan.allowed_training_tokens -
              ctx.state.user.organization?.current_month_usage
                ?.training_tokens_count,
          },
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        )
        .catch((err) => {
          console.log(err);
        });
      await strapi.query("api::monthly-usage.monthly-usage").update({
        where: { id: ctx.state.user.organization.current_month_usage.id },
        data: {
          trained_count:
            ctx.state.user.organization.current_month_usage.trained_count + 1,
        },
      });
      return {
        py_result: result.data,
        documents: document_models,
        did_create_datasource: didCreateNewDatasource,
        ...(didCreateNewDatasource
          ? {
              datasource: {
                id: datasource.id,
                name: datasource.name,
                type: datasource.type,
                knowledgebase: datasource.knowledgebase,
              },
            }
          : {}),
      };
    } catch (err) {
      return ctx.throw(500, "Server error" + err.message);
    }
  },
}));
