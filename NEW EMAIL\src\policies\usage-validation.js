"use strict";

/**
 * Validationpolicy
 */

const { errors } = require("@strapi/utils");
const { PolicyError } = errors;

module.exports = async (policyContext, config, { strapi }) => {
  try {
    console.log("global:usage-validation called for path:", policyContext.routerPath);
    const userOrg = policyContext.state.user.organization;

    if (!userOrg || !userOrg.id) {
        strapi.log.error(`Usage Validation: User ${policyContext.state.user.id} has no associated organization.`);
        throw new PolicyError("User organization not found.", 400);
    }

    switch (policyContext.routerPath) {
      case "/api/multi-agent":
        // Fetch organization details needed for total credit calculation using db.query
        const organization = await strapi.db.query('api::organization.organization').findOne({
            where: { id: userOrg.id }, // Specify the where clause
            populate: { credit_balance: true, monthly_usages: true, plan: true },
        });

        if (!organization) {
             strapi.log.error(`Usage Validation: Organization ${userOrg.id} not found for user ${policyContext.state.user.id}.`);
             // Fallback to original check if full organization data isn't available
             const fallbackMonthlyUsage = userOrg.current_month_usage;
             if (!fallbackMonthlyUsage || (parseInt(fallbackMonthlyUsage.query_count || 0) >= parseInt(fallbackMonthlyUsage.usage_quota || 0))) {
                 throw new PolicyError("Usage quota exceeded (fallback check).", 402, { error_code: 'QUOTA_EXCEEDED_FALLBACK' });
             }
             strapi.log.warn(`Usage Validation: Could not fetch full org details for ${userOrg.id}, using fallback checks. Could not verify one-time credits.`);
             break; 
        }

        // --- Find the CURRENT monthly usage record from the array --- 
        const now = new Date();
        const currentMonthlyUsageRecord = organization.monthly_usages?.find(usage => {
            // Ensure period_start and period_end exist and are valid dates
            const startDate = usage && usage.period_start ? new Date(usage.period_start) : null;
            const endDate = usage && usage.period_end ? new Date(usage.period_end) : null;
            return startDate instanceof Date && !isNaN(startDate) && 
                   endDate instanceof Date && !isNaN(endDate) && 
                   startDate <= now && now < endDate;
        }) || null; // Default to null if not found or array is empty/null
        // ---------------------------------------------------------------

        // Use the found record (or null) in subsequent logic
        const creditBalance = organization.credit_balance; // Might be null
        const plan = organization.plan; // Might be null

        // Calculate period credits remaining (handles cases where currentMonthlyUsageRecord might be null)
        const periodQuota = Number(currentMonthlyUsageRecord?.usage_quota || plan?.allowed_credits || 0);
        // Check both potential fields for usage tracking in monthly usage
        const periodUsed = Number(currentMonthlyUsageRecord?.query_count || currentMonthlyUsageRecord?.credits_used || 0); 
        const periodCreditsRemaining = currentMonthlyUsageRecord ? Math.max(0, periodQuota - periodUsed) : 0;

        // Calculate one-time credits available (handles cases where balance might be null)
        const oneTimeCreditsAvailable = creditBalance ? Number(creditBalance.available_credits || 0) : 0;

        // Calculate total available credits
        const totalAvailableCredits = periodCreditsRemaining + oneTimeCreditsAvailable;

        strapi.log.info(`Usage Validation (/multi-agent) for Org ${organization.id}: Period Remaining=${periodCreditsRemaining}, One-Time Available=${oneTimeCreditsAvailable}, Total Available=${totalAvailableCredits}`);

        // Check if total available credits are sufficient (assuming cost is 1)
        if (totalAvailableCredits < 1) {
             throw new PolicyError("Insufficient total available credits for this request.", 402, {
                 error_code: 'INSUFFICIENT_CREDITS',
                 details: {
                     period_remaining: periodCreditsRemaining,
                     one_time_available: oneTimeCreditsAvailable,
                     total_available: totalAvailableCredits,
                     required: 1 // Indicate required credits
                 }
             });
        }

        // If sufficient credits, allow the request to proceed implicitly
        break;

      case "/api/answers":
      case "/api/runanswer":
        if (
          parseInt(
            policyContext.state.user.organization.current_month_usage
              .credits_used
          ) >=
          parseInt(policyContext.state.user.organization.plan.allowed_credits)
        ) {
          throw new PolicyError("You have exceeded the query limit");
        }
        break;
      case "/api/askquestion":
        if (
          parseInt(
            policyContext.state.user.organization.current_month_usage
              .credits_used
          ) >=
          parseInt(policyContext.state.user.organization.plan.allowed_credits)
        ) {
          throw new PolicyError("You have exceeded the query limit");
        }
        break;
      case "/api/trains":
      case "/api/code-trains":
      case "/api/file-trains":
      case "/api/v3/train":
        if (
          parseInt(
            policyContext.state.user.organization.current_month_usage
              .training_tokens_count
          ) >=
          parseInt(
            policyContext.state.user.organization.plan.allowed_training_tokens
          )
        ) {
          throw new PolicyError("You have exceeded the usage limit");
        }

        break;

      case "/api/knowledgebases":
        const [kbs, kbCount] = await strapi
          .query("api::knowledgebase.knowledgebase")
          .findWithCount({
            where: {
              organization: policyContext.state.user.organization.id,
              publishedAt: {
                $notNull: true,
              },
            },
          });
        if (kbCount >= policyContext.state.user.organization.plan.allowed_kbs) {
          throw new PolicyError("You have exceeded the Chatbot limit");
        }
        break;

      default:
        break;
    }
  } catch (error) {
    // Log the error before re-throwing if it's not a PolicyError we threw intentionally
    if (!(error instanceof PolicyError)) {
        strapi.log.error(`Unexpected error in usage-validation policy: ${error.message}`, error.stack);
    }
    // Re-throw the error to be handled by Strapi's error handling middleware
    throw error;
  }
  // If no error was thrown, the policy passes
  return true;
};
