/**
 * Optimized Email Service with External Templates
 * Uses Strapi's built-in email with JSON template files
 */

const fs = require('fs');
const path = require('path');

// Template cache for performance
const templateCache = new Map();

/**
 * Load and cache email template from JSON file
 */
function loadTemplate(templateName) {
  // Check cache first
  if (templateCache.has(templateName)) {
    return templateCache.get(templateName);
  }

  try {
    const templatePath = path.join(__dirname, '../email-templates', `${templateName}.json`);

    if (!fs.existsSync(templatePath)) {
      throw new Error(`Template file not found: ${templateName}.json`);
    }

    const templateData = JSON.parse(fs.readFileSync(templatePath, 'utf8'));

    // Cache the template
    templateCache.set(templateName, templateData);

    strapi.log.info(`📧 Template loaded: ${templateName}`);
    return templateData;
  } catch (error) {
    strapi.log.error(`❌ Failed to load template ${templateName}:`, error.message);
    throw error;
  }
}

/**
 * Render template with variables
 */
function renderTemplate(template, variables = {}) {
  // Merge template variables with provided variables
  const allVariables = {
    ...template.variables,
    ...variables
  };

  let html = template.html_content;
  let text = template.text_content;
  let subject = template.subject;

  // Replace all variables in content
  Object.keys(allVariables).forEach(key => {
    const value = allVariables[key] || '';
    const regex = new RegExp(`{{${key}}}`, 'g');
    html = html.replace(regex, value);
    text = text.replace(regex, value);
    subject = subject.replace(regex, value);
  });

  return {
    html,
    text,
    subject,
    from: `${template.default_from_name} <${template.default_from_email}>`
  };
}

/**
 * Clear template cache (useful for development)
 */
function clearTemplateCache() {
  templateCache.clear();
  strapi.log.info('📧 Template cache cleared');
}

/**
 * Get priority color for email styling
 */
function getPriorityColor(priority) {
  if (!priority) return '#666666';

  const priorityLower = priority.toLowerCase();
  if (priorityLower.includes('high') || priorityLower.includes('urgent')) return '#DC2626';
  if (priorityLower.includes('medium') || priorityLower.includes('normal')) return '#F59E0B';
  if (priorityLower.includes('low')) return '#10B981';
  return '#666666'; // Default gray
}

module.exports = {
  async sendConfirmationEmail({ email, name, confirmationUrl }) {
    try {
      const template = loadTemplate('confirmation-email');
      const { html, text, subject, from } = renderTemplate(template, {
        name,
        action_url: confirmationUrl
      });

      await strapi.plugins.email.services.email.send({
        to: email,
        subject,
        html,
        text,
        from
      });

      strapi.log.info('✅ Confirmation email sent:', { email, template: template.name });
    } catch (error) {
      strapi.log.error('❌ Confirmation email failed:', error.message);
      throw error;
    }
  },

  async sendWelcomeEmail({ email, name }) {
    try {
      const template = loadTemplate('welcome-email');
      const { html, text, subject, from } = renderTemplate(template, {
        name,
        dashboard_url: process.env.FRONTEND_URL || 'https://podycy.com/dashboard'
      });

      await strapi.plugins.email.services.email.send({
        to: email,
        subject,
        html,
        text,
        from
      });

      strapi.log.info('✅ Welcome email sent:', { email, template: template.name });
    } catch (error) {
      strapi.log.error('❌ Welcome email failed:', error.message);
      throw error;
    }
  },

  async sendPasswordResetEmail({ email, name, resetUrl }) {
    try {
      const template = loadTemplate('reset-password');
      const { html, text, subject, from } = renderTemplate(template, {
        name,
        action_url: resetUrl
      });

      await strapi.plugins.email.services.email.send({
        to: email,
        subject,
        html,
        text,
        from
      });

      strapi.log.info('✅ Password reset email sent:', { email, template: template.name });
    } catch (error) {
      strapi.log.error('❌ Password reset email failed:', error.message);
      throw error;
    }
  },

  async sendTicketNotificationEmail({
    emails,
    name,
    title,
    description,
    agent_name,
    customer_email,
    ticket_url,
    organization_name = null,
    priority = null,
    assignee = null
  }) {
    try {
      const template = loadTemplate('ticket-notification');

      // Prepare template variables
      const templateVars = {
        name,
        title,
        description,
        agent_name,
        customer_email,
        ticket_url
      };

      // Add optional variables if provided
      if (organization_name) templateVars.organization_name = organization_name;
      if (priority) {
        templateVars.priority = priority;
        templateVars.priority_color = getPriorityColor(priority);
      }
      if (assignee) templateVars.assignee = assignee;

      const { html, text, subject, from } = renderTemplate(template, templateVars);

      // Send to multiple recipients
      const emailPromises = emails.map(email =>
        strapi.plugins.email.services.email.send({
          to: email,
          subject,
          html,
          text,
          from
        })
      );

      await Promise.all(emailPromises);

      strapi.log.info('✅ Ticket notification emails sent:', {
        recipients: emails,
        template: template.name,
        ticket_title: title
      });
    } catch (error) {
      strapi.log.error('❌ Ticket notification email failed:', error.message);
      throw error;
    }
  },

  // Utility functions
  loadTemplate,
  clearTemplateCache
};
