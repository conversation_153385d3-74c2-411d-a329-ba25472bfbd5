/**
 * Optimized Email Service with External Templates
 * Uses Strapi's built-in email with JSON template files
 */

const fs = require('fs');
const path = require('path');

// Template cache for performance
const templateCache = new Map();

/**
 * Load and cache email template (database first, JSON fallback)
 */
async function loadTemplate(templateName) {
  // Check cache first
  if (templateCache.has(templateName)) {
    return templateCache.get(templateName);
  }

  try {
    // Try to get from database first
    const template = await strapi.service('api::email-template.email-template').getTemplateByName(templateName);
    templateCache.set(templateName, template);
    strapi.log.info(`📧 Template loaded from database: ${templateName}`);
    return template;
  } catch (dbError) {
    // Fallback to JSON file
    try {
      const templatePath = path.join(__dirname, '../email-templates', `${templateName}.json`);

      if (!fs.existsSync(templatePath)) {
        throw new Error(`Template not found in database or JSON file: ${templateName}`);
      }

      const templateData = JSON.parse(fs.readFileSync(templatePath, 'utf8'));

      // Cache the template
      templateCache.set(templateName, templateData);

      strapi.log.warn(`📧 Template loaded from JSON fallback: ${templateName}`);
      return templateData;
    } catch (jsonError) {
      strapi.log.error(`❌ Failed to load template ${templateName}:`, jsonError.message);
      throw new Error(`Template not found: ${templateName}`);
    }
  }
}

/**
 * Render template with variables
 */
function renderTemplate(template, variables = {}) {
  // Merge template variables with provided variables
  const allVariables = {
    ...template.variables,
    ...variables
  };

  let html = template.html_content;
  let text = template.text_content;
  let subject = template.subject;

  // Replace all variables in content
  Object.keys(allVariables).forEach(key => {
    const value = allVariables[key] || '';
    const regex = new RegExp(`{{${key}}}`, 'g');
    html = html.replace(regex, value);
    text = text.replace(regex, value);
    subject = subject.replace(regex, value);
  });

  return {
    html,
    text,
    subject,
    from: `${template.default_from_name} <${template.default_from_email}>`
  };
}

/**
 * Clear template cache (useful for development)
 */
function clearTemplateCache() {
  templateCache.clear();
  strapi.log.info('📧 Template cache cleared');
}

/**
 * Get priority color for email styling
 */
function getPriorityColor(priority) {
  if (!priority) return '#666666';

  const priorityLower = priority.toLowerCase();
  if (priorityLower.includes('high') || priorityLower.includes('urgent')) return '#DC2626';
  if (priorityLower.includes('medium') || priorityLower.includes('normal')) return '#F59E0B';
  if (priorityLower.includes('low')) return '#10B981';
  return '#666666'; // Default gray
}

module.exports = {
  async sendConfirmationEmail({ email, name, confirmationUrl, userId = null, organizationId = null }) {
    let emailLog = null;
    try {
      const template = await loadTemplate('confirmation-email');
      const { html, text, subject, from } = renderTemplate(template, {
        name,
        action_url: confirmationUrl
      });

      // Send email
      const result = await strapi.plugins.email.services.email.send({
        to: email,
        subject,
        html,
        text,
        from
      });

      // Generate a unique message ID
      const messageId = result?.messageId || result?.MessageID || `conf-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // Log email activity
      emailLog = await strapi.service('api::email-log.email-log').logEmail({
        messageId,
        provider: 'postmark',
        toEmails: [email],
        fromEmail: from.split('<')[1]?.replace('>', '') || from,
        fromName: from.split('<')[0]?.trim() || 'Ajentic Team',
        subject,
        templateName: template.name,
        templateData: { name, action_url: confirmationUrl },
        emailType: 'verification',
        organizationId,
        userId,
        metadata: { confirmationUrl }
      });

      // Update status to sent
      if (emailLog) {
        await strapi.service('api::email-log.email-log').updateEmailStatus(messageId, 'sent');
      }

      strapi.log.info('✅ Confirmation email sent:', {
        email,
        template: template.name,
        logId: emailLog?.id
      });
    } catch (error) {
      // Log failure if email log was created
      if (emailLog && emailLog.message_id) {
        await strapi.service('api::email-log.email-log').logEmailFailure(
          emailLog.message_id,
          error.message
        );
      }
      strapi.log.error('❌ Confirmation email failed:', error.message);
      throw error;
    }
  },

  async sendWelcomeEmail({ email, name }) {
    try {
      const template = await loadTemplate('welcome-email');
      const { html, text, subject, from } = renderTemplate(template, {
        name,
        dashboard_url: process.env.FRONTEND_URL || 'https://podycy.com/dashboard'
      });

      await strapi.plugins.email.services.email.send({
        to: email,
        subject,
        html,
        text,
        from
      });

      strapi.log.info('✅ Welcome email sent:', { email, template: template.name });
    } catch (error) {
      strapi.log.error('❌ Welcome email failed:', error.message);
      throw error;
    }
  },

  async sendPasswordResetEmail({ email, name, resetUrl }) {
    try {
      const template = await loadTemplate('reset-password');
      const { html, text, subject, from } = renderTemplate(template, {
        name,
        action_url: resetUrl
      });

      await strapi.plugins.email.services.email.send({
        to: email,
        subject,
        html,
        text,
        from
      });

      strapi.log.info('✅ Password reset email sent:', { email, template: template.name });
    } catch (error) {
      strapi.log.error('❌ Password reset email failed:', error.message);
      throw error;
    }
  },

  async sendTicketNotificationEmail({
    emails,
    name,
    title,
    description,
    agent_name,
    customer_email,
    ticket_url,
    organization_name = null,
    priority = null,
    assignee = null,
    userId = null,
    organizationId = null,
    ticketId = null
  }) {
    const emailLogs = [];
    try {
      const template = await loadTemplate('ticket-notification');

      // Prepare template variables
      const templateVars = {
        name,
        title,
        description,
        agent_name,
        customer_email,
        ticket_url
      };

      // Add optional variables if provided
      if (organization_name) templateVars.organization_name = organization_name;
      if (priority) {
        templateVars.priority = priority;
        templateVars.priority_color = getPriorityColor(priority);
      }
      if (assignee) templateVars.assignee = assignee;

      const { html, text, subject, from } = renderTemplate(template, templateVars);

      // Send to multiple recipients with individual logging
      const emailPromises = emails.map(async (email) => {
        const messageId = `ticket-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

        // Log email before sending
        const emailLog = await strapi.service('api::email-log.email-log').logEmail({
          messageId,
          provider: 'postmark',
          toEmails: [email],
          fromEmail: from.split('<')[1]?.replace('>', '') || from,
          fromName: from.split('<')[0]?.trim() || 'Podycy Team',
          subject,
          templateName: template.name,
          templateData: templateVars,
          emailType: 'ticket-notification',
          organizationId,
          userId,
          ticketId,
          agentName: agent_name,
          customerEmail: customer_email,
          priority: priority?.toLowerCase() || null,
          metadata: {
            ticket_url,
            organization_name,
            assignee,
            recipient_name: name
          }
        });

        emailLogs.push(emailLog);

        try {
          // Send email
          const result = await strapi.plugins.email.services.email.send({
            to: email,
            subject,
            html,
            text,
            from
          });

          // Update status to sent
          await strapi.service('api::email-log.email-log').updateEmailStatus(messageId, 'sent', {
            provider_response: result
          });

          return { email, success: true, messageId };
        } catch (sendError) {
          // Log failure
          await strapi.service('api::email-log.email-log').logEmailFailure(
            messageId,
            sendError.message,
            sendError.response || null
          );
          throw sendError;
        }
      });

      const results = await Promise.all(emailPromises);

      strapi.log.info('✅ Ticket notification emails sent:', {
        recipients: emails,
        template: template.name,
        ticket_title: title,
        ticket_id: ticketId,
        agent_name,
        priority,
        logs_created: emailLogs.length,
        successful_sends: results.filter(r => r.success).length
      });

      return results;
    } catch (error) {
      strapi.log.error('❌ Ticket notification email failed:', {
        error: error.message,
        ticket_title: title,
        ticket_id: ticketId,
        agent_name,
        recipients_count: emails.length,
        logs_created: emailLogs.length
      });
      throw error;
    }
  },

  // Utility functions
  loadTemplate,
  clearTemplateCache
};
