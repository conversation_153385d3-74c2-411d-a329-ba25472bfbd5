import { useEffect, useRef } from 'react';

/**
 * Custom hook to manage chat scrolling behavior
 * @param {Array} deps - Dependencies to watch for triggering scroll effects
 * @returns {Object} - Chat ref and methods for controlling scroll behavior
 */
const useChatScroll = (deps = []) => {
  const chatRef = useRef(null);
  
  // Scroll to bottom on dependencies change
  useEffect(() => {
    if (chatRef.current) {
      scrollToBottom();
    }
  }, deps);
  
  // Check if the user is already scrolled to the bottom
  const isAtBottom = () => {
    if (!chatRef.current) return false;
    
    return (
      chatRef.current.scrollTop + chatRef.current.clientHeight >=
      chatRef.current.scrollHeight - 10
    );
  };
  
  // Scroll to bottom only if user was already at bottom
  const scrollToBottomIfNeeded = () => {
    if (isAtBottom() && chatRef.current) {
      chatRef.current.scrollTop = chatRef.current.scrollHeight;
    }
  };
  
  // Force scroll to bottom regardless of current position
  const scrollToBottom = () => {
    if (chatRef.current) {
      chatRef.current.scrollTop = chatRef.current.scrollHeight;
    }
  };
  
  // Adjust padding to accommodate textarea expansion
  const adjustBottomPadding = (textareaHeight) => {
    if (chatRef.current) {
      const padding = textareaHeight + 80 > 120
        ? textareaHeight + 80 < 240
          ? textareaHeight + 80
          : 240
        : 120;
        
      chatRef.current.style.paddingBottom = `${padding}px`;
    }
  };
  
  return {
    chatRef,
    isAtBottom,
    scrollToBottomIfNeeded,
    scrollToBottom,
    adjustBottomPadding
  };
};

export default useChatScroll; 