{"kind": "collectionType", "collectionName": "email_logs", "info": {"singularName": "email-log", "pluralName": "email-logs", "displayName": "<PERSON><PERSON>", "description": "Track email sending history and delivery status"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"message_id": {"type": "string", "unique": true}, "provider": {"type": "string", "required": true}, "to_emails": {"type": "json", "required": true}, "from_email": {"type": "email", "required": true}, "subject": {"type": "string"}, "template_id": {"type": "string"}, "template_data": {"type": "json"}, "status": {"type": "enumeration", "enum": ["pending", "sent", "delivered", "opened", "clicked", "bounced", "failed", "spam"], "default": "pending", "required": true}, "error_message": {"type": "text"}, "provider_response": {"type": "json"}, "sent_at": {"type": "datetime"}, "delivered_at": {"type": "datetime"}, "opened_at": {"type": "datetime"}, "clicked_at": {"type": "datetime"}, "organization": {"type": "relation", "relation": "manyToOne", "target": "api::organization.organization"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "email_template": {"type": "relation", "relation": "manyToOne", "target": "api::email-template.email-template"}, "retry_count": {"type": "integer", "default": 0}, "metadata": {"type": "json", "default": {}}}}