// policies/existingUserPhoneValidation.js

module.exports = async (policyContext, config, { strapi }) => {
	const { PolicyError, ApplicationError } = require("@strapi/utils").errors;
	const params = policyContext.request.body;

	let validUser= await strapi.db.query('plugin::users-permissions.user').findOne({ where: { phone: params.phone } })

	// If User exists then display user exists error.
	if (validUser ) {
		strapi.log.error('User already exists. Please signin');
		return false;
	}

	return true;
};
