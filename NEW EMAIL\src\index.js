"use strict";


const axios = require("axios");
const { createOrgForuser } = require("./helpers/create-organization-for-user");
const { isGenericEmail } = require("./helpers/utils");
const { init, track } = require("@amplitude/analytics-node");
const stripePaymentProvider = require("./providers/stripe-payment-provider");
const stripe = require("stripe")(process.env.STRIPE_SECRETE_KEY);
const admin = require('firebase-admin');
const serviceAccount = require('../config/firebase-service-account.json');
const emailService = require("./helpers/email-service");


module.exports = {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  register(/*{ strapi }*/) {
    init(process.env.AMPLITUDE_API_KEY);
  },

  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  // bootstrap(/*{ strapi }*/) {},
  async bootstrap({ strapi }) {
    // Initialize email provider factory
    try {
      const { initializeDefaultProviders, emailProviderFactory } = require('./providers/email-provider-factory');
      
      // Register and initialize email providers
      initializeDefaultProviders();
      await emailProviderFactory.initializeProviders();
      
      // Make email provider factory available globally
      strapi.emailProviderFactory = emailProviderFactory;
      
      strapi.log.info('📧 Email connector system initialized successfully');
    } catch (error) {
      strapi.log.error('❌ Failed to initialize email connector system:', error.message);
    }

    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount)
      });
    }
    strapi.firebase = admin;
    strapi.notification = async ({ title, body, token }) => {
      return await admin.messaging().send({
        token,
        notification: { title, body }
      });
    };

    console.log('✅ Firebase initialized successfully');

    strapi.db.lifecycles.subscribe({
      models: ["plugin::users-permissions.user"],

      async afterCreate(event) {
        strapi.log.info('🎉 SIGNUP FLOW: User created successfully, starting post-creation setup', {
          userId: event.result.id,
          timestamp: new Date().toISOString(),
          phase: 'afterCreate_lifecycle_hook'
        });

        try {
          strapi.log.info('🔍 SIGNUP FLOW: Fetching user details with organization data', {
            userId: event.result.id
          });

          const user = await strapi
            .query("plugin::users-permissions.user")
            .findOne({
              where: { id: event.result.id },
              populate: { organization: true },
            });

          strapi.log.info('✅ SIGNUP FLOW: User details fetched successfully', {
            userId: user.id,
            username: user.username,
            email: user.email,
            hasOrganization: !!user.organization,
            organizationId: user.organization?.id,
            organizationName: user.organization?.name,
            confirmed: user.confirmed
          });

          var org;
if (!user.organization) {
  // Double-check to prevent duplicate organization creation
  strapi.log.info('🔍 SIGNUP FLOW: No organization found, double-checking for unpopulated organization', {
    userId: user.id
  });
  
  const userWithOrg = await strapi.query("plugin::users-permissions.user").findOne({
    where: { id: user.id },
    populate: { organization: true }
  });

  if (userWithOrg.organization) {
    // Organization exists, just wasn't populated initially
    org = userWithOrg.organization;
    strapi.log.info('✅ SIGNUP FLOW: Found existing organization after re-fetch', {
      organizationId: org.id,
      organizationName: org.name,
      userId: user.id
    });
  } else {
    // Only create if truly no organization exists
    strapi.log.info('🏢 SIGNUP FLOW: No organization found, creating default organization', {
      userId: user.id,
      email: user.email,
      isGenericEmail: isGenericEmail(user.email)
    })};

            if (isGenericEmail(user.email)) {
              strapi.log.info('👤 SIGNUP FLOW: Creating individual organization for generic email', {
                username: user.username,
                organizationType: 'individual'
              });

              const body = {
                name: user.username,
                type: "individual",
                users: { connect: [user.id] },
              };
              org = await createOrgForuser(body);

              strapi.log.info('✅ SIGNUP FLOW: Individual organization created successfully', {
                organizationId: org.id,
                organizationName: org.name,
                organizationType: org.type,
                userId: user.id
              });
            } else {
              const orgName = user.email.split("@")[1].split(".")[0];
              strapi.log.info('🏢 SIGNUP FLOW: Creating business organization from email domain', {
                emailDomain: user.email.split("@")[1],
                organizationName: orgName,
                organizationType: 'organization'
              });

              const body = {
                name: orgName,
                type: "organization",
                users: { connect: [user.id] },
              };
              org = await createOrgForuser(body);

              strapi.log.info('✅ SIGNUP FLOW: Business organization created successfully', {
                organizationId: org.id,
                organizationName: org.name,
                organizationType: org.type,
                userId: user.id
              });
            }
          } else {
            org = user.organization;
            strapi.log.info('✅ SIGNUP FLOW: Using existing organization', {
              organizationId: org.id,
              organizationName: org.name,
              userId: user.id
            });
          }

          if (event.params.data.confirmed) {
            strapi.log.info('🎉 SIGNUP FLOW: User is confirmed, sending welcome email and adding to marketing', {
              userId: user.id,
              email: user.email,
              organizationName: user.organization?.name ?? org.name
            });

            // Send welcome email using email connector service
            try {
              const emailConnectorService = strapi.service('api::email-connector.email-connector');
              await emailConnectorService.sendTemplateEmail({
                templateName: 'welcome-email', // Create this template in Strapi
                to: user.email,
                templateData: {
                  // Only provide dynamic values - defaults come from template
                  name: user.username,
                  username: user.username,
                  trial_end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toLocaleDateString(),
                  trial_start_date: new Date().toLocaleDateString()
                },
                provider: 'postmark'
              });

              strapi.log.info('✅ SIGNUP FLOW: Welcome email sent successfully', { 
                userId: user.id,
                template: 'welcome-email',
                provider: 'postmark'
              });
            } catch (error) {
              strapi.log.error('❌ SIGNUP FLOW ERROR: Failed to send welcome email', {
                userId: user.id,
                error: error.message,
              });
            }

            // Add to marketing contacts (SendGrid)
            try {
              const { marketingContactsService } = require('./services/marketing-contacts');
              const marketingResult = await marketingContactsService.addContact({
                email: user.email,
                firstName: user.username,
                customFields: {
                  organization: user.organization?.name ?? org.name,
                }
              });

              if (marketingResult.success) {
                strapi.log.info('✅ SIGNUP FLOW: User added to marketing contacts successfully', {
                  userId: user.id,
                  email: user.email,
                  provider: marketingResult.provider
                });
              } else if (!marketingResult.skipped) {
                strapi.log.error('❌ SIGNUP FLOW ERROR: Failed to add user to marketing contacts', {
                  userId: user.id,
                  email: user.email,
                  error: marketingResult.error
                });
              }
            } catch (error) {
              strapi.log.error('❌ SIGNUP FLOW ERROR: Marketing contacts service error', {
                userId: user.id,
                email: user.email,
                error: error.message
              });
            }

            strapi.log.info('✅ SIGNUP FLOW: User added to SendGrid marketing contacts successfully', {
              userId: user.id,
              email: user.email,
              sendGridResponse: res?.status
            });
            console.log(res);
          } else {
            // Only send confirmation email for LOCAL email signups (not Google/OAuth signups)
            if (user.provider === 'local') {
              strapi.log.info('📧 SIGNUP FLOW: Local email signup detected, sending confirmation email', {
                userId: user.id,
                email: user.email,
                provider: user.provider,
                confirmed: user.confirmed
              });

              // Send confirmation e-mail through new email connector system
              if (!user.confirmationToken) {
                const crypto = require('crypto');
                const confirmationToken = crypto.randomBytes(20).toString('hex');
                await strapi.db.query("plugin::users-permissions.user").update({
                  where: { id: user.id },
                  data: { confirmationToken },
                });
                user.confirmationToken = confirmationToken;
              }

              // Dynamic confirmation link - use server URL from config
              const serverUrl = strapi.config.get('server.url');
              const confirmationLink = `${serverUrl}/api/auth/email-confirmation?confirmation=${user.confirmationToken}`;

              try {
                // Send confirmation email using email connector controller
                const emailConnectorService = strapi.service('api::email-connector.email-connector');
                const result = await emailConnectorService.sendTemplateEmail({
                  templateName: 'confirmation-email',
                  to: user.email,
                  templateData: {
                    // Only provide dynamic values - defaults come from template
                    name: user.username,
                    username: user.username,
                    action_url: confirmationLink,
                    trial_end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toLocaleDateString(),
                    trial_start_date: new Date().toLocaleDateString()
                  },
                  provider: 'postmark'  // Using Postmark for email delivery
                });

                strapi.log.info('✅ SIGNUP FLOW: Confirmation email sent via new email connector', { 
                  userId: user.id,
                  template: 'confirmation-email',
                  provider: 'postmark'
                });
              } catch (error) {
                strapi.log.error('❌ SIGNUP FLOW ERROR: Failed to send confirmation email via new connector', {
                  userId: user.id,
                  error: error.message,
                  stack: error.stack
                });
                
                // Temporarily disabled fallback to debug Postmark issue
                throw error; // This will show the real Postmark error
                
                // TODO: Re-enable fallback once Postmark is working
                // // Fallback to old system if new system fails
                // try {
                //   await emailService.sendEmail({
                //     emails: [user.email],
                //     name: 'PODYCY',
                //     subject: 'New Account verification - Podycy',
                //     title: 'Confirm Your Email',
                //     description: 'Thanks for signing up! Please confirm your email address to activate your account.',
                //     templateId: process.env.SENDGRID_CONFIRM_TEMPLATE_ID,
                //     agent_name: user.username,
                //     customer_email: user.email,
                //     ticket_url: confirmationLink,
                //   });
                //   strapi.log.info('✅ SIGNUP FLOW: Confirmation email sent via fallback system', { userId: user.id });
                // } catch (fallbackError) {
                //   strapi.log.error('❌ SIGNUP FLOW ERROR: Both email systems failed', {
                //     userId: user.id,
                //     newSystemError: error.message,
                //     fallbackError: fallbackError.message,
                //   });
                // }
              }
            } else {
              strapi.log.info('🔄 SIGNUP FLOW: Non-local provider signup detected, skipping confirmation email', {
                userId: user.id,
                email: user.email,
                provider: user.provider,
                confirmed: user.confirmed
              });

              // Send welcome email for Google OAuth users (they're already confirmed)
              if (user.provider === 'google' && user.confirmed) {
                try {
                  const emailConnectorService = strapi.service('api::email-connector.email-connector');
                  await emailConnectorService.sendTemplateEmail({
                    templateName: 'welcome-email', // Uses your welcome-email template in Strapi
                    to: user.email,
                    templateData: {
                      // Only provide dynamic values - defaults come from template
                      name: user.username,
                      username: user.username,
                      trial_end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toLocaleDateString(),
                      trial_start_date: new Date().toLocaleDateString()
                    },
                    provider: 'postmark'
                  });

                  strapi.log.info('✅ SIGNUP FLOW: Welcome email sent to Google OAuth user', {
                    userId: user.id,
                    template: 'welcome-email',
                    provider: 'postmark',
                    oauth_provider: user.provider
                  });
                } catch (error) {
                  strapi.log.error('❌ SIGNUP FLOW ERROR: Failed to send welcome email to Google OAuth user', {
                    userId: user.id,
                    error: error.message,
                    oauth_provider: user.provider
                  });
                }
              }
            }
          }

          strapi.log.info('📊 SIGNUP FLOW: Tracking signup event in Amplitude', {
            userId: user.id,
            email: user.email
          });

          track("Sign up", undefined, {
            user_id: user.email,
          });

          strapi.log.info('✅ SIGNUP FLOW: Amplitude tracking completed', {
            userId: user.id,
            email: user.email
          });

          console.log("**org***", org);

          strapi.log.info('🔍 SIGNUP FLOW: Checking existing knowledgebases for organization', {
            organizationId: org.id,
            organizationName: org.name
          });

          const kbCount = await strapi
          .query("api::knowledgebase.knowledgebase")
          .findMany({
            where: {
              organization: org.id,
            },
          });

        strapi.log.info('📊 SIGNUP FLOW: Knowledgebase count check completed', {
          organizationId: org.id,
          existingKbCount: kbCount.length
        });

        if (kbCount.length > 0) {
          strapi.log.info('✅ SIGNUP FLOW: Organization already has knowledgebases, skipping default KB creation', {
            organizationId: org.id,
            existingKbCount: kbCount.length
          });
          return org;
        }

        strapi.log.info('🎯 SIGNUP FLOW: Creating default knowledgebase for new organization', {
          organizationId: org.id,
          orgId: org.org_id,
          kbType: 'multiAgent',
          kbName: 'PlayList'
        });

        var data = {
          org_id: org.org_id,
          kb_type: "multiAgent",
          kb_name: "PlayList",
          openAI_assistant_id: "",
          vs_source: "supabase",
          vs_table_name: "documents",
        };

        strapi.log.info('🌐 SIGNUP FLOW: Making API call to create knowledgebase', {
          url: `${process.env.TALKBASE_BASE_URL}/v4/add_kb`,
          orgId: org.org_id,
          kbName: 'PlayList'
        });

        // Make a POST request to the external API with the request body
        const response = await axios
          .post(`${process.env.TALKBASE_BASE_URL}/v4/add_kb`, data, {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          })
          .catch((e) => {
            strapi.log.error('❌ SIGNUP FLOW ERROR: Failed to create knowledgebase via external API', {
              organizationId: org.id,
              error: e.message,
              url: `${process.env.TALKBASE_BASE_URL}/v4/add_kb`
            });
            console.log(e);
          });

        if (response.data?.status === "failure") {
          strapi.log.error('❌ SIGNUP FLOW ERROR: External API returned failure status', {
            organizationId: org.id,
            error: response.data.error,
            apiResponse: response.data
          });
          return ctx.badRequest(response.data.error ?? "Something went wrong");
        }

        strapi.log.info('✅ SIGNUP FLOW: Knowledgebase created successfully via external API', {
          organizationId: org.id,
          kbId: response.data.id,
          kbName: response.data.name,
          kbType: response.data.kb_type
        });

        strapi.log.info('🔍 SIGNUP FLOW: Fetching organization with monthly usage data', {
          organizationId: org.id
        });

        const organization = await strapi
          .query("api::organization.organization")
          .findOne({
            where: { id: org.id },
            populate: ["monthly_usages"],
          });

        strapi.log.info('✅ SIGNUP FLOW: Organization data fetched with monthly usage', {
          organizationId: organization.id,
          monthlyUsageCount: organization.monthly_usages?.length || 0
        });

        // await strapi.query("api::monthly-usage.monthly-usage").update({
        //   where: {
        //     id: organization.monthly_usages[organization.monthly_usages.length - 1]
        //       .id,
        //   },
        //   data: {
        //     kb_count:
        //       organization.monthly_usages[organization.monthly_usages.length - 1]
        //         .kb_count + 1,
        //   },
        // });
       console.log("KB response", response.data);
       console.log("organization", organization);

       strapi.log.info('💾 SIGNUP FLOW: Creating knowledgebase record in Strapi', {
         organizationId: organization.id,
         kbId: response.data.id,
         kbName: response.data.name,
         kbType: response.data.kb_type
       });

       var data = {
          data: {
            name: response.data.name,
            organization: organization.id,
            kb_id: response.data.id,
            type: response.data.kb_type,
            default_ai_task: 1,
            publishedAt: new Date(),
          },
        };
        console.log("KB data", data);
        const kb = await strapi
          .query("api::knowledgebase.knowledgebase")
          .create(data);

        strapi.log.info('✅ SIGNUP FLOW: Knowledgebase record created in Strapi successfully', {
          kbStrapiId: kb.id,
          kbExternalId: response.data.id,
          organizationId: organization.id,
          kbName: kb.name
        });

        strapi.log.info('💳 SIGNUP FLOW: Creating Stripe customer', {
          userEmail: user.email,
          userId: user.id
        });

        const customer = await stripePaymentProvider.createCustomer(user.email);

        strapi.log.info('✅ SIGNUP FLOW: Stripe customer created successfully', {
          userEmail: user.email,
          userId: user.id,
          stripeCustomerId: customer.id
        });

        console.log("Created stripe customer", customer);

        strapi.log.info('🎊 SIGNUP FLOW COMPLETED: All post-creation setup finished successfully', {
          userId: user.id,
          email: user.email,
          organizationId: org.id,
          organizationName: org.name,
          kbId: kb.id,
          kbName: kb.name,
          stripeCustomerId: customer.id,
          timestamp: new Date().toISOString(),
          totalDuration: `${Date.now() - new Date(event.result.createdAt).getTime()}ms`
        });

        } catch (e) {
          strapi.log.error('❌ SIGNUP FLOW ERROR: Post-creation setup failed', {
            userId: event.result.id,
            error: e.message,
            stack: e.stack,
            timestamp: new Date().toISOString(),
            phase: 'afterCreate_lifecycle_hook'
          });
          console.log(e);
        }
      },
      async beforeUpdate(event) {
        const { data, where, select, populate } = event.params;
        if (data.confirmed) {
          try {
            const user = await strapi
              .query("plugin::users-permissions.user")
              .findOne({
                where: { id: where.id },
                populate: { organization: true },
              });

            if (!user) {
              console.log('User not found during email confirmation:', where.id);
              return;
            }

            if (!user.organization) {
              console.log('User has no organization during email confirmation:', user.email);
              return;
            }

            if (!user.organization.name) {
              console.log('User organization has no name during email confirmation:', user.email);
              return;
            }

                          // Send welcome email when user confirms their email
              try {
                const emailConnectorService = strapi.service('api::email-connector.email-connector');
                await emailConnectorService.sendTemplateEmail({
                  templateName: 'welcome-email', // Uses your welcome-email template in Strapi
                  to: user.email,
                  templateData: {
                    // Only provide dynamic values - defaults come from template
                    name: user.username,
                    username: user.username,
                   
                  },
                  provider: 'postmark'  // Using Postmark for email delivery
                });

                console.log('✅ EMAIL CONFIRMATION: Welcome email sent successfully to:', user.email);
            } catch (error) {
              console.log('❌ EMAIL CONFIRMATION ERROR: Failed to send welcome email:', error.message);
            }

            // Add to marketing contacts (SendGrid)
            try {
              const { marketingContactsService } = require('./services/marketing-contacts');
              const marketingResult = await marketingContactsService.addContact({
                email: user.email,
                firstName: user.username,
                customFields: {
                  company: user.organization.name,
                }
              });

              if (marketingResult.success) {
                console.log('✅ EMAIL CONFIRMATION: User added to marketing contacts successfully');
              } else if (!marketingResult.skipped) {
                console.log('❌ EMAIL CONFIRMATION ERROR: Failed to add user to marketing contacts:', marketingResult.error);
              }
            } catch (error) {
              console.log('❌ EMAIL CONFIRMATION ERROR: Marketing contacts service error:', error.message);
            }
          } catch (error) {
            console.log('Error in beforeUpdate:', error.message);
          }
        }
      },
      destroy({ strapi }) {
        const apps = admin.apps;
        if (apps.length) {
          return Promise.all(apps.map(app => app.delete()));
        }
      },
    });
    
  }
  
};
