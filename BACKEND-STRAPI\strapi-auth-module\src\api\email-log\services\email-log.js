'use strict';

/**
 * Email Log Service
 * Handles logging of all email activities for tracking and analytics
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::email-log.email-log', ({ strapi }) => ({
  /**
   * Create a new email log entry
   */
  async logEmail({
    messageId,
    provider = 'postmark',
    toEmails,
    fromEmail,
    fromName,
    subject,
    templateName,
    templateId,
    templateData,
    emailType = 'transactional',
    organizationId = null,
    userId = null,
    ticketId = null,
    agentName = null,
    customerEmail = null,
    priority = null,
    metadata = {}
  }) {
    try {
      const emailLog = await strapi.entityService.create('api::email-log.email-log', {
        data: {
          message_id: messageId,
          provider,
          to_emails: Array.isArray(toEmails) ? toEmails : [toEmails],
          from_email: fromEmail,
          from_name: fromName,
          subject,
          template_name: templateName,
          template_id: templateId,
          template_data: templateData,
          status: 'pending',
          email_type: emailType,
          organization: organizationId,
          user: userId,
          ticket_id: ticketId,
          agent_name: agent<PERSON><PERSON>,
          customer_email: customerEmail,
          priority,
          retry_count: 0,
          metadata: {
            ...metadata,
            logged_at: new Date().toISOString(),
            user_agent: metadata.userAgent || 'Strapi Email Service'
          },
          tags: this.generateEmailTags(emailType, templateName, priority)
        }
      });

      strapi.log.info('📝 Email logged successfully', {
        id: emailLog.id,
        messageId,
        provider,
        emailType,
        recipients: Array.isArray(toEmails) ? toEmails.length : 1
      });

      return emailLog;
    } catch (error) {
      strapi.log.error('❌ Failed to log email:', error.message);
      throw error;
    }
  },

  /**
   * Update email status (sent, delivered, opened, etc.)
   */
  async updateEmailStatus(messageId, status, additionalData = {}) {
    try {
      const emailLog = await strapi.entityService.findMany('api::email-log.email-log', {
        filters: { message_id: messageId },
        limit: 1
      });

      if (!emailLog || emailLog.length === 0) {
        strapi.log.warn('⚠️ Email log not found for message ID:', messageId);
        return null;
      }

      const updateData = {
        status,
        ...additionalData
      };

      // Set timestamp based on status
      const now = new Date();
      switch (status) {
        case 'sent':
          updateData.sent_at = now;
          break;
        case 'delivered':
          updateData.delivered_at = now;
          break;
        case 'opened':
          updateData.opened_at = now;
          break;
        case 'clicked':
          updateData.clicked_at = now;
          break;
        case 'bounced':
          updateData.bounced_at = now;
          break;
      }

      const updatedLog = await strapi.entityService.update('api::email-log.email-log', emailLog[0].id, {
        data: updateData
      });

      strapi.log.info('📊 Email status updated', {
        messageId,
        status,
        logId: updatedLog.id
      });

      return updatedLog;
    } catch (error) {
      strapi.log.error('❌ Failed to update email status:', error.message);
      throw error;
    }
  },

  /**
   * Log email failure
   */
  async logEmailFailure(messageId, errorMessage, providerResponse = null) {
    try {
      return await this.updateEmailStatus(messageId, 'failed', {
        error_message: errorMessage,
        provider_response: providerResponse
      });
    } catch (error) {
      strapi.log.error('❌ Failed to log email failure:', error.message);
      throw error;
    }
  },

  /**
   * Increment retry count for failed emails
   */
  async incrementRetryCount(messageId, nextRetryAt = null) {
    try {
      const emailLog = await strapi.entityService.findMany('api::email-log.email-log', {
        filters: { message_id: messageId },
        limit: 1
      });

      if (!emailLog || emailLog.length === 0) {
        return null;
      }

      const currentRetryCount = emailLog[0].retry_count || 0;
      const maxRetries = emailLog[0].max_retries || 3;

      if (currentRetryCount >= maxRetries) {
        strapi.log.warn('⚠️ Max retries reached for email:', messageId);
        return await this.updateEmailStatus(messageId, 'failed', {
          error_message: 'Maximum retry attempts exceeded'
        });
      }

      return await strapi.entityService.update('api::email-log.email-log', emailLog[0].id, {
        data: {
          retry_count: currentRetryCount + 1,
          next_retry_at: nextRetryAt,
          status: 'pending'
        }
      });
    } catch (error) {
      strapi.log.error('❌ Failed to increment retry count:', error.message);
      throw error;
    }
  },

  /**
   * Get email analytics for organization
   */
  async getEmailAnalytics(organizationId, dateRange = {}) {
    try {
      const filters = { organization: organizationId };
      
      if (dateRange.start) {
        filters.createdAt = { $gte: dateRange.start };
      }
      if (dateRange.end) {
        filters.createdAt = { ...filters.createdAt, $lte: dateRange.end };
      }

      const emailLogs = await strapi.entityService.findMany('api::email-log.email-log', {
        filters,
        populate: ['organization', 'user', 'email_template']
      });

      // Calculate analytics
      const analytics = {
        total: emailLogs.length,
        sent: emailLogs.filter(log => log.status === 'sent').length,
        delivered: emailLogs.filter(log => log.status === 'delivered').length,
        opened: emailLogs.filter(log => log.status === 'opened').length,
        clicked: emailLogs.filter(log => log.status === 'clicked').length,
        bounced: emailLogs.filter(log => log.status === 'bounced').length,
        failed: emailLogs.filter(log => log.status === 'failed').length,
        byType: {},
        byProvider: {}
      };

      // Group by email type
      emailLogs.forEach(log => {
        analytics.byType[log.email_type] = (analytics.byType[log.email_type] || 0) + 1;
        analytics.byProvider[log.provider] = (analytics.byProvider[log.provider] || 0) + 1;
      });

      return analytics;
    } catch (error) {
      strapi.log.error('❌ Failed to get email analytics:', error.message);
      throw error;
    }
  },

  /**
   * Generate tags for email categorization
   */
  generateEmailTags(emailType, templateName, priority) {
    const tags = [emailType];
    
    if (templateName) {
      tags.push(`template:${templateName}`);
    }
    
    if (priority) {
      tags.push(`priority:${priority}`);
    }
    
    if (emailType === 'ticket-notification') {
      tags.push('support', 'notification');
    }
    
    return tags;
  },

  /**
   * Clean up old email logs (for maintenance)
   */
  async cleanupOldLogs(daysToKeep = 90) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const deletedCount = await strapi.entityService.deleteMany('api::email-log.email-log', {
        filters: {
          createdAt: { $lt: cutoffDate }
        }
      });

      strapi.log.info(`🧹 Cleaned up ${deletedCount} old email logs`);
      return deletedCount;
    } catch (error) {
      strapi.log.error('❌ Failed to cleanup old email logs:', error.message);
      throw error;
    }
  }
}));
