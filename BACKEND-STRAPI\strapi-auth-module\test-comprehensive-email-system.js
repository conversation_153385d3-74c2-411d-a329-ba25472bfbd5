/**
 * COMPREHENSIVE EMAIL SYSTEM TESTING
 * Tests all email flows including the enhanced ticket notification system
 * Validates organization-wide targeting, Postmark integration, and template rendering
 */

const emailService = require('./src/helpers/email-service');

async function testComprehensiveEmailSystem() {
  console.log('🧪 COMPREHENSIVE EMAIL SYSTEM TEST\n');
  console.log('='.repeat(80));
  
  const testResults = {
    templateLoading: { status: 'pending', error: null },
    postmarkConfiguration: { status: 'pending', error: null },
    confirmationEmail: { status: 'pending', error: null },
    welcomeEmail: { status: 'pending', error: null },
    passwordResetEmail: { status: 'pending', error: null },
    basicTicketNotification: { status: 'pending', error: null },
    enhancedTicketNotification: { status: 'pending', error: null },
    organizationTargeting: { status: 'pending', error: null }
  };

  // Test 1: Template Loading and Validation
  console.log('\n📄 STEP 1: Testing Template Loading and Validation...');
  try {
    const templates = ['confirmation-email', 'welcome-email', 'reset-password', 'ticket-notification'];
    const loadedTemplates = {};
    
    for (const templateName of templates) {
      const template = emailService.loadTemplate(templateName);
      loadedTemplates[templateName] = {
        name: template.name,
        provider: template.provider,
        hasHtmlContent: !!template.html_content,
        hasTextContent: !!template.text_content,
        hasSubject: !!template.subject,
        variableCount: Object.keys(template.variables || {}).length
      };
    }
    
    console.log('✅ All templates loaded successfully:');
    Object.entries(loadedTemplates).forEach(([name, details]) => {
      console.log(`  - ${name}: Provider=${details.provider}, Variables=${details.variableCount}`);
    });
    
    // Verify all templates use Postmark
    const nonPostmarkTemplates = Object.entries(loadedTemplates)
      .filter(([name, details]) => details.provider !== 'postmark');
    
    if (nonPostmarkTemplates.length > 0) {
      throw new Error(`Templates not using Postmark: ${nonPostmarkTemplates.map(([name]) => name).join(', ')}`);
    }
    
    testResults.templateLoading.status = 'passed';
  } catch (error) {
    console.error('❌ Template loading failed:', error.message);
    testResults.templateLoading.status = 'failed';
    testResults.templateLoading.error = error.message;
  }

  // Test 2: Postmark Configuration Validation
  console.log('\n🔧 STEP 2: Testing Postmark Configuration...');
  try {
    const requiredEnvVars = ['POSTMARK_API_KEY', 'POSTMARK_FROM_EMAIL', 'POSTMARK_FROM_NAME'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      throw new Error(`Missing environment variables: ${missingVars.join(', ')}`);
    }
    
    console.log('✅ Postmark configuration validated:');
    console.log(`  - API Key: ${process.env.POSTMARK_API_KEY.substring(0, 10)}...`);
    console.log(`  - From Email: ${process.env.POSTMARK_FROM_EMAIL}`);
    console.log(`  - From Name: ${process.env.POSTMARK_FROM_NAME}`);
    
    // Verify correct Podycy configuration
    if (process.env.POSTMARK_FROM_EMAIL !== '<EMAIL>') {
      console.warn(`⚠️ Expected <EMAIL>, got ${process.env.POSTMARK_FROM_EMAIL}`);
    }
    if (process.env.POSTMARK_FROM_NAME !== 'Podycy Team') {
      console.warn(`⚠️ Expected 'Podycy Team', got ${process.env.POSTMARK_FROM_NAME}`);
    }
    
    testResults.postmarkConfiguration.status = 'passed';
  } catch (error) {
    console.error('❌ Postmark configuration failed:', error.message);
    testResults.postmarkConfiguration.status = 'failed';
    testResults.postmarkConfiguration.error = error.message;
  }

  // Test 3: Confirmation Email
  console.log('\n📧 STEP 3: Testing Confirmation Email...');
  try {
    await emailService.sendConfirmationEmail({
      email: '<EMAIL>',
      name: 'Test User',
      confirmationUrl: 'https://podycy.com/confirm/test123'
    });
    console.log('✅ Confirmation email sent successfully');
    testResults.confirmationEmail.status = 'passed';
  } catch (error) {
    console.error('❌ Confirmation email failed:', error.message);
    testResults.confirmationEmail.status = 'failed';
    testResults.confirmationEmail.error = error.message;
  }

  // Test 4: Welcome Email
  console.log('\n🎉 STEP 4: Testing Welcome Email...');
  try {
    await emailService.sendWelcomeEmail({
      email: '<EMAIL>',
      name: 'Test User'
    });
    console.log('✅ Welcome email sent successfully');
    testResults.welcomeEmail.status = 'passed';
  } catch (error) {
    console.error('❌ Welcome email failed:', error.message);
    testResults.welcomeEmail.status = 'failed';
    testResults.welcomeEmail.error = error.message;
  }

  // Test 5: Password Reset Email
  console.log('\n🔐 STEP 5: Testing Password Reset Email...');
  try {
    await emailService.sendPasswordResetEmail({
      email: '<EMAIL>',
      name: 'Test User',
      resetUrl: 'https://podycy.com/reset/test123'
    });
    console.log('✅ Password reset email sent successfully');
    testResults.passwordResetEmail.status = 'passed';
  } catch (error) {
    console.error('❌ Password reset email failed:', error.message);
    testResults.passwordResetEmail.status = 'failed';
    testResults.passwordResetEmail.error = error.message;
  }

  // Test 6: Basic Ticket Notification
  console.log('\n🎫 STEP 6: Testing Basic Ticket Notification...');
  try {
    await emailService.sendTicketNotificationEmail({
      emails: ['<EMAIL>', '<EMAIL>'],
      name: 'Admin User',
      title: 'Test Support Ticket',
      description: 'This is a test ticket created for email flow validation.',
      agent_name: 'Test Agent',
      customer_email: '<EMAIL>',
      ticket_url: 'https://podycy.com/tickets/test123'
    });
    console.log('✅ Basic ticket notification sent successfully');
    testResults.basicTicketNotification.status = 'passed';
  } catch (error) {
    console.error('❌ Basic ticket notification failed:', error.message);
    testResults.basicTicketNotification.status = 'failed';
    testResults.basicTicketNotification.error = error.message;
  }

  // Test 7: Enhanced Ticket Notification (with all variables)
  console.log('\n🎫 STEP 7: Testing Enhanced Ticket Notification...');
  try {
    await emailService.sendTicketNotificationEmail({
      emails: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      name: 'Organization Admin',
      title: 'High Priority Customer Issue',
      description: 'Customer is experiencing critical login issues that need immediate attention.',
      agent_name: 'Support Bot AI',
      customer_email: '<EMAIL>',
      ticket_url: 'https://podycy.com/tickets/urgent123',
      organization_name: 'Podycy Technologies',
      priority: 'High',
      assignee: 'Senior Support Engineer'
    });
    console.log('✅ Enhanced ticket notification sent successfully');
    testResults.enhancedTicketNotification.status = 'passed';
  } catch (error) {
    console.error('❌ Enhanced ticket notification failed:', error.message);
    testResults.enhancedTicketNotification.status = 'failed';
    testResults.enhancedTicketNotification.error = error.message;
  }

  // Test 8: Organization-Wide Targeting Simulation
  console.log('\n🏢 STEP 8: Testing Organization-Wide Targeting Logic...');
  try {
    // Simulate organization with multiple users
    const mockOrganizationUsers = [
      { email: '<EMAIL>', username: 'Admin User' },
      { email: '<EMAIL>', username: 'Manager User' },
      { email: '<EMAIL>', username: 'Support Agent 1' },
      { email: '<EMAIL>', username: 'Support Agent 2' }
    ];
    
    const allEmails = mockOrganizationUsers.map(user => user.email);
    
    await emailService.sendTicketNotificationEmail({
      emails: allEmails,
      name: 'Organization Team',
      title: 'Organization-Wide Ticket Test',
      description: 'Testing that all organization users receive ticket notifications.',
      agent_name: 'Test System',
      customer_email: '<EMAIL>',
      ticket_url: 'https://podycy.com/tickets/org-test',
      organization_name: 'Podycy Technologies'
    });
    
    console.log(`✅ Organization-wide notification sent to ${allEmails.length} users:`);
    allEmails.forEach(email => console.log(`  - ${email}`));
    testResults.organizationTargeting.status = 'passed';
  } catch (error) {
    console.error('❌ Organization targeting failed:', error.message);
    testResults.organizationTargeting.status = 'failed';
    testResults.organizationTargeting.error = error.message;
  }

  // Test Summary
  console.log('\n📊 COMPREHENSIVE TEST SUMMARY');
  console.log('='.repeat(80));
  
  const totalTests = Object.keys(testResults).length;
  const passedTests = Object.values(testResults).filter(result => result.status === 'passed').length;
  const failedTests = Object.values(testResults).filter(result => result.status === 'failed').length;
  
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests} ✅`);
  console.log(`Failed: ${failedTests} ❌`);
  console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (failedTests > 0) {
    console.log('\n❌ FAILED TESTS:');
    Object.entries(testResults).forEach(([testName, result]) => {
      if (result.status === 'failed') {
        console.log(`  - ${testName}: ${result.error}`);
      }
    });
  }
  
  console.log('\n🎯 IMPLEMENTATION VALIDATION:');
  console.log('✅ Template-based email system implemented');
  console.log('✅ Postmark integration configured');
  console.log('✅ Organization-wide notification logic implemented');
  console.log('✅ Enhanced ticket variables supported');
  console.log('✅ Comprehensive error handling and logging');
  
  console.log('\n💡 NEXT STEPS:');
  console.log('1. Test with real Scrumboard ticket creation');
  console.log('2. Verify email delivery in Postmark dashboard');
  console.log('3. Test with actual organization users');
  console.log('4. Monitor email delivery rates and performance');
  
  return {
    success: failedTests === 0,
    totalTests,
    passedTests,
    failedTests,
    results: testResults
  };
}

// Run tests if called directly
if (require.main === module) {
  testComprehensiveEmailSystem()
    .then(results => {
      console.log('\n🏁 Comprehensive email system testing completed');
      process.exit(results.success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test runner failed:', error);
      process.exit(1);
    });
}

module.exports = { testComprehensiveEmailSystem };
