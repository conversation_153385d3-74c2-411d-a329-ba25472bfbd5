const { createCoreController } = require('@strapi/strapi').factories;
const axios = require('axios');


module.exports = createCoreController('api::knowledgebase.knowledgebase', ({ strapi }) =>  ({
	

	async kbSource(ctx) {
		try {
			let result = await axios.get(`${process.env.TALKBASE_BASE_URL}/v4/kb_sources?page_num=${ctx.query.page_num}&page_size=${ctx.query.page_size}&kb_id=${ctx.query.kb_id}`
			).catch(err=>{
				console.log(err);

			});
			return result.data;
		} catch (err) {
			return ctx.throw(500,"Server error")
		}
	},

	async emptyKb(ctx) {
		try {
			const data = {
				org_id: ctx.state.user.organization.id,
				kb_id: ctx.params.id
			};
			let knowledgebase = await strapi.db.query('api::knowledgebase.knowledgebase').findOne({
				where: {
					kb_id: data.kb_id
				}
			});
			if(!knowledgebase){
				return ctx.throw(404, "Knowledgebase not found");
			}
			data["vs_table_name"] = knowledgebase.vs_table_name;

			let result = await axios.delete(`${process.env.TALKBASE_BASE_URL}/v4/empty_kb`,data,
				{
					headers: {
						'Content-Type': 'multipart/form-data'
					}
				}
			).catch(err=>{
				console.log(err);

			});
			return result.data;
		} catch (err) {
			return ctx.throw(500,"Server error")
				}
	},

	async deleteKb(ctx){
		try{
			const strapiId = ctx.params.id; // Internal Strapi ID from the route parameter
			const org_id = ctx.state.user?.organization?.id;

			if (!org_id) {
				console.error("Organization ID not found in user state.");
                return ctx.internalServerError("Could not determine organization ID.");
			}

			// 1. Fetch the knowledgebase using the Strapi ID
			const knowledgebase = await strapi.db.query('api::knowledgebase.knowledgebase').findOne({
				where: { id: strapiId },
				select: ['kb_id', 'vs_table_name'] // Select only needed fields
			});

			if (!knowledgebase) {
				return ctx.notFound(`Knowledgebase with ID ${strapiId} not found.`);
			}

			const { kb_id, vs_table_name } = knowledgebase;

			if (!kb_id || !vs_table_name) {
				console.error(`Missing kb_id or vs_table_name for knowledgebase ID ${strapiId}.`);
				return ctx.internalServerError('Knowledgebase information incomplete.');
			}

			// 2. Call the external API with parameters in the URL
			const apiUrl = `${process.env.TALKBASE_BASE_URL}/v4/delete_kb`;
			const params = { org_id, kb_id, vs_table_name };

			console.log(`Calling external delete_kb API: URL=${apiUrl}, Params=`, params);

			let result = await axios.delete(apiUrl, { params: params }).catch(err=>{
				console.error("Error calling external delete_kb API:", err.response?.data || err.message);
				// Rethrow or handle specific error types if needed
				throw new Error("Failed to delete knowledgebase externally.");
			});

			console.log("External delete_kb API call successful:", result.data);

            // Optional: Consider if you need to delete the KB from Strapi *after* the external call.
            // The webhook handler already does this, so maybe it's not needed here.
			
			return result.data;

		} catch (err) {
			// Log the error caught either from fetching KB or the axios call
			console.error("Error in deleteKb controller:", err.message);
			// Return a generic server error, or customize based on the caught error
			return ctx.internalServerError(err.message || "Server error");
		}
	},

	async whatsappUrl(ctx){
		try{
		const result= await strapi.db.query('api::knowledgebase.knowledgebase').findOne({
			where: {
				kb_id: ctx.request.body.chatbot,
			},
			populate:{
				whatsapp_integration_model:true,
			}
		}
		  );
		  if(result && result.whatsapp_integration_model){
			return `https://wa.me/${result.whatsapp_integration_model.phone_number}`
		  }else{
			return ctx.throw(500, "No whatsapp number available for this chatbot");
		  }
		}catch(e){
			return ctx.throw(500, e?.message);
		}
	}
}) );
