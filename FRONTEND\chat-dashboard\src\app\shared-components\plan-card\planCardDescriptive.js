import "./plan-card.css";
import { Card } from "@mui/material";
import { Divider } from "@mui/material";
import FuseSvgIcon from "@fuse/core/FuseSvgIcon/FuseSvgIcon";

const PlanCardDescriptive = ({
  color = "#0A0A0B",
  textColor = "#CDCDCD",
  buttonColor = "#0A0A0B",
  buttonTextColor = '#FFFFFF',
  disableButton = false,
  buttonText = "Subscribe",
  isCurrentPlan = false,
  onClick,
  title = "",
  description = "" ,
}) => {
  return (
    <Card className="plan-card flex flex-col gap-8 text-start relative items-start" style={{ backgroundColor: color }}>
      {/* {isCurrentPlan?<div className='bg-base-purple rounded text-white w-fit p-6 absolute top-0 right-0'>
			Active
		</div>:<></>} */}
      <div className="plan-title text-2xl" style={{ color: textColor }}>{title} </div>
      <div className="plan-description" style={{ color: textColor }}>{description}</div>


      {disableButton ? (
        <></>
      ) : (
        
        <div className="flex-row justify-center items-center w-full">
          	<Divider className="mt-10 mb-6" style={{width:'100%' , borderColor:'#d4d4d4'}} />  <></>
        <button
          className="shine-button press-button plan-button text-center bg-base-purple text-l rounded text-white p-10 w-full mt-16"
          size="sm"
          variant="contained"
          onClick={(e) => onClick()}
          style={{ backgroundColor: buttonColor , color:buttonTextColor }}
        >
          {buttonText}
        </button>
        </div>
      )}
    </Card>
  );
};
export default PlanCardDescriptive;
