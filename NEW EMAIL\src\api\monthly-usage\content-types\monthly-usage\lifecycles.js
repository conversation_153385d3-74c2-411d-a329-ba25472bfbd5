'use strict';

/**
 * Monthly usage lifecycle
 */
const axios = require('axios');
module.exports = {
	
	async afterCreate(event){
		const newRec= await strapi.query('api::monthly-usage.monthly-usage').findOne( { where: { id: event.result.id },populate:{organization:true}});
		const data={
			org_id: newRec.organization.id,
			query_tokens_count: 0,
			cost: 0,
			cost_training: 0,
			cost_query: 0,
			training_chars_count: 0,
			query_messages_count: 0
		}
		const updatedOrg = await axios.put(`${process.env.TALKBASE_BASE_URL}/v4/update_org`, data, {
			headers: {
				'Content-Type': 'multipart/form-data'
			}
		}).catch(e=>{
			console.log(e);
		});
		
	}

  };
