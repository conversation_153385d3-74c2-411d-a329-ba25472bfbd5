{"kind": "collectionType", "collectionName": "file_trains", "info": {"singularName": "file-train", "pluralName": "file-trains", "displayName": "File Train", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"file": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["files", "images", "videos", "audios"]}, "train_status": {"type": "boolean", "default": false}, "file_name": {"type": "string"}, "knowledgebase": {"type": "relation", "relation": "oneToOne", "target": "api::knowledgebase.knowledgebase", "inversedBy": "file_trains"}}}