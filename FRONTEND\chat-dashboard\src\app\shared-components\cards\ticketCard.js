import React from "react";
import { motion } from "framer-motion";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import { PillImage } from "../pill/pill";
import WebIcon from "@mui/icons-material/Web";
import TryIcon from "@mui/icons-material/Try";

const TicketOutterCard = ({ children }) => (
  <div className="bg-white rounded-md shadow-md hover:shadow-lg transition-shadow duration-300">
    {children}
  </div>
);

const TicketCardHeader = ({ children }) => (
  <div className="px-6 py-4 flex items-center justify-between mr-16">
    {children}
  </div>
);

const TicketCardContent = ({ children }) => (
  <div className="px-6 mx-8 py-4 space-y-2">{children}</div>
);

const TicketCard = ({
  priority,
  ticketId,
  title,
  description,
  channel,
  email = null,
  phone = null,
}) => {
  const priorityColor = () => {
    switch (priority) {
      case "high":
        return "bg-red-500";
      case "medium":
        return "bg-yellow-500";
      case "low":
        return "bg-green-500";
      default:
        return "";
    }
  };

  const channelIcon = () => {
    switch (channel) {
      case "whatsapp":
        return (
          <PillImage text={"Whatsapp"} bgColor={"#dcf8c6"}>
            <WhatsAppIcon className="text-lg" />
          </PillImage>
        );
      case "web":
        return (
          <PillImage text={"Web"} bgColor={"#b9def2"}>
            <WebIcon className="text-lg" />
          </PillImage>
        );
      default:
        return null;
    }
  };

  const createdBy = () => {
    return `Created by ${email || ""}${email && phone ? " and " : ""}${
      phone || ""
    }`;
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ type: "tween", duration: 0.2 }}
      className="w-full m-32"
    >
      <TicketOutterCard>
        <TicketCardHeader>
          <div className="mx-8 flex flex-col">
            <div
              className={`flex h-6 ${priorityColor()} rounded-md w-[48px] my-8`}
            />
            <h3 className="text-lg font-medium">{ticketId}</h3>
          </div>
          <div className="flex flex-row">
            <div className="">
              <TryIcon className="mr-4 text-lg" />
              <span className="font-regular text-md mr-8">{"Created by Agent"}</span>
            </div>
            <div className="ml-4 ">{channelIcon()}</div>
          </div>
        </TicketCardHeader>
        <TicketCardContent>
          <p className="text-gray-800 text-md">{createdBy()}</p>

          <div className="flex flex-col  flex-start items-start justify-between">
            <span className=" my-8 font-medium text-lg">{title}</span>
            <span className="mb-8 font-regular text-md">{description}</span>

            <button className="mb-16 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-300">
              View Ticket
            </button>
          </div>
        </TicketCardContent>
      </TicketOutterCard>
    </motion.div>
  );
};
export default TicketCard;
