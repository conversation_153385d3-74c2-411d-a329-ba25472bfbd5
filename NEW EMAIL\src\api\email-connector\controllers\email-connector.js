'use strict';

const { emailProviderFactory } = require('../../../providers/email-provider-factory');

/**
 * Email Connector Controller
 * Handles all email-related operations through the provider factory
 */
module.exports = {
  /**
   * Send a single email
   */
  async sendEmail(ctx) {
    try {
      const { body } = ctx.request;
      const {
        to,
        subject,
        text,
        html,
        templateId,
        templateData = {},
        provider,
        from,
        attachments = []
      } = body;

      // Validate required fields
      if (!to) {
        return ctx.badRequest('Recipient email is required');
      }

      if (!templateId && !subject) {
        return ctx.badRequest('Either templateId or subject is required');
      }

      if (!templateId && !text && !html) {
        return ctx.badRequest('Email content (text or html) is required when not using a template');
      }

      // Prepare email data
      const emailData = {
        to,
        subject,
        text,
        html,
        templateId,
        templateData,
        from,
        attachments
      };

      // Send email using provider factory
      const result = await emailProviderFactory.sendEmail(emailData, provider);

      // Log the email if successful
      if (result.success) {
        await strapi.service('api::email-log.email-log').create({
          data: {
            message_id: result.messageId,
            provider: result.provider,
            to_emails: Array.isArray(to) ? to : [to],
            from_email: from || process.env.SENDGRID_FROM_EMAIL,
            subject: subject,
            template_id: templateId,
            template_data: templateData,
            status: 'sent',
            provider_response: result,
            sent_at: new Date(),
            organization: ctx.state.user?.organization?.id,
            user: ctx.state.user?.id
          }
        });
      }

      ctx.send({
        success: true,
        data: result,
        message: 'Email sent successfully'
      });

    } catch (error) {
      strapi.log.error('Email send error:', error);

      // Log failed email attempt
      try {
        const { body } = ctx.request;
        await strapi.service('api::email-log.email-log').create({
          data: {
            provider: error.provider || 'unknown',
            to_emails: Array.isArray(body.to) ? body.to : [body.to],
            from_email: body.from || process.env.SENDGRID_FROM_EMAIL,
            subject: body.subject,
            template_id: body.templateId,
            template_data: body.templateData || {},
            status: 'failed',
            error_message: error.message || error.error,
            provider_response: error,
            organization: ctx.state.user?.organization?.id,
            user: ctx.state.user?.id
          }
        });
      } catch (logError) {
        strapi.log.error('Failed to log email error:', logError);
      }

      ctx.badRequest({
        success: false,
        error: error.message || error.error || 'Failed to send email',
        details: error.details
      });
    }
  },

  /**
   * Send bulk emails
   */
  async sendBulkEmails(ctx) {
    try {
      const { body } = ctx.request;
      const { emails, provider } = body;

      if (!emails || !Array.isArray(emails) || emails.length === 0) {
        return ctx.badRequest('Emails array is required and must not be empty');
      }

      // Validate each email in the array
      for (let i = 0; i < emails.length; i++) {
        const email = emails[i];
        if (!email.to) {
          return ctx.badRequest(`Email at index ${i} is missing recipient`);
        }
        if (!email.templateId && !email.subject) {
          return ctx.badRequest(`Email at index ${i} is missing templateId or subject`);
        }
      }

      // Send bulk emails using provider factory
      const result = await emailProviderFactory.sendBulkEmails(emails, provider);

      // Log bulk email operation
      const bulkLogPromises = result.results.map(async (emailResult, index) => {
        const emailData = emails[index];
        return strapi.service('api::email-log.email-log').create({
          data: {
            message_id: emailResult.messageId,
            provider: result.provider,
            to_emails: Array.isArray(emailData.to) ? emailData.to : [emailData.to],
            from_email: emailData.from || process.env.SENDGRID_FROM_EMAIL,
            subject: emailData.subject,
            template_id: emailData.templateId,
            template_data: emailData.templateData || {},
            status: emailResult.success ? 'sent' : 'failed',
            error_message: emailResult.error,
            provider_response: emailResult,
            sent_at: emailResult.success ? new Date() : null,
            organization: ctx.state.user?.organization?.id,
            user: ctx.state.user?.id
          }
        });
      });

      await Promise.allSettled(bulkLogPromises);

      ctx.send({
        success: true,
        data: result,
        message: `Bulk email completed: ${result.successCount} sent, ${result.failureCount} failed`
      });

    } catch (error) {
      strapi.log.error('Bulk email send error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to send bulk emails',
        details: error.details
      });
    }
  },

  /**
   * Send email using template
   */
  async sendTemplateEmail(ctx) {
    try {
      const { body } = ctx.request;
      const { templateName, to, templateData = {}, provider } = body;

      if (!templateName || !to) {
        return ctx.badRequest('Template name and recipient email are required');
      }

      // Find the template (simplified)
      // Priority: Global template > System template
      let template = await strapi.query('api::email-template.email-template').findOne({
        where: { 
          name: templateName, 
          scope: 'global',
          is_active: true
        }
      });

      // Fallback to system template if no global template found
      if (!template) {
        template = await strapi.query('api::email-template.email-template').findOne({
          where: { 
            name: templateName, 
            scope: 'system',
            is_active: true
          }
        });
      }

      if (!template) {
        return ctx.badRequest(`Email template '${templateName}' not found or inactive`);
      }

      // Debug template lookup
      strapi.log.info('🔍 Template found:', {
        name: template.name,
        template_type: template.template_type,
        provider: template.provider,
        has_html_content: !!template.html_content,
        html_content_length: template.html_content?.length || 0
      });

      // Prepare email data using template
      let emailData = {};

      if (template.template_type === 'provider_template') {
        // Use provider template
        emailData = {
          to,
          templateId: template.template_id,
          templateData: {
            ...template.variables,
            ...templateData
          },
          from: template.default_from_email,
          subject: template.subject
        };
      } else if (template.template_type === 'html_content') {
        // Use HTML content with template rendering
        const TemplateRenderer = require('../../../services/template-renderer');
        
        // Debug template variables
        const mergedVariables = { ...template.variables, ...templateData };
        strapi.log.info('🎨 Rendering HTML template:', {
          template_name: template.name,
          template_variables_count: Object.keys(template.variables || {}).length,
          provided_data_count: Object.keys(templateData).length,
          merged_variables_count: Object.keys(mergedVariables).length,
          sample_variables: Object.keys(mergedVariables).slice(0, 5)
        });
        
        const renderedHtml = TemplateRenderer.renderAdvancedTemplate(
          template.html_content,
          mergedVariables
        );
        const renderedText = template.text_content ? 
          TemplateRenderer.renderTemplate(template.text_content, mergedVariables) : null;

        strapi.log.info('📧 Rendered email content:', {
          html_length: renderedHtml?.length || 0,
          text_length: renderedText?.length || 0,
          has_html: !!renderedHtml,
          has_text: !!renderedText
        });

        emailData = {
          to,
          html: renderedHtml,
          text: renderedText,
          from: template.default_from_email,
          subject: template.subject
        };
      } else if (template.template_type === 'hybrid') {
        // Try provider template first, fallback to HTML if needed
        emailData = {
          to,
          templateId: template.template_id,
          templateData: {
            ...template.variables,
            ...templateData
          },
          from: template.default_from_email,
          subject: template.subject
        };
      }

      // Send email using the template's provider or specified provider
      const templateProvider = provider || template.provider;
      
      // Debug email data being sent to provider
      strapi.log.info('📤 Sending email via provider:', {
        provider: templateProvider,
        to: emailData.to,
        has_html: !!emailData.html,
        has_text: !!emailData.text,
        has_templateId: !!emailData.templateId,
        from: emailData.from,
        subject: emailData.subject
      });
      
      const result = await emailProviderFactory.sendEmail(emailData, templateProvider);

      // Update template usage
      await strapi.query('api::email-template.email-template').update({
        where: { id: template.id },
        data: {
          usage_count: template.usage_count + 1,
          last_used: new Date()
        }
      });

      // Log the email
      if (result.success) {
        await strapi.service('api::email-log.email-log').create({
          data: {
            message_id: result.messageId,
            provider: result.provider,
            to_emails: Array.isArray(to) ? to : [to],
            from_email: emailData.from || process.env.SENDGRID_FROM_EMAIL,
            subject: emailData.subject,
            template_id: template.template_id,
            template_data: templateData,
            status: 'sent',
            provider_response: result,
            sent_at: new Date(),
            email_template: template.id,
            organization: ctx.state.user?.organization?.id,
            user: ctx.state.user?.id
          }
        });
      }

      ctx.send({
        success: true,
        data: result,
        template: {
          id: template.id,
          name: template.name,
          provider: templateProvider
        },
        message: 'Template email sent successfully'
      });

    } catch (error) {
      strapi.log.error('Template email send error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to send template email',
        details: error.details
      });
    }
  },

  /**
   * Test email providers
   */
  async testProviders(ctx) {
    try {
      const { provider } = ctx.query;
      
      const results = await emailProviderFactory.testProviders(provider);
      
      ctx.send({
        success: true,
        data: results,
        message: 'Provider test completed'
      });
    } catch (error) {
      strapi.log.error('Provider test error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to test providers'
      });
    }
  },

  /**
   * Get available email providers
   */
  async getProviders(ctx) {
    try {
      const providers = emailProviderFactory.getAvailableProviders();
      
      ctx.send({
        success: true,
        data: {
          providers,
          defaultProvider: emailProviderFactory.defaultProvider?.getProviderName()
        },
        message: 'Available providers retrieved'
      });
    } catch (error) {
      strapi.log.error('Get providers error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to get providers'
      });
    }
  },

  /**
   * Get email delivery status
   */
  async getDeliveryStatus(ctx) {
    try {
      const { messageId } = ctx.params;
      const { provider } = ctx.query;

      if (!messageId) {
        return ctx.badRequest('Message ID is required');
      }

      // Get status from provider
      const providerInstance = emailProviderFactory.getProvider(provider);
      const status = await providerInstance.getDeliveryStatus(messageId);

      // Update email log if found
      const emailLog = await strapi.query('api::email-log.email-log').findOne({
        where: { message_id: messageId }
      });

      if (emailLog && status.status !== 'unknown') {
        await strapi.query('api::email-log.email-log').update({
          where: { id: emailLog.id },
          data: {
            status: status.status,
            delivered_at: status.deliveredAt || emailLog.delivered_at,
            opened_at: status.openedAt || emailLog.opened_at,
            clicked_at: status.clickedAt || emailLog.clicked_at
          }
        });
      }

      ctx.send({
        success: true,
        data: status,
        message: 'Delivery status retrieved'
      });

    } catch (error) {
      strapi.log.error('Get delivery status error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to get delivery status'
      });
    }
  }
}; 