module.exports = [
  {
      method:  "POST",
      path:    "/send-otp",
      handler: "otp.send",
      config: {
        policies: [
          'phoneNumberValidation',
        ],
        auth: false,
      },
    },
    {
      method:  "POST",
      path:    "/otp-signin",
      handler: "otp.signin",
      config: {
        policies: [
          'phoneNumberValidation',
          'findUserValidation'
        ],
        auth: false,
      },
    },
    {
      method:  "POST",
      path:    "/otp-signup",
      handler: "otp.signup",
      config: {
        policies: [
          'phoneNumberValidation',
          'existingUserPhoneValidation'
        ],
        auth: false,
      },
    },
    {
      method:  "POST",
      path:    "/verify-otp",
      handler: "otp.verify",
      config: {
        policies: [
          'phoneNumberValidation',
          'otpSentValidation'
        ],
        auth: false,
      },
    },
    {
      method:  "POST",
      path:    "/resend-otp",
      handler: "otp.resend",
      config: {
        policies: [
          'phoneNumberValidation'
        ],
        auth: false,
      },
    },
];
