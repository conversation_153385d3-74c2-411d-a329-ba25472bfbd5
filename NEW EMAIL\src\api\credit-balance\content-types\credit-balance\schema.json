{"kind": "collectionType", "collectionName": "credit_balances", "info": {"singularName": "credit-balance", "pluralName": "credit-balances", "displayName": "Credit Balance", "description": "Stores the balance of non-expiring credits for an organization."}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"available_credits": {"type": "integer", "default": 0, "required": true, "min": 0}, "organization": {"type": "relation", "relation": "oneToOne", "target": "api::organization.organization", "inversedBy": "credit_balance"}}}