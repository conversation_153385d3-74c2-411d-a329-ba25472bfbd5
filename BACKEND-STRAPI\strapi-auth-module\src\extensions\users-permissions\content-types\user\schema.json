{"kind": "collectionType", "collectionName": "up_users", "info": {"name": "user", "description": "", "singularName": "user", "pluralName": "users", "displayName": "User"}, "options": {"draftAndPublish": false, "timestamps": true}, "attributes": {"username": {"type": "string", "minLength": 3, "unique": true, "configurable": false, "required": true}, "email": {"type": "email", "minLength": 6, "configurable": false, "required": true}, "provider": {"type": "string", "configurable": false}, "password": {"type": "password", "minLength": 6, "configurable": false, "private": true}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true}, "confirmationToken": {"type": "string", "configurable": false, "private": true}, "confirmed": {"type": "boolean", "default": false, "configurable": false}, "blocked": {"type": "boolean", "default": false, "configurable": false}, "role": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.role", "inversedBy": "users", "configurable": false}, "phone": {"type": "string", "unique": true}, "organization": {"type": "relation", "relation": "manyToOne", "target": "api::organization.organization", "inversedBy": "users"}, "acc_type": {"type": "enumeration", "enum": ["individual", "organization"]}, "scrumboards": {"type": "relation", "relation": "manyToMany", "target": "api::scrumboard.scrumboard", "mappedBy": "members"}, "scrumboard_card": {"type": "relation", "relation": "manyToOne", "target": "api::scrumboard-card.scrumboard-card", "inversedBy": "memberIds"}}}