{"kind": "collectionType", "collectionName": "email_integrations", "info": {"singularName": "email-integration", "pluralName": "email-integrations", "displayName": "Email Integration", "description": "Manage email integration users for organizations and ticket notifications"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"users": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user", "description": "Users who will receive email notifications for this integration"}, "knowledgebase": {"type": "relation", "relation": "oneToOne", "target": "api::knowledgebase.knowledgebase", "inversedBy": "email_integration", "description": "Associated knowledgebase for this email integration"}, "is_active": {"type": "boolean", "default": true, "description": "Whether this email integration is active"}, "notification_types": {"type": "json", "default": ["ticket-notification", "system-alerts"], "description": "Types of notifications this integration should receive"}, "email_frequency": {"type": "enumeration", "enum": ["immediate", "hourly", "daily", "weekly"], "default": "immediate", "description": "Frequency of email notifications"}, "last_notification_sent": {"type": "datetime", "description": "Timestamp of last notification sent to this integration"}, "total_emails_sent": {"type": "integer", "default": 0, "description": "Total number of emails sent through this integration"}, "metadata": {"type": "json", "default": {}, "description": "Additional configuration and metadata"}}}