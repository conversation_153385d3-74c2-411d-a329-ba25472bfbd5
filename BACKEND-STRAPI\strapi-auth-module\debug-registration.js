/**
 * DEBUG REGISTRATION - Test registration endpoint directly
 */

const axios = require('axios');

async function debugRegistration() {
  console.log('🚨 DEBUG: Testing registration endpoint directly\n');

  const baseURL = 'http://127.0.0.1:1337';
  const timestamp = Date.now();
  const testEmail = `debug${timestamp}@ajentic.com`;
  const testUsername = `debuguser${timestamp}`;
  const testPassword = 'DebugPassword123!';
  
  try {
    console.log('📝 Sending registration request...');
    console.log(`   Email: ${testEmail}`);
    console.log(`   Username: ${testUsername}`);
    console.log(`   Organization: individual`);
    
    const startTime = Date.now();
    
    const registerResponse = await axios.post(`${baseURL}/api/auth/local/register`, {
      username: testUsername,
      email: testEmail,
      password: testPassword,
      organization: 'individual'
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`\n✅ Registration completed in ${duration}ms`);
    console.log('📊 FULL RESPONSE ANALYSIS:');
    console.log('='.repeat(60));
    
    // Analyze response structure
    console.log('Status Code:', registerResponse.status);
    console.log('Response Headers:', Object.keys(registerResponse.headers));
    console.log('\n📋 Response Body Structure:');
    console.log('Response Keys:', Object.keys(registerResponse.data));
    
    // Check for JWT token
    if (registerResponse.data.jwt) {
      console.log('\n❌ CRITICAL ISSUE: JWT TOKEN FOUND!');
      console.log('   JWT Token Length:', registerResponse.data.jwt.length);
      console.log('   JWT Token Preview:', registerResponse.data.jwt.substring(0, 50) + '...');
      console.log('   🚨 This means user will be auto-logged in!');
    } else {
      console.log('\n✅ GOOD: No JWT token in response');
    }
    
    // Check user data
    if (registerResponse.data.user) {
      console.log('\n👤 USER DATA ANALYSIS:');
      console.log('   User ID:', registerResponse.data.user.id);
      console.log('   Email:', registerResponse.data.user.email);
      console.log('   Username:', registerResponse.data.user.username);
      console.log('   Confirmed:', registerResponse.data.user.confirmed);
      console.log('   Provider:', registerResponse.data.user.provider);
      console.log('   Blocked:', registerResponse.data.user.blocked);
      console.log('   Role:', registerResponse.data.user.role);
      console.log('   Organization:', registerResponse.data.user.organization);
      
      if (registerResponse.data.user.confirmed === false) {
        console.log('   ✅ User correctly set as unconfirmed');
      } else {
        console.log('   ❌ User incorrectly confirmed:', registerResponse.data.user.confirmed);
      }
    } else {
      console.log('\n❌ No user data in response');
    }
    
    // Check for message
    if (registerResponse.data.message) {
      console.log('\n📝 Message:', registerResponse.data.message);
    }
    
    // Test immediate access to protected endpoint
    console.log('\n🔒 TESTING PROTECTED ENDPOINT ACCESS:');
    
    if (registerResponse.data.jwt) {
      console.log('   Testing /api/users/me with JWT token...');
      
      try {
        const meResponse = await axios.get(`${baseURL}/api/users/me`, {
          headers: { Authorization: `Bearer ${registerResponse.data.jwt}` }
        });
        
        console.log('   ❌ CRITICAL: User can access /api/users/me immediately!');
        console.log('   User confirmed status:', meResponse.data.confirmed);
        console.log('   🚨 This confirms auto-login bug!');
        
      } catch (error) {
        console.log('   ✅ GOOD: JWT token rejected:', error.response?.status);
      }
    } else {
      console.log('   ✅ No JWT token to test with');
    }
    
    // Full response dump for debugging
    console.log('\n🔍 COMPLETE RESPONSE DUMP:');
    console.log('='.repeat(60));
    console.log(JSON.stringify(registerResponse.data, null, 2));
    
    console.log('\n📊 DIAGNOSIS:');
    console.log('='.repeat(60));
    
    const hasJWT = !!registerResponse.data.jwt;
    const userConfirmed = registerResponse.data.user?.confirmed;
    
    if (hasJWT && userConfirmed === false) {
      console.log('❌ BUG CONFIRMED: JWT token returned for unconfirmed user');
      console.log('   Root cause: Extension not removing JWT token properly');
    } else if (hasJWT && userConfirmed === true) {
      console.log('❌ BUG CONFIRMED: User auto-confirmed during registration');
      console.log('   Root cause: User being set as confirmed during creation');
    } else if (!hasJWT && userConfirmed === false) {
      console.log('✅ WORKING CORRECTLY: No JWT token, user unconfirmed');
    } else {
      console.log('❓ UNEXPECTED STATE:', { hasJWT, userConfirmed });
    }
    
  } catch (error) {
    console.error('❌ Registration test failed:');
    console.error('Status:', error.response?.status);
    console.error('Error:', error.response?.data || error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Server not running. Start with: npm start');
    }
  }
}

// Run the debug test
debugRegistration();
