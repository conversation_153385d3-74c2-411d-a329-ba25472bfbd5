{"kind": "collectionType", "collectionName": "bookmarks", "info": {"singularName": "bookmark", "pluralName": "bookmarks", "displayName": "Bookmark", "description": "User bookmarks for file results (tracks)"}, "options": {"draftAndPublish": false, "timestamps": true}, "attributes": {"notes": {"type": "text"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "bookmarks"}, "file_results_models": {"type": "relation", "relation": "manyToOne", "target": "api::file-results-model.file-results-model", "inversedBy": "bookmarks"}}}