import Typography from "@mui/material/Typography";
import * as React from "react";
import { useSelector } from "react-redux";
import { selectUser } from "app/store/userSlice";
import IconButton from "@mui/material/IconButton";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import { useDispatch } from "react-redux";
import { showMessage } from "app/store/fuse/messageSlice";

export default function OrganisationHeader() {
  const user = useSelector(selectUser);
  const dispatch = useDispatch();

  const copyToClipboard = (text) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        dispatch(showMessage({ message: "Copied to Clipboard" }));
      })
      .catch((err) => {
        dispatch(showMessage({ message: "Could not copy to Clipboard" }));
      });
  };

  return (
    <div className="flex w-full bg-transparent pb-16">
      <div className="flex flex-col sm:flex-row bg-transparent flex-auto min-w-0 p-24 md:p-32 pb-0 md:pb-0">
        <div className="flex flex-col flex-auto bg-transparent">
          <Typography className="text-3xl mt-32 bg-transparent font-bold tracking-tight leading-8">
            Organisation
          </Typography>
          <Typography className="font-regular bg-transparent text-gray-800 tracking-tight mt-4">
            Information about your organisation
          </Typography>
          <div className="org-id-container flex items-center mt-16"></div>
        </div>
      </div>
    </div>
  );
}
