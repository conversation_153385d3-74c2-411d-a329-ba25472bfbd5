{"kind": "collectionType", "collectionName": "scrumboard_automations", "info": {"singularName": "scrumboard-automation", "pluralName": "scrumboard-automations", "displayName": "scrumboard_automation", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"prompt": {"type": "text", "required": true, "default": "You customer service manager. Given some conversation history you have to create a ticket json model with the following fields. Don’t explain your answer. {    “title”:Subject of the inquiry    “description”: A detailed description of the customers enquiry,    “conversation_summary”:A brief overview of the conversation history,    “severity_label”:The level of severity of the inquiry as a label. Possible value “Urgent” “High” “Low” “Very Low”,    “area_label”:Category of the inquiry based on area of work as a Label,    “nature_label”: Type of inquiry, based on nature of work as a Label,    “new_customer”: Bool indicating if the customer is new or existing. True it it is a new customer, false otherwise,    “suggested_actions”: A series of recommended steps to address the customer’s inquiry,    “requires_on_site_support”: Indicates whether on-site support is needed,    “did_customer_request_visit”: Indicates if the customer requested an on-site visit,    “customer_engagement_score”: A numeric score representing the level of customer engagement out of 100\",    “lead_potential”: Assessment of the potential for the customer to become a lead as a string a label with possible values “Very High” “High” “Medium” “Low” Very Low”    “follow_up_required”: Indicates if follow-up action is needed,    “customer_segment”: Classification of the customer,    “service_interest_level”: The customer’s level of interest in the service as a label, possible values are “Very High” “High” “Medium” “Low” Very Low”    “escalation_required”: Indicates if the inquiry needs to be escalated,    “feedback_requested”: Indicates if feedback was requested from the customer,    “product_interest”: The specific product the customer is interested in    “estimated_resolution_time”: The estimated time to resolve the customer’s inquiry }"}, "automation_identifier": {"type": "enumeration", "enum": ["ticket_create"], "default": "ticket_create"}, "scrumboard": {"type": "relation", "relation": "manyToOne", "target": "api::scrumboard.scrumboard", "inversedBy": "scrumboard_automations"}}}