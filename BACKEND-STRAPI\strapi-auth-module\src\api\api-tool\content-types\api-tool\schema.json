{"kind": "collectionType", "collectionName": "api-tools", "info": {"singularName": "api-tool", "pluralName": "api-tools", "displayName": "api-tools", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "type": {"type": "enumeration", "required": true, "enum": ["inbuilt", "api"], "default": "inbuilt"}, "enabled": {"type": "boolean", "default": true}, "supports_post_request": {"type": "boolean", "default": false}, "description": {"type": "text", "required": true}, "method": {"type": "enumeration", "required": true, "enum": ["GET", "POST", "PUT", "DELETE", "PATCH"]}, "path_params": {"type": "json"}, "query_params": {"type": "json"}, "body": {"type": "json"}, "body_type": {"type": "enumeration", "enum": ["json", "form-data", "x-www-form-urlencoded"]}, "headers": {"type": "json"}, "api_docs": {"type": "text", "required": true}, "auth_type": {"type": "enumeration", "required": true, "enum": ["none", "basic", "bearer"]}, "auth_token": {"type": "string"}, "base_url": {"type": "string", "required": true}, "knowledgebases": {"type": "relation", "relation": "manyToMany", "target": "api::knowledgebase.knowledgebase", "inversedBy": "apiTools"}}}