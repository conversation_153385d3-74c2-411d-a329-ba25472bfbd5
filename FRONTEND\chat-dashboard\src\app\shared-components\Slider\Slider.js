import React, { useState } from 'react';
import './Slider.css';

const WeightSlider = () => {
  const [weight, setWeight] = useState(68);

  const handleSliderChange = (event) => {
    setWeight(Number(event.target.value));
  };

  return (
    <div className="flex flex-col items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <span className="text-lg font-medium text-gray-700">Max Sections</span>
          <span className="text-2xl font-bold text-gray-900">{weight}</span>
        </div>
        <div className="relative w-full">
          <input
            type="range"
            min={3}
            max={10}
            value={weight}
            onChange={handleSliderChange}
            className="slider w-full h-1 bg-gray-300 rounded-lg focus:outline-none"
            style={{
              appearance: 'none' }}
          />
          {/* <div
            className="absolute top-1/2 transform -translate-y-1/2 bg-white border-2 border-gray-400 rounded-full h-6 w-6"
            style={{ left: `${(weight / 150) * 100}%`, transform: 'translate(-50%, -50%)' }}
          /> */}
        </div>
      </div>
    </div>
  );
};

export default WeightSlider;
