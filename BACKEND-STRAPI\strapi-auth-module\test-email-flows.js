/**
 * Comprehensive Email Flow Testing Script
 * Tests all email flows including the new ticket notification system
 */

const emailService = require('./src/helpers/email-service');

async function testEmailFlows() {
  console.log('🧪 TESTING ALL EMAIL FLOWS');
  console.log('='.repeat(60));
  
  const testResults = {
    confirmationEmail: { status: 'pending', error: null },
    welcomeEmail: { status: 'pending', error: null },
    passwordResetEmail: { status: 'pending', error: null },
    ticketNotificationEmail: { status: 'pending', error: null },
    templateLoading: { status: 'pending', error: null }
  };

  // Test 1: Template Loading
  console.log('\n📄 Testing Template Loading...');
  try {
    const confirmationTemplate = emailService.loadTemplate('confirmation-email');
    const welcomeTemplate = emailService.loadTemplate('welcome-email');
    const resetTemplate = emailService.loadTemplate('reset-password');
    const ticketTemplate = emailService.loadTemplate('ticket-notification');
    
    console.log('✅ All templates loaded successfully:');
    console.log(`  - Confirmation: ${confirmationTemplate.name} (${confirmationTemplate.provider})`);
    console.log(`  - Welcome: ${welcomeTemplate.name} (${welcomeTemplate.provider})`);
    console.log(`  - Reset: ${resetTemplate.name} (${resetTemplate.provider})`);
    console.log(`  - Ticket: ${ticketTemplate.name} (${ticketTemplate.provider})`);
    
    testResults.templateLoading.status = 'passed';
  } catch (error) {
    console.error('❌ Template loading failed:', error.message);
    testResults.templateLoading.status = 'failed';
    testResults.templateLoading.error = error.message;
  }

  // Test 2: Confirmation Email
  console.log('\n📧 Testing Confirmation Email...');
  try {
    await emailService.sendConfirmationEmail({
      email: '<EMAIL>',
      name: 'Test User',
      confirmationUrl: 'https://podycy.com/confirm/test123'
    });
    console.log('✅ Confirmation email sent successfully');
    testResults.confirmationEmail.status = 'passed';
  } catch (error) {
    console.error('❌ Confirmation email failed:', error.message);
    testResults.confirmationEmail.status = 'failed';
    testResults.confirmationEmail.error = error.message;
  }

  // Test 3: Welcome Email
  console.log('\n🎉 Testing Welcome Email...');
  try {
    await emailService.sendWelcomeEmail({
      email: '<EMAIL>',
      name: 'Test User'
    });
    console.log('✅ Welcome email sent successfully');
    testResults.welcomeEmail.status = 'passed';
  } catch (error) {
    console.error('❌ Welcome email failed:', error.message);
    testResults.welcomeEmail.status = 'failed';
    testResults.welcomeEmail.error = error.message;
  }

  // Test 4: Password Reset Email
  console.log('\n🔐 Testing Password Reset Email...');
  try {
    await emailService.sendPasswordResetEmail({
      email: '<EMAIL>',
      name: 'Test User',
      resetUrl: 'https://podycy.com/reset/test123'
    });
    console.log('✅ Password reset email sent successfully');
    testResults.passwordResetEmail.status = 'passed';
  } catch (error) {
    console.error('❌ Password reset email failed:', error.message);
    testResults.passwordResetEmail.status = 'failed';
    testResults.passwordResetEmail.error = error.message;
  }

  // Test 5: Ticket Notification Email
  console.log('\n🎫 Testing Ticket Notification Email...');
  try {
    await emailService.sendTicketNotificationEmail({
      emails: ['<EMAIL>', '<EMAIL>'],
      name: 'Admin User',
      title: 'Test Support Ticket',
      description: 'This is a test ticket created for email flow validation.',
      agent_name: 'Test Agent',
      customer_email: '<EMAIL>',
      ticket_url: 'https://podycy.com/tickets/test123'
    });
    console.log('✅ Ticket notification email sent successfully');
    testResults.ticketNotificationEmail.status = 'passed';
  } catch (error) {
    console.error('❌ Ticket notification email failed:', error.message);
    testResults.ticketNotificationEmail.status = 'failed';
    testResults.ticketNotificationEmail.error = error.message;
  }

  // Test Summary
  console.log('\n📊 TEST SUMMARY');
  console.log('='.repeat(60));
  
  const totalTests = Object.keys(testResults).length;
  const passedTests = Object.values(testResults).filter(result => result.status === 'passed').length;
  const failedTests = Object.values(testResults).filter(result => result.status === 'failed').length;
  
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests} ✅`);
  console.log(`Failed: ${failedTests} ❌`);
  
  if (failedTests > 0) {
    console.log('\n❌ FAILED TESTS:');
    Object.entries(testResults).forEach(([testName, result]) => {
      if (result.status === 'failed') {
        console.log(`  - ${testName}: ${result.error}`);
      }
    });
  }
  
  console.log('\n🔧 ENVIRONMENT CHECK:');
  console.log(`POSTMARK_API_KEY: ${process.env.POSTMARK_API_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log(`POSTMARK_FROM_EMAIL: ${process.env.POSTMARK_FROM_EMAIL || '<EMAIL> (default)'}`);
  console.log(`POSTMARK_FROM_NAME: ${process.env.POSTMARK_FROM_NAME || 'Podycy Team (default)'}`);
  
  return {
    success: failedTests === 0,
    totalTests,
    passedTests,
    failedTests,
    results: testResults
  };
}

// Run tests if called directly
if (require.main === module) {
  testEmailFlows()
    .then(results => {
      console.log('\n🏁 Email flow testing completed');
      process.exit(results.success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test runner failed:', error);
      process.exit(1);
    });
}

module.exports = { testEmailFlows };
