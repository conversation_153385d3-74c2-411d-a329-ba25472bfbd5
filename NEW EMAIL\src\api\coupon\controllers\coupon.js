'use strict';

/**
 * coupon controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::coupon.coupon', ({ strapi }) => ({
  /**
   * Custom action to redeem a coupon.
   * @param {Object} ctx - Koa context.
   */
  async redeem(ctx) {
    const { couponCode, organizationId } = ctx.request.body;

    if (!couponCode || !organizationId) {
      return ctx.badRequest('Coupon code and organization ID are required.');
    }
    
    // Get the authenticated user
    const user = ctx.state.user;
    if (!user) {
      return ctx.unauthorized('You must be authenticated to redeem a coupon.');
    }
    
    // Ensure organizationId is treated as a number if it comes as a string
    const orgId = parseInt(organizationId, 10);
    if (isNaN(orgId)) {
        return ctx.badRequest('Valid Organization ID is required.');
    }

    try {
      // Step 1: Find the coupon by code
      const coupons = await strapi.entityService.findMany('api::coupon.coupon', {
        filters: { code: couponCode },
        // Populate redemption_logs and the organization within each log for accurate checks
        populate: { 
          redemption_logs: {
            populate: { organization: true }
          }
        }
      });

      if (!coupons || coupons.length === 0) {
        return ctx.badRequest('Invalid coupon code.');
      }
      const coupon = coupons[0]; // code is unique

      // Step 2: Validate the coupon (active, not expired, etc.)
      if (!coupon.is_active) {
        return ctx.badRequest('This coupon is no longer active.');
      }

      const now = new Date();
      if (coupon.valid_from && new Date(coupon.valid_from) > now) {
        return ctx.badRequest('This coupon is not yet valid.');
      }
      if (coupon.valid_until && new Date(coupon.valid_until) < now) {
        return ctx.badRequest('This coupon has expired.');
      }

      // Step 3: Check if the organization has already redeemed this coupon type beyond allowed limit
      const redemptionsByThisOrg = coupon.redemption_logs.filter(
        log => log.organization && log.organization.id === orgId
      );

      if (redemptionsByThisOrg.length >= coupon.max_redemptions_per_organization) {
        return ctx.badRequest('You have already redeemed this coupon the maximum number of times.');
      }

      // Step 4: Check overall redemption limits for the coupon
      if (coupon.max_total_redemptions !== null && coupon.max_total_redemptions > 0) {
        if (coupon.redemption_logs.length >= coupon.max_total_redemptions) {
          return ctx.badRequest('This coupon has reached its overall redemption limit.');
        }
      }

      // Step 5: Fetch or create the organization\'s credit balance
      let creditBalanceEntries = await strapi.entityService.findMany('api::credit-balance.credit-balance', {
        filters: { organization: orgId },
        limit: 1
      });

      let updatedCreditBalance;
      if (creditBalanceEntries && creditBalanceEntries.length > 0) {
        const currentBalance = creditBalanceEntries[0];
        updatedCreditBalance = await strapi.entityService.update('api::credit-balance.credit-balance', currentBalance.id, {
          data: {
            available_credits: (currentBalance.available_credits || 0) + coupon.credits_awarded
          }
        });
      } else {
        updatedCreditBalance = await strapi.entityService.create('api::credit-balance.credit-balance', {
          data: {
            organization: orgId,
            available_credits: coupon.credits_awarded
          }
        });
      }
      
      // Step 6: Create a one-time order for the coupon redemption
      const oneTimeOrder = await strapi.entityService.create('api::one-time-order.one-time-order', {
        data: {
          organization: orgId,
          user: user.id,
          purchase_type: 'coupon-one-time',
          credits_added: coupon.credits_awarded,
          platform: 'coupon',
          purchase_identifier: `coupon-redemption-${coupon.id}-${orgId}-${Date.now()}`,
          coupon_code: couponCode,
          purchase_timestamp: now.toISOString(),
          processed_timestamp: now.toISOString()
        }
      });
      
      // Step 7: Create a coupon redemption log entry
      const redemptionLog = await strapi.entityService.create('api::coupon-redemption-log.coupon-redemption-log', {
        data: {
          coupon_used: coupon.id,
          organization: orgId,
          redemption_date: now.toISOString(),
          credits_applied_at_redemption: coupon.credits_awarded,
          // publishedAt: now.toISOString(), // Set publishedAt if auto-publish is off for this CT
        }
      });

      // Step 8: Return success response
      return ctx.send({
        message: 'Coupon redeemed successfully!',
        coupon: coupon.code,
        creditsAwarded: coupon.credits_awarded,
        organizationCreditBalance: updatedCreditBalance,
        redemptionLogId: redemptionLog.id,
        oneTimeOrderId: oneTimeOrder.id,
        userId: user.id
      });

    } catch (err) {
      strapi.log.error(`Error redeeming coupon: ${err.message}`, err);
      if (err.name === 'ValidationError' || err.isCustomError) {
        return ctx.badRequest(err.message, err.details || {});
      }
      return ctx.internalServerError('An error occurred during coupon redemption.', { error: err.message });
    }
  },
})); 