import React, { useState } from 'react';
import { Prism as Synta<PERSON><PERSON><PERSON>lighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';

// Custom style to match ChatGPT's code block appearance
const customStyle = {
	...tomorrow,
	'pre[class*="language-"]': {
		...tomorrow['pre[class*="language-"]'],
		backgroundColor: '#f8f8f8',  // Light gray background
		color: '#333',  // Dark text color for better readability
		padding: '15px',  // Padding to give some spacing within the code block
		borderRadius: '0 0 8px 8px',  // Rounded corners for the code block
		overflowX: 'auto',  // Enable horizontal scrolling if code overflows
		margin: '0',  // Reset margin
		borderTop: 'none', // No top border since we have a header
	},
	'code[class*="language-"]': {
		...tomorrow['code[class*="language-"]'],
		backgroundColor: '#f8f8f8',  // Light gray background
		color: '#333',  // Dark text color for better readability
		fontSize: '14px',
		fontFamily: "'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace",
	}
};

// Component for the copy button and code header
const CodeBlockHeader = ({ language, onCopy, copied }) => {
	return (
		<div className="code-block-header">
			<span>{language.toUpperCase()}</span>
			<button className="copy-button" onClick={onCopy}>
				<span className="copy-icon">
					{copied ? (
						<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
							<polyline points="20 6 9 17 4 12"></polyline>
						</svg>
					) : (
						<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
							<rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
							<path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
						</svg>
					)}
				</span>
				{copied ? "Copied!" : "Copy code"}
			</button>
		</div>
	);
};

// Utility function to split content and highlight code blocks
const highlightCode = (content) => {
	// Regex to match code blocks, assuming code blocks are wrapped with `tsx` or similar
	const codeBlockPattern = /```(tsx|javascript|jsx|js|html|css|python|json|bash|shell|cmd|powershell|typescript)?([\s\S]*?)```/g;
	
	const parts = [];
	let lastIndex = 0;

	content.replace(codeBlockPattern, (match, language, code, index) => {
		// Push the text before the code block
		if (index > lastIndex) {
			parts.push(content.slice(lastIndex, index));
		}

		// Push the highlighted code block
		parts.push({
			type: 'code',
			language: language || 'javascript', // Default to 'javascript' if no language specified
			code: code.trim(),
		});

		// Update lastIndex to continue from where the code block ended
		lastIndex = index + match.length;
	});

	// Push remaining text after the last code block
	if (lastIndex < content.length) {
		parts.push(content.slice(lastIndex));
	}

	return parts;
};

const CodeResponse = ({ result }) => {
	const parts = highlightCode(result);
	const [copiedIndex, setCopiedIndex] = useState(-1);

	const handleCopy = (code, index) => {
		navigator.clipboard.writeText(code).then(() => {
			setCopiedIndex(index);
			setTimeout(() => setCopiedIndex(-1), 2000);
		});
	};

	return (
		<div>
			{parts.map((part, index) => {
				if (part.type === 'code') {
					const isCodeCopied = copiedIndex === index;
					return (
						<div key={index} className="code-block-wrapper" style={{ margin: '16px 0', borderRadius: '8px', overflow: 'hidden', border: '1px solid #e5e5e5', boxShadow: '0 2px 6px rgba(0, 0, 0, 0.05)' }}>
							<CodeBlockHeader 
								language={part.language} 
								onCopy={() => handleCopy(part.code, index)}
								copied={isCodeCopied}
							/>
							<SyntaxHighlighter
								language={part.language}
								style={customStyle}
							>
								{part.code}
							</SyntaxHighlighter>
						</div>
					);
				} else {
					// Render plain text for non-code parts
					return <p key={index} style={{ margin: '16px 0', lineHeight: '1.6' }}>{part}</p>;
				}
			})}
		</div>
	);
};

export default CodeResponse;
