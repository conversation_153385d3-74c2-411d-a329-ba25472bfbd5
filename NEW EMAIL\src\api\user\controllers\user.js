'use strict';

/**
 * user controller
 */




module.exports = {
	async verifyEmail(ctx, next) { 

		const user = await strapi.query('plugin::users-permissions.user').findOne({
			where: { email:  ctx.request.body.email },
		}).catch(err => {
			console.log(err);
			return 'valid';
		});
		if(user)
			return 'invalid';
		else 
			return 'valid';

	},

	async verifyUsername(ctx, next) {

		const user = await strapi.query('plugin::users-permissions.user').findOne({
			where: { username:  ctx.request.body.username },
		}).catch(err => {
			console.log(err);
			return 'valid';
		});
		if(user)
			return 'invalid';
		else 
			return 'valid';
	},


};

