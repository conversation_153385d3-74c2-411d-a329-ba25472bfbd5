import React from "react";
import PersonIcon from "@mui/icons-material/Person";
import { Typography, Avatar, Stack, Paper, useTheme } from "@mui/material";
const MessageCard = ({ isSender, text, sources }) => {
  const theme = useTheme();

  return (
    <Stack
      direction={"row"}
      spacing={2}
      width={"100%"}
      alignItems={"flex-start"}
    >
      {/* Avatar positioned outside of the Card */}
      <Avatar
        src={!isSender && "/assets/images/logo/ajentic-logo.png"}
        alt="Sender"
        className={`w-28 h-28 shadow-none ${
          isSender ? "bg-blue-500" : "bg-orange-500"
        }`}
      >
        {isSender && <PersonIcon />}
      </Avatar>

      <Paper
        elevation={0}
        className={`p-8 border-1 ${
          isSender ? "bg-grey-100" : "bg-white"
        } border-grey-300 rounded`}
      >
        <Typography
          className="text-gray-800"
          variant="body1"
          color="text.primary"
        >
          {text}
        </Typography>

        {!isSender && (
          <details className="max-w-xs w-full mt-10">
            <summary className="font-medium text-sm cursor-pointer ">
              Sources
            </summary>
            <p className="text-sm text-gray-700 mt-4 px-8">
              {sources || "No sources"}
            </p>
          </details>
        )}
      </Paper>
    </Stack>
  );
};

export default MessageCard;
