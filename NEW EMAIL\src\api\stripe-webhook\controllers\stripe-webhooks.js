// api/stripe-webhooks/controllers/stripe-webhooks.js
"use strict";
const _ = require("lodash");


const stripePaymentProvider = require("../../../providers/stripe-payment-provider");


module.exports = {
  async webhook(ctx, next) {
    console.log("Stripe webhook trigger");

    try {
      const { type, data } = ctx.request.body;
      const object = data.object;
      console.log("Type", type);

      switch (type) {
        case "checkout.session.completed":
          //await stripePaymentProvider.handleCheckoutSessionCompleted(ctx);
          break;
        case "customer.subscription.created":
          try {
            console.log("in customer.subscription.created");
            await stripePaymentProvider.handleSubscriptionCreated({
              ctx: ctx,
              customerId: object.customer,
              subscriptionId: object.id,
              isTrial: object.trial_start !== null,
              trialStart: object.trial_start,
              trialEnd: object.trial_end,
              startDate: object.current_period_start,
              endDate: object.current_period_end,
              product_id: object.plan.product
            });
          } catch (e) {
            console.log(e);
          }
          break;
        case "invoice.payment_succeeded":
          try {
            console.log("in invoice.payment_succeeded");
            await stripePaymentProvider.handlePaymentSuccess({
              ctx: ctx,
              subscriptionId: object.subscription,
              product_id: object.lines.data[0].plan?.product,
              customerId: object.customer,
              invoice_url: object.hosted_invoice_url,
              start_date: object.lines.data[0].period.start,
              end_date: object.lines.data[0].period.end,

          });
          } catch (e) {
            console.log(e);
          }
          break;
        case "invoice.payment_failed":
          try {
            await stripePaymentProvider.handlePaymentFailed({
              ctx: ctx,
              customerId: object.customer,
              subscriptionId: object.subscription,
              start_date: object.lines.data[0].period.start,
              end_date: object.lines.data[0].period.end,
              invoice_url: object.hosted_invoice_url,
            });
          } catch (e) {
            console.log(e);
          }
          break;
        case "customer.subscription.trial_will_end":
          try {
            console.log("Trial will end");
          } catch (e) {
            console.log(e);
          }
          break;
        case 'customer.subscription.updated':
          try {
            const subscription = data.object;
            await stripePaymentProvider.handleSubscriptionUpdated({
              ctx,
              customerId: subscription.customer,
              subscriptionId: subscription.id,
              isTrial: subscription.trial_end !== null,
              trialEnd: subscription.trial_end,
              startDate: subscription.current_period_start,
              endDate: subscription.current_period_end,
              status: subscription.status,
              product_id: subscription.plan.product
            });
          } catch (e) {
            console.log(e);
          }
          break;
        case "customer.subscription.deleted":
          try {
            const subscription = data.object;
            await stripePaymentProvider.handleSubscriptionDeleted({
              ctx,
              customerId: subscription.customer,
              subscriptionId: subscription.id
            });
          } catch (e) {
            console.log(e);
          }
          break;
        case 'customer.subscription.trial_will_end':
          // try {
          //   await stripePaymentProvider.handleTrialWillEnd(ctx);
          // } catch (e) {
          //   console.log(e);
          // }
          break;
        // Add more cases for other events you want to handle
        default:
          console.log(`Unhandled event type: ${type}`);
      }
      return "Stripe webhook success";
    } catch (e) {
      console.error("Error handling webhook event:", e);
      return "Stripe webhook error";
    }
  },

};
