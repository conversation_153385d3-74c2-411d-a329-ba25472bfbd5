'use strict';

/**
 * In-App Purchase custom controller
 */

const _ = require('lodash');
const axios = require('axios');

module.exports = {
  async verifyAppleReceipt(ctx) {
    try {
      const { receipt_data, product_id, email } = ctx.request.body;
      
      if (!email) {
        return ctx.badRequest('Email is required');
      }

      // Find user by email
      const user = await strapi.query("plugin::users-permissions.user").findOne({
        where: { email },
        populate: {
          organization: true
        }
      });
      
      if (!user) {
        return ctx.unauthorized('User not found');
      }

      // Find the corresponding plan in our system
      const plan = await strapi.query("api::plan.plan").findOne({
        where: { stripe_prod_id: product_id }
      });
      
      if (!plan) {
        return ctx.badRequest('Plan not found');
      }
      
      // *** Add check for purchase_type ***
      if (plan.purchase_type === 'one-time') {
          strapi.log.warn(`Attempted to use subscription verification endpoint for one-time product: ${product_id}, email: ${email}`);
          return ctx.badRequest(`This endpoint is for subscriptions. Use the one-time purchase endpoint for product ID ${product_id}.`);
      }

      // Define Apple verification URLs
      const productionUrl = 'https://buy.itunes.apple.com/verifyReceipt';
      const sandboxUrl = 'https://sandbox.itunes.apple.com/verifyReceipt';
      let verificationData;

      try {
        if (process.env.IAP_ENV === 'production') {
          // --- Production Environment: Use Fallback Logic ---
          strapi.log.info(`[verifyAppleReceipt][Production] Attempting verification with Production URL: ${productionUrl} for email: ${email}`);
          let response = await axios.post(productionUrl, {
            'receipt-data': receipt_data,
            'password': process.env.APPLE_IAP_SECRET
          });
          verificationData = response.data;
          strapi.log.info(`[verifyAppleReceipt][Production] Production URL response status: ${verificationData.status}`);

          // Check for Sandbox status code (21007)
          if (verificationData.status === 21007) {
            strapi.log.info(`[verifyAppleReceipt][Production] Received status 21007 (Sandbox receipt), retrying with Sandbox URL: ${sandboxUrl}`);
            response = await axios.post(sandboxUrl, {
              'receipt-data': receipt_data,
              'password': process.env.APPLE_IAP_SECRET
            });
            verificationData = response.data;
            strapi.log.info(`[verifyAppleReceipt][Production] Sandbox URL response status: ${verificationData.status}`);
          }
        } else {
          // --- Non-Production Environment: Use Sandbox URL Directly ---
          strapi.log.info(`[verifyAppleReceipt][Sandbox] Attempting verification with Sandbox URL: ${sandboxUrl} for email: ${email}`);
          const response = await axios.post(sandboxUrl, {
            'receipt-data': receipt_data,
            'password': process.env.APPLE_IAP_SECRET
          });
          verificationData = response.data;
          strapi.log.info(`[verifyAppleReceipt][Sandbox] Sandbox URL response status: ${verificationData.status}`);
        }

      } catch (appleError) {
          // Handle errors during the POST request itself
          strapi.log.error(`[verifyAppleReceipt] Error calling Apple verification API: ${appleError.message}`, appleError);
          if (appleError.response && appleError.response.data) {
            strapi.log.error('[verifyAppleReceipt] Apple API error response data:', appleError.response.data);
          }
          return ctx.badRequest(`Failed to communicate with Apple verification service: ${appleError.message}`);
      }

      // --- Final Status Check ---
      if (verificationData.status !== 0) {
        strapi.log.warn(`[verifyAppleReceipt] Invalid receipt. Final status: ${verificationData.status}. Email: ${email}`);
        return ctx.badRequest(`Invalid receipt. Final status: ${verificationData.status}`);
      }

      // Process the purchase
      const result = await this.processIapPurchase(ctx, user.email, plan, 'apple', receipt_data);
      
      return result;
    } catch (error) {
      console.error('Error verifying Apple receipt:', error);
      strapi.log.error('Error verifying Apple receipt:', error);
      return ctx.badRequest(`Failed to verify Apple receipt: ${error.message}`);
    }
  },
  
  async verifyGooglePurchase(ctx) {
    try {
      const { purchase_token, product_id, subscription_id, email } = ctx.request.body;
      
      if (!email) {
        return ctx.badRequest('Email is required');
      }

      // Find user by email
      const user = await strapi.query("plugin::users-permissions.user").findOne({
        where: { email },
        populate: {
          organization: true
        }
      });
      
      if (!user) {
        return ctx.unauthorized('User not found');
      }
      
      // Find the corresponding plan in our system
      const plan = await strapi.query("api::plan.plan").findOne({
        where: { stripe_prod_id: product_id }
      });
      
      if (!plan) {
        return ctx.badRequest('Plan not found');
      }
      
      // *** Add check for purchase_type ***
      if (plan.purchase_type === 'one-time') {
          strapi.log.warn(`Attempted to use subscription verification endpoint for one-time product: ${product_id}, email: ${email}`);
          return ctx.badRequest(`This endpoint is for subscriptions. Use the one-time purchase endpoint for product ID ${product_id}.`);
      }

      // Process the purchase
      const result = await this.processIapPurchase(ctx, user.email, plan, 'google', purchase_token);
      
      return result;
    } catch (error) {
      console.error('Error verifying Google purchase:', error);
      strapi.log.error('Error verifying Google purchase:', error);
      return ctx.badRequest(`Failed to verify Google purchase: ${error.message}`);
    }
  },
  
  async processIapPurchase(ctx, email, plan, platform, receiptData) {
    try {
      const user = await strapi.query("plugin::users-permissions.user").findOne({
        where: { email: email },
        populate: {
          organization: {
            populate: {
              plan: true,
              monthly_usages: true,
              subscription_orders: true,
            },
          },
        },
      });
      
      if (!user || !user.organization) {
        return ctx.badRequest('User or organization not found');
      }
      
      // Calculate subscription period dates
      const startDate = Math.floor(Date.now() / 1000);
      const endDate = plan.type === "yearly" 
        ? Math.floor(Date.now() / 1000) + 365 * 24 * 60 * 60 
        : Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60;
      
      // Create a subscription order for tracking
      const subscriptionId = `iap_${platform}_${Date.now()}`;
      
      const strapiPaymentHandlers = require('../../../providers/strapi-payment-handlers');
      
      // Create subscription order with direct database call to include platform info
      const subscriptionOrder = await strapi.entityService.create('api::subscription-order.subscription-order', {
        data: {
          type: "subscription",
          plan: plan.id,
          status: "active",
          plantype: plan.type,
          sessionId: `iap_session_${Date.now()}`,
          subscriptionId: subscriptionId,
          current_period_start: new Date(startDate * 1000),
          current_period_end: new Date(endDate * 1000),
          isPaid: true,
          organization: user.organization.id,
          purchase_platform: platform,
          receipt_data: receiptData
        }
      });
      
      // Add monthly usage
      await strapiPaymentHandlers.addMonthlyUsage({
        ctx,
        email,
        startDate,
        endDate,
        product_id: plan.stripe_prod_id,
      });
      
      // Update organization with plan
      await strapiPaymentHandlers.addPlan({
        ctx,
        email,
        product_id: plan.stripe_prod_id,
        isTrial: false,
      });
      
      // Update organization subscription status
      await strapiPaymentHandlers.updateOrganizationSubscription({
        ctx,
        email,
        status: "subscribed",
      });
      
      return {
        success: true,
        message: 'In-app purchase processed successfully',
        plan: plan,
        subscription: {
          id: subscriptionId,
          start_date: startDate,
          end_date: endDate,
        }
      };
    } catch (error) {
      console.error('Error processing IAP purchase:', error);
      strapi.log.error('Error processing IAP purchase:', error);
      throw error;
    }
  },
  
  async getSubscriptionStatus(ctx) {
    try {
      const { email } = ctx.query;
      if (!email) return ctx.badRequest('Email is required');

      // Fetch user with all relevant populated data
      const userWithOrg = await strapi.query("plugin::users-permissions.user").findOne({
        where: { email },
        populate: {
          organization: {
            populate: {
              plan: true,             // Current subscription plan
              // Fetch ALL monthly usages and one-time orders for lifetime calculations
              monthly_usages: true,   
              one_time_orders: true,  // Populate the renamed relation
              subscription_orders: { // Still only need the latest for current status
                sort: 'createdAt:desc',
                limit: 1
              },
              credit_balance: true   // Current one-time balance
            },
          },
        },
      });

      if (!userWithOrg || !userWithOrg.organization) {
        return ctx.badRequest('User organization not found');
      }

      const org = userWithOrg.organization;
      const currentPlan = org.plan;
      const latestSubscription = org.subscription_orders?.[0];
      // Find the most recent monthly usage for current period stats
      const currentMonthlyUsage = org.monthly_usages?.length > 0
        ? _.maxBy(org.monthly_usages, 'createdAt') // Or filter for currently active period if needed
        : null;
      const creditBalance = org.credit_balance;
      // Calculate available one-time credits early for lifetime calculation
      const oneTimeCreditsAvailable = creditBalance ? Number(creditBalance.available_credits || 0) : 0;

      // --- Lifetime Calculations ---
      let totalLifetimeSubscriptionCreditsUsed = 0; // Renamed from totalLifetimeUsedCredits
      let totalLifetimeSubscriptionCreditsPurchased = 0; // Renamed from totalLifetimeSubscriptionCredits
      if (org.monthly_usages && org.monthly_usages.length > 0) {
          org.monthly_usages.forEach(usage => {
              totalLifetimeSubscriptionCreditsUsed += Number(usage.query_count || 0);
              totalLifetimeSubscriptionCreditsPurchased += Number(usage.usage_quota || 0);
          });
      }

      let totalLifetimeOneTimeCreditsPurchased = 0; // Renamed from totalLifetimeOneTimeCredits
      if (org.one_time_orders && org.one_time_orders.length > 0) {
          org.one_time_orders.forEach(order => {
              totalLifetimeOneTimeCreditsPurchased += Number(order.credits_added || 0);
          });
      }

      const totalLifetimeOneTimeCreditsUsed = Math.max(0, totalLifetimeOneTimeCreditsPurchased - oneTimeCreditsAvailable);
      const totalLifetimeUsedCredits = totalLifetimeSubscriptionCreditsUsed + totalLifetimeOneTimeCreditsUsed; // Correct overall usage

      const totalLifetimePurchasedCredits = totalLifetimeSubscriptionCreditsPurchased + totalLifetimeOneTimeCreditsPurchased;
      // Note: total_used_credits now reflects both subscription and one-time usage.

      // --- Current Status Calculations ---
      let isActive = false;
      if (latestSubscription) {
        const now = new Date();
        const endDate = new Date(latestSubscription.current_period_end);
        isActive = (
          (latestSubscription.status === 'active' || latestSubscription.status === 'trialing') &&
          endDate > now
        );
      }

      const periodCreditsRemaining = currentMonthlyUsage
        ? Math.max(0, Number(currentMonthlyUsage.usage_quota || 0) - Number(currentMonthlyUsage.query_count || 0))
        : 0;

      const totalAvailableCredits = periodCreditsRemaining + oneTimeCreditsAvailable;

      // --- Construct Enhanced Response --- //
      return {
        success: true,
        // --- Current Status ---
        subscription_status: org.subscription,
        plan: currentPlan ? {
          id: currentPlan.id,
          name: currentPlan.name,
          type: currentPlan.type, 
          purchase_type: currentPlan.purchase_type || 'subscription',
          allowed_credits: currentPlan.allowed_credits, 
          stripe_prod_id: currentPlan.stripe_prod_id,
          description: currentPlan.description,
          price: Number(currentPlan.paid),
        } : null,
        is_active: isActive, 
        period_credits: { 
          total: currentMonthlyUsage ? Number(currentMonthlyUsage.usage_quota || 0) : 0,
          used: currentMonthlyUsage ? Number(currentMonthlyUsage.query_count || 0) : 0,
          remaining: periodCreditsRemaining
        },
        one_time_credits: { 
          available: oneTimeCreditsAvailable
        },
        total_available_credits: totalAvailableCredits, 
        subscription_details: latestSubscription ? { 
          platform: latestSubscription.purchase_platform || 'stripe',
          status: latestSubscription.status, 
          current_period_end: latestSubscription.current_period_end,
          is_trial: latestSubscription.isTrial || false 
        } : null,
        // --- Lifetime Totals ---
        lifetime_totals: {
            total_credits_purchased: totalLifetimePurchasedCredits,
            total_subscription_credits_purchased: totalLifetimeSubscriptionCreditsPurchased, // Use renamed variable
            total_one_time_credits_purchased: totalLifetimeOneTimeCreditsPurchased, // Use renamed variable
            total_used_credits: totalLifetimeUsedCredits // Use new correct calculation
        }
      };

    } catch (error) { 
      strapi.log.error('Error getting enhanced subscription status:', error);
      return ctx.badRequest(`Failed to get subscription status: ${error.message}`);
    }
  },

  /**
   * Processes a one-time purchase, adding credits to the organization's balance.
   */
  async processOneTimePurchase(ctx, email, plan, platform, transactionIdentifier) {
    // --- Start Debug Logging ---
    strapi.log.info(`[processOneTimePurchase] Called for email: ${email}, plan: ${plan.id}, platform: ${platform}, identifier: ${transactionIdentifier}`);
    // --- End Debug Logging ---
    try {
      const user = await strapi.query("plugin::users-permissions.user").findOne({
        where: { email: email },
        populate: {
          organization: {
            populate: {
              credit_balance: true, // Populate the new relation
            },
          },
        },
      });

      // --- Start Debug Logging ---
      if (!user || !user.organization) {
        strapi.log.error(`[processOneTimePurchase] User or organization not found for email: ${email}. Aborting.`);
        throw new Error('User or organization not found'); // Ensure this throws
      }
      strapi.log.info(`[processOneTimePurchase] Found user ${user.id} and org ${user.organization.id}`);
      // --- End Debug Logging ---

      const org = user.organization;
      const creditsToAdd = plan.allowed_credits;

      if (creditsToAdd == null || creditsToAdd <= 0) {
          strapi.log.error(`[processOneTimePurchase] Plan ${plan.id} has invalid/zero credits: ${creditsToAdd}. Aborting.`);
          throw new Error('Invalid credit amount for one-time purchase plan.');
      }

      let creditBalance;
      let currentCredits = 0;

      // --- Start Debug Logging ---
      strapi.log.info(`[processOneTimePurchase] Attempting to find/create credit balance for org ${org.id}`);
      // --- End Debug Logging ---
      if (org.credit_balance) {
        creditBalance = await strapi.entityService.findOne('api::credit-balance.credit-balance', org.credit_balance.id);
        // Ensure currentCredits is treated as a number
        currentCredits = Number(creditBalance.available_credits) || 0;
      } else {
        strapi.log.info(`[processOneTimePurchase] Creating new credit balance for organization ${org.id}`);
        creditBalance = await strapi.entityService.create('api::credit-balance.credit-balance', {
          data: { available_credits: 0, organization: org.id },
        });
        // currentCredits is already 0 (a number)
      }

      // Ensure both operands are numbers before adding
      const numericCreditsToAdd = Number(creditsToAdd);
      const newCreditTotal = Number(currentCredits) + numericCreditsToAdd;

      // --- Start Debug Logging ---
      strapi.log.info(`[processOneTimePurchase] Current Credits: ${currentCredits} (Type: ${typeof currentCredits}), Credits to Add: ${numericCreditsToAdd} (Type: ${typeof numericCreditsToAdd}), New Total: ${newCreditTotal} (Type: ${typeof newCreditTotal})`);
      strapi.log.info(`[processOneTimePurchase] Updating credit balance ${creditBalance.id} for org ${org.id} to ${newCreditTotal}`);
      // --- End Debug Logging ---

      // --- Wrap Update in Try/Catch ---
      let updatedBalance = null;
      try {
          updatedBalance = await strapi.db.query('api::credit-balance.credit-balance').update({
              where: { id: creditBalance.id },
              data: { available_credits: newCreditTotal },
          });
          // --- Log Update Result ---
          strapi.log.info(`[processOneTimePurchase] Successfully updated credit balance. Result: ${JSON.stringify(updatedBalance)}`);
      } catch (updateError) {
          strapi.log.error(`[processOneTimePurchase] FAILED to update credit balance ${creditBalance.id} for org ${org.id}. Error:`, updateError);
          console.error("[processOneTimePurchase] Full error object during credit-balance update:", updateError);
          // Decide if we should throw here or just log and continue (order was created but balance failed)
          // For now, let's re-throw to make the failure explicit in the API response
          throw new Error(`Failed to update credit balance: ${updateError.message}`);
      }
      // --- End Update Try/Catch ---

      // Log the purchase (Original Info Log)
      strapi.log.info(`One-time purchase processed for org ${org.id}. Added ${creditsToAdd} credits. Platform: ${platform}. New balance: ${newCreditTotal}`);

      // --- ADD ONE-TIME ORDER ENTRY START ---
      try {
          // --- Start Debug Logging ---
          strapi.log.info(`[processOneTimePurchase] Attempting to create one-time-order entry...`);
          const oneTimeOrderData = {
              organization: org.id,
              user: user.id,
              plan: plan.id,
              purchase_type: 'one-time',
              credits_added: numericCreditsToAdd,
              platform: platform,
              purchase_identifier: transactionIdentifier,
              processed_timestamp: new Date(),
              publishedAt: new Date(),
          };
          strapi.log.debug(`[processOneTimePurchase] one-time-order data: ${JSON.stringify(oneTimeOrderData)}`);
          // --- End Debug Logging ---
          await strapi.entityService.create('api::one-time-order.one-time-order', {
              data: oneTimeOrderData
          });
          strapi.log.info(`[processOneTimePurchase] One Time Order log created successfully for org: ${org.id}`);
      } catch (logError) {
          // --- Start Debug Logging ---
          strapi.log.error(`[processOneTimePurchase] FAILED to create One Time Order log entry for org ${org.id}. Error:`, logError);
          // Log the full error object for more details
          console.error("[processOneTimePurchase] Full error object during one-time-order creation:", logError);
          // --- End Debug Logging ---
      }
      // --- ADD ONE-TIME ORDER ENTRY END ---

      // --- Temporary Debug: Re-fetch balance before returning ---
      try {
          const finalCheckBalance = await strapi.entityService.findOne('api::credit-balance.credit-balance', creditBalance.id);
          strapi.log.debug(`[processOneTimePurchase] Final check of credit balance ${creditBalance.id} before returning: ${JSON.stringify(finalCheckBalance)}`);
      } catch (checkError) {
          strapi.log.warn(`[processOneTimePurchase] Failed to re-fetch credit balance for final check: ${checkError.message}`);
      }
      // --- End Temporary Debug ---

      // Return success response, ensuring numbers are returned
      return {
        success: true,
        message: 'One-time purchase processed successfully.',
        added_credits: numericCreditsToAdd, // Use the numeric version
        new_balance: newCreditTotal,      // Use the calculated numeric total
      };

    } catch (error) {
      strapi.log.error(`Error processing one-time purchase for email ${email}:`, error);
      // Rethrow or handle appropriately for the calling function
      throw error; // Caller function should handle sending ctx response
    }
  },

  /**
   * Verifies an Apple receipt specifically for a one-time purchase.
   */
  async verifyAppleOneTimePurchase(ctx) {
    try {
      const { receipt_data, product_id, email } = ctx.request.body;
      strapi.log.info(`********* [verifyAppleOneTimePurchase] Received request for email: ${email}, product_id: ${product_id}`);
      if (!email) return ctx.badRequest('Email is required');
      if (!receipt_data) return ctx.badRequest('Receipt data is required');
      if (!product_id) return ctx.badRequest('Product ID is required');

      const user = await strapi.query("plugin::users-permissions.user").findOne({ where: { email } });
      if (!user) return ctx.unauthorized('User not found');

      // ---- Apple Receipt Verification (Conditional) ----
      const productionUrl = 'https://buy.itunes.apple.com/verifyReceipt';
      const sandboxUrl = 'https://sandbox.itunes.apple.com/verifyReceipt';
      let verificationData;

      try {
        if (process.env.IAP_ENV === 'production') {
          // --- Production Environment: Use Fallback Logic ---
          strapi.log.info(`[verifyAppleOneTimePurchase][Production] Attempting verification with Production URL: ${productionUrl} for email: ${email}`);
          let response = await axios.post(productionUrl, {
            'receipt-data': receipt_data,
            'password': process.env.APPLE_IAP_SECRET
          });
          verificationData = response.data;
          strapi.log.info(`[verifyAppleOneTimePurchase][Production] Production URL response status: ${verificationData.status}`);

          // Check for Sandbox status code (21007)
          if (verificationData.status === 21007) {
            strapi.log.info(`[verifyAppleOneTimePurchase][Production] Received status 21007 (Sandbox receipt), retrying with Sandbox URL: ${sandboxUrl}`);
            response = await axios.post(sandboxUrl, {
              'receipt-data': receipt_data,
              'password': process.env.APPLE_IAP_SECRET
            });
            verificationData = response.data;
            strapi.log.info(`[verifyAppleOneTimePurchase][Production] Sandbox URL response status: ${verificationData.status}`);
          }
        } else {
          // --- Non-Production Environment: Use Sandbox URL Directly ---
          strapi.log.info(`[verifyAppleOneTimePurchase][Sandbox] Attempting verification with Sandbox URL: ${sandboxUrl} for email: ${email}`);
          const response = await axios.post(sandboxUrl, {
            'receipt-data': receipt_data,
            'password': process.env.APPLE_IAP_SECRET
          });
          verificationData = response.data;
          strapi.log.info(`[verifyAppleOneTimePurchase][Sandbox] Sandbox URL response status: ${verificationData.status}`);
        }

      } catch (appleError) {
          strapi.log.error(`[verifyAppleOneTimePurchase] Error calling Apple verification API: ${appleError.message}`, appleError);
          if (appleError.response && appleError.response.data) {
            strapi.log.error('[verifyAppleOneTimePurchase] Apple API error response data:', appleError.response.data);
          }
          return ctx.badRequest(`Failed to communicate with Apple verification service: ${appleError.message}`);
      }
      // ---- End Apple Verification ----

      // --- Final Status Check ---
      if (verificationData.status !== 0) {
          strapi.log.warn(`[verifyAppleOneTimePurchase] Invalid Apple receipt. Final status: ${verificationData.status}`, { email, product_id });
          return ctx.badRequest(`Invalid Apple receipt (Final Status: ${verificationData.status})`);
      }

      // --- Extract Transaction ID from Verified Receipt --- 
      let transactionId = null;
      const receiptInfo = verificationData.receipt || {};
      const inAppPurchases = receiptInfo.in_app || [];
      
      const relevantPurchases = inAppPurchases
          .filter(p => p.product_id === product_id)
          .sort((a, b) => Number(b.purchase_date_ms || 0) - Number(a.purchase_date_ms || 0));

      if (relevantPurchases.length > 0) {
          transactionId = relevantPurchases[0].transaction_id;
          strapi.log.info(`[verifyAppleOneTimePurchase] Extracted transaction_id: ${transactionId} for product: ${product_id}`);
      } else {
          strapi.log.error(`[verifyAppleOneTimePurchase] Product ID ${product_id} not found within the verified receipt's in_app purchases for email ${email}.`);
          // Optionally add fallback logic here if needed
      }

      if (!transactionId) {
        strapi.log.error(`[verifyAppleOneTimePurchase] Could not extract a valid transaction_id for product ${product_id}, email ${email}. Receipt dump: ${JSON.stringify(receiptInfo)}`); // Log receipt on failure
        return ctx.badRequest('Could not identify transaction for the specified product in the receipt.');
      }
      // ---- End Apple Verification & Extraction ----

      // Find the corresponding plan
      const plan = await strapi.query("api::plan.plan").findOne({
        where: {
            stripe_prod_id: product_id,
            purchase_type: 'one-time'
        }
      });
      if (!plan) {
          strapi.log.warn(`One-time purchase plan not found or not marked as 'one-time' for product_id: ${product_id}`);
          return ctx.badRequest('One-time purchase plan not found');
      }

      // --- Start Debug Logging ---
      if (plan) {
          strapi.log.debug(`[verifyAppleOneTimePurchase] Fetched plan object: ${JSON.stringify(plan)}`);
          strapi.log.debug(`[verifyAppleOneTimePurchase] Plan allowed_credits: ${plan.allowed_credits} (Type: ${typeof plan.allowed_credits})`);
      } else {
          strapi.log.warn(`[verifyAppleOneTimePurchase] Plan not found for product_id: ${product_id}`);
          return ctx.badRequest('One-time purchase plan not found');
      }
      // --- End Debug Logging ---

      // *** CRITICAL: Ensure the extracted transactionId variable is passed ***
      strapi.log.info(`[verifyAppleOneTimePurchase] Calling processOneTimePurchase with transactionId: ${transactionId}`);
      const result = await this.processOneTimePurchase(ctx, email, plan, 'apple', transactionId);
      return ctx.send(result);

    } catch (error) {
      strapi.log.error('Error verifying Apple one-time purchase:', error);
      return ctx.badRequest(`Failed to verify Apple one-time purchase: ${error.message || 'An unexpected error occurred.'}`);
    }
  },

  /**
   * Verifies a Google purchase specifically for a one-time purchase.
   * NOTE: Google Play Developer API interaction for verification needs implementation.
   */
  async verifyGoogleOneTimePurchase(ctx) {
    try {
      const { purchase_token, product_id, email } = ctx.request.body; // subscription_id not typically used for one-time

      if (!email) return ctx.badRequest('Email is required');
      if (!purchase_token) return ctx.badRequest('Purchase token is required');
      if (!product_id) return ctx.badRequest('Product ID is required');

      // Find user
      const user = await strapi.query("plugin::users-permissions.user").findOne({ where: { email } });
      if (!user) return ctx.unauthorized('User not found');

      // ---- Google Purchase Verification ----
      // TODO: Implement verification call to Google Play Developer API for one-time products.
      // Use googleapis library: `google.androidpublisher('v3').purchases.products.get(...)`
      // Check purchaseState (should be 0 for PURCHASED) and consumptionState (should be 0 for NOT_CONSUMED initially).
      // Consider consuming the purchase via API if required by Google's flow after granting credits.
      // Example placeholder verification:
      // const isGooglePurchaseValid = await verifyGoogleOneTimeProduct(purchase_token, product_id);
      // if (!isGooglePurchaseValid) {
      //   strapi.log.warn(`Invalid Google one-time purchase token/product.`, { email, product_id });
      //   return ctx.badRequest('Invalid Google purchase');
      // }
      strapi.log.warn(`TODO: Implement actual Google Play Developer API verification for one-time product: ${product_id}, token: ${purchase_token}`);
      // ---- End Google Verification ----

      // Find the corresponding plan, ensuring it's 'one-time'
      const plan = await strapi.query("api::plan.plan").findOne({
        where: {
          stripe_prod_id: product_id, // Assuming stripe_prod_id holds the Google product ID
          purchase_type: 'one-time'
        }
      });
      if (!plan) {
           strapi.log.warn(`One-time purchase plan not found or not marked as 'one-time' for product_id: ${product_id}`);
          return ctx.badRequest('One-time purchase plan not found');
      }

      // --- Start Debug Logging ---
      if (plan) {
          strapi.log.debug(`[verifyGoogleOneTimePurchase] Fetched plan object: ${JSON.stringify(plan)}`);
          strapi.log.debug(`[verifyGoogleOneTimePurchase] Plan allowed_credits: ${plan.allowed_credits} (Type: ${typeof plan.allowed_credits})`);
      } else {
          strapi.log.warn(`[verifyGoogleOneTimePurchase] Plan not found for product_id: ${product_id}`);
          return ctx.badRequest('One-time purchase plan not found');
      }
      // --- End Debug Logging ---

      // Process the one-time purchase, passing the purchase_token as the identifier
      const result = await this.processOneTimePurchase(ctx, email, plan, 'google', purchase_token);
      // Optionally: If verification succeeded AND processing succeeded, consume the Google purchase via API
      // await consumeGooglePurchase(purchase_token, product_id);
      return ctx.send(result);

    } catch (error) {
      strapi.log.error('Error verifying Google one-time purchase:', error);
      return ctx.badRequest(`Failed to verify Google one-time purchase: ${error.message || 'An unexpected error occurred.'}`);
    }
  },

  // --- ADD getPurchaseHistory START ---
  async getPurchaseHistory(ctx) {
      try {
          const userEmail = ctx.query.email;
          strapi.log.debug(`[getPurchaseHistory] Starting for email: ${userEmail}`);

          if (!userEmail) {
             strapi.log.warn('[getPurchaseHistory] Email query parameter is missing');
             return ctx.badRequest('Email query parameter is required');
          }
 
          strapi.log.debug('[getPurchaseHistory] Fetching user and organization ID...');
          const userWithOrg = await strapi.query("plugin::users-permissions.user").findOne({
            where: { email: userEmail }, 
            populate: {
              organization: { fields: ['id'] } // Populate only the organization ID
            },
          });
          strapi.log.debug(`[getPurchaseHistory] User query result: ${JSON.stringify(userWithOrg)}`);
    
          strapi.log.debug('[getPurchaseHistory] Checking user and organization...');
          if (!userWithOrg || !userWithOrg.organization) {
            strapi.log.warn(`[getPurchaseHistory] User organization not found for email: ${userEmail}`);
            return ctx.badRequest('User organization not found');
          }

          let orgId = userWithOrg.organization.id;
          strapi.log.debug(`[getPurchaseHistory] Found organization ID: ${orgId}`);

          // Extract pagination parameters from query
          const { page = 1, pageSize = 10 } = ctx.query.pagination || {};
          const limit = parseInt(pageSize, 10);
          const start = (parseInt(page, 10) - 1) * limit;
          strapi.log.debug(`[getPurchaseHistory] Pagination: page=${page}, pageSize=${pageSize}, limit=${limit}, start=${start}`);

          // Fetch purchase logs count for pagination metadata
          strapi.log.debug(`[getPurchaseHistory] Counting one-time orders for orgId: ${orgId}...`);
          const totalCount = await strapi.db.query('api::one-time-order.one-time-order').count({ 
              where: { organization: orgId }
          });
          strapi.log.debug(`[getPurchaseHistory] Total order count: ${totalCount}`);

          // Fetch purchase logs using db.query for testing
          strapi.log.debug(`[getPurchaseHistory] Fetching one-time orders for orgId: ${orgId} using db.query...`);
          const history = await strapi.db.query('api::one-time-order.one-time-order').findMany({
              where: { organization: orgId },
              populate: { plan: { fields: ['id', 'name', 'description', 'purchase_type', 'paid'] } },
              orderBy: { processed_timestamp: 'desc' }, // Note: orderBy instead of sort
              limit: limit,
              offset: start, // Note: offset instead of start
          });
          strapi.log.debug(`[getPurchaseHistory] Fetched history records (using db.query): ${history ? history.length : 0}`);

          // Format the response
          strapi.log.debug(`[getPurchaseHistory] Formatting history response...`);
          const formattedHistory = history.map(item => ({
              id: item.id,
              plan_name: item.plan?.name || 'Plan details unavailable',
              plan_description: item.plan?.description || 'Plan description unavailable',
              price: Number(item.plan?.paid),
              purchase_type: item.purchase_type,
              credits_added: item.credits_added, // Will be null/0 for subscriptions unless logged
              platform: item.platform,
              date: item.processed_timestamp,
              // Exclude purchase_identifier unless explicitly needed by admin/support roles
          }));
          
          strapi.log.debug(`[getPurchaseHistory] Returning success response for email: ${userEmail}`);
          return {
              success: true,
              history: formattedHistory,
              pagination: {
                  page: parseInt(page, 10),
                  pageSize: limit,
                  pageCount: Math.ceil(totalCount / limit),
                  total: totalCount,
              },
          };

      } catch (error) {
          strapi.log.error('[getPurchaseHistory] Error caught:', error); // Enhanced log
          // Also log the specific message for easier identification
          strapi.log.error(`[getPurchaseHistory] Error message: ${error.message}`); 
          return ctx.badRequest(`Failed to get purchase history: ${error.message}`);
      }
  }
  // --- ADD getPurchaseHistory END ---
}; 