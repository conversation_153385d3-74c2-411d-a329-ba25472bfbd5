"use strict";

/**
 * session controller
 */

const axios = require("axios");

const chance = new require("chance")();
module.exports = {
  async start(ctx, next) {
    let { data } = await axios
      .post(
        `http://api.ipapi.com/api/${ctx.request.ip}?access_key=${process.env.IPAPIKEY}`
      )
      .catch((err) => {
        console.log(err);
        throw err;
      });
    const response = {
      session: chance.guid(),
      country_code: data.country_code,
      calling_code: data.location.calling_code,
    };

    return response;
  },
};
