{"kind": "collectionType", "collectionName": "answers", "info": {"singularName": "answer", "pluralName": "answers", "displayName": "Answer", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"question": {"type": "text", "required": true}, "answer": {"type": "text"}, "answer_status": {"type": "boolean", "default": false}, "sources": {"type": "json"}, "kb_id": {"type": "text"}, "org_id": {"type": "text"}, "api_source": {"type": "string"}, "session": {"type": "string"}, "url_trackers": {"type": "relation", "relation": "oneToMany", "target": "api::url-tracker.url-tracker", "mappedBy": "query_id"}, "did_find_answer": {"type": "boolean"}, "knowledgebase": {"type": "relation", "relation": "oneToOne", "target": "api::knowledgebase.knowledgebase"}, "contact_booking": {"type": "relation", "relation": "oneToOne", "target": "api::contact-booking.contact-booking", "mappedBy": "query"}, "sessionModel": {"type": "relation", "relation": "manyToOne", "target": "api::session.session", "inversedBy": "answers"}}}