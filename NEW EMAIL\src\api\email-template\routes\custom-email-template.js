'use strict';

/**
 * Email Template Custom Router
 * Custom routes for email template operations
 */
module.exports = {
  routes: [
    // Test email template
    {
      method: 'POST',
      path: '/email-template/:id/test',
      handler: 'email-template.testTemplate',
      config: {
        policies: [
          'global::user-details-populate',
          'global::update-org-before-api-call',
        ]
      }
    },

    // Preview email template HTML
    {
      method: 'POST',
      path: '/email-template/:id/preview',
      handler: 'email-template.previewTemplate',
      config: {
        policies: [
          'global::user-details-populate',
          'global::update-org-before-api-call',
        ]
      }
    },

    // Validate template HTML
    {
      method: 'POST',
      path: '/email-template/validate-html',
      handler: 'email-template.validateTemplateHtml',
      config: {
        policies: [
          'global::user-details-populate',
        ]
      }
    },

    // Clone template for organization
    {
      method: 'POST',
      path: '/email-template/:id/clone',
      handler: 'email-template.cloneTemplate',
      config: {
        policies: [
          'global::user-details-populate',
          'global::update-org-before-api-call',
        ]
      }
    },

    // Get template hierarchy
    {
      method: 'GET',
      path: '/email-template/:id/hierarchy',
      handler: 'email-template.getTemplateHierarchy',
      config: {
        policies: [
          'global::user-details-populate',
        ]
      }
    },

    // Get available template scopes
    {
      method: 'GET',
      path: '/email-template/scopes',
      handler: 'email-template.getTemplateScopes',
      config: {
        policies: [
          'global::user-details-populate',
        ]
      }
    }
  ]
}; 