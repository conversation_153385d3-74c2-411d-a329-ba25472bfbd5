"use strict";

/**
 * Scrumboard card usage lifecycle
 */
const _ = require("lodash");
module.exports = {
  beforeCreate: async (data) => {
    try {
      if (!data.params.data.dueDate) {
        data.params.data['dueDate'] = new Date(); // Set current date and time
      }
      const board = await strapi.query('api::scrumboard.scrumboard').findOne({
        where: {  
          id: data.params.data.scrumboard,
        },
      });
      const latest = await strapi.query('api::scrumboard-card.scrumboard-card').findOne({
        where: {  
          scrumboard: data.params.data.scrumboard,
        },
        orderBy: { id: 'desc' },
      });
      var count=1;
      if(latest){
        count = latest.id+1;
      }
      data.params.data['ticketId']=`${board.title.substring(0, 2).toUpperCase()}-${count}`;
    } catch (e) {
      console.log(e);
    }
  },
};
