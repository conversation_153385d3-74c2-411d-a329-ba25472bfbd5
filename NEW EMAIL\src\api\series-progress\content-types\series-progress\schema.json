{"kind": "collectionType", "collectionName": "series_progress", "info": {"singularName": "series-progress", "pluralName": "series-progresses", "displayName": "Series Progress", "description": "User progress state for series - aggregates computed on-demand"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "series": {"type": "relation", "relation": "manyToOne", "target": "api::series.series"}, "last_episode_id": {"type": "string", "description": "UUID of the last accessed episode"}, "started_at": {"type": "datetime", "required": true}, "last_accessed_at": {"type": "datetime", "required": true}, "completed_at": {"type": "datetime", "description": "Set when all required episodes are completed"}, "notes": {"type": "text", "description": "User notes about the series"}}}