import React from "react";
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';

function FileViewerList({ files, title, onRemove }) {
  return (
    <div style={{ marginTop: "30px" }} className="w-full">
      <h2 style={{ marginBottom: "10px" }} className="text-lg text-green font-medium mb-2">{title}</h2>
      <ul className="border border-gray-300 rounded-lg">
        {files.map((file, index) => (
          <li key={index} style={{ padding: "10px" }} className="flex items-center justify-between px-4 py-2 border-b border-gray-300 last:border-b-0">
            <div className="flex items-center">
            <button style={{ marginRight: "10px" }} className="text-red-600 hover:text-red-700 text-lg" onClick={() => onRemove(index)}>
                <FuseSvgIcon className="text-red-600" size={20} color="action">
                    heroicons-solid:x-circle
                </FuseSvgIcon>
             </button>
              <p className="truncate max-w-md">{file.name}</p>
            </div>
            <p className="text-xs">{(file.size / 1000).toFixed(2)} KB</p>
          </li>
        ))}
      </ul>
    </div>
  );
}
  
export default FileViewerList;
