const React = require('react');
const { injectReducers } = require('app/store');
const generateReducersFromSlices = require('./generateReducersFromSlices');

/**
 * Injects reducers grouped by common key.
 */
const injectReducersGroupedByCommonKey = async (slices) => {
  injectReducers(generateReducersFromSlices(slices));
  return true;
};

/**
 * A Higher Order Component that injects reducers for the provided slices.
 */
const withSlices = (slices) => (WrappedComponent) => {
  injectReducersGroupedByCommonKey(slices);

  return function WithInjectedReducer(props) {
    return <WrappedComponent {...props} />;
  };
};

module.exports = withSlices;
