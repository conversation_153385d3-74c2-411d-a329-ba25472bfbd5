'use strict';

/**
 * series billing routes
 */
module.exports = {
  routes: [
    // Credit-based series purchase
    {
      method: 'POST',
      path: '/series/:seriesId/purchase/credits',
      handler: 'billing-controller.purchaseSeriesWithCredits',
      config: {
        policies: ['global::user-details-populate', 'global::usage-validation']
      }
    },

    // One-time payment series purchase
    {
      method: 'POST',
      path: '/series/:seriesId/purchase/one-time',
      handler: 'billing-controller.purchaseSeriesOneTime',
      config: {
        policies: ['global::user-details-populate']
      }
    }
  ]
}; 