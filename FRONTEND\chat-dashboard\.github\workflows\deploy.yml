name: Docker Build and Deploy

on:
  push:
    branches:
      - production
      - development

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      
      - name: Set up Docker to allow insecure registries
        run: |
          echo '{ "insecure-registries": ["139.59.28.213"] }' | sudo tee /etc/docker/daemon.json
          sudo systemctl restart docker

      - name: Login to Docker Registry (Harbor)
        run: docker login -u ${{ secrets.HARBOR_USERNAME }} -p ${{ secrets.HARBOR_PASSWORD }} 139.59.28.213

      - name: Build Docker Image
        run: |
          if [[ $GITHUB_REF == refs/heads/production ]]; then
            docker build -t 139.59.28.213/chat-masala-prod/chat-masala-dashboard .
          else
            docker build -t 139.59.28.213/chat-masala-dev/chat-masala-dashboard .
          fi

      - name: Push Docker Image to Harbor
        run: | 
         if [[ $GITHUB_REF == refs/heads/production ]]; then
            docker push 139.59.28.213/chat-masala-prod/chat-masala-dashboard
          else
            docker push 139.59.28.213/chat-masala-dev/chat-masala-dashboard
          fi

      - name: Update Portainer Stack
        run: |
          if [[ $GITHUB_REF == refs/heads/production ]]; then
           curl -X POST \
              -H "Content-Type: application/json" \
              http://142.93.220.94:9000/api/webhooks/5bdd0a04-cc52-4a3a-98ea-a1384b36c53f
          else
            curl -X POST \
              -H "Content-Type: application/json" \
              http://206.189.131.232:9000/api/webhooks/2b0242f1-58db-4d25-a95b-cc3a01605a64
          fi
