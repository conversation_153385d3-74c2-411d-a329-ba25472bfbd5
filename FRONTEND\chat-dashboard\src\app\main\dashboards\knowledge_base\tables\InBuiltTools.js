import React, { useState, useEffect } from "react";
import axios from "axios";
import { formatDateToHumanReadable } from "src/app/utils/date-utils";
import PaginationComponent from "src/app/shared-components/pagination/PaginationComponent";
import StatusPill, {
  APIMethodPill,
} from "src/app/shared-components/pill/StatusPill";
import { validateUrl } from "src/app/utils/validations";
import Pill from "app/shared-components/pill/pill";
import qs from "qs";
import CTAButon from "app/shared-components/buttons/cta-button";
import { Body } from "app/shared-components/text/texts";
import CircularButton from "app/shared-components/buttons/circular-button";
import LoadingSpinner from "app/shared-components/loading-spinner/loading-spinner";
import ConfirmModal from "app/shared-components/modals/ConfirmModal";
import { H5 } from "app/shared-components/text/texts";

export function InBuiltToolsTable({
  kbId,
  onAddClick,
  onDeleteAllClick,
  onToolClick,
  isInitiallyOpen = false,
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [inBuiltTools, setInBuiltTools] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [lastPage, setLastPage] = useState(-1);
  const itemsPerPage = 5;
  const [selectedForDelete, setSelectedForDelete] = useState("");

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const getDatasource = async (page) => {
    setLoading(true);
    setInBuiltTools([]);
    try {
      const query = {
        populate: {
          inBuiltTools: {
            sort: "createdAt:asc",
            limit: itemsPerPage,
            start: (page - 1) * itemsPerPage,
          },
        },
      };
      const queryToString = qs.stringify(query, { encode: false });
      const kb = await axios.get(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/knowledgebases/${kbId}?${queryToString}`
      );

      setInBuiltTools((prev) => [
        ...prev,
        ...kb.data.data.attributes.inBuiltTools.data,
      ]);
      setLastPage(
        kb.data.data.attributes.inBuiltTools.data.length < itemsPerPage
          ? page
          : -1
      );
      setLoading(false);
    } catch (e) {
      console.log(e);
      setLoading(false);
    }
  };

  useEffect(() => {
    getDatasource(1);
  }, [kbId]);

  useEffect(() => {
    setIsOpen(isInitiallyOpen);
  }, [isInitiallyOpen]);

  const totalPages =
    lastPage != -1
      ? lastPage
      : inBuiltTools
      ? Math.ceil(inBuiltTools.length / itemsPerPage) + 1
      : 1;

  const onPageChange = (page) => {
    setCurrentPage(page);
    if (lastPage != page && inBuiltTools.length < page * itemsPerPage) {
      getDatasource(page);
    }
  };

  const onDeleteClick = async (toolId) => {
    setLoading(true);
    try {
      await axios.delete(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/inBuilt-tools/${toolId}`
      );
      setInBuiltTools((prev) => prev.filter((tool) => tool.id !== toolId));
      setLoading(false);
      getDatasource(1);
    } catch (e) {
      console.log(e);
      setLoading(false);
    }
  };

  return (
    <div className="m-5 mt-32 border border-gray-300 shadow-lg rounded-lg overflow-hidden">
      <div
        className="bg-white text-black p-4 flex flex-col cursor-pointer"
        onClick={handleToggle}
      >
        <div className="flex justify-between items-center">
          <div className="m-16 flex flex-row">
            <img
              src="assets/images/tools-icon.png"
              className="m-4 w-32 h-32 p-4 bg-[#EBEFF9] rounded-xl"
            ></img>
            <H5 className="ml-8 mt-6 font-semibold text-title-text-color">
              Built In Tools
            </H5>
            <div className="mx-16 mt-4">
              <Pill text={"New"} />
            </div>
          </div>

          <div className="flex mr-16 mt-4">
            <div>
              <CTAButon
                text="Add Tools"
                image="assets/images/add-icon.svg"
                onPress={onAddClick}
              />
            </div>
          </div>
        </div>
        <div className="mb-16">
          <Body className="mt-2 w-1/2 ml-16 mb-16 text-body-text-color">
            These are built in tools that are available for use in your agents.
          </Body>
        </div>
      </div>
      <div
        className={`transition-max-height duration-500 ease-in-out ${
          isOpen ? "max-h-screen" : "max-h-0 overflow-hidden"
        }`}
      >
        {loading && (
          <div className="flex items-center justify-center p-16">
            <LoadingSpinner size={32} />
          </div>
        )}
        {isOpen && !loading && (
          <>
            {inBuiltTools && inBuiltTools.length > 0 ? (
              <>
                <div className="flex bg-[#F9FBFC] p-16 border-grey-300 border-t-1 border-b-1">
                  <div className="w-96 font-semibold p-2 pl-16">SL No</div>
                  <div className="w-1/4 font-semibold p-2">Tool Name</div>
                  <div className="w-1/4 font-semibold p-2 text-start">
                    Tool Purpose
                  </div>
                  <div className="w-[100px] font-semibold p-2">Date Added</div>
                  <div className="flex grow items-center justify-center px-16 font-semibold p-2">
                    Actions
                  </div>
                </div>
                {inBuiltTools
                  .slice(
                    (currentPage - 1) * itemsPerPage,
                    currentPage * itemsPerPage
                  )
                  .map((_, index) => (
                    <div
                      onClick={(e) => {
                        e.preventDefault();
                        onToolClick(
                          inBuiltTools[(currentPage - 1) * itemsPerPage + index]
                        );
                      }}
                      key={index}
                      className="flex p-4 text-md border-b bg-[#faf9fb] border-gray-200 last:border-none hover:bg-white transition-colors hover:cursor-pointer"
                    >
                      <div className="w-96 p-16 px-32 text-grey-900">
                        {(currentPage - 1) * itemsPerPage + index + 1}
                      </div>
                      {
                        <div className="flex flex-row w-1/4 p-16">
                          <img
                            src={
                              inBuiltTools[
                                (currentPage - 1) * itemsPerPage + index
                              ].attributes.tool_image_url
                            }
                            className="w-24 h-24 mr-4"
                          />
                          <div className="ml-8 align-middle mt-2 text-grey-900 ">
                            {
                              inBuiltTools[
                                (currentPage - 1) * itemsPerPage + index
                              ].attributes.name
                            }
                          </div>
                        </div>
                      }

                      <div className="w-1/4 p-16 text-grey-900 text-start">
                        {
                          inBuiltTools[(currentPage - 1) * itemsPerPage + index]
                            .attributes.description
                        }
                      </div>
                      <div className="w-[100px] py-16 text-grey-900">
                        {formatDateToHumanReadable(
                          inBuiltTools[(currentPage - 1) * itemsPerPage + index]
                            .attributes.createdAt
                        )}
                      </div>
                      <div className="flex grow py-16 items-center justify-center px-16 text-grey-800">
                        <CircularButton
                          image="assets/images/delete-icon.png"
                          onPress={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            setSelectedForDelete(
                              inBuiltTools[
                                (currentPage - 1) * itemsPerPage + index
                              ].id
                            );
                          }}
                        />
                      </div>
                    </div>
                  ))}
                <div className="">
                  <PaginationComponent
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={onPageChange}
                  />
                </div>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center p-10">
                <img
                  src="/assets/images/no-data.jpg"
                  alt="No data"
                  className="mb-4 h-[150px]"
                />
                <Body className="text-body-text-color mx-64 w-[300px] text-center my-16">
                  Looks like you haven't added any data to this datasource yet.
                  Click the add button to add your data
                </Body>
              </div>
            )}
          </>
        )}
      </div>
      <ConfirmModal
        open={selectedForDelete !== ""}
        onCancel={() => {
          setSelectedForDelete("");
        }}
        onClose={() => setSelectedForDelete("")}
        onConfirm={() => {
          onDeleteClick(selectedForDelete);
          setSelectedForDelete("");
        }}
        title="Are you sure you want to delete all data?"
        content="This action cannot be undone."
      />
    </div>
  );
}

export default InBuiltToolsTable;
