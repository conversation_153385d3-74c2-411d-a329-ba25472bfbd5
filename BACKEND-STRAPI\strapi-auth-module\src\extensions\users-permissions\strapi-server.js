const _ = require("lodash");

console.log('🚨 DEBUG: Extension file loaded at module level!');

module.exports = (plugin) => {
  console.log('🚨 DEBUG: Users-permissions extension loaded!');
  console.log('🚨 DEBUG: Plugin object:', !!plugin);
  console.log('🚨 DEBUG: Plugin controllers:', !!plugin?.controllers);
  console.log('🚨 DEBUG: Plugin auth controller:', !!plugin?.controllers?.auth);

  if (typeof strapi !== 'undefined') {
    strapi.log.info('🚨 DEBUG: Users-permissions extension loaded!');
  }

  const register = plugin.controllers.auth.register;
  const callback = plugin.controllers.auth.callback;

  console.log('🚨 DEBUG: Original register function exists:', !!register);
  strapi.log.info('🚨 DEBUG: Original register function exists:', !!register);

  plugin.controllers.auth.register = async (ctx) => {
    console.log('🚨 DEBUG: Custom registration controller called!');
    strapi.log.info('🚨 DEBUG: Custom registration controller called!', {
      email: ctx.request.body.email,
      username: ctx.request.body.username
    });

    try {
      // Store original request body
      const originalBody = { ...ctx.request.body };

      console.log('🚨 DEBUG: About to handle organization creation');
      strapi.log.info('🚨 DEBUG: About to handle organization creation');

      // Handle organization creation logic (same as before)
      if (typeof ctx.request.body.organization === "number") {
        ctx.request.body = {
          ...ctx.request.body,
          acc_type: "organization",
        };
      } else {
        let org;
        const userBody = ctx.request.body;
        if (ctx.request.body.organization === "individual") {
          // Create individual organization using entity service
          org = await strapi.entityService.create('api::organization.organization', {
            data: {
              name: ctx.request.body.username,
              type: "individual"
            }
          });
        } else {
          // Create organization using entity service
          org = await strapi.entityService.create('api::organization.organization', {
            data: {
              name: ctx.request.body.organization,
              type: "organization"
            }
          });
        }
        ctx.request.body = {
          ...userBody,
          organization: org.id,
          acc_type:
            userBody.organization === "individual"
              ? "individual"
              : "organization",
        };
      }

      console.log('🚨 DEBUG: About to call original register function');
      strapi.log.info('🚨 DEBUG: About to call original register function');

      // Call the original register function
      await register(ctx);

      console.log('🚨 DEBUG: Original register function completed. Response:', {
        hasBody: !!ctx.body,
        hasUser: !!(ctx.body && ctx.body.user),
        hasJWT: !!(ctx.body && ctx.body.jwt),
        userConfirmed: ctx.body && ctx.body.user ? ctx.body.user.confirmed : 'no user',
        responseKeys: ctx.body ? Object.keys(ctx.body) : 'no body'
      });

      strapi.log.info('🚨 DEBUG: Original register function completed', {
        hasBody: !!ctx.body,
        hasUser: !!(ctx.body && ctx.body.user),
        hasJWT: !!(ctx.body && ctx.body.jwt),
        userConfirmed: ctx.body && ctx.body.user ? ctx.body.user.confirmed : 'no user',
        responseKeys: ctx.body ? Object.keys(ctx.body) : 'no body'
      });

      // CRITICAL FIX: For local registrations, ALWAYS remove JWT token and require email confirmation
      if (ctx.body && ctx.body.user && ctx.body.user.provider === 'local') {
        console.log('🚨 DEBUG: Local registration detected - ALWAYS removing JWT token for email confirmation');
        strapi.log.info('🔒 REGISTRATION: Local registration - removing JWT token to require email confirmation', {
          email: ctx.body.user.email,
          confirmed: ctx.body.user.confirmed,
          hadJWT: !!ctx.body.jwt,
          provider: ctx.body.user.provider
        });

        // FORCE user to be unconfirmed in the response
        ctx.body.user.confirmed = false;

        // Return user data without JWT token for ALL local registrations
        ctx.body = {
          user: ctx.body.user,
          message: 'Registration successful. Please check your email to confirm your account.'
        };

        console.log('🚨 DEBUG: JWT token removed for local registration, new response:', {
          hasJWT: !!ctx.body.jwt,
          userConfirmed: ctx.body.user.confirmed,
          message: ctx.body.message
        });
        strapi.log.info('🔒 REGISTRATION: Response sent without JWT token (email confirmation required)');
      } else {
        console.log('🚨 DEBUG: NOT removing JWT token because:', {
          hasBody: !!ctx.body,
          hasUser: !!(ctx.body && ctx.body.user),
          userProvider: ctx.body && ctx.body.user ? ctx.body.user.provider : 'no user',
          userConfirmed: ctx.body && ctx.body.user ? ctx.body.user.confirmed : 'no user'
        });
        strapi.log.info('🚨 DEBUG: NOT removing JWT token (non-local registration)', {
          hasBody: !!ctx.body,
          hasUser: !!(ctx.body && ctx.body.user),
          userProvider: ctx.body && ctx.body.user ? ctx.body.user.provider : 'no user'
        });
      }

    } catch (e) {
      strapi.log.error('❌ REGISTRATION: Registration failed:', e);
      ctx.throw(400, e);
    }
  };

  // Override callback to handle email confirmation properly
  plugin.controllers.auth.callback = async (ctx) => {
    try {
      // Handle email confirmation
      const { confirmation } = ctx.query;

      if (confirmation) {
        // Find user by confirmation token
        const user = await strapi.query('plugin::users-permissions.user').findOne({
          where: { confirmationToken: confirmation }
        });

        if (!user) {
          return ctx.badRequest('Invalid confirmation token');
        }

        if (user.confirmed) {
          return ctx.badRequest('Account already confirmed');
        }

        // Update user as confirmed and clear confirmation token
        await strapi.query('plugin::users-permissions.user').update({
          where: { id: user.id },
          data: {
            confirmed: true,
            confirmationToken: null
          }
        });

        strapi.log.info('✅ User email confirmed successfully', {
          userId: user.id,
          email: user.email
        });

        // Send welcome email after confirmation
        try {
          const emailService = require('../../helpers/email-service');
          await emailService.sendWelcomeEmail({
            email: user.email,
            name: user.username
          });
          strapi.log.info('✅ Welcome email sent after confirmation');
        } catch (emailError) {
          strapi.log.error('❌ Failed to send welcome email:', emailError);
        }

        // Generate JWT token for confirmed user (auto-login after confirmation)
        const jwt = strapi.plugin('users-permissions').service('jwt');
        const token = jwt.issue({ id: user.id });

        // Sanitize user data for response
        const { sanitize } = require('@strapi/utils');
        const sanitizedUser = await sanitize.contentAPI.output(user, strapi.getModel('plugin::users-permissions.user'));

        return ctx.send({
          message: 'Email confirmed successfully',
          confirmed: true,
          jwt: token,
          user: sanitizedUser
        });
      }

      await callback(ctx);
    } catch (error) {
      strapi.log.error('❌ Callback error:', error);
      ctx.throw(400, error);
    }
  };

  // Override forgot password to use Postmark instead of SendGrid
  plugin.controllers.auth.forgotPassword = async (ctx) => {
    const { email } = ctx.request.body;

    if (!email) {
      return ctx.badRequest('Email is required');
    }

    try {
      // Find user by email
      const user = await strapi.query('plugin::users-permissions.user').findOne({
        where: { email: email.toLowerCase() }
      });

      if (!user) {
        // Don't reveal if email exists or not for security
        return ctx.send({ ok: true });
      }

      // Generate reset token
      const crypto = require('crypto');
      const resetPasswordToken = crypto.randomBytes(64).toString('hex');

      // Update user with reset token
      await strapi.query('plugin::users-permissions.user').update({
        where: { id: user.id },
        data: { resetPasswordToken }
      });

      // Send reset email using Postmark
      const emailService = require('../../helpers/email-service');
      const resetUrl = `${process.env.FRONTEND_URL}/reset-password?code=${resetPasswordToken}`;

      strapi.log.info('📧 Sending password reset email via Postmark', {
        email: user.email,
        resetUrl: resetUrl
      });

      try {
        await emailService.sendPasswordResetEmail({
          email: user.email,
          name: user.username,
          resetUrl: resetUrl
        });

        strapi.log.info('✅ Password reset email sent successfully via Postmark');
        return ctx.send({ ok: true });

      } catch (emailError) {
        strapi.log.error('❌ Failed to send password reset email:', emailError);
        return ctx.badRequest({
          error: 'Failed to send reset email',
          details: emailError.message || emailError,
          provider: emailError.provider || 'unknown'
        });
      }

    } catch (error) {
      strapi.log.error('❌ Forgot password error:', error);
      return ctx.badRequest({
        error: 'Failed to process password reset request',
        details: error.message
      });
    }
  };

  // Add reset password endpoint
  plugin.controllers.auth.resetPassword = async (ctx) => {
    const { code, password, passwordConfirmation } = ctx.request.body;

    strapi.log.info('🔄 Password reset request received', {
      hasCode: !!code,
      hasPassword: !!password,
      hasPasswordConfirmation: !!passwordConfirmation,
      codeLength: code?.length
    });

    if (!code) {
      strapi.log.warn('❌ Password reset failed: No reset code provided');
      return ctx.badRequest('Reset code is required');
    }

    if (!password) {
      strapi.log.warn('❌ Password reset failed: No password provided');
      return ctx.badRequest('Password is required');
    }

    if (!passwordConfirmation) {
      strapi.log.warn('❌ Password reset failed: No password confirmation provided');
      return ctx.badRequest('Password confirmation is required');
    }

    if (password !== passwordConfirmation) {
      strapi.log.warn('❌ Password reset failed: Passwords do not match');
      return ctx.badRequest('Passwords do not match');
    }

    if (password.length < 6) {
      strapi.log.warn('❌ Password reset failed: Password too short');
      return ctx.badRequest('Password must be at least 6 characters long');
    }

    try {
      // Find user by reset token
      const user = await strapi.query('plugin::users-permissions.user').findOne({
        where: { resetPasswordToken: code }
      });

      if (!user) {
        strapi.log.info('⚠️ Invalid or expired reset token');
        return ctx.badRequest('Invalid or expired reset token');
      }

      if (user.blocked) {
        strapi.log.info('⚠️ User is blocked', { email: user.email });
        return ctx.badRequest('This user is blocked');
      }

      // Hash the new password
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash(password, 10);

      // Update user with new password and clear reset token
      await strapi.query('plugin::users-permissions.user').update({
        where: { id: user.id },
        data: {
          password: hashedPassword,
          resetPasswordToken: null
        }
      });

      strapi.log.info('✅ Password reset successfully', {
        email: user.email,
        userId: user.id
      });

      // Return success without auto-login for security
      return ctx.send({
        message: 'Password reset successfully',
        success: true
      });

    } catch (error) {
      strapi.log.error('❌ Reset password error:', error);
      return ctx.badRequest({
        error: 'Failed to reset password',
        details: error.message
      });
    }
  };

  // Add a test route to verify extension is working
  plugin.routes['content-api'].routes.push({
    method: 'GET',
    path: '/auth/test-extension',
    handler: 'auth.testExtension',
    config: {
      prefix: '',
      policies: []
    }
  });

  plugin.controllers.auth.testExtension = async (ctx) => {
    console.log('🚨 DEBUG: Test extension endpoint called!');
    ctx.body = {
      message: 'Extension is working!',
      timestamp: new Date().toISOString()
    };
  };

  // Override the user service to force confirmed: false
  const originalAdd = plugin.services.user.add;
  plugin.services.user.add = async (params) => {
    console.log('🚨 DEBUG: User service add called!', {
      email: params.email,
      confirmed: params.confirmed,
      provider: params.provider
    });

    // Force confirmed to false for local registrations
    if (params.provider === 'local') {
      params.confirmed = false;
      console.log('🚨 DEBUG: Forced confirmed to false for local registration');
    }

    const result = await originalAdd(params);

    console.log('🚨 DEBUG: User created with confirmed status:', result.confirmed);

    return result;
  };

  return plugin;
};
