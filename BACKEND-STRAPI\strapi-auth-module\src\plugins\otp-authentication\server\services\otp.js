let plivo = require( 'plivo' );

module.exports = ({ strapi }) => ({
	sendSms: async ( params ) => {
		let client = new plivo.Client( process.env.PLIVO_AUTH_ID
			, process.env.PLIVO_AUTH_TOKEN );

		client.messages.create(
			process.env.PLIVO_APP_NUMBER,
			params.dst,
			'Hi, your OTP is '+params.text,
		).then( result => {
			return Promise.resolve( result );
		} ).catch( err => {
			return Promise.reject( err );
		} );
	},
  });
