"use strict";
const stripe = require("stripe")(process.env.STRIPE_SECRETE_KEY);

/**
 * subscription-order controller
 */

// const { createCoreController } = require('@strapi/strapi').factories;

// module.exports = createCoreController('api::subscription-order.subscription-order');

const { createCoreController } = require("@strapi/strapi").factories;

module.exports = createCoreController(
  "api::subscription-order.subscription-order",
  ({ strapi }) => ({
    async create(ctx) {
      try {
        const plan = await strapi
          .query("api::plan.plan")
          .findOne({
            where: { id: ctx.request.body.plan },
          })
          .catch((err) => {
            console.log(err);
          });
        const prices = await stripe.prices.list({
          product: plan.stripe_prod_id,
          active: true,
        });
        let customer = await stripe.customers.list({
          email: ctx.state.user.email,
          limit: 1,
        });
        console.log(customer);
        var sessiondata;
        if (customer.data.length <= 0) {
          // customer = await stripe.customers.create({ email: ctx.state.user.email, name: ctx.state.user.username, currency: 'inr'});
          sessiondata = {
            mode: "subscription",
            success_url: `${process.env.DASHBOARD_URL}/pages/payment/success`,
            cancel_url: `${process.env.DASHBOARD_URL}/pages/payment/failed`,
            currency:'inr',
            customer_email: ctx.state.user.email,
            metadata: { organization: ctx.state.user.organization.id },
            line_items: [{ price: prices.data[0].id, quantity: 1 }],
          };
        } else {
          sessiondata = {
            mode: "subscription",
            success_url: `${process.env.DASHBOARD_URL}/pages/payment/success`,
            cancel_url: `${process.env.DASHBOARD_URL}/pages/payment/failed`,
            customer: customer.data[0].id,
            metadata: { organization: ctx.state.user.organization.id },
            line_items: [{ price: prices.data[0].id, quantity: 1 }],
          };
        }
        strapi.query("api::organization.organization").update({
          where: { id: ctx.state.user.organization.id },
          data: { stripe_customer: customer.id },
        });
        const session = await stripe.checkout.sessions.create(sessiondata);

        await strapi
          .service("api::subscription-order.subscription-order")
          .create({
            data: {
              type: "subscription",
              plan: plan.id,
              plantype: plan.type,
              stripeSessionId: session.id,
              isPaid: false,
              organization: ctx.state.user.organization.id,
            },
          })
          .catch((err) => {
            console.log("subscription error ");
            console.log(err);
          });

        return { stripeSession: session };
      } catch (error) {
        console.log(error);
        ctx.response.status = 500;
        return { error };
      }
    },

    async unsubscribe(ctx) {
      try {
        const customer = await stripe.customers.list({
          email: ctx.state.user.email,
          limit: 1,
        });

        if (customer.data.length === 0) {
          return ctx.throw(500, "Customer not found.");
        }
        const subscription = await stripe.subscriptions.list({
          customer: customer.data[0].id,
        });
        if (subscription.data.length === 0) {
          return ctx.throw(500, "You Don't Have subscription.");
        }
        // const canceledSubscription = await stripe.subscriptions.cancel(subscription.data[0].id);
        const canceledSubscription = await stripe.subscriptions.update(
          subscription.data[0].id,
          {
            cancel_at_period_end: true,
          }
        );
        return "Unsusbcribed";
      } catch (e) {
        console.log(e);
        return ctx.throw(500, "Something went wrong");
      }
    },
  })
);
