import { yup<PERSON>esolver } from "@hookform/resolvers/yup";
import { Controller, useForm } from "react-hook-form";
import Button from "@mui/material/Button";
import Checkbox from "@mui/material/Checkbox";
import FormControl from "@mui/material/FormControl";
import FormControlLabel from "@mui/material/FormControlLabel";
import TextField from "@mui/material/TextField";
import { Link } from "react-router-dom";
import * as yup from "yup";
import _ from "@lodash";
import Box from "@mui/material/Box";
import Paper from "@mui/material/Paper";
import jwtService from "../../auth/services/jwtService";
import "./sign-in.css";
import Typography from "@mui/material/Typography";
import FormHelperText from "@mui/material/FormHelperText";
import LoadingSpinner from "app/shared-components/loading-spinner/loading-spinner";
import { useState } from "react";
import DividerWithText from "app/shared-components/dividers/DividerWithText";
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import InputAdornment from "@mui/material/InputAdornment";
/**
 * Form Validation Schema
 */
const schema = yup.object().shape({
  email: yup
    .string()
    .email("You must enter a valid email")
    .required("You must enter a email"),
  password: yup
    .string()
    .required("Please enter your password.")
    .min(4, "Password is too short - must be at least 4 chars."),
});

const defaultValues = {
  email: "",
  password: "",
  remember: true,
  terms: false,
  phonenumber: "",
};

function SignInPage() {
  const { control, formState, handleSubmit, setError, setValue } = useForm({
    mode: "onChange",
    defaultValues,
    resolver: yupResolver(schema),
  });

  const { isValid, dirtyFields, errors, isLoading } = formState;

  const [loading, setLoading] = useState(false);

  function onSubmit({ email, password }) {
    setLoading(true);
    jwtService
      .signInWithEmailAndPassword(email, password)
      .then((user) => {
        setLoading(false);
      })
      .catch((error) => {
        setLoading(false);
        // setError(error);
      });
  }

  return (
    <div className="flex flex-col xl:h-screen 2xl:h-screen lg:h-screen 3xl:h-screen xl:flex-row  2xl:flex-row  md:flex-row lg:flex-row w-full align-center items-center justify-end bg-[#F8F7FF]">
      <div className="flex bg-[#F8F7FF] flex-col w-full  2xl:w-[40%] xl:w-[40%] md:w-[40%] lg:w-[40%] align-center items-center justify-center sm:mb-16 xs:mb-16">
        <div className="w-full mb-16 mt-32 px-32">
          <div className="flex w-full justify-center">
            <img
              className="logo-icon mt-2  w-24 h-24"
              src="assets/images/logo/ajentic-logo.png"
              alt="logo"
            />
            <span className="flex ml-8 text-2xl font-bold md:block">
              Ajentic
            </span>
          </div>

          <div className="mx-auto max-w-360 items-center justify-center">
            <Typography className="mt-16 text-4xl font-bold tracking-tight leading-tight">
              <Typography className="mt-16 text-lg font-regular tracking-tight leading-tight text-center">
                Login to your account
              </Typography>


              <div className="mt-32 mb-24 bg-white shadow-md rounded-lg">
                <div
                  className="flex p-12 border rounded place-content-center cursor-pointer  hover:bg-gray-800 hover:text-white"
                  onClick={() =>
                    (window.location = `${process.env.REACT_APP_AUTH_BASE_URL}/api/connect/google`)
                  }
                >
                  <div className="google-image">
                    <svg
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 48 48"
                      className="LgbsSe-Bz112c"
                    >
                      <g>
                        <path
                          fill="#EA4335"
                          d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"
                        ></path>
                        <path
                          fill="#4285F4"
                          d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"
                        ></path>
                        <path
                          fill="#FBBC05"
                          d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"
                        ></path>
                        <path
                          fill="#34A853"
                          d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"
                        ></path>
                        <path fill="none" d="M0 0h48v48H0z"></path>
                      </g>
                    </svg>
                  </div>
                  <span className="font-[400] text-base mt-1 ml-4 tracking-normal">
                    Sign in with Google
                  </span>
                </div>
              </div>

              <DividerWithText> or </DividerWithText>

              <form
                name="loginForm"
                noValidate
                className="flex flex-col justify-center w-full mt-24"
                onSubmit={handleSubmit(onSubmit)}
              >
                <Controller
                  name="email"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      className="mb-24"
                      size="medium"
                      style={{
                        backgroundColor: "transparent",
                      }}
                      placeholder="Enter your email address"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <EmailIcon className="text-grey-500 text-xl"/>
                          </InputAdornment>
                        ),
                        disableunderline: "true",
                        style: {
                          borderRadius: "8px",
                          backgroundColor: "#FFFfff",
                          paddingRight: "4px",
                          fontWeight: 400,
                          height: "2rem !important",
                          fontSize: "1.5rem",
                          letterSpacing: "0.01em",
                        },
                      }}
                      autoFocus
                      type="email"
                      error={!!errors.email}
                      helperText={errors?.email?.message}
                      variant="outlined"
                      fullWidth
                    />
                  )}
                />
                <Controller
                  name="password"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      className="mb-24"
                      style={{
                        backgroundColor: "transparent",
                      }}
                      placeholder="Enter your password"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LockIcon className="text-grey-500 text-xl"/>
                          </InputAdornment>
                        ),
                        disableunderline: "true",
                        style: {
                          borderRadius: "8px",
                          backgroundColor: "#FFFfff",
                          paddingRight: "4px",
                          fontWeight: 400,
                          height: "2rem !important",
                          fontSize: "1.5rem",
                          letterSpacing: "0.01em",
                        },
                      }}
                      type="password"
                      error={!!errors.password}
                      helperText={errors?.password?.message}
                      variant="outlined"
                      fullWidth
                    />
                  )}
                />
                <div className="flex w-full">
                  <div className="flex flex-row w-full items-center justify-start space-x-0 ">
                    {" "}
                    {/* Adjusted this div */}
                    <Controller
                      name="remember"
                      control={control}
                      render={({ field }) => (
                        <FormControl>
                          <FormControlLabel
                            label={
                              <div className="!flex !w-full !place-content-between text-sm font-light tracking-normal text-black">
                                Remember me
                                <span className=""></span>
                                <Link
                                  className="text-sm text-base-purple font-light tracking-normal"
                                  to="/forgot-password"
                                >
                                  Forgot password?
                                </Link>
                              </div>
                            }
                            className="!flex !w-full !place-content-between signin-checkbox text-xs cta-label-style "
                            control={
                              <Checkbox
                                size="small"
                                className="cta-label-style"
                                {...field}
                              />
                            }
                          />
                        </FormControl>
                      )}
                    />
                  </div>
                </div>
                {loading && (
                  <div className="mt-4 flex align-center justify-center items-center">
                    <LoadingSpinner size={24} />
                  </div>
                )}
                {!loading && (
                  <Button
                    variant="contained"
                    color="secondary"
                    className="press-button shine-button w-full mt-16 rounded-md bg-primary hover:bg-primary text-white"
                    aria-label="Login"
                    // disabled={_.isEmpty(dirtyFields) || !isValid}
                    type="submit"
                    size="large"
                  >
                    Sign in
                  </Button>
                )}
              </form>

              <div className="mt-24 flex justify-center items-center text-center font-normal text-base tracking-normal text-black">
                <p>Don't have an account?</p>
                <a
                  className="font-normal text-base !text-base-purple ml-4 no-underline tracking-normal"
                  href={
                    process.env.REACT_APP_DISABLE_SIGNUP === "true"
                      ? "https://cal.com/talkbase/15min"
                      : "/sign-up"
                  }
                >
                  Create an account{" "}
                </a>
              </div>
            </Typography>
          </div>
        </div>
      </div>
      <div className="flex bg-white justify-center items-center rounded-md w-full 2xl:w-[60%] xl:w-[60%] md:w-[60%] lg:w-[60%]">
        <img
          src="assets/images/<EMAIL>"
          alt=""
          className="object-contain  h-[100%]"
        />
      </div>
    </div>
  );
}

export default SignInPage;
