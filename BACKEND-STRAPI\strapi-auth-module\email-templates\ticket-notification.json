{"name": "ticket-notification", "description": "Ticket notification template for Scrumboard ticket creation", "provider": "postmark", "template_id": "", "html_content": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>New Ticket Created - {{title}}</title>\n</head>\n<body style=\"margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;\">\n    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#fefefe\" style=\"padding: 40px 0;\">\n        <tr>\n            <td align=\"center\">\n                <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" style=\"background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);\">\n                    <!-- Header -->\n                    <tr>\n                        <td style=\"padding: 40px 40px 20px 40px; text-align: center;\">\n                            <img src=\"{{podycy_logo_url}}\" alt=\"Podycy\" style=\"max-width: 150px; height: auto;\">\n                        </td>\n                    </tr>\n                    \n                    <!-- Main Content -->\n                    <tr>\n                        <td style=\"padding: 0 40px 40px 40px;\">\n                            <h1 style=\"color: #333333; font-size: 28px; margin: 0 0 20px 0; text-align: center;\">🎫 New Ticket Created</h1>\n                            <h2 style=\"color: #666666; font-size: 18px; margin: 0 0 30px 0; text-align: center; font-weight: normal;\">{{title}}</h2>\n                            \n                            <p style=\"color: #666666; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;\">Hi {{name}},</p>\n                            \n                            <p style=\"color: #666666; font-size: 16px; line-height: 1.6; margin: 0 0 30px 0;\">\n                                A new support ticket has been created in your organization's Scrumboard by <strong>{{agent_name}}</strong>. Here are the details:\n                            </p>\n                            \n                            <!-- Ticket Details Box -->\n                            <div style=\"background-color: #f8f9fa; border-left: 4px solid #4F46E5; padding: 20px; margin: 30px 0; border-radius: 4px;\">\n                                <h3 style=\"color: #333333; font-size: 18px; margin: 0 0 15px 0;\">📋 Ticket Details</h3>\n                                <p style=\"color: #666666; font-size: 14px; margin: 0 0 10px 0;\"><strong>Title:</strong> {{title}}</p>\n                                <p style=\"color: #666666; font-size: 14px; margin: 0 0 10px 0;\"><strong>Description:</strong></p>\n                                <p style=\"color: #666666; font-size: 14px; margin: 0 0 15px 0; padding-left: 15px;\">{{description}}</p>\n                                <p style=\"color: #666666; font-size: 14px; margin: 0 0 10px 0;\"><strong>Agent:</strong> {{agent_name}}</p>\n                                {{#if priority}}<p style=\"color: #666666; font-size: 14px; margin: 0 0 10px 0;\"><strong>Priority:</strong> <span style=\"color: {{priority_color}}; font-weight: bold;\">{{priority}}</span></p>{{/if}}\n                                {{#if assignee}}<p style=\"color: #666666; font-size: 14px; margin: 0 0 10px 0;\"><strong>Assigned to:</strong> {{assignee}}</p>{{/if}}\n                                <p style=\"color: #666666; font-size: 14px; margin: 0 0 10px 0;\"><strong>Customer Contact:</strong> {{customer_email}}</p>\n                                {{#if organization_name}}<p style=\"color: #666666; font-size: 14px; margin: 0;\"><strong>Organization:</strong> {{organization_name}}</p>{{/if}}\n                            </div>\n                            \n                            <!-- CTA Button -->\n                            <div style=\"text-align: center; margin: 40px 0;\">\n                                <a href=\"{{ticket_url}}\" style=\"background-color: #4F46E5; color: #ffffff; padding: 15px 30px; text-decoration: none; border-radius: 6px; font-size: 16px; font-weight: bold; display: inline-block;\">View Ticket</a>\n                            </div>\n                            \n                            <p style=\"color: #666666; font-size: 14px; line-height: 1.6; margin: 30px 0 0 0;\">\n                                If the button doesn't work, you can also copy and paste this link into your browser:\n                                <br><a href=\"{{ticket_url}}\" style=\"color: #4F46E5; word-break: break-all;\">{{ticket_url}}</a>\n                            </p>\n                            \n                            <p style=\"color: #666666; font-size: 14px; line-height: 1.6; margin: 30px 0 0 0;\">\n                                Please review this ticket and take appropriate action as needed.\n                            </p>\n                        </td>\n                    </tr>\n                    \n                    <!-- Footer -->\n                    <tr>\n                        <td style=\"padding: 30px 40px; background-color: #f8f9fa; border-top: 1px solid #e9ecef; border-radius: 0 0 8px 8px;\">\n                            <p style=\"color: #999999; font-size: 12px; margin: 0; text-align: center;\">\n                                This email was sent by {{sender_name}} from {{company_name}}<br>\n                                {{company_address}}\n                            </p>\n                        </td>\n                    </tr>\n                </table>\n            </td>\n        </tr>\n    </table>\n</body>\n</html>", "text_content": "🎫 New Ticket Created\n\nHi {{name}},\n\nA new support ticket has been created in your organization's Scrumboard by {{agent_name}}.\n\nTicket Details:\n- Title: {{title}}\n- Description: {{description}}\n- Agent: {{agent_name}}\n{{#if priority}}- Priority: {{priority}}\n{{/if}}{{#if assignee}}- Assigned to: {{assignee}}\n{{/if}}- Customer Contact: {{customer_email}}\n{{#if organization_name}}- Organization: {{organization_name}}\n{{/if}}\nView the ticket: {{ticket_url}}\n\nPlease review this ticket and take appropriate action as needed.\n\nBest regards,\n{{sender_name}}\n{{company_name}}", "template_type": "html_content", "category": "notification", "subject": "🎫 New Ticket: {{title}}", "default_from_email": "<EMAIL>", "default_from_name": "Podycy Team", "variables": {"sender_name": "Podycy Team", "company_name": "Podycy Technologies", "product_name": "<PERSON><PERSON><PERSON>", "company_address": "Podycy Technologies, Customer Support Center", "podycy_logo_url": "https://podycy.com/assets/logo-email.png", "header_image_url": "https://podycy.com/assets/email-header.png"}, "is_active": true, "scope": "global"}