/**
 * Email Schema Migration and Testing Script
 * Migrates JSON templates to database and tests the new email system
 */

async function migrateEmailSchemas() {
  console.log('🚀 EMAIL SCHEMA MIGRATION AND TESTING');
  console.log('='.repeat(80));
  
  const results = {
    templateMigration: { status: 'pending', error: null, count: 0 },
    schemaValidation: { status: 'pending', error: null },
    emailLogging: { status: 'pending', error: null },
    apiTesting: { status: 'pending', error: null }
  };

  // Step 1: Migrate JSON templates to database
  console.log('\n📦 STEP 1: Migrating JSON Templates to Database...');
  try {
    const migratedTemplates = await strapi.service('api::email-template.email-template').migrateJsonTemplates();
    
    console.log(`✅ Successfully migrated ${migratedTemplates.length} templates:`);
    migratedTemplates.forEach(template => {
      console.log(`  - ${template.name} (${template.category}, ${template.provider})`);
    });
    
    results.templateMigration.status = 'passed';
    results.templateMigration.count = migratedTemplates.length;
  } catch (error) {
    console.error('❌ Template migration failed:', error.message);
    results.templateMigration.status = 'failed';
    results.templateMigration.error = error.message;
  }

  // Step 2: Validate schema relationships
  console.log('\n🔗 STEP 2: Validating Schema Relationships...');
  try {
    // Test email-template schema
    const templates = await strapi.entityService.findMany('api::email-template.email-template', {
      limit: 1
    });
    
    // Test email-log schema
    const logs = await strapi.entityService.findMany('api::email-log.email-log', {
      limit: 1
    });
    
    // Test email-integration schema
    const integrations = await strapi.entityService.findMany('api::email-integration.email-integration', {
      limit: 1
    });
    
    console.log('✅ Schema validation successful:');
    console.log(`  - Email Templates: ${templates.length} found`);
    console.log(`  - Email Logs: ${logs.length} found`);
    console.log(`  - Email Integrations: ${integrations.length} found`);
    
    results.schemaValidation.status = 'passed';
  } catch (error) {
    console.error('❌ Schema validation failed:', error.message);
    results.schemaValidation.status = 'failed';
    results.schemaValidation.error = error.message;
  }

  // Step 3: Test email logging functionality
  console.log('\n📝 STEP 3: Testing Email Logging Functionality...');
  try {
    // Create a test email log
    const testLog = await strapi.service('api::email-log.email-log').logEmail({
      messageId: `test-${Date.now()}`,
      provider: 'postmark',
      toEmails: ['<EMAIL>'],
      fromEmail: '<EMAIL>',
      fromName: 'Podycy Team',
      subject: 'Test Email Log',
      templateName: 'ticket-notification',
      templateData: { test: 'data' },
      emailType: 'ticket-notification',
      metadata: { test: true }
    });
    
    // Update the status
    await strapi.service('api::email-log.email-log').updateEmailStatus(testLog.message_id, 'sent');
    
    console.log('✅ Email logging test successful:');
    console.log(`  - Log ID: ${testLog.id}`);
    console.log(`  - Message ID: ${testLog.message_id}`);
    console.log(`  - Status updated to: sent`);
    
    results.emailLogging.status = 'passed';
  } catch (error) {
    console.error('❌ Email logging test failed:', error.message);
    results.emailLogging.status = 'failed';
    results.emailLogging.error = error.message;
  }

  // Step 4: Test API endpoints
  console.log('\n🌐 STEP 4: Testing API Endpoints...');
  try {
    // Test template API
    const templateByName = await strapi.service('api::email-template.email-template').getTemplateByName('ticket-notification');
    
    // Test analytics
    const analytics = await strapi.service('api::email-log.email-log').getEmailAnalytics(null, {});
    
    console.log('✅ API testing successful:');
    console.log(`  - Template retrieval: ${templateByName.name}`);
    console.log(`  - Analytics: ${analytics.total} total emails`);
    
    results.apiTesting.status = 'passed';
  } catch (error) {
    console.error('❌ API testing failed:', error.message);
    results.apiTesting.status = 'failed';
    results.apiTesting.error = error.message;
  }

  // Migration Summary
  console.log('\n📊 MIGRATION SUMMARY');
  console.log('='.repeat(80));
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(result => result.status === 'passed').length;
  const failedTests = Object.values(results).filter(result => result.status === 'failed').length;
  
  console.log(`Total Steps: ${totalTests}`);
  console.log(`Passed: ${passedTests} ✅`);
  console.log(`Failed: ${failedTests} ❌`);
  console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (failedTests > 0) {
    console.log('\n❌ FAILED STEPS:');
    Object.entries(results).forEach(([stepName, result]) => {
      if (result.status === 'failed') {
        console.log(`  - ${stepName}: ${result.error}`);
      }
    });
  }
  
  console.log('\n🎯 NEW FEATURES AVAILABLE:');
  console.log('✅ Database-managed email templates');
  console.log('✅ Comprehensive email logging and analytics');
  console.log('✅ Enhanced email integration management');
  console.log('✅ Template usage statistics and analytics');
  console.log('✅ Email delivery tracking and status updates');
  console.log('✅ Organization-wide email analytics');
  
  console.log('\n📋 API ENDPOINTS CREATED:');
  console.log('📧 Email Templates:');
  console.log('  - GET    /api/email-templates');
  console.log('  - POST   /api/email-templates/migrate');
  console.log('  - GET    /api/email-templates/name/:name');
  console.log('  - GET    /api/email-templates/category/:category');
  console.log('  - POST   /api/email-templates/:id/test');
  console.log('  - GET    /api/email-templates/:id/analytics');
  console.log('');
  console.log('📊 Email Logs:');
  console.log('  - GET    /api/email-logs');
  console.log('  - GET    /api/email-logs/analytics/organization/:organizationId');
  console.log('  - GET    /api/email-logs/status/:messageId');
  console.log('  - PUT    /api/email-logs/status/:messageId');
  console.log('  - GET    /api/email-logs/ticket/:ticketId');
  
  console.log('\n💡 NEXT STEPS:');
  console.log('1. Run Strapi server to apply schema changes');
  console.log('2. Access admin panel to manage email templates');
  console.log('3. Test ticket creation to verify email logging');
  console.log('4. Monitor email analytics in the admin dashboard');
  console.log('5. Use API endpoints for custom email management');
  
  return {
    success: failedTests === 0,
    totalTests,
    passedTests,
    failedTests,
    results
  };
}

// Run migration if called directly
if (require.main === module) {
  // This would be run in a Strapi context
  console.log('⚠️ This script should be run within Strapi context');
  console.log('Example: strapi console');
  console.log('Then run: await require("./scripts/migrate-email-schemas.js")()');
}

module.exports = migrateEmailSchemas;
