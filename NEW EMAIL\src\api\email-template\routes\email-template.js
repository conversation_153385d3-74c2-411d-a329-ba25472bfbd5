'use strict';

/**
 * Email Template Router
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

module.exports = createCoreRouter('api::email-template.email-template', {
  config: {
    find: {
      policies: [
        'global::user-details-populate',
        'global::update-org-before-api-call',
      ]
    },
    findOne: {
      policies: [
        'global::user-details-populate',
        'global::update-org-before-api-call',
      ]
    },
    create: {
      policies: [
        'global::user-details-populate',
        'global::update-org-before-api-call',
      ]
    },
    update: {
      policies: [
        'global::user-details-populate',
        'global::update-org-before-api-call',
      ]
    },
    delete: {
      policies: [
        'global::user-details-populate',
        'global::update-org-before-api-call',
      ]
    }
  }
}); 