import React from "react";
import { formatDistance } from "date-fns";
import NavLinkAdapter from "@fuse/core/NavLinkAdapter";

const BoardItem = ({ board }) => {
  const boardMembers = board.attributes.members.data.map((m) => ({
    id: m.id,
    avatar: m.attributes.profilePicture ?? "",
  }));

  return (
    <NavLinkAdapter
      to={board.id.toString()}
      role="button"
      className="flex flex-col justify-between pt-10 pr-4 pb-10 pl-10 w-full border border-gray-200 rounded-md bg-white shadow-[inset_0_0_0_1px_rgba(0,0,0,0.08)] ring-1 ring-gray-100 group transition-all duration-300 hover:ring-primary/50 hover:shadow-md"
    >
      <div>
        <h3 className="text-base font-semibold text-gray-800 mb-3 group-hover:text-primary transition-colors duration-200">
          {board.attributes.title}
        </h3>
        <p className="text-sm text-gray-500 line-clamp-2 leading-relaxed group-hover:text-gray-700 transition-colors duration-200">
          {board.attributes.description}
        </p>
      </div>

      <div className="mt-4">
        {boardMembers?.length > 0 && (
          <div className="flex -space-x-2 mb-2">
            {boardMembers.slice(0, 4).map((member, index) => (
              <img
                key={index}
                src={member.avatar}
                alt="member"
                className="w-7 h-7 rounded-full border-2 border-white shadow-sm"
              />
            ))}
            {boardMembers.length > 4 && (
              <div className="w-7 h-7 flex items-center justify-center text-xs font-medium bg-gray-200 text-gray-700 rounded-full border-2 border-white">
                +{boardMembers.length - 4}
              </div>
            )}
          </div>
        )}

        <p className="text-xs text-gray-400 group-hover:text-primary transition-colors duration-200">
          {formatDistance(new Date(board.attributes.createdAt), new Date(), {
            addSuffix: true,
          })}
        </p>
      </div>
    </NavLinkAdapter>
  );
};

export default BoardItem;
