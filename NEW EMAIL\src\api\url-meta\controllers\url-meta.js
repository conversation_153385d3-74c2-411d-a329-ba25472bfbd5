'use strict';

/**
 * url-meta controller
 */


// const axios = require('axios');
// const cheerio = require( 'cheerio' );
// const _ = require( 'lodash' );

module.exports = {

	async metascrape(ctx) {
		// const urls=ctx.request.body.urls
		// try {
		// 	const metadataArray = [];
    	// 	for (const url of urls) {
		// 	const result = await axios( url, {
		// 		method:      'GET',
		// 		credentials: 'include',
		// 		headers:     {
		// 			'Content-Type': 'application/json,text/plain',
		// 			'User-Agent':   'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36',
		// 		},
		// 	} ).catch( err => {
		// 		console.log(err);
		// 		return false;
		// 	} );
		// 	if ( result === false ){
		// 		console.log('Could not fetch url data for:', url);
        // 		continue;
		// 	}
		// 	const html = result?.data;
		// 	const $ = cheerio.load( html );
		// 	const metadata = {
		// 		author:      $( 'meta[name="author"]' ).attr( 'content' ),
		// 		clearbit:    $( 'meta[name="clearbit"]' ).attr( 'content' ),
		// 		date:        $( 'meta[name="date"]' ).attr( 'content' ),
		// 		description: $( 'meta[name="description"]' ).attr( 'content' ),
		// 		image:       $( 'meta[property="og:image"]' ).attr( 'content' ),
		// 		logo:        $( 'meta[name="logo"]' ).attr( 'content' ),
		// 		publisher:   $( 'meta[name="publisher"]' ).attr( 'content' ),
		// 		title:       $( 'meta[property="og:title"]' ).attr( 'content' ),
		// 		url:         $( 'meta[name="url"]' ).attr( 'content' ) ?? url,
		// 	};
		// 	let metaBody = _( metadata ).omitBy( _.isUndefined ).omitBy( _.isNull ).value();
		// 	metadataArray.push(metaBody);
		// }

		// return metadataArray;
		// } catch (err) {
		// 	return ctx.throw(500,"Server error")
		// }
	}
};

