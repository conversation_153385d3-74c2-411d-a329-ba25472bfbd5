import * as PropTypes from 'prop-types';
import { ReactSVG } from 'react-svg';
import AnswerSourcePopper from './answer-sources';
import { showMessage } from 'app/store/fuse/messageSlice';
import clipboardCopy from 'clipboard-copy';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon/FuseSvgIcon';
import { useDispatch } from 'react-redux';
import { TruncatedText } from 'app/shared-components/text-truncate/text-truncate';
import BaseDialog from 'app/shared-components/dialog/base-dialog';
import { useState } from 'react';

export default function QuestionAnswerCard(props) {
	const [answerDialogOpen, setAnswerDialogOpen]= useState(false);
	const dispatch = useDispatch();
	const handleDialogClose = () => {
		setAnswerDialogOpen(false);
	}
	const getSourceImage = (type) => {
		const lowerCaseType = type.toLowerCase();

		if (lowerCaseType.startsWith('whatsapp')) {
			return 'assets/images/icons/query-whatsapp.svg';
		}

		switch (lowerCaseType) {
			case 'dashboard':
				return 'assets/images/icons/query-dashboard.svg';
			case 'chatbot':
				return 'assets/images/icons/query-dashboard.svg';
			default:
				break;
		}
	};
	return (
		<div className="flex flex-col p-24 shadow-base rounded-lg border border-gray-200 text-lg bg-white">
			<div className="flex flex-row justify-between">
				<div className='flex flex-row'>
					<div>{props.question}</div>
					<FuseSvgIcon className="text-48 cursor-pointer ml-20" size={24} color="action" 
						onClick={(e)=>{clipboardCopy(`question: ${props.question} \nanswer: ${props.answer}`);
							dispatch(
							showMessage({ message: "Sharable link cpoied to clipboard" })
							);
						}} 
					>feather:copy
					</FuseSvgIcon>
				</div>

				<div>
					<AnswerSourcePopper sources={props.sources} />
				</div>
			</div>
			<div 
			 onClick={(e)=>{
				setAnswerDialogOpen(true);
			 }} 
			>
				<TruncatedText text={props.answer}></TruncatedText>
			</div>
			{/* <div className="mt-14 text-[#667085] text-base">{props.answer}</div> */}
			<div className="flex flex-row mt-20 gap-16 text-[#667085]">
				<div className="flex flex-row gap-4 text-base">
					<ReactSVG src="assets/images/icons/time.svg" />
					{props.created}
				</div>
				<div className="flex flex-row gap-4 text-base">
					<ReactSVG src={getSourceImage(props.sourceType)} />
					{props.sourceType.toLowerCase().startsWith('whatsapp')
						? props.sourceType.match(/\d+/)?.[0]
						: props.sourceType}
				</div>
				{props.knowledgebase &&
				(<div className="flex flex-row gap-4 bg-[#EDE7FA] px-20 rounded-3xl text-base">
					{/* <ReactSVG src='assets/images/icons/time.svg' /> */}
					{props.knowledgebase}
				</div>)}
				{props.did_find_answer==null?<div></div>:
				<div className={`flex flex-row gap-4 ${props.did_find_answer?'bg-[#DBFF76]':'bg-[#FF9575]'} px-20 rounded-3xl text-base`} >
					{props.did_find_answer?'Answered': "Not answered"}
				</div>}
			</div>
			<BaseDialog open={answerDialogOpen} handleClose={handleDialogClose} title={props.question}>
				<div className='mb-20'>{props.answer}</div>
			</BaseDialog>
		</div>
	);
}
QuestionAnswerCard.propTypes = {
	question: PropTypes.string,
	answer: PropTypes.string,
	sources: PropTypes.array,
	created: PropTypes.string,
	sourceType: PropTypes.string,
	knowledgebase: PropTypes.string,
	did_find_answer: PropTypes.bool
};
