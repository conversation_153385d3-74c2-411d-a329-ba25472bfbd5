import React from "react";
import AssignmentIcon from "@mui/icons-material/Assignment";
import { formatDateToHumanReadable } from "src/app/utils/date-utils";
import { ColoredPillLabel } from "app/shared-components/pill/pill";
import { getTicketStatePillColors } from "src/app/utils/colors";

export const TaskItem = ({
  id,
  title,
  ticketState,
  onClick,
  isSelected,
  createdAt,
}) => (
  <div
    className={`flex px-8 flex-col items-start space-y-4 py-2 my-8 cursor-pointer ${
      isSelected ? "bg-blue-50" : ""
    }`}
    onClick={onClick}
  >
    <div className="flex flex-row w-full mt-4 align-center justify-between mx-4">
      <div className="align-center">
        <AssignmentIcon className="text-lg text-base-purple" />
        <span className="ml-8 text-lg font-bold text-black">{id}</span>
      </div>

      <ColoredPillLabel
        text={ticketState}
        bgColor={getTicketStatePillColors(ticketState).background}
        textColor={getTicketStatePillColors(ticketState).label}
      />
    </div>

    <span className="text-md font-medium text-black mb-4">{title}</span>
    <span className="text-sm text-black">
      {formatDateToHumanReadable(createdAt)}
    </span>
    <hr className="w-full mt-8 border-t border-gray-200" />
  </div>
);
