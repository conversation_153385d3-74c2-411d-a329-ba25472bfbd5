'use strict';

/**
 * series custom router
 */
module.exports = {
  routes: [
    // Public series routes (no authentication required)
    {
      method: 'GET',
      path: '/series/public',
      handler: 'custom-controller.getPublicSeries',
      config: {
        auth: false,
      }
    },
    {
      method: 'GET',
      path: '/series/featured',
      handler: 'custom-controller.getFeaturedSeries',
      config: {
        auth: false,
      }
    },

    // Series management routes (authentication required)
    {
      method: 'POST',
      path: '/series/:seriesId/episodes',
      handler: 'custom-controller.addEpisode',
      config: {
        policies: ['global::user-details-populate']
      }
    },

    // Access management routes for private series
    {
      method: 'POST',
      path: '/series/:seriesId/grant-access',
      handler: 'custom-controller.grantSeriesAccess',
      config: {
        policies: ['global::user-details-populate']
      }
    },
    {
      method: 'DELETE',
      path: '/series/:seriesId/access/:accessId',
      handler: 'custom-controller.revokeSeriesAccess',
      config: {
        policies: ['global::user-details-populate']
      }
    },

    // Progress tracking routes (authentication required)
    {
      method: 'PUT',
      path: '/series/:seriesId/episodes/:episodeId/progress',
      handler: 'custom-controller.updateEpisodeProgress',
      config: {
        policies: ['global::user-details-populate']
      }
    },
    {
      method: 'POST',
      path: '/series/:seriesId/episodes/:episodeId/complete',
      handler: 'custom-controller.completeEpisode',
      config: {
        policies: ['global::user-details-populate']
      }
    },

    // User progress routes
    {
      method: 'GET',
      path: '/users/me/series-progress',
      handler: 'custom-controller.getUserSeriesProgress',
      config: {
        policies: ['global::user-details-populate']
      }
    }
  ]
}; 