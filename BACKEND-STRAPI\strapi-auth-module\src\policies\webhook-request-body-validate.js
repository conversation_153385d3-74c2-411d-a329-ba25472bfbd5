"use strict";

/**
 * webhook body validation policy
 */
const yup = require("yup");

module.exports = async (ctx, config, { strapi }) => {
  // Defined fileResultsSchema schema based on the Strapi model
  const fileResultsSchema = yup.object().shape({
    id: yup.string().required(),
    ai_model_name: yup.string(),
    ai_tokens_used_prompt: yup.number().integer().default(0),
    ai_tokens_used_completion: yup.number().integer().default(0),
    base_cost_per_token: yup.number().default(0.0),
    credits_used: yup.number().integer().default(0),
    docx_url: yup.string(),
    pdf_url: yup.string(),
    markdown_url: yup.string(),
    query: yup.string(),
    start_time: yup.string(),
    end_time: yup.string(),
    total_cost: yup.number().default(0.0),
    knowledgebase: yup.string().required(),
    status: yup.string(),
  });
  const fileResultsUpdateSchema = yup.object().shape({
    id: yup.string().required(),
    ai_model_name: yup.string(),
    ai_tokens_used_prompt: yup.number().integer(),
    ai_tokens_used_completion: yup.number().integer(),
    base_cost_per_token: yup.number(),
    credits_used: yup.number().integer(),
    docx_url: yup.string(),
    pdf_url: yup.string(),
    markdown_url: yup.string(),
    query: yup.string(),
    start_time: yup.string(),
    end_time: yup.string(),
    total_cost: yup.number(),
    knowledgebase: yup.string(),
    status: yup.string(),
  });
  const documentUpdateSchema = yup.object().shape({
    document_id: yup.string().required(),
    status: yup.string(),
    tokens_used: yup.number().integer(),
    character_count: yup.number().integer(),
    vector_store_name: yup.string(),
    index_name: yup.string(),
    namepsace_prefix: yup.string(),
    embedding_model: yup.string(),
  });
  const createChunkSchema = yup.object().shape({
    content: yup.string().required(),
    embedding_id: yup.string().required(),
    kb_id: yup.string().required(),
    document_id: yup.string().required(),
    status: yup.string().required(),
  });

  try {
    switch (ctx.request.body.action_type) {
      case "add_files_result_model":
        await fileResultsSchema.validate(ctx.request.body.data);
        return true;
      case "update_files_result_model":
        await fileResultsUpdateSchema.validate(ctx.request.body.data);
        return true;
      case "update_document":
        await documentUpdateSchema.validate(ctx.request.body.data);
        return true;
      case "create_chunk":
        await createChunkSchema.validate(ctx.request.body.data);
        return true;
      default:
        return false;
    }
  } catch (err) {
    console.log(err);
    // Handle validation errors
    ctx.status = 400;
    ctx.body = {
      error: err.errors,
    };
    return false; // Stop the request processing
  }
};
