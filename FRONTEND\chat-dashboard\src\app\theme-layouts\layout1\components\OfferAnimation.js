import React, { useState } from 'react';
import <PERSON><PERSON> from 'react-lottie';
import * as animationData from './offer.json'

const OfferAnimation = () => {
  const [isStopped, setIsStopped] = useState(false);
  const [isPaused, setIsPaused] = useState(false);

  const buttonStyle = {
    display: 'block',
    margin: '10px auto'
  };

  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData: animationData,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice'
    }
  };

  return (
    <div>
      <Lottie options={defaultOptions}
              height={50}
              width={50}
              isStopped={isStopped}
              isPaused={isPaused}/>
    </div>
  );
};

export default OfferAnimation;
