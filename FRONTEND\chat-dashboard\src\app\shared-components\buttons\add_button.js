import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';

function AddButton({ onClick , metaData }) {
  return <button
    type="button"
    onClick={() => onClick(metaData)}
    className="mt-2 py-2 px-8 inline-flex items-center text-center align-center  border border-black text-sm  font-medium rounded-[4px] text-black bg-white hover:bg-base-purple hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
  >
    <AddCircleOutlineIcon size={8} className="p-2 mr-2 hover:text-white" />
    Add
  </button>;
}

export default AddButton;
