const axios = require("axios");
const { WebClient } = require("@slack/web-api");
const CryptoJS = require("crypto-js");

// Read a token from the environment variables
const token = process.env.SLACK_TOKEN;

// Initialize

var blockPayload = {
  blocks: [
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: "Hello, {aj_user_name}! {aj_aget_name} has created a new ticket for you.\n\n *Here are the details of the ticket:*",
      },
    },
    {
      type: "divider",
    },
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: "*Subject*",
      },
    },
    {
      type: "section",
      text: {
        type: "plain_text",
        text: "{aj_subject}",
        emoji: true,
      },
    },
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: "*Contact Information*",
      },
    },
    {
      type: "section",
      text: {
        type: "plain_text",
        text: "{aj_contact_info}",
        emoji: true,
      },
    },
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: "*Title*",
      },
    },
    {
      type: "section",
      text: {
        type: "plain_text",
        text: "{aj_title}",
        emoji: true,
      },
    },
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: "*Description*",
      },
    },
    {
      type: "section",
      text: {
        type: "plain_text",
        text: "{aj_description}",
        emoji: true,
      },
    },
    {
      type: "divider",
    },
    {
      type: "actions",
      elements: [
        {
          type: "button",
          text: {
            type: "plain_text",
            text: "View in Dashboard",
            emoji: true,
          },
          value: "https://app.ajentic.com",
        },
      ],
    },
  ],
};

module.exports = {
  async sendSlackMessage({
    slackAuthToken,
    slackChannelId,
    name,
    subject,
    title,
    description,
    agent_name,
    contact_info,
    ticket_url,
  }) {
    try{
      console.log("Slack auth token",slackAuthToken);
    const web = new WebClient(
      CryptoJS.AES.decrypt(slackAuthToken, process.env.ENCRYPTION_KEY).toString(
        CryptoJS.enc.Utf8
      )
    );
    console.log("Slack auth token decrypted",CryptoJS.AES.decrypt(slackAuthToken, process.env.ENCRYPTION_KEY).toString(
      CryptoJS.enc.Utf8
    ));
    var message = JSON.stringify(blockPayload["blocks"]);

    message = message.replace("{aj_user_name}", name);
    message = message.replace("{aj_aget_name}", agent_name);
    message = message.replace("{aj_subject}", subject);
    message = message.replace("{aj_contact_info}", contact_info);
    message = message.replace("{aj_title}", title);
    message = message.replace("{aj_description}", description);
    console.log(message);

    const result = await web.chat.postMessage({
      blocks: message,
      channel: slackChannelId,
    });
    console.log("Slack result:",result)
  }catch(e){
    console.log("Slack error:",e);
  }
  },
};
