'use strict';

/**
 * Email Template Routes
 * API routes for email template management
 */

module.exports = {
  routes: [
    // Standard CRUD routes
    {
      method: 'GET',
      path: '/email-templates',
      handler: 'email-template.find',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/email-templates/:id',
      handler: 'email-template.findOne',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/email-templates',
      handler: 'email-template.create',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'PUT',
      path: '/email-templates/:id',
      handler: 'email-template.update',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'DELETE',
      path: '/email-templates/:id',
      handler: 'email-template.delete',
      config: {
        policies: [],
        middlewares: [],
      },
    },

    // Custom routes for template management
    {
      method: 'POST',
      path: '/email-templates/migrate',
      handler: 'email-template.migrate',
      config: {
        policies: [],
        middlewares: [],
        description: 'Migrate JSON templates to database'
      },
    },
    {
      method: 'GET',
      path: '/email-templates/name/:name',
      handler: 'email-template.getByName',
      config: {
        policies: [],
        middlewares: [],
        description: 'Get template by name'
      },
    },
    {
      method: 'GET',
      path: '/email-templates/category/:category',
      handler: 'email-template.getByCategory',
      config: {
        policies: [],
        middlewares: [],
        description: 'Get templates by category'
      },
    },
    {
      method: 'GET',
      path: '/email-templates/:id/export',
      handler: 'email-template.exportJson',
      config: {
        policies: [],
        middlewares: [],
        description: 'Export template to JSON format'
      },
    },
    {
      method: 'POST',
      path: '/email-templates/import',
      handler: 'email-template.createFromJson',
      config: {
        policies: [],
        middlewares: [],
        description: 'Create template from JSON data'
      },
    },
    {
      method: 'POST',
      path: '/email-templates/:id/test',
      handler: 'email-template.testTemplate',
      config: {
        policies: [],
        middlewares: [],
        description: 'Test template rendering with sample data'
      },
    },
    {
      method: 'GET',
      path: '/email-templates/:id/analytics',
      handler: 'email-template.getAnalytics',
      config: {
        policies: [],
        middlewares: [],
        description: 'Get template usage analytics'
      },
    },
    {
      method: 'POST',
      path: '/email-templates/:id/clone',
      handler: 'email-template.clone',
      config: {
        policies: [],
        middlewares: [],
        description: 'Clone an existing template'
      },
    }
  ],
};
