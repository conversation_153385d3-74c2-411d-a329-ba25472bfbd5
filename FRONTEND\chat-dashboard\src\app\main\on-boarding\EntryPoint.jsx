// EntryPoint.jsx
import React, { useEffect } from 'react';
import { useHistory } from 'react-router-dom';


//Experimental for later use
const EntryPoint = () => {
  const history = useHistory();

  useEffect(() => {
    const savedStep = localStorage.getItem('onboardingStep');
    if (savedStep) {
      history.replace(`/onboarding/${savedStep}`);
    } else {
      history.replace('/onboarding/1');
    }
  }, []);

  return null; // Or a loading spinner if necessary
};

export default EntryPoint;
