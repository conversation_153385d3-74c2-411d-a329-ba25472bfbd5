{"kind": "collectionType", "collectionName": "knowledgebases", "info": {"singularName": "knowledgebase", "pluralName": "knowledgebases", "displayName": "knowledgebase", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "description": {"type": "text"}, "kb_id": {"type": "string"}, "organization": {"type": "relation", "relation": "oneToOne", "target": "api::organization.organization"}, "file_trains": {"type": "relation", "relation": "oneToOne", "target": "api::file-train.file-train", "mappedBy": "knowledgebase"}, "trains": {"type": "relation", "relation": "oneToOne", "target": "api::train.train", "mappedBy": "knowledgebase"}, "default_ai_task": {"type": "relation", "relation": "oneToOne", "target": "api::ai-task.ai-task"}, "shareLink": {"type": "string"}, "type": {"type": "enumeration", "enum": ["datasource", "shopify", "assistants", "autoAgent", "multiAgent"], "default": "datasource"}, "assistant_id": {"type": "string"}, "datasource": {"type": "relation", "relation": "oneToMany", "target": "api::datasource.datasource", "mappedBy": "knowledgebase"}, "whatsapp_integration_model": {"type": "relation", "relation": "oneToOne", "target": "api::whatsapp-integration-model.whatsapp-integration-model", "mappedBy": "knowledgebase"}, "slack_integration_model": {"type": "relation", "relation": "oneToOne", "target": "api::slack-integration-model.slack-integration-model", "mappedBy": "knowledgebase"}, "file_results_models": {"type": "relation", "relation": "oneToMany", "target": "api::file-results-model.file-results-model", "mappedBy": "knowledgebase"}, "shopify_model": {"type": "relation", "relation": "oneToOne", "target": "api::shopify-model.shopify-model", "mappedBy": "knowledgebase"}, "chatbot_style": {"type": "relation", "relation": "oneToOne", "target": "api::chatbot-style.chatbot-style", "mappedBy": "knowledgebase"}, "prompt_prefix": {"type": "text"}, "public": {"type": "boolean", "default": false}, "scrumboard": {"type": "relation", "relation": "manyToOne", "target": "api::scrumboard.scrumboard", "inversedBy": "knowledgebases"}, "search_type": {"type": "string", "default": "similarity"}, "k_similarity": {"type": "integer", "default": 3}, "fetch_k": {"type": "integer", "default": 20}, "lambda_mul_mmr": {"type": "integer", "default": 60}, "similarity_score_threshold": {"type": "float", "default": 50}, "ai_model_name": {"type": "string", "default": "gpt-3.5-turbo"}, "vs_source": {"type": "string", "default": "supabase"}, "vs_table_name": {"type": "string", "default": "documents"}, "collect_leads": {"type": "enumeration", "enum": ["Always", "TriggeredWhenUnanswered", "NoLeads"], "default": "Always"}, "ai_agent": {"type": "relation", "relation": "manyToOne", "target": "api::ai-agent.ai-agent", "inversedBy": "knowledgebases"}, "enable_scrape": {"type": "boolean", "default": true}, "email_integration": {"type": "relation", "relation": "oneToOne", "target": "api::email-integration.email-integration", "mappedBy": "knowledgebase"}}}