import React, { useState, useEffect } from 'react';

//Experimental for later use
const FormStep = ({ nextStep, prevStep, updateOverallFormData, data, renderForm }) => {
  const [formState, setFormState] = useState({});

  useEffect(() => {
    // Initialize form state with data from overall form data
    setFormState(data);
  }, [data]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormState((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    updateOverallFormData(formState);
    if (nextStep) {
      nextStep();
    } else {
      // Final submission logic
      console.log('Form submitted:', overallFormData);
      // Clear saved progress
      localStorage.removeItem('onboardingData');
      localStorage.removeItem('onboardingStep');
      // Redirect to a completion page or dashboard
      history.push('/dashboard'); // Replace with your desired route
    }
  };


  return (
    <form onSubmit={handleSubmit} className="p-4">
      {renderForm({ formState, handleChange })}
      <div className="flex justify-between mt-4">
        {prevStep && (
          <button
            type="button"
            onClick={() => {
              updateOverallFormData(formState);
              prevStep();
            }}
            className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
          >
            Previous
          </button>
        )}
        <button
          className={`${
            nextStep ? 'bg-blue-500 hover:bg-blue-700' : 'bg-green-500 hover:bg-green-700'
          } text-white font-bold py-2 px-4 rounded`}
          type="submit"
        >
          {nextStep ? 'Next' : 'Submit'}
        </button>
      </div>
    </form>
  );
};

export default FormStep;
