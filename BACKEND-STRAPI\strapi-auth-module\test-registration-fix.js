/**
 * Test Registration Fix - Check if JWT token is being returned for unconfirmed users
 */

const axios = require('axios');

async function testRegistrationFix() {
  console.log('🧪 TESTING REGISTRATION FIX\n');

  const baseURL = 'http://127.0.0.1:1337';
  const timestamp = Date.now();
  const testEmail = `test${timestamp}@example.com`;
  const testUsername = `testuser${timestamp}`;
  const testPassword = 'TestPassword123!';
  
  try {
    console.log('📝 Testing User Registration...');
    console.log(`   Email: ${testEmail}`);
    console.log(`   Username: ${testUsername}`);
    
    const registerResponse = await axios.post(`${baseURL}/api/auth/local/register`, {
      username: testUsername,
      email: testEmail,
      password: testPassword,
      organization: 'individual'
    });
    
    console.log('\n📊 REGISTRATION RESPONSE:');
    console.log('Status:', registerResponse.status);
    console.log('Response Data:', JSON.stringify(registerResponse.data, null, 2));
    
    // Check if JWT token is present
    if (registerResponse.data.jwt) {
      console.log('\n❌ CRITICAL ISSUE: JWT token returned for unconfirmed user!');
      console.log('   JWT Token:', registerResponse.data.jwt.substring(0, 50) + '...');
      console.log('   This means user will be auto-logged in');
    } else {
      console.log('\n✅ GOOD: No JWT token returned');
      console.log('   User will need to confirm email first');
    }
    
    // Check user confirmation status
    if (registerResponse.data.user) {
      console.log('\n👤 USER DATA:');
      console.log('   ID:', registerResponse.data.user.id);
      console.log('   Email:', registerResponse.data.user.email);
      console.log('   Confirmed:', registerResponse.data.user.confirmed);
      console.log('   Provider:', registerResponse.data.user.provider);
      
      if (registerResponse.data.user.confirmed === false) {
        console.log('   ✅ User correctly set as unconfirmed');
      } else {
        console.log('   ❌ User incorrectly set as confirmed');
      }
    }
    
    // Test if we can access protected endpoints without JWT
    console.log('\n🔒 Testing Protected Endpoint Access...');
    
    try {
      const meResponse = await axios.get(`${baseURL}/api/users/me`);
      console.log('❌ CRITICAL: /api/users/me accessible without JWT token!');
      console.log('   This should not happen');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ GOOD: /api/users/me correctly requires authentication');
      } else {
        console.log('❌ Unexpected error:', error.response?.status, error.response?.data);
      }
    }
    
    // Test with JWT token if it was returned
    if (registerResponse.data.jwt) {
      console.log('\n🔑 Testing with JWT Token...');
      
      try {
        const meWithTokenResponse = await axios.get(`${baseURL}/api/users/me`, {
          headers: { Authorization: `Bearer ${registerResponse.data.jwt}` }
        });
        
        console.log('❌ CRITICAL: Unconfirmed user can access /api/users/me with JWT!');
        console.log('   User Data:', {
          id: meWithTokenResponse.data.id,
          email: meWithTokenResponse.data.email,
          confirmed: meWithTokenResponse.data.confirmed
        });
      } catch (error) {
        console.log('✅ GOOD: JWT token rejected for unconfirmed user');
      }
    }
    
    console.log('\n📋 SUMMARY:');
    console.log('='.repeat(50));
    
    if (!registerResponse.data.jwt && registerResponse.data.user?.confirmed === false) {
      console.log('✅ REGISTRATION FIX WORKING CORRECTLY');
      console.log('   - No JWT token returned');
      console.log('   - User set as unconfirmed');
      console.log('   - User must confirm email to get access');
    } else {
      console.log('❌ REGISTRATION FIX NOT WORKING');
      console.log('   Issues found:');
      if (registerResponse.data.jwt) {
        console.log('   - JWT token returned (should not happen)');
      }
      if (registerResponse.data.user?.confirmed !== false) {
        console.log('   - User not set as unconfirmed');
      }
    }
    
    console.log('\n🎯 EXPECTED BEHAVIOR:');
    console.log('1. Registration returns user data WITHOUT JWT token');
    console.log('2. User.confirmed should be false');
    console.log('3. User should receive confirmation email');
    console.log('4. Only after email confirmation should user get JWT token');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the Strapi server is running:');
      console.log('   npm start');
    }
  }
}

// Run the test
testRegistrationFix();
