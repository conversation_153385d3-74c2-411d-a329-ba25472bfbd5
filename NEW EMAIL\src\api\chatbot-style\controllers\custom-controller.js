"use strict";

/**
 * chatbot-style controller
 */

const { createCoreController } = require("@strapi/strapi").factories;


module.exports = createCoreController("api::chatbot-style.chatbot-style", ({ strapi }) => ({
  async getStyle(ctx) {
    try {
		const kb=await strapi.query("api::knowledgebase.knowledgebase").findOne({where:{kb_id:ctx.params.id}});
		const style = await strapi.query("api::chatbot-style.chatbot-style").findOne({where:{knowledgebase:kb.id}});
		return style??[];
	}catch(e){
		return [];
	}
}
}));