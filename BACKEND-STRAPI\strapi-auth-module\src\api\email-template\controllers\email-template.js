'use strict';

/**
 * Email Template Controller
 * Handles API endpoints for email template management
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::email-template.email-template', ({ strapi }) => ({
  /**
   * Migrate JSON templates to database
   */
  async migrate(ctx) {
    try {
      const migratedTemplates = await strapi.service('api::email-template.email-template').migrateJsonTemplates();
      
      ctx.body = {
        success: true,
        message: `Successfully migrated ${migratedTemplates.length} templates`,
        templates: migratedTemplates.map(t => ({
          id: t.id,
          name: t.name,
          category: t.category,
          provider: t.provider
        }))
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Get template by name
   */
  async getByName(ctx) {
    try {
      const { name } = ctx.params;
      const template = await strapi.service('api::email-template.email-template').getTemplateByName(name);
      
      ctx.body = {
        success: true,
        template
      };
    } catch (error) {
      ctx.status = 404;
      ctx.body = {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Get templates by category
   */
  async getByCategory(ctx) {
    try {
      const { category } = ctx.params;
      const { active = true } = ctx.query;
      
      const templates = await strapi.service('api::email-template.email-template').getTemplatesByCategory(
        category, 
        active === 'true'
      );
      
      ctx.body = {
        success: true,
        templates,
        count: templates.length
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Export template to JSON
   */
  async exportJson(ctx) {
    try {
      const { id } = ctx.params;
      const jsonTemplate = await strapi.service('api::email-template.email-template').exportToJson(id);
      
      ctx.body = {
        success: true,
        template: jsonTemplate
      };
    } catch (error) {
      ctx.status = 404;
      ctx.body = {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Create template from JSON
   */
  async createFromJson(ctx) {
    try {
      const { template: jsonData } = ctx.request.body;
      const userId = ctx.state.user?.id;
      
      const template = await strapi.service('api::email-template.email-template').createFromJson(jsonData, userId);
      
      ctx.body = {
        success: true,
        message: 'Template created successfully',
        template: {
          id: template.id,
          name: template.name,
          category: template.category,
          provider: template.provider
        }
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Test template rendering
   */
  async testTemplate(ctx) {
    try {
      const { id } = ctx.params;
      const { testData = {} } = ctx.request.body;
      
      const template = await strapi.entityService.findOne('api::email-template.email-template', id);
      
      if (!template) {
        ctx.status = 404;
        ctx.body = { success: false, error: 'Template not found' };
        return;
      }
      
      // Use preview data if no test data provided
      const renderData = Object.keys(testData).length > 0 ? testData : template.preview_data;
      
      // Simple template variable replacement for testing
      let renderedHtml = template.html_content || '';
      let renderedText = template.text_content || '';
      let renderedSubject = template.subject || '';
      
      Object.entries(renderData).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        renderedHtml = renderedHtml.replace(regex, value);
        renderedText = renderedText.replace(regex, value);
        renderedSubject = renderedSubject.replace(regex, value);
      });
      
      ctx.body = {
        success: true,
        rendered: {
          html: renderedHtml,
          text: renderedText,
          subject: renderedSubject,
          from: `${template.default_from_name} <${template.default_from_email}>`,
          testData: renderData
        }
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Get template analytics
   */
  async getAnalytics(ctx) {
    try {
      const { id } = ctx.params;
      
      const template = await strapi.entityService.findOne('api::email-template.email-template', id, {
        populate: ['email_logs']
      });
      
      if (!template) {
        ctx.status = 404;
        ctx.body = { success: false, error: 'Template not found' };
        return;
      }
      
      // Calculate analytics
      const emailLogs = template.email_logs || [];
      const analytics = {
        usage_count: template.usage_count || 0,
        last_used: template.last_used,
        total_emails: emailLogs.length,
        successful_emails: emailLogs.filter(log => ['sent', 'delivered', 'opened'].includes(log.status)).length,
        failed_emails: emailLogs.filter(log => log.status === 'failed').length,
        opened_emails: emailLogs.filter(log => log.status === 'opened').length,
        clicked_emails: emailLogs.filter(log => log.status === 'clicked').length,
        bounced_emails: emailLogs.filter(log => log.status === 'bounced').length,
        delivery_rate: emailLogs.length > 0 ? 
          (emailLogs.filter(log => ['delivered', 'opened', 'clicked'].includes(log.status)).length / emailLogs.length * 100).toFixed(2) : 0,
        open_rate: emailLogs.length > 0 ? 
          (emailLogs.filter(log => ['opened', 'clicked'].includes(log.status)).length / emailLogs.length * 100).toFixed(2) : 0
      };
      
      ctx.body = {
        success: true,
        template: {
          id: template.id,
          name: template.name,
          category: template.category
        },
        analytics
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: error.message
      };
    }
  },

  /**
   * Clone template
   */
  async clone(ctx) {
    try {
      const { id } = ctx.params;
      const { name: newName } = ctx.request.body;
      const userId = ctx.state.user?.id;
      
      const originalTemplate = await strapi.entityService.findOne('api::email-template.email-template', id);
      
      if (!originalTemplate) {
        ctx.status = 404;
        ctx.body = { success: false, error: 'Template not found' };
        return;
      }
      
      // Create cloned template
      const clonedTemplate = await strapi.entityService.create('api::email-template.email-template', {
        data: {
          name: newName || `${originalTemplate.name}-copy`,
          display_name: `${originalTemplate.display_name} (Copy)`,
          description: originalTemplate.description,
          provider: originalTemplate.provider,
          html_content: originalTemplate.html_content,
          text_content: originalTemplate.text_content,
          template_type: originalTemplate.template_type,
          category: originalTemplate.category,
          subject: originalTemplate.subject,
          default_from_email: originalTemplate.default_from_email,
          default_from_name: originalTemplate.default_from_name,
          variables: originalTemplate.variables,
          is_active: false, // Start as inactive
          scope: originalTemplate.scope,
          parent_template: id,
          created_by_user: userId,
          tags: [...(originalTemplate.tags || []), 'cloned'],
          preview_data: originalTemplate.preview_data,
          version: '1.0.0'
        }
      });
      
      ctx.body = {
        success: true,
        message: 'Template cloned successfully',
        template: {
          id: clonedTemplate.id,
          name: clonedTemplate.name,
          category: clonedTemplate.category
        }
      };
    } catch (error) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: error.message
      };
    }
  }
}));
