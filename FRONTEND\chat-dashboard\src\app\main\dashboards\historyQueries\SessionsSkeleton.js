import { Skeleton, Stack } from "@mui/material";

export default function SessionsSkeleton() {
  return (
    <Stack spacing={2} className="flex flex-col divide-y-1">
      {[...new Array(6)].map((i) => (
        <Stack key={i} className="px-12 py-10" spacing={1}>
          <Skeleton variant="circular" width={28} height={28} />
          <Stack direction={"row"} justifyContent={"space-between"} spacing={2}>
            <Skeleton variant="text" className="text-[16px] flex-1" />
            <Skeleton
              variant="circular"
              width={16}
              height={16}
              className="flex-none"
            />
          </Stack>

          <Stack direction={"row"} spacing={1}>
            <Skeleton
              variant="rounded"
              className="w-[70px] text-lg rounded-full"
            />
            <Skeleton
              variant="rounded"
              className="w-[70px] text-lg rounded-full"
            />
          </Stack>
        </Stack>
      ))}
    </Stack>
  );
}
