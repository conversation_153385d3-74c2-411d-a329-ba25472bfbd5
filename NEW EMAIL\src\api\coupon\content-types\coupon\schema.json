{"kind": "collectionType", "collectionName": "coupons", "info": {"singularName": "coupon", "pluralName": "coupons", "displayName": "Coupon", "description": "Defines a coupon offer that can be redeemed by organizations."}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"code": {"type": "string", "required": true, "unique": true, "maxLength": 50}, "description": {"type": "text"}, "feature_image_url": {"type": "text"}, "credits_awarded": {"type": "integer", "required": true, "min": 1, "default": 1}, "is_active": {"type": "boolean", "default": true, "required": true}, "valid_from": {"type": "datetime"}, "valid_until": {"type": "datetime"}, "max_redemptions_per_organization": {"type": "integer", "min": 1, "default": 1, "required": true}, "max_total_redemptions": {"type": "integer", "min": 0}, "redemption_logs": {"type": "relation", "relation": "oneToMany", "target": "api::coupon-redemption-log.coupon-redemption-log", "mappedBy": "coupon_used"}}}