const { createCoreController } = require('@strapi/strapi').factories;
const axios = require('axios');


module.exports = createCoreController('api::document.document', ({ strapi }) =>  ({
	

	async deleteDocument(ctx){
		try{
			const { document_id } = ctx.request.body;

			if (!document_id) {
				return ctx.badRequest('Missing document_id in request body');
			}

			// 1. Find the document and populate its datasource and the knowledgebase associated with that datasource
			const document = await strapi.db.query('api::document.document').findOne({
				where: { id: document_id },
				populate: {
					datasource: {        // Populate the datasource relation
						populate: {
							knowledgebase: true // Populate the knowledgebase within the datasource
						}
					}
				}
			});


			if (!document) {
				return ctx.notFound(`Document with ID ${document_id} not found.`);
			}

            // Check if datasource and knowledgebase are properly populated
            if (!document.datasource || !document.datasource.knowledgebase) {
                console.error(`Document ${document_id} found but is missing its associated datasource or knowledgebase.`);
                // Decide if you want to delete the document anyway or return an error.
                // For now, let's just delete the document without calling the external API.
                await strapi.db.query('api::document.document').delete({ where: { id: document_id } });
                return ctx.send({ message: `Document ${document_id} deleted, but datasource or knowledgebase information was missing.` });
            }

			const kb = document.datasource.knowledgebase; // Access knowledgebase through datasource
			const kb_id = kb.kb_id; // The string kb_id
            const org_id = ctx.state.user?.organization?.id; // Get org_id from user state
            const index_name = kb.vs_table_name; // Get index_name from the knowledgebase

			if (!org_id) {
                 console.error("Organization ID not found in user state.");
                 return ctx.internalServerError("Could not determine organization ID.");
            }
			if (!kb_id || !index_name) {
				console.error(`Missing kb_id or vs_table_name for knowledgebase associated with document ${document_id}.`);
				return ctx.internalServerError('Knowledgebase information incomplete.');
			}


			// 2. Find all chunks associated with the document and get embedding_ids
			const chunks = await strapi.db.query('api::chunk.chunk').findMany({
				where: { document: document_id },
				select: ['embedding_id'] // Only select the embedding_id
			});

			const embedding_ids = chunks.map(chunk => chunk.embedding_id).filter(id => id != null); // Extract non-null IDs


			// 3. Call external API if there are embeddings to delete
            let externalApiSuccess = true; // Assume success if no embeddings
			if (embedding_ids.length > 0) {
				console.log(`Attempting to delete ${embedding_ids.length} embeddings for document ${document_id} from KB ${kb_id}`);
                const apiUrl = `${process.env.TALKBASE_BASE_URL}/v4/delete_embeddings`;
                const params = { org_id, kb_id, index_name };
                const body = { embedding_ids };

				try {
					const response = await axios.delete(apiUrl, { params: params, data: body }); // Pass body in data for DELETE
                    console.log(`External API call successful for document ${document_id}:`, response.data);
				} catch (axiosError) {
                    externalApiSuccess = false;
					console.error(`Error calling external API for document ${document_id}:`, axiosError.response?.data || axiosError.message);
                    // Decide if you should stop or continue to delete the Strapi document
					// For now, let's return an error and NOT delete the document
                    return ctx.internalServerError(`Failed to delete embeddings externally: ${axiosError.message}`);
				}
			} else {
                console.log(`No embeddings found for document ${document_id}, skipping external API call.`);
            }

            // 4. Delete the document itself from Strapi if external call was successful (or skipped)
            if (externalApiSuccess) {
                await strapi.db.query('api::document.document').delete({ where: { id: document_id } });
                console.log(`Successfully deleted document ${document_id} from Strapi.`);
                return ctx.send({ message: `Document ${document_id} and associated embeddings successfully deleted.` });
            } else {
				// This part might be redundant if we return early on axiosError, but included for clarity
				console.log(`Skipping deletion of document ${document_id} due to external API failure.`);
				// Error was already returned
			}


		} catch (err) {
			console.error("Error in deleteDocument controller:", err);
			// Add a catch block to handle any unexpected errors
			return ctx.internalServerError("An unexpected error occurred while deleting the document.");
		}
	}

	
}) );
