import React from 'react';

/**
 * Renders a section of the assessment report with a title and content
 */
const ReportSection = ({ title, children, className = '' }) => {
  if (!children) return null;
  
  return (
    <div className={`report-section ${className}`}>
      <h3 className="report-section-title">{title}</h3>
      <div className="report-section-content">
        {children}
      </div>
    </div>
  );
};

/**
 * Renders a list of items with bullet points
 */
const BulletList = ({ items }) => {
  if (!items || items.length === 0) return null;
  
  return (
    <ul className="bullet-list">
      {items.map((item, index) => (
        <li key={index}>{item}</li>
      ))}
    </ul>
  );
};

/**
 * Renders a simple key-value pair
 */
const KeyValue = ({ label, value }) => {
  if (!value) return null;
  
  return (
    <div className="key-value">
      <span className="key">{label}:</span>
      <span className="value">{value}</span>
    </div>
  );
};

/**
 * Renders the phase summaries by parsing JSON strings
 */
const PhaseSummaries = ({ phaseSummaries }) => {
  if (!phaseSummaries) return null;
  
  // Convert phase summaries from JSON strings to objects
  const parsedPhases = {};
  
  try {
    Object.entries(phaseSummaries).forEach(([key, value]) => {
      try {
        parsedPhases[key] = JSON.parse(value);
      } catch (e) {
        console.error(`Failed to parse phase summary for ${key}:`, e);
      }
    });
  } catch (e) {
    console.error('Failed to process phase summaries:', e);
    return null;
  }
  
  if (Object.keys(parsedPhases).length === 0) return null;
  
  return (
    <div className="phase-summaries">
      {Object.entries(parsedPhases).map(([phaseName, phaseData]) => (
        <div key={phaseName} className="phase-summary">
          <h4 className="phase-name">{phaseName.replace(/_/g, ' ')}</h4>
          
          {phaseData.detailed_assessment && (
            <p className="detailed-assessment">{phaseData.detailed_assessment}</p>
          )}
          
          {phaseData.strengths && phaseData.strengths.length > 0 && (
            <div className="phase-strengths">
              <h5>Strengths</h5>
              <BulletList items={phaseData.strengths} />
            </div>
          )}
          
          {phaseData.development_areas && phaseData.development_areas.length > 0 && (
            <div className="phase-development">
              <h5>Areas for Development</h5>
              <BulletList items={phaseData.development_areas} />
            </div>
          )}
          
          {phaseData.key_insights && phaseData.key_insights.length > 0 && (
            <div className="phase-insights">
              <h5>Key Insights</h5>
              <BulletList items={phaseData.key_insights} />
            </div>
          )}
          
          {phaseData.overall_recommendation && (
            <div className="phase-recommendation">
              <h5>Recommendation</h5>
              <p>{phaseData.overall_recommendation}</p>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

/**
 * Assessment Report Component
 * Displays the assessment report data from the API response
 */
const AssessmentReport = ({ reportData }) => {
  if (!reportData) return <div className="assessment-report-empty">No assessment data available</div>;
  
  return (
    <div className="assessment-report">
      <div className="report-header">
        <h2>Assessment Report</h2>
        {reportData.report_created_at && (
          <div className="report-timestamp">
            Generated on: {new Date(reportData.report_created_at).toLocaleString()}
          </div>
        )}
      </div>
      
      <div className="report-overview">
        <KeyValue label="Candidate" value={reportData.candidate_name} />
        <KeyValue label="Current Role" value={reportData.candidate_current_role} />
        <KeyValue label="Target Role" value={reportData.candidate_target_role} />
        <KeyValue label="Years of Experience" value={reportData.candidate_years_experience} />
        <KeyValue label="Assessment Type" value={reportData.assessment_type} />
      </div>
      
      <ReportSection title="Key Strengths">
        <BulletList items={reportData.key_strengths} />
      </ReportSection>
      
      <ReportSection title="Development Areas">
        <BulletList items={reportData.key_development_areas} />
      </ReportSection>
      
      <ReportSection title="Phase Summaries">
        <PhaseSummaries phaseSummaries={reportData.phase_summaries} />
      </ReportSection>
      
      <ReportSection title="Risk Factors">
        <BulletList items={reportData.identified_risks} />
      </ReportSection>
      
      <ReportSection title="Risk Mitigations">
        <BulletList items={reportData.risk_mitigations} />
      </ReportSection>
      
      <ReportSection title="Recommended Next Steps">
        <BulletList items={reportData.recommended_next_steps} />
      </ReportSection>
      
      <ReportSection title="Overall Evaluation">
        <div className="overall-evaluation">
          {reportData.overall_evaluation || "Evaluation in progress"}
        </div>
      </ReportSection>
      
      {reportData.overall_recommendation_text && (
        <ReportSection title="Recommendation">
          <div className="overall-recommendation">
            {reportData.overall_recommendation_text}
          </div>
        </ReportSection>
      )}
      
      <div className="report-footer">
        <div className="report-id">Report ID: {reportData.report_id || "Not available"}</div>
        <div className="report-version">Version: {reportData.report_version || "1.0"}</div>
      </div>
    </div>
  );
};

export default AssessmentReport; 