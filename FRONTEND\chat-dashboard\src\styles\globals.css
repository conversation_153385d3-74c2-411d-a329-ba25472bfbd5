@tailwind base;
@tailwind components;
@tailwind utilities;

/* Force Inter + Helvetica font everywhere */
html, body, * {
  font-family: "Inter", Helvetica, Arial, sans-serif !important;
}

/**
 * Custom base styles
 */

* {
  /* Text rendering */
  text-rendering: optimizeLegibility;
  -o-text-rendering: optimizeLegibility;
  -ms-text-rendering: optimizeLegibility;
  -moz-text-rendering: optimizeLegibility;
  -webkit-text-rendering: optimizeLegibility;
  -webkit-tap-highlight-color: transparent;
}

* :focus {
  outline: none !important;
}



html {
  font-size: 62.5%;
  font-family: "Inter var";
  background-color: #121212;
}

body {
  font-size: 14px;
  line-height: 1.4;
  overflow-x: hidden;
  font-feature-settings: "salt";
  font-family: "Inter var";
}

.logo-heading {
  font-size: 18px;
  margin-left: 10px;
  font-weight: 600;
  background: -webkit-linear-gradient(
    168.48deg,
    #713ac3 -2.01%,
    #1aa4cf 104.47%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
html,
body {
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
}

html,
body {
  display: flex;
  flex-direction: column;
  position: relative;
  margin: 0;
  min-height: 100%;
  width: 100%;
  flex: 1 1 auto;
}

.FusePageSimple-contentWrapper {
  background-color: white; /* Body content change */
}

#root {
  display: flex;
  flex: 1 1 auto;
  width: 100%;
  height: 100%;
}

h1,
.h1 {
  font-size: 24px;
}

h2,
.h2 {
  font-size: 20px;
}

h3,
.h3 {
  font-size: 16px;
}

h4,
.h4 {
  font-size: 15px;
}

h5,
.h5 {
  font-size: 13px;
}

h6,
.h6 {
  font-size: 12px;
}

.ps > .ps__rail-y,
.ps > .ps__rail-x {
  z-index: 99;
}

a[role="button"] {
  text-decoration: none;
}

[role="tooltip"] {
  z-index: 9999;
}

.MuiModal-root {
  z-index: 9999;
}

/* Medium Devices, Desktops Only */
@media only screen and (min-width: 992px) {
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: rgba(0, 0, 0, 0);
  }

  ::-webkit-scrollbar:hover {
    width: 8px;
    height: 8px;
    background-color: rgba(0, 0, 0, 0.06);
  }

  ::-webkit-scrollbar-thumb {
    border: 2px solid transparent;
    border-radius: 20px;
  }

  ::-webkit-scrollbar-thumb:active {
    border-radius: 20px;
  }
}

form label {
  z-index: 99;
}

body.no-animate *,
body.no-animate *::before,
body.no-animate *::after {
  transition: none !important;
  animation: none !important;
}

button:focus {
  outline: none;
}

/* Removes webkit's autofill backgorund color */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  transitiondelay: 9999s;
  transitionproperty: background-color, color;
}

:focus {
  outline-color: transparent;
}

/*fullcalendar Fix*/
.fc-scrollgrid-section-liquid {
  height: 1px !important;
}

.widget-title {
  font-size: 22px !important;
  font-weight: "medium";
  color: #242810;
}

.widget-label-tiny {
  font-size: 10px;
  font-weight: "medium";
  color: #667085;
}

.widget-label-small {
  font-size: 12px;
  font-weight: "regular";
  color: #667085;
}

.widget-label-regular {
  font-size: 14px;
  font-weight: "regular";
  color: #667085;
}

.widget-label-large {
  font-size: 20px;
  font-weight: 500 !important;
  color: #101828;
}

.widget-label-medium {
  font-size: 18px;
  font-weight: 500 !important;
  color: #101828;
}

.page-heading {
  font-size: 24px;
  font-weight: "medium";
  color: #101828;
}

.page-background {
  background-color: #fcfcfd;
}

.green-icon-pill {
  padding: 0.25rem; /* equivalent to p-1 in Tailwind */
  background-color: #ecfdf3; /* equivalent to bg-gray-200 in Tailwind */
  border-radius: 9999px; /* equivalent to rounded-full in Tailwind */
}

.green-pill-fg-text {
  color: #027a48;
  font-weight: 500;
  font-size: 14px;
}

.dashboard-title {
  font-size: 18px !important;
  font-weight: "medium" !important;
}

.dashboard-shadow {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.25s ease-in-out, transform 0.25s ease-in-out;
  border-radius: 8px;
}

.dashboard-shadow:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.light-purple-square-container {
  background-color: rgba(241, 235, 248, 0.97);
  width: 48px; /* or whatever size you want for the square */
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  overflow: hidden; /* to ensure the image also gets the corner radius */
}

.styled-image {
  max-width: 100%;
  height: auto;
  display: block; /* to remove any space below the image */
}

.MuiOutlinedInput-notchedOutline {
  border-radius: 8px !important;
}

.shine-button {
  position: relative;
  overflow: hidden;
  transform: translate3d(0, 0, 0); /* For better GPU performance */
}

.shine-button::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -70%;
  width: 200%;
  height: 200%;
  z-index: 1;
  opacity: 0;
  transform: rotate(30deg);
  pointer-events: none; /* Ensure the user can click the button and not the pseudo-element */
  background: linear-gradient(
    to right,
    transparent 10%,
    rgba(255, 255, 255, 0.9) 45%,
    transparent 90%
  );
  transition: all 0.6s ease-out;
}
.MuiButton-root:hover {
  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2),
    0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);
}

.shine-button:hover::after {
  opacity: 0.9; /* More pronounced shine */
  left: 120%; /* Move the shine effect farther to the right on hover */
  transition: all 0.6s ease-out;
}

.shine-button:active::after {
  opacity: 1; /* Maximum prominence on click */
  left: 120%; /* Move the shine effect farther to the right on click */
  transition: all 0.4s ease-out; /* Slightly faster transition for click */
}

.press-button {
  position: relative;
  background-color: #7f56d9; /* Change to desired button color */
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  color: white;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); /* Initial shadow to give it a raised effect */
  text-align: center;
  outline: none;

}

.press-button:disabled,
.press-button[disabled] {
  border: 1px solid #999999;
  background-color: #cccccc;
  color: #666666;
}

.press-button:active {
  transform: translateY(4px); /* Pushes the button down */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* Reduces the shadow to make it look pressed */
}

.press-button-border {
  position: relative;
  background-color: #7f56d9; /* Change to desired button color */
  padding: 10px 20px;
  border-radius: 5px;
  color: white;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); /* Initial shadow to give it a raised effect */
  text-align: center;
  outline: none;
}

.press-button-border:disabled,
.press-button-border[disabled] {
  border: 1px solid #999999;
  background-color: #cccccc;
  color: #666666;
}

.press-button-border:active {
  transform: translateY(4px); /* Pushes the button down */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* Reduces the shadow to make it look pressed */
}

.customSelect .MuiOutlinedInput-root {
  border-radius: 1px !important;
}

.customSelect .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline {
  border-radius: 1px !important;
}

.component-title {
  font-size: 14px !important;
  font-weight: "regular";
  color: #242810;
}

.divider-style {
  color: #e0e0e0;
  width: 60%;
}

.settings-text-field {
  border: 1px solid #ccc;
  border-radius: 6px;
  width: 60%;
  padding: 10px;
  transition: border-color 0.3s, box-shadow 0.3s; /* Added box-shadow transition */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative; /* Added box-shadow */
}

.settings-text-field:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2); /* Added focus box-shadow */
}

.settings-text-field:hover::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 8px;
  border: 2px solid rgba(0, 123, 255, 0.5);
}

.settings-text-field::after {
  content: "Hint text";
  position: absolute;
  top: calc(100% + 5px);
  left: 0;
  color: #aaa;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s;
}

.settings-text-field::placeholder {
  font-size: 14px !important;
  color: #667085 !important;
  font-weight: 300;
  padding-right: 4px;
}
.settings-text-field:focus::after {
  opacity: 1;
}

.settings-text-field.valid {
  border-color: #28a745;
}

.settings-text-field.invalid {
  border-color: #dc3545;
}

.settings-text-field:invalid {
  outline: none;
  border-color: #dc3545;
}

.settings-text-field + .error-message {
  display: none;
  color: #dc3545;
  margin-top: 5px;
}

.settings-text-field:invalid + .error-message {
  display: block;
}

.settigs-grid-container {
  background-color: white;
  display: flex;
  flex-direction: column;
  align-items: left;
  text-align: left;
  width: 100%;
  padding: 24px;
  margin-top: 32px;
  border-radius: 8px;
  border: solid 1px #e0e0e0;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0); /* subtle shadow */
  transition: box-shadow 0.3s; /* transition for micro interaction */
}

.settigs-grid-container:hover {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); /* amplified shadow on hover */
}

.settings-grid-item {
  margin-bottom: 10px;
  width: 100%;
}

.purple-radio {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid #7f56d9;
  border-radius: 50%;
  background-color: rgba(28, 128, 0, 0);
  outline: none;
  cursor: pointer;
  margin-top: 16px;
  padding: 8px; /* Add padding to create a gap */
  box-sizing: border-box;
  position: relative;
  transition: transform 0.2s; /* Add a transition effect for scaling */
  position: relative; /* Adding this to ensure pseudo-element is positioned relative to the input */
}

.purple-radio::before {
  content: "";
  display: block;
  position: absolute;
  top: -50%; /* Starting position of the shine effect */
  left: -50%;
  width: 200%; /* Width to cover the element and overflow */
  height: 200%; /* Height to cover the element and overflow */
  background: rgba(
    255,
    255,
    255,
    0.2
  ); /* Semi-transparent white to simulate shine */
  transform: rotate(-30deg) translate(-50%, -80%); /* Rotate and position the shine */
  transition: transform 0.7s ease; /* Smooth transition of shine moving */
  pointer-events: none; /* Ensures the shine doesn't interfere with clicks */
  opacity: 0; /* Hidden by default */
  z-index: 1; /* Make sure the shine is above the radio button */
}

.purple-radio:hover::before {
  transform: rotate(-30deg) translate(-50%, 200%); /* Move the shine downwards on hover */
  opacity: 1; /* Show the shine on hover */
}

.purple-radio::after {
  content: "";
  display: none; /* Hide the pseudo-element by default */
  width: 70%;
  height: 70%;
  background-color: #7f56d9;
  border-radius: 50%;
  box-sizing: border-box;
  margin: auto; /* Center the pseudo-element */
  margin-top: 0px;
  position: absolute; /* Use absolute positioning for the pseudo-element */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: transform 0.3s, opacity 0.3s; /* Transition for scaling and fading in */
  transform: translate(-50%, -50%) scale(0); /* Start the inner circle as scaled down */
  opacity: 0; /* Start with the inner circle hidden */ /* Center the pseudo-element within the input */
}

.purple-radio:checked::after {
  display: block;
  transform: translate(-50%, -50%) scale(1); /* Scale the inner circle to its normal size */
  opacity: 1; /* Make the inner circle fully visible */ /* Show the pseudo-element when radio is checked */
}

.purple-radio:hover {
  transform: scale(1.1); /* Slightly scale up the radio button on hover */
  border-color: #6a29f4; /* Slightly different shade of purple on hover */
  background-color: rgba(155, 75, 209, 0.1);
  transform: translate(-5%, -5%) scale(1); /* Scale the inner circle to its normal size */
  opacity: 1; /* Slight purple tint as background */
}

.apexcharts-yaxis-label tspan, .apexcharts-xaxis-label tspan {
  fill: #717886;
  font-size: 12px;
  font-weight: 300;
  }

  .clamp-3-lines{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    -webkit-box-pack: end;
    padding-top: 1em;
  }
  .MuiClockNumber-root.Mui-selected {
    color: white !important;
  }

  .MuiPickersDay-root.Mui-selected {
    color: white !important;
  }
  .MuiIconButton-root.MuiClock-amButton {
    color: gray !important;
  }
  .MuiIconButton-root.MuiClock-pmButton {
    color: gray !important;
  }
  .ai-assesment .MuiFilledInput-root.Mui-disabled{
    background-color: #FFFBFF;
    color: black;
  }
  .ai-assesment .MuiFilledInput-input.Mui-disabled{
    color: black;
    -webkit-text-fill-color:black;
  }


  @keyframes shimmer {
    0% {
      background-position: -150%;
    }
    100% {
      background-position: 150%;
    }
  }

  .shine-effects::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      to right,
      transparent 0%,
      rgba(214, 32, 32, 0.8) 50%,
      transparent 100%
    );
    animation: shimmer 2s infinite;
  }

  .gradient-text {
    background: linear-gradient(90deg, #0b0b0c, #7F56D9, #0b0b0c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

