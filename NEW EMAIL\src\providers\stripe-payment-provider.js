const stripe = require("stripe")(process.env.STRIPE_SECRETE_KEY);
const _ = require("lodash");
const strapiPaymentHandlers = require("./strapi-payment-handlers");

module.exports = {
  async createCustomer(email) {
    try {
      const customer = await stripe.customers.create({
        email: email,
      });
      console.log("Customer created", customer);
      const updatedOrganization =
        await strapiPaymentHandlers.updateOrgWithStripeCustomerId(
          email,
          customer.id
        );
      // const planId = "prod_RJ0flIaj4wdZXQ";
      // const price = await stripe.prices.list({
      //   product: planId,
      //   active: true,
      // });

      // if (price.data.length === 0) {
      //   throw new Error("No active price found for the product.");
      // }

      // const subscription = await stripe.subscriptions.create({
      //   customer: customer.id, // Use the organization ID as the customer ID
      //   items: [{ price: price.data[0].id }], // Use the price ID here
      //   trial_end: Math.floor(Date.now() / 1000) + 14 * 24 * 60 * 60, // 14 days trial
      // });
      // console.log("Subscription created", subscription);
      return updatedOrganization;
    } catch (error) {
      console.error("Error creating customer:", error);
      throw error;
    }
  },

  async initiateCheckoutSession(ctx, plan, code) {
    try {
      console.log("in initiateCheckoutSession", plan, code);
      const prices = await stripe.prices.list({
        product: plan.stripe_prod_id,
        active: true,
      });

      if (prices.data.length <= 0) {
        throw new Error("No active prices found for the plan");
      }

      let customer = await stripe.customers.list({
        email: ctx.state.user.email,
        limit: 1,
      });

      if (customer.data.length <= 0) {
        customer = await this.createCustomer(ctx.state.user.email);
      }

      sessiondata = {
        mode: "subscription",
        success_url: `${process.env.DASHBOARD_URL}/pages/payment/success`,
        cancel_url: `${process.env.DASHBOARD_URL}/pages/payment/failed`,
        customer: customer.data[0].id,
        metadata: { organization: ctx.state.user.organization.id },
        line_items: [{ price: prices.data[0].id, quantity: 1 }],
        ...(
          (code === process.env.TRIAL_CODE && plan.stripe_prod_id === process.env.ALLOWED_TRIAL_PLAN) ||
          (code === process.env.TEST_TRIAL_CODE && plan.stripe_prod_id === process.env.ALLOWED_TRIAL_PLAN)
        ? {
          subscription_data: {
            ...(code === process.env.TRIAL_CODE ? {
              trial_end: Math.floor(Date.now() / 1000) + 14 * 24 * 60 * 60 // 14 days trial
            } : {}),
            ...(code === process.env.TEST_TRIAL_CODE ? {
              trial_end: Math.floor(Date.now() / 1000) + 3 * 24 * 60 * 60 // 3 days trial
            } : {})
          }
        } : {})
      };

      console.log("Session data", sessiondata);

      const session = await stripe.checkout.sessions.create(sessiondata);

      return { reason: "create", stripeSession: session };
    } catch (error) {
      console.error("Error initiating checkout session:", error);
      throw error;
    }
  },

  async initiateSubscriptionUpdate(ctx, subscriptionId, newPriceId) {
    try {
      console.log("***!!Initiating subscription update***");
      console.log("Subscription ID", subscriptionId);
      console.log("New Price ID", newPriceId);
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);
      if (!subscription) {
        throw new Error("No existing Subscription not found.");
      } else {
        console.log("****Subscription", subscription);
      }
      let customer = await stripe.customers.list({
        email: ctx.state.user.email,
        limit: 1,
      });
      if (customer.data.length <= 0) {
        throw new Error("Customer not found.");
      }
      const currentPlan = await strapi.query("api::plan.plan").findOne({
        where: {
          id: newPriceId,
        },
      });
      if (!currentPlan) {
        throw new Error("Plan not found.");
      }

      const currentSubscriptionOrder = await strapi
        .query("api::subscription-order.subscription-order")
        .findOne({
          where: {
            subscriptionId: subscriptionId,
          },
        });
      console.log("Current Subscription Order", currentSubscriptionOrder);
      var session;

      if (currentSubscriptionOrder.status === "trialing") {
        console.log("Current Subscription Order is trialing. Updatiing to new plan");
        const prices = await stripe.prices.list({
          product: currentPlan.stripe_prod_id,
          active: true,
        });
        if (!prices) {
          throw new Error("No prices found for the plan");
        }
        console.log("Prices", prices);
        if (prices.data.length <= 0) {
          throw new Error("No active prices found for the plan");
        }
        // const updatedSubscription = await stripe.subscriptions.update(
        //   subscriptionId,
        //   {
        //     items: [
        //       {
        //         id: subscription.items.data[0].id,
        //         price: prices.data[0].id,
        //       },
        //     ],
        //     trial_end: 'now',
        //     proration_behavior: 'create_prorations',
        //   }
        // );
        session = {
          mode: "subscription",
          customer: customer.data[0].id,
          line_items: [
            {
              price: prices.data[0].id,
              quantity: 1,
            },
          ],
          subscription_data: {
            metadata: {
              original_subscription_id: subscriptionId,
            },
            trial_end: Math.floor(Date.now() / 1000) + 2 * 24 * 60 * 60, // End trial immediately (1 second in the future)

          },
          success_url: `${process.env.DASHBOARD_URL}/pages/payment/success`,
          cancel_url: `${process.env.DASHBOARD_URL}/pages/payment/failed`,
        };
        const updatedSubscription = await stripe.checkout.sessions.create(
          session
        );

        return { reason: "updateFromTrial", stripeSession: updatedSubscription };
      }
      else
        console.log("***!!Initiating change of plan***");
        console.log("Subscription ID", subscriptionId);
        console.log("New Price ID", newPriceId);
        const plan = await strapi.query("api::plan.plan").findOne({
          where: {
            id: newPriceId,
          },
        });
        if (!plan) {
          throw new Error("Plan not found.");
        }
        const prices = await stripe.prices.list({
          product: plan.stripe_prod_id,
          active: true,
        });
        if (!prices) {
          throw new Error("No prices found for the plan");
        }
        console.log("Prices", prices);
        if (prices.data.length <= 0) {
          throw new Error("No active prices found for the plan");
        }
        // Ensure there are items and prices available
        if (subscription.items.data.length === 0) {
          throw new Error("No subscription items found.");
        }
        if (prices.data.length === 0) {
          throw new Error("No active prices found for the plan.");
        }

        session = {
          mode: "subscription",
          customer: customer.data[0].id,
          line_items: [
            {
              price: prices.data[0].id,
              quantity: 1,
            },
          ],
          subscription_data: {
            metadata: {
              original_subscription_id: subscriptionId,
            },
            proration_behavior: 'create_prorations',
            billing_cycle_anchor: Math.floor(subscription.current_period_end) + (1 * 60),

          },
          success_url: `${process.env.DASHBOARD_URL}/pages/payment/success`,
          cancel_url: `${process.env.DASHBOARD_URL}/pages/payment/failed`,
        };
        const updatedSubscription = await stripe.checkout.sessions.create(
          session
        );

        return { reason: "update", stripeSession: updatedSubscription };

    } catch (error) {
      console.error("Error initiating subscription update:", error);
      throw error;
    }
  },

  async handleSubscriptionCreated({
    ctx,
    customerId,
    subscriptionId,
    isTrial = false,
    trialStart,
    trialEnd,
    startDate,
    endDate,
    product_id,
  }) {
    try {
      // console.log("in handleSubscriptionCreated");
      // console.log("Subscription ID", subscriptionId);
      // const customer = await stripe.customers.retrieve(customerId);
      // if (!customer) {
      //   throw new Error("Customer not found.");
      // }
      // console.log("Trial subscription created", isTrial);
      // console.log("Trial start", trialStart);
      // console.log("Trial end", trialEnd);
      // const plan = await strapi.query("api::plan.plan").findOne({
      //   where: {
      //     stripe_prod_id: product_id,
      //   },
      // });
      // if (!plan) {
      //   throw new Error("Plan not found.");
      // }

      // const subscriptionOrder = await strapi
      //   .query("api::subscription-order.subscription-order")
      //   .findOne({
      //     where: {
      //       subscriptionId: subscriptionId,
      //     },
      //   });

      // if (!subscriptionOrder || !subscriptionOrder?.isPaid) {
      //   console.log("Creating new subscription order in handleSubscriptionCreated");
      //   const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId);
      //   await strapiPaymentHandlers.createSubscriptionOrder({
      //     ctx: ctx,
      //     email: customer.email,
      //     plan: plan,
      //     subscriptionId: subscriptionId,
      //     status: isTrial ? "trialing" : "pending",
      //     isTrial: isTrial,
      //     trialStart: trialStart,
      //     trialEnd: trialEnd,
      //     startDate: startDate,
      //     endDate: endDate,
      //     isPaid: true,
      //     plantype: stripeSubscription.plan.interval,
      //   });
      // }
    } catch (error) {
      console.error("Error handling subscription created:", error);
      throw error;
    }
  },

  async handlePaymentSuccess({
    ctx,
    subscriptionId,
    product_id,
    customerId,
    invoice_url,
    start_date,
    end_date,
  }) {
    try {
      console.log("in handlePaymentSuccess with product_id", product_id);
      console.log("*****!subscription id", subscriptionId);
      const customer = await stripe.customers.retrieve(customerId);
      if (!customer) {
        throw new Error("Customer not found.");
      }


        console.log("Subscription order not found. Creating new subscription order.");
        const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId);
        if (stripeSubscription !== null) {
          const plan = await strapi.query("api::plan.plan").findOne({
            where: {
              stripe_prod_id: product_id,
            },
          });
          subscriptionOrder = await strapiPaymentHandlers.createSubscriptionOrder({
            ctx: ctx,
            email: customer.email,
            plan: plan,
            subscriptionId: subscriptionId,
            status: stripeSubscription.status,
            isTrial: false,
            startDate: start_date,
            endDate: end_date,
            isPaid: true,
            invoiceUrl: invoice_url,
            plantype: stripeSubscription.plan.interval,
            trial_start_date: stripeSubscription?.trial_start ?? null,
            trial_end_date: stripeSubscription?.trial_end ?? null,
          });


      }

      await strapiPaymentHandlers.updateSubscriptionOrder({
        ctx,
        email: customer.email,
        status: subscriptionOrder.status === "trialing" ? "trialing" : "active",
        subscriptionId: subscriptionId,
        startDate: start_date,
        endDate: end_date,
        invoiceUrl: invoice_url,
      });
      await strapiPaymentHandlers.addMonthlyUsage({
        ctx: ctx,
        email: customer.email,
        startDate: start_date,
        endDate: end_date,
        product_id: product_id,
      });
      await strapiPaymentHandlers.addPlan({
        ctx: ctx,
        email: customer.email,
        product_id: product_id,
        isTrial: subscriptionOrder.status === "trialing" ? true : false,
      });
      await strapiPaymentHandlers.updateOrganizationSubscription({
        ctx: ctx,
        email: customer.email,
        status:
          subscriptionOrder.status === "trialing" ? "trial" : "subscribed",
      });
    } catch (error) {
      console.error("Error handling payment success:", error);
      throw error;
    }
  },

  async handlePaymentFailed({
    ctx,
    customerId,
    subscriptionId,
    start_date,
    end_date,
    invoice_url,
  }) {
    try {
      const customer = await stripe.customers.retrieve(customerId);
      if (!customer) {
        throw new Error("Customer not found.");
      }

      await strapiPaymentHandlers.updateSubscriptionOrder({
        ctx: ctx,
        email: customer.email,
        status: "failed",
        subscriptionId: subscriptionId,
        startDate: start_date,
        endDate: end_date,
        invoiceUrl: invoice_url,
        isPaid: false,
      });
    } catch (error) {
      console.error("Error handling payment failed:", error);
      throw error;
    }
  },

  async handleTrialDidEnd({ ctx, customerId, subscriptionId }) {
    try {
      const customer = await stripe.customers.retrieve(customerId);
      if (!customer) {
        throw new Error("Customer not found.");
      }
      await strapiPaymentHandlers.updateSubscriptionOrder({
        ctx: ctx,
        email: customer.email,
        status: "incomplete",
      });
    } catch (error) {
      console.error("Error handling trial will end:", error);
      throw error;
    }
  },

  async initiateSubscriptionChange({
    ctx,
    newPriceId,
    customerId,
    originalSubscriptionId,
    isTrial,
  }) {
    try {
      const customer = await stripe.customers.retrieve(customerId);
      if (!customer) {
        throw new Error("Customer not found.");
      }


      const originalSubscription = await stripe.subscriptions.retrieve(
        originalSubscriptionId
      );

      // Create a new Checkout Session for the subscription update
      const session = await stripe.checkout.sessions.create({
        mode: "subscription",
        customer: customer.id,
        line_items: [
          {
            price: newPriceId,
            quantity: 1,
          },
        ],
        subscription_data: {
          trial_from_plan: isTrial,
          metadata: {
            original_subscription_id: originalSubscription.id,
            original_subscription_item_id:
              originalSubscription.items.data[0].id,
          },
        },
        success_url: `${process.env.DASHBOARD_URL}/pages/payment/success`,
        cancel_url: `${process.env.DASHBOARD_URL}/pages/payment/failed`,
      });

      // Create a new subscription order in your database
      await strapiPaymentHandlers.createSubscriptionOrder({
        ctx: ctx,
        email: customer.email,
        plan: newPriceId, // Assuming newPriceId corresponds to the plan
        sessionId: session.id,
        status: "pending", // or any other status you want to set
        isTrial: isTrial,
        startDate: start_date,
        endDate: end_date,
      });

      // Return the session ID to the frontend
      return {
        success: true,
        stripeSession: session,
      };
    } catch (error) {
      console.error("Error initiating subscription change:", error);
      throw error;
    }
  },

  async handleSubscriptionUpdated({
    ctx,
    customerId,
    subscriptionId,
    isTrial,
    trialEnd,
    startDate,
    endDate,
    invoiceUrl,
    status,
    product_id,
  }) {
    try {
      // Retrieve the customer using the provided customerId
      const customer = await stripe.customers.retrieve(customerId);
      if (!customer) {
        throw new Error("Customer not found.");
      }
      console.log("Sub status", status);

      const user = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: { email: customer.email },
          populate: {
            organization: {
              populate: {
                plan: true,
                monthly_usages: true,
                subscription_orders: true,
              },
            },
          },
        });

      if (!user) {
        throw new Error("Organization not found.");
      }


      console.log("user.organization.plan", user.organization.plan);
      console.log("new product_id", product_id);
      console.log("current product_id", user.organization.plan.stripe_prod_id);
      if(user.organization.plan && user.organization.plan.stripe_prod_id !== product_id){
        //Plan has changed, so we need to update the plan
        console.log("Plan has changed, so we need to update the plan");

        await strapiPaymentHandlers.updateMonthlyUsage({
          ctx: ctx,
          email: customer.email,
          startDate: startDate,
          endDate: endDate,
          product_id: product_id,
        });

        await strapiPaymentHandlers.addPlan({
            ctx: ctx,
            email: customer.email,
            product_id: product_id,
            isTrial: isTrial,
        });

      }
      else{
        //Plan has not changed, so we need to update the subscription order
        console.log("Plan has not changed, so we need to update the subscription order");
        await strapiPaymentHandlers.updateSubscriptionOrder({
          ctx: ctx,
          email: customer.email,
          status: status,
          subscriptionId: subscriptionId,
          startDate: startDate,
          endDate: endDate,
          isPaid: status === "active" ? true : false,
          isTrial: isTrial,
          invoiceUrl: invoiceUrl,
        });

        if (status === "past_due" || status === "unpaid" || status === "canceled") {
          await strapiPaymentHandlers.updateOrganizationSubscription({
            ctx: ctx,
            email: customer.email,
            status: "unsubscribed",
          });
        }

      }

      return {
        success: true,
        message: "Subscription updated successfully.",
      };
    } catch (error) {
      console.error("Error handling subscription update:", error);
      throw error;
    }
  },

  async cancelSubscription( ctx ) {
    try {
      console.log("in cancelSubscription", ctx);
      const user = await strapi
      .query("plugin::users-permissions.user")
      .findOne({
        where: { email: ctx.state.user.email },
        populate: {
          organization: {
            populate: {
              plan: true,
              monthly_usages: true,
              subscription_orders: true,
            },
          },
        },
      });

      if (!user) {
        throw new Error("User not found.");
      }

      if (user.organization.subscription_orders.length == 0) {
        throw new Error("User has an active subscription.");
      }

      if (user.organization.subscription === "unsubscribed") {
        throw new Error("User has no active subscription.");
      }
      const stripeCustomer = await stripe.customers.list({
        email: user.email,
        limit: 1,
      });
      if (!stripeCustomer) {
        throw new Error("Stripe customer not found.");
      }
      const latestSubscriptionOrder = _.maxBy(
        user.organization.subscription_orders,
        "createdAt"
      );



    // Optionally, you can also delete the subscription completely
    await stripe.subscriptions.del(latestSubscriptionOrder.subscriptionId);

    return {
      success: true,
      message: "Subscription cancelled successfully.",
    };

    } catch (error) {
      console.error("Error handling subscription deleted:", error);
      throw error;
    }
  },

  async handleSubscriptionDeleted({ ctx, customerId, subscriptionId }) {
    try {
      const customer = await stripe.customers.retrieve(customerId);
      if (!customer) {
        throw new Error("Customer not found.");
      }
      await strapiPaymentHandlers.updateOrganizationSubscription({
        ctx: ctx,
        email: customer.email,
        status: "pendingUnsubscribed",
      });

    } catch (error) {
      console.error("Error handling subscription deleted:", error);
      throw error;
    }
  },
};
