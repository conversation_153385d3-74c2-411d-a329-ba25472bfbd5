{"kind": "collectionType", "collectionName": "documents", "info": {"singularName": "document", "pluralName": "documents", "displayName": "Document", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"document_name": {"type": "string", "required": true}, "document_type": {"type": "enumeration", "required": true, "enum": ["url", "pdf", "youtube"]}, "document_store_url": {"type": "text"}, "status": {"type": "enumeration", "required": true, "enum": ["none", "processing", "succeeded", "failed"], "default": "none"}, "character_count": {"type": "integer", "default": 0}, "tokens_used": {"type": "integer", "default": 0}, "vector_store_name": {"type": "string"}, "index_name": {"type": "string"}, "namespace_prefix": {"type": "string"}, "embedding_model": {"type": "string"}, "datasource": {"type": "relation", "required": true, "relation": "manyToOne", "target": "api::datasource.datasource", "inversedBy": "documents"}, "chunks": {"type": "relation", "relation": "oneToMany", "target": "api::chunk.chunk", "mappedBy": "document"}}}