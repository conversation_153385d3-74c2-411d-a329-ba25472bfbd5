// policies/FindUserValidation.js

module.exports = async (policyContext, config, { strapi }) => {
	const { PolicyError } = require("@strapi/utils").errors;
	const params = policyContext.request.body;

	let validUser= await strapi.db.query('plugin::users-permissions.user').findOne({ where: { phone: params.phone } })

	// If User does not exist then display user not found error.
	if (!validUser ) {
		strapi.log.error('User not found');
		return false;
	}

	return true;
};
