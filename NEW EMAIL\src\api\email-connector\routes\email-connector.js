'use strict';

/**
 * Email Connector Router
 * Defines routes for email operations
 */
module.exports = {
  routes: [
    // Send single email
    {
      method: 'POST',
      path: '/email/send',
      handler: 'email-connector.sendEmail',
      config: {
        policies: [
          'global::user-details-populate',
          'global::update-org-before-api-call',
        ]
      }
    },

    // Send bulk emails
    {
      method: 'POST',
      path: '/email/send-bulk',
      handler: 'email-connector.sendBulkEmails',
      config: {
        policies: [
          'global::user-details-populate',
          'global::update-org-before-api-call',
        ]
      }
    },

    // Send email using template
    {
      method: 'POST',
      path: '/email/send-template',
      handler: 'email-connector.sendTemplateEmail',
      config: {
        policies: [
          'global::user-details-populate',
          'global::update-org-before-api-call',
        ]
      }
    },

    // Test email providers
    {
      method: 'GET',
      path: '/email/test-providers',
      handler: 'email-connector.testProviders',
      config: {
        policies: [
          'global::user-details-populate',
        ]
      }
    },

    // Get available providers
    {
      method: 'GET',
      path: '/email/providers',
      handler: 'email-connector.getProviders',
      config: {
        policies: [
          'global::user-details-populate',
        ]
      }
    },

    // Get email delivery status
    {
      method: 'GET',
      path: '/email/status/:messageId',
      handler: 'email-connector.getDeliveryStatus',
      config: {
        policies: [
          'global::user-details-populate',
        ]
      }
    }
  ]
}; 