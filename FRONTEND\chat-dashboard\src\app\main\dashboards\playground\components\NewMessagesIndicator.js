import React from 'react';

/**
 * New messages indicator component
 * @param {Object} props - Component props
 * @param {Function} props.onClick - Click handler for scrolling to bottom
 */
const NewMessagesIndicator = ({ onClick }) => (
  <div className="new-messages-indicator visible" onClick={onClick}>
    New messages below
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="M7 13l5 5 5-5M7 6l5 5 5-5"/>
    </svg>
  </div>
);

export default NewMessagesIndicator; 