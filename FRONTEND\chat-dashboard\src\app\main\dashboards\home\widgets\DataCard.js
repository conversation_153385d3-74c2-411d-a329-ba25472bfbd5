import ReactApexCharts from "react-apexcharts";

export default function DataCard({
  title,
  description,
  value,
  subTitle,
  graphData,
  color = "#7F56D9",
  showButtton = false,
}) {
  const series = [
    {
      name: title,
      data: Object.values(sortByDate(graphData)),
    },
  ];

  function sortByDate(jsonObject) {
    return Object.keys(jsonObject)
      .sort()
      .reduce((acc, key) => {
        acc[key] = jsonObject[key];
        return acc;
      }, {});
  }

  const options = {
    chart: {
      type: "line",
      toolbar: {
        show: false,
      },
      offsetX: 0,
      offsetY: 0,
      parentHeightOffset: 0,
      parentWidthOffset: 0,
      sparkline: {
        enabled: true,
      },
    },
    stroke: {
      width: 0,
      curve: "smooth",
      colors: [color], // your color here
    },
    fill: {
      type: "gradient",
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 1,
        opacityTo: 0, // Set the transparency at the bottom to full transparent
        stops: [0, 80, 100],
        colorStops: [
          {
            offset: 0,
            color: color, // purple color
            opacity: 1.0,
          },
          {
            offset: 80,
            color: color,
            opacity: 0.8,
          },
          {
            offset: 100,
            color: color,
            opacity: 0.7,
          },
        ],
      },
      colors: ["#6200ea"], // your base color (purple)
    },

    xaxis: {
      categories: Object.keys(graphData),
      labels: {
        show: false,
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      labels: {
        show: false,
      },
    },
    grid: {
      show: false,
    },
    tooltip: {
      enabled: true,
    },
    markers: {
      size: 0,
      colors: ["#6200ea"], // your color here
    },
  };
  return (
    <div className="bg-white border-1 p-16 pt-20 mr-24 w-full rounded-lg shadow-base transform hover:-translate-y-0.5 hover:shadow-xl">
      <div className="flex justify-between items-center">
        <div className="text-2xl font-bold text-black">{title}</div>
      </div>
      <span className="text-sm font-regular mt-4 text-gray-700">
        {description}
      </span>
      <div className="text-4xl font-bold mt-4 inline-block w-full">
        <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#A89EF2] to-[#7C53FE]" style={{
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
        }}>
          {value}
        </span>
      </div>

      {/* <div className="rounded-lg">
        <ReactApexCharts
          options={options}
          series={series}
          type="area"
          height={25}
        />
      </div> */}

    </div>
  );
}
