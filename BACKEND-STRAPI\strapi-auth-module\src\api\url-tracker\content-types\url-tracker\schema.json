{"kind": "collectionType", "collectionName": "url_trackers", "info": {"singularName": "url-tracker", "pluralName": "url-trackers", "displayName": "URL Tracker", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"url": {"type": "string"}, "tracking_id": {"type": "uid"}, "query_id": {"type": "relation", "relation": "manyToOne", "target": "api::answer.answer", "inversedBy": "url_trackers"}}}