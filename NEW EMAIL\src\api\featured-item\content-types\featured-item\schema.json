{"kind": "collectionType", "collectionName": "featured_items", "info": {"singularName": "featured-item", "pluralName": "featured-items", "displayName": "Featured Item", "description": "Manages featured content like playlists, podcasts, tracks, and announcements."}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "description": {"type": "text"}, "type": {"type": "enumeration", "enum": ["Playlist", "PodcastShow", "Track", "Announcement"], "required": true}, "cover_image": {"type": "media", "multiple": false, "required": true, "allowedTypes": ["images"]}, "pill_text": {"type": "string"}, "call_to_action_text": {"type": "string", "default": "Learn More"}, "navigation_target_playlist": {"type": "relation", "relation": "oneToOne", "target": "api::playlist.playlist"}, "navigation_target_podcast_show": {"type": "relation", "relation": "oneToOne", "target": "api::knowledgebase.knowledgebase"}, "navigation_target_track": {"type": "relation", "relation": "oneToOne", "target": "api::file-results-model.file-results-model"}, "internal_link_path": {"type": "string"}, "external_url": {"type": "string"}, "display_order": {"type": "integer"}, "start_date": {"type": "datetime"}, "end_date": {"type": "datetime"}}}