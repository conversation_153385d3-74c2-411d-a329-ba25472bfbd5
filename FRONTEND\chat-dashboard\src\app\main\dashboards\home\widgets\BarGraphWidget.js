import React from "react";
import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";
import ReactApexChart from "react-apexcharts";


function BarGraphWidget(props) {
  const { datasets, mainTitle } = props;

  // Calculate the date for 6 days ago
  const sixDaysAgo = new Date();
  sixDaysAgo.setDate(sixDaysAgo.getDate() - 6);

  const lastSevenDays = [];
  for (
    let i = new Date(sixDaysAgo);
    i <= new Date();
    i.setDate(i.getDate() + 1)
  ) {
    lastSevenDays.push(new Date(i).toISOString().split("T")[0]);
  }

  const processedData =
    datasets != undefined
      ? datasets.map((dataset) => {
          const counts = lastSevenDays.map((date) => dataset.data[date] || 0);
          return {
            title: dataset.title,
            counts: counts,
            color: dataset.color,
          };
        })
      : [];

  const seriesColors = processedData.map((dataset) => dataset.color);

  // Process datasets

  const categories = () => {
    const data = [];
    const inoutData = datasets[0]["data"];
    for (const [key, value] of Object.entries(inoutData)) {
      const dateParts = key.split("-");
      const year = dateParts[0];
      const month = parseInt(dateParts[1]);
      const day = parseInt(dateParts[2]);

      const formattedDate = new Date(year, month - 1, day).toLocaleString(
        "en-US",
        {
          month: "long",
          day: "numeric",
        }
      );

      data.push(formattedDate);
    }

    return data;
  };

  const chartOptions = {
    chart: {
      id: "keyMetricsChart",
      toolbar: {
        tools: {
          download: false,
          reset: false,
          selection: false,
          pan: false,
          zoom: false,
          zoomin: false,
          zoomout: false,
        },
      },
      selection: {
        enabled: false, // Set to false to disable selection
      },
      zoom: {
        enabled: false, // Disable zooming
      },
      events: {
        pan: function (event, chartContext, config) {
          return false; // Disable dragging
        },
      },
    },
    grid: {
      show: true,
      borderColor: "#F1F1F1",
      xaxis: {
        lines: {
          show: true,
       
        },
      },
      yaxis: {
        lines: {
          show: true,
        
        },
      },
      row: {
        colors: undefined,
        opacity: 0.5,
      },
      column: {
        colors: undefined,
        opacity: 0.5,
      },
      padding: {
        top: 2,
        right: 16,
        bottom: 2,
        left:16,
      },
    },

    stroke: {
      show: true,
      width: 2, // Adjust the width as needed
      curve: "smooth",
      lineCap: "butt",
      colors: seriesColors, // Add this to set the stroke colors
    },

    fill: {
      type: "gradient",
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.25, // Or adjustedOpacity as before
        opacityTo: 0,
        stops: [0, 70, 100],
        type: "vertical", // This ensures that each dataset has its gradient
        colorStops: seriesColors.map((color) => [
          // Mapping the colors for gradient
          {
            offset: 0,
            color: color,
            opacity: 0.25,
          },
          {
            offset: 70,
            color: color,
            opacity: 0.05,
          },
          {
            offset: 100,
            color: color,
            opacity: 0,
          },
        ]),
      },
      colors: ["#6200ea"], // your base color (purple)
    },
    xaxis: {
      categories: lastSevenDays.map((date) => {
        const dateParts = date.split("-");
        const year = dateParts[0];
        const month = parseInt(dateParts[1]);
        const day = parseInt(dateParts[2]);

        const formattedDate = new Date(year, month - 1, day).toLocaleString(
          "en-US",
          {
            month: "long",
            day: "numeric",
          }
        );

        return formattedDate;
      }),
      labels: {
        show: true, // default is true, but just to ensure they're visible
        style: {
          fontSize: "12px", // adjust as per your requirement
        },
      },
    },
    yaxis: {
      labels: {
        show: true, // default is true, but just to ensure they're visible
        style: {
          color: "#89cd6f",
          fontSize: "12px", // adjust as per your requirement
        },
      },
      title: {
        show: false,
      },
    },
  };

  return (
    <div className="flex bg-white border-1 w-full flex-col flex-auto p-24 rounded-lg shadow-base transform hover:-translate-y-0.5 hover:shadow-xl">
      <div className="flex flex-col sm:flex-row items-start justify-between">
        <Typography className="widget-title  font-bold leading-6 truncate">
          Key metrics
        </Typography>
      </div>
      <div className="grid grid-cols-1 grid-flow-row gap-24 w-full mt-32 sm:mt-16">
        <div className="flex flex-col flex-auto">
          {processedData && (
            <ReactApexChart
              options={chartOptions}
              series={processedData.map((dataset) => ({
                name: dataset.title,
                color: dataset.color,
                data: dataset.counts,
                type: "area",
              }))}
              height={320}
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default BarGraphWidget;
