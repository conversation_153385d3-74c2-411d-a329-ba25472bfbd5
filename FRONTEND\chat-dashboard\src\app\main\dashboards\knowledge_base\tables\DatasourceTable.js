import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import { formatDateToHumanReadable } from "src/app/utils/date-utils";
import PaginationComponent from "src/app/shared-components/pagination/PaginationComponent";
import StatusPill from "src/app/shared-components/pill/StatusPill";
import { validateUrl } from "src/app/utils/validations";
import Pill from "app/shared-components/pill/pill";
import qs from "qs";
import CTAButon from "app/shared-components/buttons/cta-button";
import { H5, Body } from "app/shared-components/text/texts";

export function DatasourceTable({
  datastoreId,
  onAddClick,
  onDeleteAllClick,
  onDocumentClick,
  isInitiallyOpen = false,
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [documents, setDocuments] = useState([]);
  const [datasourceName, setDatasourceName] = useState("Datasource");
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [lastPage, setLastPage] = useState(-1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const tableRef = useRef(null);

  const handleToggle = () => setIsOpen(!isOpen);

  const getDatasource = async (page, pageSize = itemsPerPage) => {
    try {
      setLoading(true);
      const query = {
        populate: {
          documents: {
            sort: "createdAt:asc",
            limit: pageSize,
            start: (page - 1) * pageSize,
          },
        },
      };
      const queryToString = qs.stringify(query, { encode: false });
      const datasource = await axios.get(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/datasources/${datastoreId}?${queryToString}`
      );
      setDatasourceName(datasource.data.data.attributes.name);
      const docs = datasource.data.data.attributes.documents.data;
      if (page === 1) {
        setDocuments(docs);
      } else {
        setDocuments((prev) => [...prev, ...docs]);
      }
      setLastPage(docs.length < pageSize ? page : -1);
    } catch (e) {
      console.log(e);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setDocuments([]);
    setCurrentPage(1);
    getDatasource(1, itemsPerPage);
  }, [datastoreId, itemsPerPage]);

  useEffect(() => {
    setIsOpen(isInitiallyOpen);
  }, [isInitiallyOpen]);

  useEffect(() => {
    if (tableRef.current) {
      tableRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }, [currentPage]);

  const totalPages =
    lastPage !== -1
      ? lastPage
      : documents
      ? Math.ceil(documents.length / itemsPerPage) + 1
      : 1;

  const onPageChange = (page) => {
    setCurrentPage(page);
    if (lastPage !== page && documents.length < page * itemsPerPage) {
      getDatasource(page);
    }
  };

  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(parseInt(e.target.value));
    setCurrentPage(1);
    setDocuments([]);
    setLastPage(-1);
  };

  return (
    <div
      ref={tableRef}
      className="m-5 mt-32 border border-gray-300 shadow-lg rounded-lg overflow-hidden"
    >
      <div
        className="bg-white text-black p-4 flex flex-col cursor-pointer"
        onClick={handleToggle}
      >
        <div className="flex justify-between items-center">
          <div className="m-16 flex flex-row">
            <img
              src="assets/images/datasource-icon.png"
              className="w-32 h-32 p-4 bg-[#EBEFF9] rounded-2xl"
            />
            <H5 className="ml-8 mt-6">{datasourceName}</H5>
            <div className="mx-16 mt-6">
              <Pill text={"New"} />
            </div>
          </div>
          <div className="flex mr-16 mt-4">
            <CTAButon
              text="Add Data"
              image="assets/images/add-icon.svg"
              onPress={onAddClick}
            />
          </div>
        </div>
        <Body className="mt-2 w-1/2 ml-16 mb-16 text-body-text-color">
          A datasource is where you will add your data to be used for training
          your agents. Whenever you add your data, it will be stored here in a
          datasource.
        </Body>
      </div>
      <div
        className={`transition-max-height duration-500 ease-in-out ${
          isOpen ? "max-h-screen" : "max-h-0 overflow-hidden"
        }`}
      >
        {isOpen && (
          <>
            <div className="flex justify-end items-center px-16 py-8 bg-[#f5f8fa]">
              <label className="mr-4 text-sm font-medium text-gray-700">
                Items per page:
              </label>
              <select
                value={itemsPerPage}
                onChange={handleItemsPerPageChange}
                className="p-2 border border-gray-300 rounded"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={15}>15</option>
                <option value={20}>20</option>
              </select>
            </div>
            {documents && documents.length > 0 ? (
              <>
                <div className="flex bg-[#F9FBFC] p-16 border-grey-300 border-t-1 border-b-1">
                  <div className="w-96 font-semibold p-2 pl-16">SL No</div>
                  <div className="w-1/3 font-semibold p-2">Document Name</div>
                  <div className="w-128 font-semibold p-2">Status</div>
                  <div className="flex-1 w-[90px] font-semibold p-2">Type</div>
                  <div className="flex-1 font-semibold p-2">Date Added</div>
                  <div className="flex w-[130px] font-semibold p-2">
                    Tokens Used
                  </div>
                </div>

                {/* Scrollable container */}
                <div className="max-h-[400px] overflow-y-auto custom-scrollbar">
                  {documents
                    .slice(
                      (currentPage - 1) * itemsPerPage,
                      currentPage * itemsPerPage
                    )
                    .map((_, index) => (
                      <div
                        onClick={() => {
                          onDocumentClick(
                            documents[
                              (currentPage - 1) * itemsPerPage + index
                            ].id,
                            documents[
                              (currentPage - 1) * itemsPerPage + index
                            ].attributes.document_name
                          );
                        }}
                        key={index}
                        className="flex p-4 text-md border-b bg-[#faf9fb] border-gray-200 last:border-none hover:bg-white transition-colors hover:cursor-pointer"
                      >
                        <div className="w-96 p-16 px-32 text-grey-900">
                          {(currentPage - 1) * itemsPerPage + index + 1}
                        </div>
                        {validateUrl(
                          documents[
                            (currentPage - 1) * itemsPerPage + index
                          ].attributes.document_name
                        ) ? (
                          <div className="w-1/3 p-16 text-start text-grey-800 bg-transparent">
                            <a
                              href={
                                documents[
                                  (currentPage - 1) * itemsPerPage + index
                                ].attributes.document_name
                              }
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-inherit no-underline bg-transparent text-left justify-start">
                              {
                                documents[
                                  (currentPage - 1) * itemsPerPage + index
                                ].attributes.document_name
                              }
                            </a>
                          </div>
                        ) : (
                          <div className="w-1/3 p-16 text-grey-800">
                            {
                              documents[
                                (currentPage - 1) * itemsPerPage + index
                              ].attributes.document_name
                            }
                          </div>
                        )}
                        <div className="w-128 py-16">
                          <StatusPill
                            status={
                              documents[
                                (currentPage - 1) * itemsPerPage + index
                              ].attributes.status
                            }
                          />
                        </div>
                        <div className="flex-1 w-[90px] py-16 px-16 text-grey-800 font-semibold">
                          {documents[
                            (currentPage - 1) * itemsPerPage + index
                          ].attributes.document_type ?? "url"}
                        </div>
                        <div className="flex-1 py-16 text-grey-800">
                          {formatDateToHumanReadable(
                            documents[
                              (currentPage - 1) * itemsPerPage + index
                            ].attributes.createdAt
                          )}
                        </div>
                        <div className="flex w-[130px] flex-row py-16 text-grey-800 font-semibold">
                          <img
                            src="assets/images/token_count.png"
                            className="w-24 h-24 object-contain block mr-4"
                          />
                          <span className="mt-2">
                            {
                              documents[
                                (currentPage - 1) * itemsPerPage + index
                              ].attributes.tokens_used
                            }
                          </span>
                        </div>
                      </div>
                    ))}
                </div>

                <PaginationComponent
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={onPageChange}
                />
              </>
            ) : (
              <div className="flex flex-col items-center justify-center p-10">
                <img
                  src="/assets/images/no-data.jpg"
                  alt="No data"
                  className="mb-4 h-[150px]"
                />
                <Body className="text-body-text-color mx-64 w-[300px] text-center my-16">
                  Looks like you haven't added any data to this datasource yet.
                  Click the add button to add your data
                </Body>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

export default DatasourceTable;
