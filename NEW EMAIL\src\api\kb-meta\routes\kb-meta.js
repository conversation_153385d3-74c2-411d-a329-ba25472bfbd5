'use strict';

/**
 * answer router
 */

module.exports = {
	routes: [
	 
	//   { 
	// 	method: 'GET',
	// 	path: '/kb_meta', 
	// 	handler: 'kb-meta.metainfo',
	//   },
	  { 
		method: 'GET',
		path: '/kb_meta', 
		handler: 'kb-meta.find',
		config: {
			policies: [
				// point to a registered policy
				'global::user-details-populate',
				'global::update-org-before-api-call',
			]
		}

	  },
	  {
		method: 'GET',
		path: '/kb_meta/:id',
		handler: 'kb-meta.findOne',
		config: {
			policies: [
				// point to a registered policy
				'global::user-details-populate',
				'global::update-org-before-api-call',
			]
		}
	  },
	  {
		method: 'POST',
		path: '/kb_meta',
		handler: 'kb-meta.create',
		config: {
			policies: [
				// point to a registered policy
				'global::user-details-populate',
				'global::update-org-before-api-call',
			]
		}
	  },
	  {
		method: 'GET',
		path: '/kb_sources',
		handler: 'kb-meta.kbSource',
		config: {
			policies: [
				
				'global::user-details-populate',
				'global::update-org-before-api-call',
			]
		}
	  },
	  
	]
  }
