{"kind": "collectionType", "collectionName": "plans", "info": {"singularName": "plan", "pluralName": "plans", "displayName": "Plan", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "description": {"type": "string"}, "stripe_prod_id": {"type": "string"}, "paid": {"type": "decimal", "default": 20}, "allowed_kbs": {"type": "integer", "default": 5}, "organizations": {"type": "relation", "relation": "oneToMany", "target": "api::organization.organization", "mappedBy": "plan"}, "allowed_training_tokens": {"type": "integer", "default": 100000}, "key_features": {"type": "json", "default": []}, "type": {"type": "enumeration", "enum": ["monthly", "yearly"], "default": "monthly"}, "allowed_credits": {"type": "biginteger"}, "allowed_scrape_train_count": {"type": "integer", "default": 5}}}