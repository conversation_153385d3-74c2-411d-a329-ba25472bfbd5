import Paper from "@mui/material/Paper";
import Typography from "@mui/material/Typography";
import { createTheme } from "@mui/material/styles";
import { memo } from "react";
import { useSelector } from "react-redux";
import { selectUsage } from "app/store/usageSlice";
import CircularProgressWithLabel from "app/shared-components/indicators/circularProgressLabel";
import formatNumber from "./FormatNumber";
import Button from "@mui/material/Button";

function TrainingUsage(props) {
  const usage = useSelector(selectUsage);
  const { training_tokens_count, allowed_training_tokens_count } = usage;

  function calcProgressVal(val, limit) {
    const percentage = (val * 100) / limit;
    return percentage > 100 ? 100 : percentage;
  }

  // Create a custom theme with the desired color
  const theme = createTheme({
    palette: {
      primary: {
        main: "#7666CE",
      },
    },
  });

  return (
    <Paper className="flex border-1 flex-col flex-auto px-24  rounded-md overflow-hidden ">
      <Typography className="mt-24 text-base font-regular text-gray-700">
        Training Usage
      </Typography>

      <div className="my-16 space-y-32">
        <div className="flex flex-row items-center justify-around">
          <div className="w-1/3 -ml-8">
            <CircularProgressWithLabel
              value={calcProgressVal(
                training_tokens_count,
                allowed_training_tokens_count
              ).toFixed(0)}
            />
          </div>
          <div className="ml-24  flex flex-col items-center">
            <Typography className="text-4xl font-bold">
              {formatNumber({ num: training_tokens_count ?? 0 }) +
                "/" +
                formatNumber({ num: allowed_training_tokens_count ?? 0 })}
            </Typography>
            <Typography className="text-sm text-center ml-4 text-gray-600">
              Tokens Used
            </Typography>
            <Button
              onClick={() => {
                history.push("/subscription", "update");
              }}
              variant="contained"
              color="secondary"
              className="press-button shine-button w-full mt-16 rounded-md bg-primary hover:bg-primary text-white"
              aria-label="Sign in"
              // disabled={_.isEmpty(dirtyFields) || !isValid}
              type="submit"
              size="small"
            >
              Upgrage
            </Button>
          </div>
        </div>
      </div>
    </Paper>
  );
}

export default memo(TrainingUsage);
