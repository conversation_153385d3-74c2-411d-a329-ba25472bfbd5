import { motion } from 'framer-motion';
import clsx from 'clsx';
import { format } from 'date-fns';
import './historyAnswers.css';
import QuestionAnswerCard from './question-answer/question-answer';

const container = {
	show: {
		transition: {
			staggerChildren: 0.05,
		},
	},
};

function HistoryAnswers({
	list,
	className,
	onNext,
	currentPage = 1,
	pageCount = 1,
	onPrevious,
}) {
	const formatDateToIST = (timestamp) => {
		const createdAtDate = new Date(timestamp);
		// Format the date using the appropriate locale.
		const formattedDate = format(createdAtDate, 'd MMM,yyyy hh:mm a');
		return formattedDate;
	};

	return (
		list.length > 0 && (
			<motion.div
				variants={container}
				initial="hidden"
				animate="show"
				className={clsx('flex flex-col gap-20', className)}
			>
        <div className="flex gap-16 place-content-end font-bold">
					<button
						onClick={() => onPrevious()}
						disabled={currentPage === 1}
					>
						Previous
					</button>
					<div>{currentPage}</div>
					<button
						onClick={() => onNext()}
						disabled={currentPage === pageCount}
					>
						Next
					</button>
				</div>
				{list.map((haq) => (
					<QuestionAnswerCard
						key={haq.id}
						question={haq.attributes.question}
						answer={haq.attributes.answer}
						created={formatDateToIST(haq.attributes.createdAt)}
						sourceType={haq.attributes.api_source}
						knowledgebase={haq.attributes.knowledgebase?.data?.attributes?.name}
						sources={haq.attributes.sources}
						did_find_answer={haq.attributes.did_find_answer}
					/>
				))}
				
			</motion.div>
		)
	);
}

export default HistoryAnswers;
