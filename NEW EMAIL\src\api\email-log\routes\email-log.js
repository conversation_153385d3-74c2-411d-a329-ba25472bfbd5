'use strict';

/**
 * Email Log Router
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

module.exports = createCoreRouter('api::email-log.email-log', {
  config: {
    find: {
      policies: [
        'global::user-details-populate',
        'global::update-org-before-api-call',
      ]
    },
    findOne: {
      policies: [
        'global::user-details-populate',
        'global::update-org-before-api-call',
      ]
    },
    // Disable create, update, delete for email logs (they should only be created by the system)
    create: false,
    update: false,
    delete: false
  }
}); 