import React from 'react';
import { Alert, AlertTitle, Paper, Typography, Button, Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';

const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  maxWidth: 600,
  margin: '0 auto',
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[4],
  border: `1px solid ${theme.palette.success.light}`,
  backgroundColor: theme.palette.background.paper
}));

/**
 * Alert component displayed when an assessment is completed
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether the alert is visible
 * @param {Function} props.onClose - Handler for closing the alert
 * @returns {JSX.Element} Assessment completion alert
 */
function AssessmentCompletionAlert({ open, onClose }) {
  if (!open) return null;

  return (
    <Box sx={{ 
      position: 'fixed', 
      top: 0, 
      left: 0, 
      right: 0, 
      bottom: 0, 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'center', 
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      zIndex: 9999
    }}>
      <StyledPaper>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
          <CheckCircleOutlineIcon color="success" sx={{ fontSize: 60, mb: 2 }} />
          
          <AlertTitle sx={{ fontSize: 24, fontWeight: 'bold', mb: 2 }}>
            Assessment Completed
          </AlertTitle>
          
          <Typography variant="body1" sx={{ mb: 3 }}>
            Thank you for completing this assessment. Your responses have been recorded.
            The interviewer will review your performance and provide feedback.
          </Typography>
          
          <Button 
            variant="contained" 
            color="primary" 
            onClick={onClose}
            sx={{ minWidth: 120 }}
          >
            Close
          </Button>
        </Box>
      </StyledPaper>
    </Box>
  );
}

export default AssessmentCompletionAlert; 