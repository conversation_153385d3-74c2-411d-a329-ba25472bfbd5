.syntax-code {
    display: inline-block;
    width: fit-content;
    padding: 8px 16px;   /* Adds padding around the code */
    background-color: #f8f8f8; /* Light gray background */
    border-radius: 4px; /* Rounded corners */
    box-shadow: 0 1px 3px rgba(0,0,0,0.1); /* A light shadow for depth */
    font-family: 'Courier New', Courier, monospace; /* Suggested font for code */
    transition: background-color 0.3s, box-shadow 0.3s; /* Transition effects */
}

.syntax-code:hover {
    background-color: #e8e8e8; /* Darker gray on hover */
    box-shadow: 0 2px 6px rgba(0,0,0,0.15); /* A deeper shadow on hover */
}

.bullet-points {
    list-style-type: disc;
    margin-left: 1.5em;
    padding-left: 0;
}

.bullet-point {
    margin-bottom: 0.5em;
    transition: margin-left 0.2s; /* Transition for a slight move effect */
}

.bullet-point:hover {
    margin-left: 1em; /* Moves the bullet point slightly to the left on hover */
}

.leading-normal table {
    overflow-x: auto;
    margin-top: 10px;
    transition: box-shadow 0.3s; /* Transition for shadow */
}

.leading-normal table:hover {
    box-shadow: 0 2px 6px rgba(0,0,0,0.1); /* Adds a shadow to the table on hover */
}

.leading-normal table {
    border-collapse: collapse;
    width: 100%;
}

.leading-normal table th, table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    transition: background-color 0.3s; /* Transition for hover effect */
}

.leading-normal table th {
    background-color: white;
    font-weight: bold;
}

.leading-normal table tr:hover td {
    background-color: #f5f5f5; /* Slight gray background for row on hover */
}
