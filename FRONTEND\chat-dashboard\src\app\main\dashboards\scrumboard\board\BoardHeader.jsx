import Button from '@mui/material/Button';
import NavLinkAdapter from '@fuse/core/NavLinkAdapter';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import BoardTitle from './BoardTitle';
import { useAppSelector } from 'app/store/index';
import { selectBoard } from '../store/boardSlice';
import { useState } from 'react';



/**
 * The board header component.
 */
function BoardHeader(props) {
	const { onSetSidebarOpen , onKanbanBoard } = props;
	const { data: board } = useAppSelector(selectBoard);

	return (
		<div className="p-24 sm:p-32 w-full flex flex-col sm:flex-row items-center justify-between container">
			<div className="flex items-center mb-12 sm:mb-0">
				<BoardTitle />

			</div>
			<div className='flex items-center mr-24'>
			{/* <Button
				className="ml-16 px-8 whitespace-nowrap  text-grey-800 text-base font-regular rounded"
				startIcon={<FuseSvgIcon size={16}>heroicons-outline:view-boards</FuseSvgIcon>}
				onClick={onKanbanBoard}
			>
				Kanban Board
			</Button> */}
			<Button
                className="ml-16 px-8 whitespace-nowrap text-grey-800 text-base font-regular rounded"
                component={NavLinkAdapter}
                to={`/dashboards/knowledge_base/items_detail_page?kbID=${board.attributes.knowledgebases.data[0].attributes.kb_id}&kbName=${board.attributes.knowledgebases.data[0].attributes.name}&kbrID=${board.attributes.knowledgebases.data[0].id}`}
                startIcon={<FuseSvgIcon size={16}>heroicons-outline:user-circle</FuseSvgIcon>}
              >
               View Agent
              </Button>
			</div>

		</div>
	);
}

export default BoardHeader;
