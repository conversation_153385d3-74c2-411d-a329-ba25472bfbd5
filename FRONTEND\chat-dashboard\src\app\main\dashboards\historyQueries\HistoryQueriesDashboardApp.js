import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import withReducer from "app/store/withReducer";
import reducer from "./store";
import FusePageSimple from "@fuse/core/FusePageSimple";
import UsageDashboardAppHeader from "./HistoryQueriesAppHeader";
import HistoryAnswers from "./HistoryAnswers";
import {
  selectQuestionAnswers,
  getQuestionAnswers,
} from "./store/questionAnswersSlice";
import LoadingSpinner from "app/shared-components/loading-spinner/loading-spinner";
import EmptyLottie from "app/shared-components/empty-data/EmptyLottie";
import SessionsSidebar from "./SessionsSidebar";
import { Box, Stack } from "@mui/material";
import Messages from "./Messages";

function HistoryQueriesDashboardApp() {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const questionAnswers = useSelector(selectQuestionAnswers);

  useEffect(() => {
    setIsLoading(true);
    dispatch(getQuestionAnswers(currentPage))
      .then(() => {
        setIsLoading(false); // Set isLoading to false when API call is completed
      })
      .catch((error) => {
        setIsLoading(false); // Set isLoading to false if there's an error during API call
      });
  }, [currentPage]);
  const onNext = () => {
    if (currentPage < questionAnswers.meta.pagination.pageCount) {
      setCurrentPage((prevPage) => prevPage + 1);
    }
  };
  const onPrevious = () => {
    if (currentPage > 1) {
      setCurrentPage((prevPage) => prevPage - 1);
    }
  };

  return (
    <FusePageSimple
      content={
        <Stack spacing={4} direction={"row"} className="w-full">
          <Box
            maxWidth={"300px"}
            width={"100%"}
            height={"100vh"}
            className="overflow-y-auto"
          >
            <SessionsSidebar />
          </Box>
          <Box className="overflow-y-auto h-screen w-full flex-1">
            <UsageDashboardAppHeader />
            <Messages />
          </Box>
        </Stack>
      }
    />
  );
}

export default withReducer(
  "historyQueriesDashboardApp",
  reducer
)(HistoryQueriesDashboardApp);
