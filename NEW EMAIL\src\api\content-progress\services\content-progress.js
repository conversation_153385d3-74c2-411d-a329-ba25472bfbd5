'use strict';

/**
 * content-progress service
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::content-progress.content-progress', ({ strapi }) => ({

  // Get aggregated progress stats for a user across all content
  async getUserProgressStats(userId) {
    const stats = await strapi.db.connection.raw(`
      SELECT 
        COUNT(*) as total_content_accessed,
        COUNT(*) FILTER (WHERE completed = true) as total_completed,
        SUM(time_spent) as total_time_spent,
        COUNT(DISTINCT series_id) FILTER (WHERE series_id IS NOT NULL) as series_accessed,
        COUNT(*) FILTER (WHERE series_id IS NULL) as standalone_tracks_accessed
      FROM content_progress 
      WHERE user_id = ?
    `, [userId]);
    
    return stats.rows[0];
  },

  // Get user's progress within a specific series
  async getSeriesProgressStats(userId, seriesId) {
    const stats = await strapi.db.connection.raw(`
      SELECT 
        COUNT(*) as episodes_accessed,
        COUNT(*) FILTER (WHERE completed = true) as episodes_completed,
        SUM(time_spent) as total_time_spent,
        MAX(last_accessed_at) as last_activity,
        se.total_episodes
      FROM content_progress cp
      JOIN (
        SELECT series_id, COUNT(*) as total_episodes 
        FROM series_episodes 
        WHERE series_id = ? AND is_optional = false
        GROUP BY series_id
      ) se ON se.series_id = cp.series_id
      WHERE cp.user_id = ? AND cp.series_id = ?
      GROUP BY se.total_episodes
    `, [seriesId, userId, seriesId]);
    
    return stats.rows[0];
  },

  // Get next episode to play in a series
  async getNextEpisodeInSeries(userId, seriesId) {
    // Find the last accessed episode
    const lastProgress = await strapi.db.query('api::content-progress.content-progress').findOne({
      where: { user: userId, series_id: seriesId },
      orderBy: { last_accessed_at: 'desc' }
    });

    let nextEpisode;
    
    if (!lastProgress) {
      // User hasn't started series, get first episode
      nextEpisode = await strapi.db.query('api::series-episode.series-episode').findOne({
        where: { series: seriesId },
        orderBy: { position: 'asc' },
        populate: ['file_results_model']
      });
    } else {
      // Check if last episode was completed
      if (lastProgress.completed) {
        // Get next episode after the completed one
        const currentEpisode = await strapi.db.query('api::series-episode.series-episode').findOne({
          where: { episode_id: lastProgress.episode_id }
        });
        
        nextEpisode = await strapi.db.query('api::series-episode.series-episode').findOne({
          where: { 
            series: seriesId,
            position: { $gt: currentEpisode.position }
          },
          orderBy: { position: 'asc' },
          populate: ['file_results_model']
        });
      } else {
        // Continue with current episode
        nextEpisode = await strapi.db.query('api::series-episode.series-episode').findOne({
          where: { episode_id: lastProgress.episode_id },
          populate: ['file_results_model']
        });
      }
    }

    return nextEpisode;
  },

  // Check if user can access next episode (progressive unlocking)
  async canAccessEpisode(userId, episodeId) {
    const episode = await strapi.db.query('api::series-episode.series-episode').findOne({
      where: { episode_id: episodeId },
      populate: ['series']
    });

    if (!episode) return false;

    // Check if user has access to the series
    const hasSeriesAccess = await strapi.service('api::series.series').checkSeriesAccess(userId, episode.series.id);
    if (!hasSeriesAccess) return false;

    // Preview episodes are always accessible
    if (episode.is_preview) return true;

    // Check prerequisites
    if (episode.prerequisites && episode.prerequisites.length > 0) {
      const completedPrereqs = await strapi.db.query('api::content-progress.content-progress').count({
        where: {
          user: userId,
          episode_id: { $in: episode.prerequisites },
          completed: true
        }
      });
      
      return completedPrereqs === episode.prerequisites.length;
    }

    // For first episode or no prerequisites, check if previous episode is completed
    const previousEpisode = await strapi.db.query('api::series-episode.series-episode').findOne({
      where: {
        series: episode.series.id,
        position: { $lt: episode.position }
      },
      orderBy: { position: 'desc' }
    });

    if (!previousEpisode) {
      // This is the first episode
      return true;
    }

    // Check if previous episode is completed
    const previousProgress = await strapi.db.query('api::content-progress.content-progress').findOne({
      where: {
        user: userId,
        episode_id: previousEpisode.episode_id,
        completed: true
      }
    });

    return !!previousProgress;
  }

})); 