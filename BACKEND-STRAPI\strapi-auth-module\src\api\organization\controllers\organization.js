"use strict";

/**
 * organization controller
 */

const { createCoreController } = require("@strapi/strapi").factories;
const axios = require("axios");
module.exports = createCoreController(
  "api::organization.organization",
  ({ strapi }) => ({
    async create(ctx) {
      const result = await axios
        .post(
          `${process.env.TALKBASE_BASE_URL}/v4/add_org`,
          { org_name: ctx.request.body.name },
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        )
        .catch((err) => {
          throw new Error(
            "Failed to create organization. Please try again later."
          );
        });
      ctx.request.body.data = {
        ...ctx.request.body,
        type:
          ctx.request.body.name === "individual"
            ? "individual"
            : "organization",
        org_id: result.data.id,
        usage_quota: 20 / 2,
        users_limit: 10,
        tokens_used: result.data.tokens_used,
        paid: 20,
        cost_used: result.data.cost,
      };
      // some logic here
      const response = await super.create(ctx);
      // some more logic

      return response.data;
    },
    async find(ctx, next) {
      const response = await super.find(ctx);
      return response;
    },


  })
);
