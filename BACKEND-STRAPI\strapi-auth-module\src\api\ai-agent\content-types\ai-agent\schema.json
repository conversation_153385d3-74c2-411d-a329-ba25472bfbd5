{"kind": "collectionType", "collectionName": "ai_agents", "info": {"singularName": "ai-agent", "pluralName": "ai-agents", "displayName": "AI Agent", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"Title": {"type": "string"}, "agent_role": {"type": "string"}, "company_name": {"type": "string"}, "company_business": {"type": "text"}, "company_values": {"type": "text"}, "conversation_purpose": {"type": "text"}, "conversation_type": {"type": "enumeration", "enum": ["call"], "default": "call"}, "knowledgebases": {"type": "relation", "relation": "oneToMany", "target": "api::knowledgebase.knowledgebase", "mappedBy": "ai_agent"}, "agent_name": {"type": "string"}, "booking_base_url": {"type": "string"}}}