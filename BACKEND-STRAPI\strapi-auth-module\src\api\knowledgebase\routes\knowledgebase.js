'use strict';

/**
 * knowledgebase router
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

module.exports = createCoreRouter('api::knowledgebase.knowledgebase', {
	
	config: {
		create: {
			policies: [
				'global::user-details-populate',
				'global::update-org-before-api-call',
				'global::usage-validation',
			]
		},
		find:{
			policies: [
				'global::user-details-populate',
				'global::update-org-before-api-call',
			]

		},
		findOne:{
			policies: [
				'global::user-details-populate',
				'global::update-org-before-api-call',
			]

		},
		delete:{
			policies: [
				'global::user-details-populate',
				'global::update-org-before-api-call',
			]

		}
	}
});
