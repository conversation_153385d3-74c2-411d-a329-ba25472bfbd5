import { <PERSON><PERSON>, <PERSON>ack } from "@mui/material";
import MessageCard from "./MessageCard";
import { useSearchParams } from "react-router-dom";
import { useEffect, useState } from "react";
import axios from "axios";
import MessagesSkeleton from "./MessagesSkeleton";
import EmptyLottie from "app/shared-components/empty-data/EmptyLottie";

export default function Messages() {
  const [searchParams] = useSearchParams();
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({});

  async function fetchMessages(page) {
    setLoading(true);
    console.log(searchParams.get("sessionId"));
    try {
      const resp = await axios.get(
        `${
          process.env.REACT_APP_AUTH_BASE_URL
        }/api/answers?filters[sessionModel][session_id][$eq]=${searchParams.get(
          "sessionId"
        )}${page ? `&pagination[page]=${page}` : ""}`
      );
      setMessages(
        page === 1 ? resp.data?.data : [...messages, ...resp.data?.data]
      );
      setPagination(resp.data?.meta?.pagination);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    setMessages([]);
    fetchMessages(1);
    return () => {
      setLoading(false);
    };
  }, [searchParams]);

  async function handleFetchMore() {
    fetchMessages(pagination.page ? pagination.page + 1 : 1);
  }

  return (
    <Stack spacing={4} className="w-full px-26 py-16 pr-16">
      {loading && <MessagesSkeleton />}
      {messages?.length === 0 && !loading ? (
        <EmptyLottie text="No conversation history found. Create an agent so that your customers can chat with you." />
      ) : (
        messages.map((message) => (
          <Stack key={message.id} spacing={1}>
            <MessageCard text={message.attributes.question} isSender />
            <MessageCard
              text={message.attributes.answer}
              sources={
                message.attributes?.sources || message.attributes?.api_source
              }
            />
          </Stack>
        ))
      )}

      {pagination.page < pagination.pageCount && (
        <div>
          <Button
            disabled={loading}
            onClick={handleFetchMore}
            variant="outlined"
            size="small"
            className="mt-4"
          >
            Load more
          </Button>
        </div>
      )}
    </Stack>
  );
}
