const axios = require("axios");

module.exports = {
	async createOrgForuser( data ) {
		strapi.log.info('🏢 SIGNUP FLOW: Starting organization creation process', {
			organizationName: data.name,
			organizationType: data.type,
			timestamp: new Date().toISOString(),
			helper: 'create-organization-for-user'
		});

		try{
			strapi.log.info('🌐 SIGNUP FLOW: Making API call to create organization externally', {
				url: `${process.env.TALKBASE_BASE_URL}/v4/add_org`,
				organizationName: data.name
			});

			const result = await axios.post(
				`${process.env.TALKBASE_BASE_URL}/v4/add_org`,
				{"org_name":data.name},
				{
					headers: {
					  'Content-Type': 'multipart/form-data'
					}
				}
				).catch(err=>{
					strapi.log.error('❌ SIGNUP FLOW ERROR: External organization creation failed', {
						organizationName: data.name,
						error: err.message,
						url: `${process.env.TALKBASE_BASE_URL}/v4/add_org`
					});
					throw new Error('Failed to create organization. Please try again later.');
				});

			strapi.log.info('✅ SIGNUP FLOW: External organization created successfully', {
				organizationName: data.name,
				externalOrgId: result.data.id,
				tokensUsed: result.data.tokens_used || 0,
				cost: result.data.cost
			});

			data = {
				...data,
				type: data.name==='individual'?"individual":"organization",
				org_id: result.data.id,
				usage_quota: 20/2,
				users_limit: 10,
				tokens_used: result.data.tokens_used??0,
				paid: 20,
				cost_used: result.data.cost
			  };

			strapi.log.info('💾 SIGNUP FLOW: Creating organization record in Strapi database', {
				organizationName: data.name,
				organizationType: data.type,
				externalOrgId: data.org_id,
				usageQuota: data.usage_quota,
				usersLimit: data.users_limit,
				tokensUsed: data.tokens_used,
				costUsed: data.cost_used
			});

			const response = await strapi.query('api::organization.organization').create({data:data});

			strapi.log.info('✅ SIGNUP FLOW: Organization record created in Strapi successfully', {
				strapiOrgId: response.id,
				externalOrgId: response.org_id,
				organizationName: response.name,
				organizationType: response.type,
				usersLimit: response.users_limit,
				usageQuota: response.usage_quota
			});

		  
			return response;
		}
		catch(e){
			strapi.log.error('❌ SIGNUP FLOW ERROR: Organization creation process failed', {
				organizationName: data.name,
				error: e.message,
				stack: e.stack,
				timestamp: new Date().toISOString(),
				helper: 'create-organization-for-user'
			});
			console.log(e);
		}
	}
}