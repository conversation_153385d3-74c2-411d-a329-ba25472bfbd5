{"kind": "collectionType", "collectionName": "customer_info", "info": {"singularName": "customer-info", "pluralName": "customer-infos", "displayName": "Customer Info", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"userId": {"type": "string"}, "email": {"type": "email"}, "session": {"type": "relation", "relation": "oneToOne", "target": "api::session.session", "inversedBy": "customerInfo"}}}