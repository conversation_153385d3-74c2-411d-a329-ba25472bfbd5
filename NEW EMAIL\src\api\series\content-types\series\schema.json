{"kind": "collectionType", "collectionName": "series", "info": {"singularName": "series", "pluralName": "series-collection", "displayName": "Series", "description": "Content series for progressive learning paths"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true, "maxLength": 255}, "description": {"type": "text"}, "cover_image_url": {"type": "string", "maxLength": 500}, "estimated_duration": {"type": "integer", "description": "Estimated total duration in minutes"}, "difficulty_level": {"type": "enumeration", "enum": ["beginner", "intermediate", "advanced"], "default": "beginner"}, "category": {"type": "string", "maxLength": 100}, "tags": {"type": "json", "default": []}, "public": {"type": "boolean", "default": true, "description": "Public series are accessible to all users"}, "featured": {"type": "boolean", "default": false}, "display_order": {"type": "integer"}, "access_tier": {"type": "enumeration", "enum": ["free", "basic", "premium", "enterprise"], "default": "free", "description": "Required subscription tier for access"}, "credit_cost": {"type": "integer", "default": 0, "description": "Credits required to unlock this series"}, "organization": {"type": "relation", "relation": "manyToOne", "target": "api::organization.organization", "inversedBy": "series"}, "created_by_user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "series_episodes": {"type": "relation", "relation": "oneToMany", "target": "api::series-episode.series-episode", "mappedBy": "series"}}}