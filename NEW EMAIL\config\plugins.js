module.exports = ({ env }) => ({
  sentry: {
    enabled: true,
    config: {
      dsn: env("SENTRY_DSN"),
    },
  },
  migrations: {
    enabled: true,
    config: {
      autoStart: true,
      migrationFolderPath: "migrations",
    },
  },
  "measurement-protocol": {
    config: {
      apiSecret: "4YbZDjHUSVyr4VBvKMVATw",
      measurementId: "G-7T37DM6B90",
      useValidationServer: false,
    },
  },
  "import-export-entries": {
    enabled: true,
    config: {
      // See `Config` section.
    },
  },

  "otp-authentication": {
    enabled: true,
    resolve: "./src/plugins/otp-authentication",
  },

  "users-permissions": {
    enabled: true,
    config: {
      jwt: {
        expiresIn: "15d",
      },
      ratelimit: {
        interval: 2000,
        max: 5,
      },
    },
  },
  email: {
    config: {
      provider: "postmark",
      providerOptions: {
        apiKey: env("POSTMARK_API_KEY"),
      },
      settings: {
        defaultFrom: env("POSTMARK_FROM_NAME", "Podycy Team") + " <" + env("POSTMARK_FROM_EMAIL", "<EMAIL>") + ">",
        defaultReplyTo: env("POSTMARK_FROM_EMAIL", "<EMAIL>"),
        testAddress: env("POSTMARK_FROM_EMAIL", "<EMAIL>"),
      },
    },
  },
  upload: {
    provider: "aws-s3",
    providerOptions: {
      accessKeyId: env("AWS_SECRET_ACCESS_KEY"),
      secretAccessKey: env("AWS_ACCESS_KEY_ID"),
      region: env("AWS_REGION"),
      params: {
        Bucket: env("AWS_BUCKET"),
      },
    },
  },

  provider: {
    google: {
      enabled: true,
      callback: "/api/auth/google/callback",
      icon: "google",
      key: env("GOOGLE_AUTH_KEY"),
      redirectUri: "http://localhost:1337/api/connect/google/callback",
      scope: ["email"],
      secret: env("GOOGLE_AUTH_SECRET"),
    },

    facebook: {
      enabled: true,
      icon: "facebook-square",
      key: env("FACEBOOK_AUTH_KEY"),
      redirectUri: "http://localhost:1337/api/connect/facebook/callback",
      scope: ["email"],
      callback: "/api/auth/facebook/callback",
      secret: env("FACEBOOK_AUTH_SECRET"),
    },
  },
});
