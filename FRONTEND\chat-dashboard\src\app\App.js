// import '@mock-api';
import React, { useEffect } from "react";
import BrowserRouter from "@fuse/core/BrowserRouter";
import FuseLayout from "@fuse/core/FuseLayout";
import FuseTheme from "@fuse/core/FuseTheme";
import { SnackbarProvider } from "notistack";
import { useSelector } from "react-redux";
import rtlPlugin from "stylis-plugin-rtl";
import createCache from "@emotion/cache";
import { CacheProvider } from "@emotion/react";
import { selectCurrentLanguageDirection } from "app/store/i18nSlice";
import { selectUser } from "app/store/userSlice";
import themeLayouts from "app/theme-layouts/themeLayouts";
import { selectMainTheme } from "app/store/fuse/settingsSlice";
import FuseAuthorization from "@fuse/core/FuseAuthorization";
import settingsConfig from "app/configs/settingsConfig";
import withAppProviders from "./withAppProviders";
import { AuthProvider } from "./auth/AuthContext";
import ReactGA from "react-ga";
import history from "@history";
import _ from "@lodash";
import '../styles/globals.css';


const emotionCacheOptions = {
  rtl: {
    key: "muirtl",
    stylisPlugins: [rtlPlugin],
    insertionPoint: document.getElementById("emotion-insertion-point"),
  },
  ltr: {
    key: "muiltr",
    stylisPlugins: [],
    insertionPoint: document.getElementById("emotion-insertion-point"),
  },
};

const App = () => {

  // useEffect(() => {
  //   if (process.env.REACT_APP_ENV === "development") {
  //     const chatbotDevCss = document.createElement("link");
  //     chatbotDevCss.rel = "stylesheet";
  //     chatbotDevCss.href = "https://bot-dev.talkbase.ai/chatbot.css";
  //     document.head.appendChild(chatbotDevCss);

  //     const chatbotDevScript = document.createElement("script");
  //     chatbotDevScript.src = "https://bot-dev.talkbase.ai/chatbot.js";
  //     chatbotDevScript.defer = true;
  //     chatbotDevScript.setAttribute(
  //       "orgid",
  //       "U2FsdGVkX1+9oOL/SUGba/T6Z14hJs7+6VN3G1oBkJkMe26htIsGMkfys5klhONfAFDE741gYbQQi9SL9iz1FA=="
  //     );
  //     chatbotDevScript.setAttribute(
  //       "userInfo",
  //       JSON.stringify({"userId": "1234", "userEmail": "<EMAIL>"})
  //     );
  //     chatbotDevScript.setAttribute(
  //       "chatbotid",
  //       "U2FsdGVkX1/mh0wwwy4Ozo8l2eNOiYbuB6u2w2gg4vciOQTWupWSjL0ms1nKj7Rp++c+IU4CRr0lyFlmeIoDMA=="
  //     );
  //     chatbotDevScript.setAttribute("position", "right");
  //     chatbotDevScript.setAttribute("showclosebutton", "true");
  //     chatbotDevScript.setAttribute("usethemeargs", "false");
  //     chatbotDevScript.setAttribute("showTooltip", "true");
  //     chatbotDevScript.setAttribute("isinitiallyopen", "false");
  //     document.body.appendChild(chatbotDevScript);

  //     const gtagScript = document.createElement("script");
  //     gtagScript.src =
  //       "https://www.googletagmanager.com/gtag/js?id=G-BNL97GKPK1";
  //     gtagScript.async = true;
  //     document.body.appendChild(gtagScript);

  //     const gtagConfigScript = document.createElement("script");
  //     gtagConfigScript.innerHTML = `
  //       window.dataLayer = window.dataLayer || [];
  //       function gtag(){ dataLayer.push(arguments); }
  //       gtag('js', new Date());
  //       gtag('config', 'G-BNL97GKPK1');
  //     `;
  //     document.body.appendChild(gtagConfigScript);
  //   } else if(process.env.REACT_APP_ENV === "production") {
  //     const chatbotProdCss = document.createElement("link");
  //     chatbotProdCss.rel = "stylesheet";
  //     chatbotProdCss.href = "https://bot.talkbase.ai/chatbot.css";
  //     document.head.appendChild(chatbotProdCss);

  //     const chatbotProdScript = document.createElement("script");
  //     chatbotProdScript.src = "https://bot.talkbase.ai/chatbot.js";
  //     chatbotProdScript.defer = true;
  //     chatbotProdScript.setAttribute(
  //       "orgid",
  //       "U2FsdGVkX1/FLWVwEZcQMcoGDNziG2wxRe8iJOJDVcSOMnt3Aco5nOoOwdrKQNN03k7W1KhnI8Z8a2k8G/4kVg=="
  //     );
  //     chatbotProdScript.setAttribute(
  //       "chatbotid",
  //       "U2FsdGVkX19HPxptaHD9kGrktdq1RuvJVueRHl3lQ+OF7tiMimI2glinF/WHKEaoqUfrM5akGuA31j/qfe1R6g=="
  //     );
  //     chatbotProdScript.setAttribute("position", "right");
  //     chatbotProdScript.setAttribute("showclosebutton", "true");
  //     chatbotProdScript.setAttribute("usethemeargs", "false");
  //     chatbotProdScript.setAttribute("showTooltip", "true");
  //     chatbotProdScript.setAttribute("isinitiallyopen", "false");
  //     document.body.appendChild(chatbotProdScript);

  //     const gtagScript = document.createElement("script");
  //     gtagScript.src =
  //       "https://www.googletagmanager.com/gtag/js?id=G-BNL97GKPK1";
  //     gtagScript.async = true;
  //     document.body.appendChild(gtagScript);

  //     const gtagConfigScript = document.createElement("script");
  //     gtagConfigScript.innerHTML = `
  //       window.dataLayer = window.dataLayer || [];
  //       function gtag(){ dataLayer.push(arguments); }
  //       gtag('js', new Date());
  //       gtag('config', 'G-BNL97GKPK1');
  //     `;
  //     document.body.appendChild(gtagConfigScript);
  //   }
  // }, []);

  const setGA = () => {
    ReactGA.initialize(process.env.REACT_APP_GA_TRACKING_ID);
  };

  useEffect(() => {
    setGA();
  }, []);

  const user = useSelector(selectUser);
  const langDirection = useSelector(selectCurrentLanguageDirection);
  const mainTheme = useSelector(selectMainTheme);
  // if (
  //   !_.isEmpty(user.data?.organization) &&
  //   !(
  //     user.data?.organization?.subscription === "subscribed" ||
  //     user.data?.organization?.subscription === "trial"
  //   )
  // ) {
  //   // setTimeout(() => history.push('subscription'), 20);
  //   history.push("subscription");
  // }

  return (
    <CacheProvider value={createCache(emotionCacheOptions[langDirection])}>
      <FuseTheme theme={mainTheme} direction={langDirection}>
        <AuthProvider>

          <BrowserRouter>
            <FuseAuthorization
              userRole={user.role}
              loginRedirectUrl={settingsConfig.loginRedirectUrl}
            >
              <SnackbarProvider
                maxSnack={5}
                anchorOrigin={{
                  vertical: "bottom",
                  horizontal: "right",
                }}
                classes={{
                  containerRoot:
                    "bottom-0 right-0 mb-52 md:mb-68 mr-8 lg:mr-80 z-99",
                }}
              >
                <FuseLayout layouts={themeLayouts} />
              </SnackbarProvider>
            </FuseAuthorization>
          </BrowserRouter>
        </AuthProvider>
      </FuseTheme>
    </CacheProvider>
  );
};

export default withAppProviders(App)();
