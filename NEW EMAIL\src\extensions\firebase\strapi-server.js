'use strict';

const admin = require('firebase-admin');
const serviceAccount = require('../../../config/firebase-service-account.json');

module.exports = plugin => {
  console.log('🔄 Initializing Firebase extension...');

  // Initialize Firebase Admin if not already initialized
  if (!admin.apps.length) {
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });
  }

  // Add Firebase to global strapi instance
  global.strapi.firebase = admin;
  global.strapi.notification = async ({ title, body, token, data = {}, sound = 'default' }) => {
    console.log('Sending data message:', { title, body, token, data, sound });
    
    // Convert all data values to strings
    const stringifiedData = Object.keys(data).reduce((acc, key) => {
      acc[key] = data[key]?.toString() || '';
      return acc;
    }, {});

    // Add title and body to data payload instead of notification
    stringifiedData.title = title;
    stringifiedData.body = body;

    return await admin.messaging().send({
      token,
      data: stringifiedData,
      android: {
        priority: 'high'
      },
      apns: {
        headers: {
          'apns-priority': '10',
        },
        payload: {
          aps: {
            contentAvailable: true
          }
        }
      }
    });
  };

  console.log('✅ Firebase extension initialized successfully');

  return plugin;
};
