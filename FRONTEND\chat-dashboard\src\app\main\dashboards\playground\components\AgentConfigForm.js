import React, { useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

/**
 * AgentConfigForm Component
 * Allows configuring the agent parameters before starting a chat session
 */
const AgentConfigForm = ({ onSessionStart }) => {
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    current_role: '',
    years_of_experience: '',
    target_role: '',
    assessment_type: 'technical',
    compulsory_questions: []
  });
  
  // Form errors state
  const [errors, setErrors] = useState({});
  
  // State for the current question being edited
  const [currentQuestion, setCurrentQuestion] = useState({
    question: '',
    expected_answer: '',
    phase: 'technical_knowledge',
    criteria: ['']
  });
  
  // State for showing the question form
  const [showQuestionForm, setShowQuestionForm] = useState(false);
  
  // State for editing mode (new vs edit)
  const [editingIndex, setEditingIndex] = useState(-1);
  
  // Assessment type options with icons and descriptions
  const assessmentTypes = [
    { 
      value: 'technical', 
      label: 'Technical', 
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M16 18l6-6-6-6M8 6l-6 6 6 6"/>
        </svg>
      ),
      description: 'Focused on technical skills and knowledge'
    },
    { 
      value: 'general', 
      label: 'General', 
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="12" cy="12" r="10"/>
          <line x1="2" y1="12" x2="22" y2="12"/>
          <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
        </svg>
      ),
      description: 'Overall assessment of skills and aptitude'
    },
    { 
      value: 'comprehensive', 
      label: 'Comprehensive', 
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <line x1="9" y1="3" x2="9" y2="21"/>
          <line x1="15" y1="3" x2="15" y2="21"/>
          <line x1="3" y1="9" x2="21" y2="9"/>
          <line x1="3" y1="15" x2="21" y2="15"/>
        </svg>
      ),
      description: 'In-depth evaluation of all aspects of performance'
    },
    { 
      value: 'leadership', 
      label: 'Leadership', 
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
          <circle cx="8.5" cy="7" r="4"/>
          <polyline points="17 11 19 13 23 9"/>
        </svg>
      ),
      description: 'Focus on leadership qualities and management skills'
    }
  ];
  
  // Phase options for questions
  const phaseOptions = [
    { value: 'initial', label: 'Initial' },
    { value: 'professional_background', label: 'Professional Background' },
    { value: 'behavioral_capabilities', label: 'Behavioral Capabilities' },
    { value: 'job_fit', label: 'Job Fit' },
    { value: 'cultural_alignment', label: 'Cultural Alignment' },
    { value: 'motivation_goals', label: 'Motivation & Goals' },
    { value: 'technical_knowledge', label: 'Technical Knowledge' },
    { value: 'problem_solving', label: 'Problem Solving' },
    { value: 'technical_experience', label: 'Technical Experience' },
    { value: 'learning_agility', label: 'Learning Agility' },
    { value: 'technical_communication', label: 'Technical Communication' },
    { value: 'leadership_potential', label: 'Leadership Potential' },
    { value: 'emotional_intelligence', label: 'Emotional Intelligence' },
    { value: 'communication_style', label: 'Communication Style' },
    { value: 'team_management', label: 'Team Management' },
    { value: 'growth_mindset', label: 'Growth Mindset' },
    { value: 'conclusion', label: 'Conclusion' }
  ];
  
  // Handle input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    
    // Clear error for this field when user starts typing
    if (errors[name]) {
      setErrors({ ...errors, [name]: null });
    }
  };
  
  // Handle assessment type selection
  const handleAssessmentTypeSelect = (type) => {
    setFormData({ ...formData, assessment_type: type });
  };
  
  // Handle question input changes
  const handleQuestionChange = (e) => {
    const { name, value } = e.target;
    setCurrentQuestion({ ...currentQuestion, [name]: value });
  };
  
  // Handle criteria changes
  const handleCriteriaChange = (index, value) => {
    const updatedCriteria = [...currentQuestion.criteria];
    updatedCriteria[index] = value;
    setCurrentQuestion({ ...currentQuestion, criteria: updatedCriteria });
  };
  
  // Add new criteria field
  const addCriteria = () => {
    setCurrentQuestion({
      ...currentQuestion,
      criteria: [...currentQuestion.criteria, '']
    });
  };
  
  // Remove criteria field
  const removeCriteria = (index) => {
    const updatedCriteria = [...currentQuestion.criteria];
    updatedCriteria.splice(index, 1);
    setCurrentQuestion({ ...currentQuestion, criteria: updatedCriteria });
  };
  
  // Save the current question
  const saveQuestion = () => {
    // Validate question fields
    if (!currentQuestion.question.trim() || !currentQuestion.expected_answer.trim() || !currentQuestion.phase) {
      return; // Don't save if required fields are missing
    }
    
    // Filter out empty criteria
    const filteredCriteria = currentQuestion.criteria.filter(criterion => criterion.trim() !== '');
    
    // Prepare the question object
    const questionObject = {
      ...currentQuestion,
      criteria: filteredCriteria.length > 0 ? filteredCriteria : undefined
    };
    
    // Update form data with the new/edited question
    if (editingIndex >= 0) {
      // Editing existing question
      const updatedQuestions = [...formData.compulsory_questions];
      updatedQuestions[editingIndex] = questionObject;
      setFormData({ ...formData, compulsory_questions: updatedQuestions });
    } else {
      // Adding new question
      setFormData({
        ...formData,
        compulsory_questions: [...formData.compulsory_questions, questionObject]
      });
    }
    
    // Reset the form
    resetQuestionForm();
  };
  
  // Edit an existing question
  const editQuestion = (index) => {
    const questionToEdit = formData.compulsory_questions[index];
    setCurrentQuestion({
      ...questionToEdit,
      criteria: questionToEdit.criteria ? [...questionToEdit.criteria] : ['']
    });
    setEditingIndex(index);
    setShowQuestionForm(true);
  };
  
  // Delete a question
  const deleteQuestion = (index) => {
    const updatedQuestions = [...formData.compulsory_questions];
    updatedQuestions.splice(index, 1);
    setFormData({ ...formData, compulsory_questions: updatedQuestions });
  };
  
  // Reset the question form
  const resetQuestionForm = () => {
    setCurrentQuestion({
      question: '',
      expected_answer: '',
      phase: 'technical_knowledge',
      criteria: ['']
    });
    setEditingIndex(-1);
    setShowQuestionForm(false);
  };
  
  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.current_role.trim()) {
      newErrors.current_role = 'Current role is required';
    }
    
    if (!formData.years_of_experience) {
      newErrors.years_of_experience = 'Years of experience is required';
    } else if (isNaN(formData.years_of_experience) || formData.years_of_experience < 0) {
      newErrors.years_of_experience = 'Please enter a valid number';
    }
    
    if (!formData.target_role.trim()) {
      newErrors.target_role = 'Target role is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      // Generate a new session ID
      const sessionId = uuidv4();
      
      // Call the parent component's handler with the form data and new session ID
      onSessionStart({
        ...formData,
        sessionId,
        years_of_experience: Number(formData.years_of_experience)
      });
    }
  };
  
  // Find the current assessment type object
  const currentAssessmentType = assessmentTypes.find(type => type.value === formData.assessment_type);
  
  return (
    <div className="agent-config-form">
      <div className="form-header">
        <h2>Configure Assessment Session</h2>
        <p>Provide the details below to customize your assessment experience</p>
      </div>
      
      <form onSubmit={handleSubmit}>
        {/* Basic info section */}
        <div className="form-section">
          <h3 className="section-title">Basic Information</h3>
          
          <div className="form-group">
            <label htmlFor="name">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                <circle cx="12" cy="7" r="4"/>
              </svg>
              Your Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter your full name"
              className={errors.name ? 'error' : ''}
            />
            {errors.name && <div className="error-message">{errors.name}</div>}
          </div>
          
          <div className="form-group">
            <label htmlFor="current_role">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="2" y="7" width="20" height="14" rx="2" ry="2"/>
                <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/>
              </svg>
              Current Role
            </label>
            <input
              type="text"
              id="current_role"
              name="current_role"
              value={formData.current_role}
              onChange={handleChange}
              placeholder="Enter your current role"
              className={errors.current_role ? 'error' : ''}
            />
            {errors.current_role && <div className="error-message">{errors.current_role}</div>}
          </div>
          
          <div className="form-group">
            <label htmlFor="years_of_experience">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12 6 12 12 16 14"/>
              </svg>
              Years of Experience
            </label>
            <input
              type="number"
              id="years_of_experience"
              name="years_of_experience"
              value={formData.years_of_experience}
              onChange={handleChange}
              placeholder="Enter years of experience"
              min="0"
              className={errors.years_of_experience ? 'error' : ''}
            />
            {errors.years_of_experience && <div className="error-message">{errors.years_of_experience}</div>}
          </div>
          
          <div className="form-group">
            <label htmlFor="target_role">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M18 8h1a4 4 0 0 1 0 8h-1"/>
                <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"/>
                <line x1="6" y1="1" x2="6" y2="4"/>
                <line x1="10" y1="1" x2="10" y2="4"/>
                <line x1="14" y1="1" x2="14" y2="4"/>
              </svg>
              Target Role
            </label>
            <input
              type="text"
              id="target_role"
              name="target_role"
              value={formData.target_role}
              onChange={handleChange}
              placeholder="Enter your target role"
              className={errors.target_role ? 'error' : ''}
            />
            {errors.target_role && <div className="error-message">{errors.target_role}</div>}
          </div>
          
          <div className="form-group">
            <label>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
              </svg>
              Assessment Type
            </label>
            
            <div className="assessment-type-pills">
              {assessmentTypes.map(type => (
                <div 
                  key={type.value}
                  className={`assessment-pill ${formData.assessment_type === type.value ? 'active' : ''}`}
                  onClick={() => handleAssessmentTypeSelect(type.value)}
                >
                  <span className="pill-icon">{type.icon}</span>
                  {type.label}
                </div>
              ))}
            </div>
            
            {currentAssessmentType && (
              <div className="assessment-info">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"/>
                  <line x1="12" y1="16" x2="12" y2="12"/>
                  <line x1="12" y1="8" x2="12.01" y2="8"/>
                </svg>
                {currentAssessmentType.description}
              </div>
            )}
          </div>
        </div>
        
        {/* Compulsory Questions Section */}
        <div className="form-section">
          <h3 className="section-title">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"/>
              <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
              <line x1="12" y1="17" x2="12.01" y2="17"/>
            </svg>
            Compulsory Questions
          </h3>
          
          {/* Questions List */}
          {formData.compulsory_questions.length > 0 ? (
            <div className="questions-list">
              {formData.compulsory_questions.map((q, index) => (
                <div key={index} className="question-item">
                  <div className="question-content">
                    <div className="question-header">
                      <span className="question-phase">{q.phase}</span>
                      <div className="question-actions">
                        <button 
                          type="button" 
                          className="action-button edit" 
                          onClick={() => editQuestion(index)}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                          </svg>
                        </button>
                        <button 
                          type="button" 
                          className="action-button delete" 
                          onClick={() => deleteQuestion(index)}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <polyline points="3 6 5 6 21 6"/>
                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
                            <line x1="10" y1="11" x2="10" y2="17"/>
                            <line x1="14" y1="11" x2="14" y2="17"/>
                          </svg>
                        </button>
                      </div>
                    </div>
                    <div className="question-text">{q.question}</div>
                    <div className="question-expected">
                      <strong>Expected:</strong> {q.expected_answer.length > 60 ? 
                        `${q.expected_answer.substring(0, 60)}...` : 
                        q.expected_answer}
                    </div>
                    {q.criteria && q.criteria.length > 0 && (
                      <div className="question-criteria">
                        <strong>Criteria:</strong> {q.criteria.length} item(s)
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-questions">
              <p>No compulsory questions added yet.</p>
            </div>
          )}
          
          {/* Question Form */}
          {showQuestionForm ? (
            <div className="question-form">
              <div className="question-form-header">
                <h4>{editingIndex >= 0 ? 'Edit Question' : 'Add Question'}</h4>
                <button 
                  type="button" 
                  className="close-form-button" 
                  onClick={resetQuestionForm}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"/>
                    <line x1="6" y1="6" x2="18" y2="18"/>
                  </svg>
                </button>
              </div>
              
              <div className="form-group">
                <label htmlFor="question">Question</label>
                <input
                  type="text"
                  id="question"
                  name="question"
                  value={currentQuestion.question}
                  onChange={handleQuestionChange}
                  placeholder="Enter the question"
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="expected_answer">Expected Answer</label>
                <textarea
                  id="expected_answer"
                  name="expected_answer"
                  value={currentQuestion.expected_answer}
                  onChange={handleQuestionChange}
                  placeholder="Describe what you expect in the answer"
                  rows="3"
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="phase">Phase</label>
                <select
                  id="phase"
                  name="phase"
                  value={currentQuestion.phase}
                  onChange={handleQuestionChange}
                >
                  {phaseOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="form-group">
                <label>Criteria (optional)</label>
                {currentQuestion.criteria.map((criterion, index) => (
                  <div key={index} className="criteria-item">
                    <input
                      type="text"
                      value={criterion}
                      onChange={(e) => handleCriteriaChange(index, e.target.value)}
                      placeholder="Enter assessment criteria"
                    />
                    <button 
                      type="button" 
                      className="remove-criteria-button"
                      onClick={() => removeCriteria(index)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                      </svg>
                    </button>
                  </div>
                ))}
                <button 
                  type="button" 
                  className="add-criteria-button"
                  onClick={addCriteria}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="12" y1="5" x2="12" y2="19"/>
                    <line x1="5" y1="12" x2="19" y2="12"/>
                  </svg>
                  Add Criteria
                </button>
              </div>
              
              <div className="question-form-actions">
                <button 
                  type="button" 
                  className="cancel-button"
                  onClick={resetQuestionForm}
                >
                  Cancel
                </button>
                <button 
                  type="button" 
                  className="save-button"
                  onClick={saveQuestion}
                >
                  Save Question
                </button>
              </div>
            </div>
          ) : (
            <button 
              type="button" 
              className="add-question-button"
              onClick={() => setShowQuestionForm(true)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
              Add Compulsory Question
            </button>
          )}
        </div>
        
        <button type="submit" className="start-session-button">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"/>
            <path d="M12 8l4 4-4 4M8 12h7"/>
          </svg>
          Start Assessment Session
        </button>
      </form>
    </div>
  );
};

export default AgentConfigForm; 