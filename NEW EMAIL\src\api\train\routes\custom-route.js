'use strict';

/**
 * train custom router
 */
 module.exports = {
	routes: [
	 
	  { 
		method: 'POST',
		path: '/linkextrack', 
		handler: 'custom-controller.linkExtract',
		config: {
			policies: [
				'global::user-details-populate',
				'global::update-org-before-api-call',
				'global::usage-validation',
			]
		}
	  },
	  { 
		method: 'POST',
		path: '/v3/train', 
		handler: 'custom-controller.train',
		config: {
			policies: [
				'global::user-details-populate',
				'global::update-org-before-api-call',
				'global::usage-validation',
			]
		}
	  },
	]
  }