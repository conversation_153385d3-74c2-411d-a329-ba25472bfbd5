const _ = require("lodash");

module.exports = {
  async updateOrgWithPlan(organization, plan) {
    try {
      // Update organization with the plan details
      await strapi.query("api::organization.organization").update({
        where: { id: organization?.id },
        data: {
          subscription: "subscribed",
          plan: plan.id,
        },
      });
	  const monthlyUsage = await strapi
        .query("api::monthly-usage.monthly-usage")
        .findOne({
          where: { organization: organization?.id },
        });
	  if(!monthlyUsage){
		await strapi
        .query("api::monthly-usage.monthly-usage")
        .create({
          data: {
            organization: organization?.id,
            usage_quota: plan.allowed_queries,
          },
        });
	  }
	  else{
		await strapi
        .query("api::monthly-usage.monthly-usage")
        .update({
          where: { organization: organization?.id },
          data: {
            usage_quota: monthlyUsage.usage_quota + plan.allowed_queries,
          },
        });
	  }

    } catch (e) {
      console.log("Org Update failed", e);
    }
  },
};
