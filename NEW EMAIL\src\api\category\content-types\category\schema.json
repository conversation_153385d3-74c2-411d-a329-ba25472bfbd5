{"kind": "collectionType", "collectionName": "categories", "info": {"singularName": "category", "pluralName": "categories", "displayName": "Category", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "description": {"type": "text"}, "icon": {"type": "string", "required": false, "default": null}, "playlists": {"type": "relation", "relation": "manyToMany", "target": "api::playlist.playlist", "mappedBy": "categories"}}}