{"kind": "collectionType", "collectionName": "coupon_redemption_logs", "info": {"singularName": "coupon-redemption-log", "pluralName": "coupon-redemption-logs", "displayName": "Coupon Redemption Log", "description": "Records each instance of a coupon being redeemed by an organization."}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"coupon_used": {"type": "relation", "relation": "manyToOne", "target": "api::coupon.coupon", "inversedBy": "redemption_logs", "required": true}, "organization": {"type": "relation", "relation": "manyToOne", "target": "api::organization.organization", "required": true}, "redemption_date": {"type": "datetime", "required": true}, "credits_applied_at_redemption": {"type": "integer", "required": true, "min": 0}}}