import {
  <PERSON><PERSON>,
  Chip,
  ListItem,
  ListItemText,
  Stack,
  Typography,
} from "@mui/material";
import SupportAgent from "@mui/icons-material/SupportAgent";
import { useSearchParams } from "react-router-dom";
import { formatDistanceToNow } from "date-fns";
import WebAssetIcon from "@mui/icons-material/WebAsset";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";

export default function SessionItem({
  apiSource,
  question,
  knowledgeBaseName,
  sessionId,
  createdAt,
}) {
  const [searchParams, setSearchParams] = useSearchParams();

  const getSourceImage = (type) => {
    const lowerCaseType = type.toLowerCase();
    if (lowerCaseType.startsWith("whatsapp"))
      return <WhatsAppIcon className="w-18 h-18" />;
    else if (lowerCaseType === "chatbot")
      return <WebAssetIcon className="w-16 h-16" />;
  };

  return (
    <ListItem
      onClick={() => setSearchParams({ sessionId })}
      className={`hover:bg-gray-200 cursor-pointer ${
        searchParams.get("sessionId") == sessionId ? "bg-gray-200" : ""
      }`}
    >
      <ListItemText
        primary={
          <Stack direction={"row"} gap={1} alignItems={"center"}>
            <Avatar className="text-white bg-base-purple w-28 h-28">
              {getSourceImage(apiSource)}
            </Avatar>
            <Typography className="text-ellipsis line-clamp-2">
              {question}
            </Typography>
          </Stack>
        }
        secondary={
          <Stack direction={"row"} spacing={1} flexWrap={"wrap"} mt={2}>
            <Chip
              icon={
                <SupportAgent
                  color="red"
                  className="w-16 h-16 text-base-purple"
                />
              }
              size="small"
              label={knowledgeBaseName}
              variant="filled"
              className="text-[11px] bg-base-purple/10 text-base-purple"
            />
            <Typography className="text-[12px] text-grey-500 mt-8">
              {formatDistanceToNow(new Date(createdAt), {
                addSuffix: true,
              })}
            </Typography>
          </Stack>
        }
      />
    </ListItem>
  );
}
