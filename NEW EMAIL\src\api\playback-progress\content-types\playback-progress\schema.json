{"kind": "collectionType", "collectionName": "playback_progress", "info": {"singularName": "playback-progress", "pluralName": "playback-progresses", "displayName": "Playback Progress", "description": "Per-user progress for each track"}, "options": {"draftAndPublish": false}, "attributes": {"user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "track": {"type": "relation", "relation": "manyToOne", "target": "api::file-results-model.file-results-model"}, "positionSec": {"type": "decimal", "required": true}, "durationSec": {"type": "decimal", "required": true}, "status": {"type": "enumeration", "enum": ["in-progress", "completed", "abandoned"], "default": "in-progress"}, "deviceId": {"type": "string"}}}