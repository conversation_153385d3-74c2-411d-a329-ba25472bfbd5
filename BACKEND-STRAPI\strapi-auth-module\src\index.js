'use strict';

const axios = require("axios");
const { createOrgForuser } = require("./helpers/create-organization-for-user");
const { isGenericEmail } = require("./helpers/utils");
const { init, track } = require("@amplitude/analytics-node");
const emailService = require("./helpers/email-service");

const marketingContactsService = require("./services/marketing-contacts");

module.exports = {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  register(/*{ strapi }*/) {
    init(process.env.AMPLITUDE_API_KEY);
  },

  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  async bootstrap({ strapi }) {
    // Email system ready - using Strapi's built-in email with Postmark
    strapi.log.info('✅ Email system initialized with Postmark');
    strapi.db.lifecycles.subscribe({
      models: ['plugin::users-permissions.user'],

      async afterCreate(event) {
          try{
            console.log('🚨 DEBUG: User afterCreate lifecycle triggered!');
            console.log('🚨 DEBUG: Event result:', {
              id: event.result.id,
              email: event.result.email,
              confirmed: event.result.confirmed,
              provider: event.result.provider
            });

            // CRITICAL FIX: Force user to be unconfirmed for local registrations
            if (event.result.provider === 'local' && event.result.confirmed === true) {
              console.log('🚨 DEBUG: User was auto-confirmed, forcing to unconfirmed!');
              strapi.log.info('🚨 CRITICAL FIX: User was auto-confirmed, forcing to unconfirmed', {
                userId: event.result.id,
                email: event.result.email
              });

              // Force update user to be unconfirmed
              await strapi.db.query("plugin::users-permissions.user").update({
                where: { id: event.result.id },
                data: { confirmed: false }
              });

              console.log('🚨 DEBUG: User forced to unconfirmed status');
              strapi.log.info('✅ User forced to unconfirmed status');
            }

            const user = await strapi.query("plugin::users-permissions.user").findOne({
              where: { id: event.result.id },
              populate: { organization: true },
            });
            var org;
              if (!user.organization) {
                if (isGenericEmail(user.email)) {
                  const body = {
                    name: user.username,
                    type: "individual",
                    users: { connect: [user.id] },
                  };
                  org = await createOrgForuser(body);
                  //  await strapi.controllers["api::organization.organization"].create(
                  //   ctx
                  // );
                } else {
                  const body = {
                    name: user.email.split("@")[1].split(".")[0],
                    type: "organization",
                    users: { connect: [user.id] },
                  };
                  org = await createOrgForuser(body);
                  //  await strapi.controllers["api::organization.organization"].create(
                  //   ctx
                  // );
                }
              }
              if (user.confirmed) {
                // Add user to marketing contacts using the new service
                try {
                  if (marketingContactsService.isEnabled()) {
                    await marketingContactsService.addContact({
                      email: user.email,
                      firstName: user.username,
                      customFields: {
                        organization: user.organization?.name ?? org.name
                      }
                    });
                  } else {
                    strapi.log.info('📊 Marketing contacts service disabled - skipping contact addition');
                  }
                } catch (error) {
                  strapi.log.error('❌ Failed to add user to marketing contacts:', error);
                }
              }
                    else {
                      // User is not confirmed, send confirmation email
                      strapi.log.info('📧 User not confirmed, preparing to send confirmation email', {
                        userId: user.id,
                        email: user.email,
                        confirmed: user.confirmed,
                        hasConfirmationToken: !!event.result.confirmationToken
                      });

                      try {
                        // Generate confirmation token if not exists
                        let confirmationToken = event.result.confirmationToken;
                        if (!confirmationToken) {
                          const crypto = require('crypto');
                          confirmationToken = crypto.randomBytes(20).toString('hex');

                          // Update user with confirmation token
                          await strapi.db.query("plugin::users-permissions.user").update({
                            where: { id: user.id },
                            data: { confirmationToken }
                          });

                          strapi.log.info('🔑 Generated confirmation token for user', {
                            userId: user.id,
                            email: user.email
                          });
                        }

                        // Send the confirmation email using new email service
                        const emailService = require('./helpers/email-service');
                        const confirmationUrl = `${process.env.FRONTEND_URL}/auth/email-confirmation?confirmation=${confirmationToken}`;

                        strapi.log.info('📧 Sending confirmation email via Postmark', {
                          email: user.email,
                          name: user.username,
                          confirmationUrl: confirmationUrl,
                          confirmationToken: confirmationToken
                        });

                        await emailService.sendConfirmationEmail({
                          email: user.email,
                          name: user.username,
                          confirmationUrl: confirmationUrl
                        });

                        strapi.log.info('✅ Confirmation email sent successfully via Postmark');
                      } catch (error) {
                        strapi.log.error('❌ Error sending confirmation email via Postmark:', error);

                        // Fallback to original method
                        try {
                          await strapi.plugins['users-permissions'].services.user.sendConfirmationEmail(event.result);
                          strapi.log.info('✅ Confirmation email sent via fallback SendGrid method');
                        } catch (fallbackError) {
                          strapi.log.error('❌ Both Postmark and SendGrid confirmation emails failed:', fallbackError);
                        }
                      }
                  }
                
                    track('Sign up', undefined, {
                      user_id: user.email,
                    });
                    // amplitude.track({
                    //   event_type: 'Sign Up', })
              // if(user.provider==='google'){

              //   axios.put('https://api.sendgrid.com/v3/marketing/contacts',{
              //     "contacts": [
              //       {
              //         "email": user.email,
              //         "first_name": user.username,
              //         "custom_fields": {
              //           "organization": user.organization.name,
              //           "isEmailConfirmed": true,
              //           "isUserBlocked": false
              //         }
              //       }
              //     ]
              //   },
              //   {headers: {
              //     'Authorization': `Bearer ${process.env.SENDGRID_API_KEY}`,
              //     'Content-Type': 'application/json'
              //   }}
              //   ).catch((e)=>{
              //     console.log(e);
              //   });
              // }
          }catch(e){
            console.log(e);
          }
      },
      async beforeUpdate(event) {
        const { data, where, select, populate } = event.params;
        if (data.confirmed) {
          const user =   await strapi.query("plugin::users-permissions.user").findOne({
            where: { id: where.id },
            populate: { organization: true },
          });
           
         
          // Send welcome email when user confirms their email
          try {
            await emailService.sendWelcomeEmail({
              email: user.email,
              name: user.username
            });
            strapi.log.info('✅ EMAIL CONFIRMATION: Welcome email sent successfully to:', user.email);
          } catch (error) {
            strapi.log.error('❌ EMAIL CONFIRMATION ERROR: Failed to send welcome email:', error.message);
          }

          // Add confirmed user to marketing contacts using the new service
          try {
            await marketingContactsService.addContact({
              email: user.email,
              firstName: user.username,
              customFields: {
                organization: user.organization?.name || 'Unknown'
              }
            });
          } catch (error) {
            strapi.log.error('Failed to add confirmed user to marketing contacts:', error);
          }
              }
      },

    });
    }
};
