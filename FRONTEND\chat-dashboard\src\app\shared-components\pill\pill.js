import React from 'react';

export const Pill = ({ text }) => {
  return (
    <div className="relative inline-flex items-center px-4 py-2 text-white font-bold rounded-sm overflow-hidden text-xs from-base-purple to-secondary-color bg-gradient-to-r">
      {text}
      <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent to-white opacity-25 rounded-full transform translate-x-[-100%] animate-shine"></div>
    </div>
  );
};


export const PillLabel = ({ text }) => {
  return (
    <div className="relative inline-flex items-center px-4 py-2 text-white font-medium rounded-sm overflow-hidden text-md bg-base-purple">
      {text}
      <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent to-white opacity-25 rounded-full transform translate-x-[-100%] animate-shine"></div>
    </div>
  );
};

export const ColoredPillLabel = ({ text , bgColor = 'black' , textColor= 'red'}) => {
  return (
    <div className="relative inline-flex items-center px-4 py-2 font-regular rounded-sm overflow-hidden text-xs"
     style={{
      backgroundColor:bgColor,
      // Changed textColor to color
      color:textColor,
     }}>
      <span style={{
        // Changed textColor to color
        color:textColor,
      }}>{text}</span>
      <div className="absolute top-0 left-0 w-full h-full rounded-full transform translate-x-[-100%]"></div>
    </div>
  );
};

export const PillImage = ({ children, text , bgColor = 'green' , textColor = 'black' }) => {
  return (
    <div className="relative inline-flex items-center px-8 py-2 text-white font-bold rounded-sm overflow-hidden text-xs"
      style={{
        backgroundColor:bgColor,
        textColor:textColor,
        color:textColor,
      }}>
      <div className='mr-2'>{children}</div>
      <span className='font-normal text-sm'>{text}</span>
      <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent to-white opacity-25 rounded-full transform translate-x-[-100%] animate-shine"></div>
    </div>
  );
};


export const DisabledPill = ({ text }) => {
  return (
    <div className="relative inline-flex items-center px-4 py-2 text-white font-bold rounded-sm overflow-hidden text-xs from-red-400 to-base-purple bg-gradient-to-r">
      {text}
      <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent to-white opacity-25 rounded-full transform translate-x-[-100%] animate-shine"></div>
    </div>
  );
};

export const PricingPill = ({ text }) => {
  return (
    <div className="relative inline-flex bg-white items-center px-8 py-4 text-light-text font-regular rounded-lg overflow-hidden text-md border border-light-border-color">
      {text}
      <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent to-white opacity-25 rounded-full transform translate-x-[-100%] animate-shine"></div>
    </div>
  );
};

export default Pill;


