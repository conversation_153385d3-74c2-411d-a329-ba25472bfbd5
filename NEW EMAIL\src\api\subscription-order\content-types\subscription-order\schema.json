{"kind": "collectionType", "collectionName": "subscription_orders", "info": {"singularName": "subscription-order", "pluralName": "subscription-orders", "displayName": "Subscription Order", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"type": {"type": "enumeration", "enum": ["subscription", "renew"]}, "organization": {"type": "relation", "relation": "manyToOne", "target": "api::organization.organization", "inversedBy": "subscription_orders"}, "status": {"type": "enumeration", "enum": ["pending", "active", "processing", "trialing", "paused", "past_due", "cancelled", "unpaid", "incomplete", "incomplete_expired", "failed", "renewing", "expired", "refunded", "disputed", "pendingCancellation"]}, "current_period_start": {"type": "datetime"}, "current_period_end": {"type": "datetime"}, "isPaid": {"type": "boolean", "default": false, "required": false}, "stripeSessionId": {"type": "string"}, "subscriptionId": {"type": "string"}, "invoice": {"type": "string"}, "plan": {"type": "relation", "relation": "oneToOne", "target": "api::plan.plan"}, "plantype": {"type": "enumeration", "enum": ["monthly", "yearly", "one-time"], "default": "monthly"}, "isTrial": {"type": "boolean", "default": false, "required": false}, "trial_start_date": {"type": "datetime"}, "trial_end_date": {"type": "datetime"}, "purchase_platform": {"type": "enumeration", "enum": ["web", "apple", "google", "stripe"], "default": "stripe"}, "receipt_data": {"type": "text", "private": true}}}