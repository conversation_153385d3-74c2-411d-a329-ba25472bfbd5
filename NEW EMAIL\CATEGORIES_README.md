# 📂 Categories System - Simple Guide

A simple category system to organize your playlists. Each playlist can belong to multiple categories, and each category can contain multiple playlists.

## 🎯 What You Can Do

- ✅ Create categories (Music, Podcasts, Education, etc.)
- ✅ Assign categories to playlists
- ✅ View playlists organized by categories
- ✅ Update category assignments

---

## 🔧 Setup Required

**Important:** Before testing, enable permissions in Strapi Admin:

1. Go to `http://localhost:1337/admin`
2. Navigate to **Settings** → **Users & Permissions Plugin** → **Roles** → **Public**
3. Enable these permissions for **Category**:
   - ✅ find
   - ✅ findOne  
   - ✅ create
   - ✅ update
   - ✅ delete
4. Click **Save**

---

## 📋 Category Management

### 1️⃣ Create a Category

**Method:** `POST`  
**URL:** `http://localhost:1337/api/categories`

**Headers:**
```
Content-Type: application/json
```

**Option A - With Icon (Optional):**
```json
{
  "data": {
    "name": "Music",
    "description": "Music and audio content",
    "icon": "🎵"
  }
}
```

**Option B - Without Icon:**
```json
{
  "data": {
    "name": "Podcasts",
    "description": "Podcast episodes and series"
  }
}
```

**Option C - Minimal (Name Only):**
```json
{
  "data": {
    "name": "Education"
  }
}
```

**What happens:** Creates a new category that you can assign to playlists. Only the `name` field is required!

---

### 2️⃣ View All Categories

**Method:** `GET`  
**URL:** `http://localhost:1337/api/categories`

**Headers:** None required

**What you get:** List of all categories with their IDs (you'll need these IDs to assign categories to playlists).

---

### 3️⃣ View Single Category

**Method:** `GET`  
**URL:** `http://localhost:1337/api/categories/1`

Replace `1` with the actual category ID.

**What you get:** Details of a specific category.

---

### 4️⃣ Update a Category

**Method:** `PUT`  
**URL:** `http://localhost:1337/api/categories/1`

**Headers:**
```
Content-Type: application/json
```

**With Icon:**
```json
{
  "data": {
    "name": "Music & Audio",
    "description": "Updated description",
    "icon": "🎶"
  }
}
```

**Without Icon:**
```json
{
  "data": {
    "name": "Music & Audio",
    "description": "Updated description"
  }
}
```

**Name Only:**
```json
{
  "data": {
    "name": "Music & Audio"
  }
}
```

---

## 🎵 Playlist & Categories

### 5️⃣ Create Playlist with Categories

**Method:** `POST`  
**URL:** `http://localhost:1337/api/playlists`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "data": {
    "name": "Top Hits 2024",
    "description": "Best songs of the year",
    "type": "featured",
    "public": true,
    "categories": [1, 2, 3]
  }
}
```

**Important:** Use the actual category IDs from step 2.

---

### 6️⃣ Add Categories to Existing Playlist

**Method:** `PUT`  
**URL:** `http://localhost:1337/api/playlists/1`

Replace `1` with the actual playlist ID.

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "data": {
    "categories": [1, 2]
  }
}
```

**What happens:** Assigns the playlist to the specified categories.

---

### 7️⃣ View Playlists with Their Categories

**Method:** `GET`  
**URL:** `http://localhost:1337/api/playlists?populate=categories`

**What you get:** All playlists showing which categories they belong to.

---

### 8️⃣ View Single Playlist with Categories

**Method:** `GET`  
**URL:** `http://localhost:1337/api/playlists/1?populate=categories`

**What you get:** Specific playlist with its assigned categories.

---

### 9️⃣ View Category with Its Playlists

**Method:** `GET`  
**URL:** `http://localhost:1337/api/categories/1?populate=playlists`

**What you get:** Specific category showing all playlists that belong to it.

---

## 🚀 Quick Start Example

Follow these steps to test the complete workflow:

### Step 1: Create Categories
Create 3 categories using the **Create a Category** request above:

**Category 1 - With Icon:**
```json
{"data": {"name": "Music", "description": "Music content", "icon": "🎵"}}
```

**Category 2 - Without Icon:**
```json
{"data": {"name": "Podcasts", "description": "Podcast episodes"}}
```

**Category 3 - Minimal:**
```json
{"data": {"name": "Education"}}
```

### Step 2: Check Category IDs
Use **View All Categories** to see the IDs (usually 1, 2, 3).

### Step 3: Create Playlist with Categories
Use **Create Playlist with Categories** with:
```json
{
  "data": {
    "name": "My First Playlist",
    "description": "Testing categories",
    "type": "featured",
    "categories": [1, 2]
  }
}
```

### Step 4: Verify
Use **View Playlists with Their Categories** to see the result.

---

## 📝 Important Notes

- **Required Field:** Only `name` is required when creating categories
- **Optional Fields:** `description` and `icon` are completely optional
- **Category IDs:** Always use the numeric ID (1, 2, 3), not the name
- **Array Format:** Categories must be in array format `[1, 2, 3]`
- **Replace All:** When updating categories, it replaces all existing ones
- **Many-to-Many:** One playlist can have multiple categories, one category can have multiple playlists

---

## 🔍 Troubleshooting

**Getting "Forbidden access" error?**
→ Check that permissions are enabled in Strapi Admin (see Setup Required section)

**Can't find category/playlist IDs?**
→ Use the "View All" requests to get the correct IDs

**Categories not showing in playlist?**
→ Make sure you're using `?populate=categories` in the URL

---

## 📊 Response Examples

**Category Response (with icon):**
```json
{
  "data": {
    "id": 1,
    "attributes": {
      "name": "Music",
      "description": "Music and audio content", 
      "icon": "🎵",
      "createdAt": "2024-01-15T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:00:00.000Z"
    }
  }
}
```

**Category Response (without icon):**
```json
{
  "data": {
    "id": 2,
    "attributes": {
      "name": "Podcasts",
      "description": "Podcast episodes", 
      "icon": null,
      "createdAt": "2024-01-15T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:00:00.000Z"
    }
  }
}
```

**Playlist with Categories:**
```json
{
  "data": {
    "id": 1,
    "attributes": {
      "name": "My Playlist",
      "type": "featured",
      "categories": {
        "data": [
          {
            "id": 1,
            "attributes": {
              "name": "Music",
              "icon": "🎵"
            }
          }
        ]
      }
    }
  }
}
```

That's it! You now have a fully functional category system for organizing your playlists. 🎉 