/**
 * Test Ticket Notification Email System
 * Tests the modern email service with ticket notifications
 */

const modernEmailService = require('../src/helpers/modern-email-service');

async function testTicketNotifications() {
  console.log('🎫 TESTING TICKET NOTIFICATION SYSTEM');
  console.log('='.repeat(60));
  
  const testResults = {
    providerAvailability: { status: 'pending', error: null },
    templateEmail: { status: 'pending', error: null },
    ticketNotification: { status: 'pending', error: null }
  };

  // Test 1: Check Provider Availability
  console.log('\n🔌 Testing Provider Availability...');
  try {
    const providers = modernEmailService.getAvailableProviders();
    console.log('✅ Available providers:', providers);
    
    if (providers.includes('postmark')) {
      console.log('✅ Postmark provider is available');
      testResults.providerAvailability.status = 'passed';
    } else {
      throw new Error('Postmark provider not available');
    }
  } catch (error) {
    console.error('❌ Provider availability check failed:', error.message);
    testResults.providerAvailability.status = 'failed';
    testResults.providerAvailability.error = error.message;
  }

  // Test 2: Template Email System
  console.log('\n📧 Testing Template Email System...');
  try {
    const result = await modernEmailService.sendTemplateEmail({
      templateName: 'ticket-notification',
      to: ['<EMAIL>'],
      templateData: {
        name: 'Test User',
        title: 'Test Ticket',
        description: 'This is a test ticket for email validation',
        agent_name: 'Test Agent',
        customer_email: '<EMAIL>',
        ticket_url: 'https://podycy.com/tickets/test123'
      },
      provider: 'postmark'
    });
    
    console.log('✅ Template email sent successfully:', result);
    testResults.templateEmail.status = 'passed';
  } catch (error) {
    console.error('❌ Template email failed:', error.message);
    testResults.templateEmail.status = 'failed';
    testResults.templateEmail.error = error.message;
  }

  // Test 3: Ticket Notification Helper
  console.log('\n🎫 Testing Ticket Notification Helper...');
  try {
    const result = await modernEmailService.sendTicketNotificationEmail({
      emails: ['<EMAIL>', '<EMAIL>'],
      name: 'Organization Admin',
      title: 'Customer Support Request',
      description: 'Customer is experiencing login issues and needs assistance.',
      agent_name: 'Support Bot',
      customer_email: '<EMAIL>',
      ticket_url: 'https://podycy.com/tickets/12345'
    });
    
    console.log('✅ Ticket notification sent successfully:', result);
    testResults.ticketNotification.status = 'passed';
  } catch (error) {
    console.error('❌ Ticket notification failed:', error.message);
    testResults.ticketNotification.status = 'failed';
    testResults.ticketNotification.error = error.message;
  }

  // Test Summary
  console.log('\n📊 TEST SUMMARY');
  console.log('='.repeat(60));
  
  const totalTests = Object.keys(testResults).length;
  const passedTests = Object.values(testResults).filter(result => result.status === 'passed').length;
  const failedTests = Object.values(testResults).filter(result => result.status === 'failed').length;
  
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests} ✅`);
  console.log(`Failed: ${failedTests} ❌`);
  
  if (failedTests > 0) {
    console.log('\n❌ FAILED TESTS:');
    Object.entries(testResults).forEach(([testName, result]) => {
      if (result.status === 'failed') {
        console.log(`  - ${testName}: ${result.error}`);
      }
    });
  }
  
  console.log('\n🔧 CONFIGURATION CHECK:');
  console.log(`POSTMARK_API_KEY: ${process.env.POSTMARK_API_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log(`POSTMARK_FROM_EMAIL: ${process.env.POSTMARK_FROM_EMAIL || '<EMAIL> (default)'}`);
  console.log(`POSTMARK_FROM_NAME: ${process.env.POSTMARK_FROM_NAME || 'Podycy Team (default)'}`);
  
  console.log('\n💡 NEXT STEPS:');
  console.log('1. Ensure POSTMARK_API_KEY is set in environment variables');
  console.log('2. Verify Postmark account is active and not under review');
  console.log('3. Test with real organization users in Scrumboard ticket creation');
  console.log('4. Monitor email delivery in Postmark dashboard');
  
  return {
    success: failedTests === 0,
    totalTests,
    passedTests,
    failedTests,
    results: testResults
  };
}

// Run tests if called directly
if (require.main === module) {
  testTicketNotifications()
    .then(results => {
      console.log('\n🏁 Ticket notification testing completed');
      process.exit(results.success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test runner failed:', error);
      process.exit(1);
    });
}

module.exports = { testTicketNotifications };
