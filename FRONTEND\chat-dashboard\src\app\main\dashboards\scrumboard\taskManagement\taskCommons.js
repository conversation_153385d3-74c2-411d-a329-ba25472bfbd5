import React from "react";

const priorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "bg-red-500";
      case "medium":
        return "bg-yellow-500";
      case "low":
        return "bg-green-500";
      default:
        return "";
    }
  };

  export const Card = ({ children, className = "" }) => (
    <div className={`bg-white rounded-lg shadow-sm ${className}`}>{children}</div>
  );

  export const CardHeader = ({ children, className = "" }) => (
    <div className={`p-4 border-b ${className}`}>{children}</div>
  );

  export const CardContent = ({ children, className = "" }) => (
    <div className={`p-4 ${className}`}>{children}</div>
  );

  export const Avatar = ({ className = "" }) => (
    <div className={`w-8 h-8 rounded-full bg-gray-300 ${className}`} />
  );

  export const Badge = ({ children, className = "" }) => (
    <span className={`px-2 py-1 rounded-full text-xs font-semibold ${className}`}>
      {children}
    </span>
  );