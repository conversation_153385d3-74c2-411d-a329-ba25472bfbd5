import { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import _ from "@lodash";
import FusePageSimple from "@fuse/core/FusePageSimple";
import { motion } from "framer-motion";
import history from "@history";
import { getUsage, selectUsage } from "app/store/usageSlice";
import UsageDashboardAppHeader from "./HomeDashboardAppHeader";
import CreditUsage from "./widgets/CreditUsage";
import { selectUser } from "app/store/userSlice";
import TrainingUsage from "./widgets/TrainingUsage";
import BarGraphWidget from "./widgets/BarGraphWidget";
import reducer from "./store";
import withReducer from "app/store/withReducer";
import { getHomeData, selectHomeData } from "./store/homeSlice";
import DataCard from "./widgets/DataCard";
import TableCardComponent from "app/shared-components/table/tableCard";
import LoadingSpinner from "app/shared-components/loading-spinner/loading-spinner";
import { Typography } from "@mui/material";
import TicketsWidget from "./widgets/TicketsWidget";

function HomeDashboardApp() {
  const dispatch = useDispatch();
  const usage = useSelector(selectUsage);
  const user = useSelector(selectUser);
  const homeData = useSelector(selectHomeData);

  useEffect(() => {
    if (
      !(
        user.data?.organization?.subscription === "subscribed" ||
        user.data?.organization?.subscription === "trial"
      )
    ) {
      history.push("subscription");
    }
  }, []);
  useEffect(() => {
    dispatch(getUsage());
    dispatch(getHomeData());
  }, [dispatch]);

  return (
    <FusePageSimple
      header={<UsageDashboardAppHeader />}
      content={
        <div className="w-full h-full pl-8 pr-8 md:px-32 pb-24">
          {useMemo(() => {
            const container = {
              show: {
                transition: {
                  staggerChildren: 0.06,
                },
              },
            };

            const item = {
              hidden: { opacity: 0, y: 20 },
              show: { opacity: 1, y: 0 },
            };

            return (
              (_.isEmpty(usage && homeData) && (
                <div className="flex flex-col justify-center items-center w-full h-full">
                  <LoadingSpinner className={"w-60 h-60"} color={"#6200ea"} />
                  <Typography className={"mt-16 text-lg font-regular"}>
                    Calm before the AI storm, loading your dashboard..
                  </Typography>
                </div>
              )) ||
              (!_.isEmpty(usage && homeData) && (
                <motion.div
                  className="w-full flex flex-row"
                  variants={container}
                  initial="hidden"
                  animate="show"
                >
                  <div className="grid grid-cols-1  gap-32 mt-32 w-full">
                    <div className="flex flex-col justify-start gap-y-16 2xl:flex-row xl:flex-row lg:flex-row md:flex-row sm:flex-row">
                      <div className="w-1/2 mx-16 overflow-y-auto">
                        <TicketsWidget
                          className="mt-16"
                          organizationId={user.data?.organization?.id}
                        />
                      </div>
                      <div
                        className="flex flex-col w-1/2 mr-16 gap-y-32"
                        style={{ height: "400px" }}
                      >
                        <DataCard
                          title="Queries"
                          description="This is the count of the number of questions asked to the AI-agent"
                          value={Object.values(homeData.query_counts).reduce(
                            (a, b) => a + b,
                            0
                          )}
                          subTitle={"Last 7 days"}
                          graphData={homeData.query_counts}
                          color="#7F56D9"
                          showButtton={true}
                        />
                        <DataCard
                          title="Sources Shown"
                          description="This is the count of the number of urls shown to the user. Urls are shown only if your agent is trained with links"
                          value={Object.values(
                            homeData.links_produced_counts
                          ).reduce((a, b) => a + b, 0)}
                          subTitle={"Last 7 days"}
                          graphData={homeData.links_produced_counts}
                          color="#5c96c9"
                        />
                        <DataCard
                          title="Sources Clicked"
                          description="This is the count of the number of urls clicked by the user. This is only applicable if your agent is trained with links"
                          value={Object.values(
                            homeData.links_track_counts
                          ).reduce((a, b) => a + b, 0)}
                          subTitle={"Last 7 days"}
                          graphData={homeData.links_track_counts}
                          color="#80CFA9"
                        />
                      </div>
                    </div>
                    <div className="flex flex-col md:flex-row lg:flex-row xl:flex-row 2xl:flex-row">
                      <div className="w-full md:w-[67%] lg:w-[67%] xl:w-[67%] 2xl:w-[67%] xl:pr-24 2xl:pr-24 lg:-pr-24 md:pr-24 ">
                        <motion.div>
                          <BarGraphWidget
                            mainTitle="Main Title"
                            datasets={[
                              {
                                title: "Queries",
                                data: homeData.query_counts ?? {},
                                color: "#6200ea",
                              },
                              {
                                title: "Sources Shown",
                                data: homeData.links_produced_counts ?? {},
                                color: "#5c96c9",
                              },
                              {
                                title: "Sources Clicked",
                                data: homeData.links_track_counts ?? {},
                                color: "#80CFA9",
                              },
                            ]}
                          />
                        </motion.div>
                      </div>

                      <div className="flex-start w-full mt-16 md:mt-0 lg:mt-0 xl:mt-0 2xl:mt-0 md:w-[32%] lg:w-[32%] xl:w-[32%] 2xl:w-[32%]">
                        <TableCardComponent
                          data={homeData.top_reffered_links}
                        />
                      </div>
                    </div>

                    <div className="flex flex-row">
                      <motion.div
                        variants={item}
                        className="flex flex-col flex-auto mx-8 "
                      >
                        <CreditUsage />
                      </motion.div>
                      <motion.div
                        variants={item}
                        className="flex flex-col flex-auto mx-8"
                      >
                        <TrainingUsage />
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              ))
            );
          }, [usage, homeData])}
        </div>
      }
    />
  );
}

export default withReducer("homeDashboardApp", reducer)(HomeDashboardApp);
