/**
 * TEST EMAIL WITH CORRECT DOMAIN
 * Tests email functionality with ajentic.com domain to avoid Postmark restrictions
 */

const axios = require('axios');

async function testEmailWithCorrectDomain() {
  console.log('📧 TESTING EMAIL WITH CORRECT DOMAIN\n');
  console.log('='.repeat(60));

  const baseURL = 'http://127.0.0.1:1337';
  const timestamp = Date.now();
  
  // Use ajentic.com domain to match sender domain
  const testEmail = `test${timestamp}@ajentic.com`;
  const testUsername = `testuser${timestamp}`;
  const testPassword = 'TestPassword123!';
  
  try {
    console.log('📝 STEP 1: Testing user registration with correct domain...');
    console.log(`   Email: ${testEmail}`);
    console.log(`   Username: ${testUsername}`);
    console.log(`   Domain: ajentic.com (matches sender domain)`);
    
    const registerResponse = await axios.post(`${baseURL}/api/auth/local/register`, {
      username: testUsername,
      email: testEmail,
      password: testPassword,
      organization: 'individual'
    });

    if (registerResponse.data.user) {
      console.log('✅ User registration successful');
      console.log(`   User ID: ${registerResponse.data.user.id}`);
      console.log(`   Email: ${registerResponse.data.user.email}`);
      console.log(`   Confirmed: ${registerResponse.data.user.confirmed}`);
      
      if (registerResponse.data.jwt) {
        console.log('⚠️ JWT token returned (auto-confirmation bug may still exist)');
      } else {
        console.log('✅ No JWT token returned (confirmation required)');
      }
    }

    console.log('\n📧 STEP 2: Testing password reset with correct domain...');
    
    const forgotPasswordResponse = await axios.post(`${baseURL}/api/auth/forgot-password`, {
      email: testEmail
    });

    if (forgotPasswordResponse.data.ok) {
      console.log('✅ Password reset request successful');
      console.log('   Check server logs for reset URL');
      console.log('   Email should be sent without domain restrictions');
    }

    console.log('\n📊 DOMAIN COMPATIBILITY TEST RESULTS:');
    console.log('='.repeat(60));
    console.log(`✅ Sender domain: ajentic.com`);
    console.log(`✅ Recipient domain: ajentic.com`);
    console.log(`✅ Domain match: YES`);
    console.log(`✅ Postmark restrictions: BYPASSED`);
    
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('1. Use @ajentic.com addresses for testing during Postmark approval');
    console.log('2. Once Postmark account is approved, any domain will work');
    console.log('3. Consider adding domain validation in production');
    console.log('4. Monitor server logs for actual email delivery confirmation');

  } catch (error) {
    console.error('\n❌ TEST FAILED:');
    console.error('Error:', error.message);
    console.error('Response:', error.response?.data);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Server not running. Start with: npm start');
    }
  }
}

async function testEmailConnectorDirectly() {
  console.log('\n🔧 TESTING EMAIL CONNECTOR DIRECTLY\n');
  console.log('='.repeat(60));

  const baseURL = 'http://127.0.0.1:1337';
  
  try {
    // Test direct email sending via email connector
    const emailResponse = await axios.post(`${baseURL}/api/email-connector/send-template-email`, {
      templateName: 'welcome-email',
      to: '<EMAIL>',
      templateData: {
        name: 'Test User',
        username: 'testuser'
      },
      provider: 'postmark'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Direct email connector test successful');
    console.log('Response:', emailResponse.data);

  } catch (error) {
    console.error('❌ Direct email connector test failed:');
    console.error('Error:', error.message);
    console.error('Response:', error.response?.data);
  }
}

// Run both tests
async function runAllTests() {
  await testEmailWithCorrectDomain();
  await testEmailConnectorDirectly();
}

runAllTests();
