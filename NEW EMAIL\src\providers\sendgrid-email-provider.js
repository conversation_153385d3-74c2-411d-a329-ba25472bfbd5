const EmailProviderInterface = require('./email-provider-interface');
const sgMail = require('@sendgrid/mail');

/**
 * SendGrid Email Provider Implementation
 * Implements the EmailProviderInterface for SendGrid service
 */
class SendGridEmailProvider extends EmailProviderInterface {
  constructor(config = {}) {
    super(config);
    this.apiKey = config.apiKey || process.env.SENDGRID_API_KEY;
    this.defaultFrom = config.defaultFrom || process.env.SENDGRID_FROM_EMAIL || '<EMAIL>';
    this.defaultFromName = config.defaultFromName || 'PODYCY';
    this.initialized = false;
  }

  /**
   * Initialize SendGrid with API key
   */
  async initialize(config = {}) {
    try {
      const apiKey = config.apiKey || this.apiKey;
      
      if (!apiKey) {
        throw new Error('SendGrid API key is required');
      }

      sgMail.setApiKey(apiK<PERSON>);
      this.initialized = true;
      
      // Test the connection
      const connectionTest = await this.testConnection();
      if (!connectionTest) {
        throw new Error('Failed to connect to SendGrid');
      }

      strapi.log.info('✅ SendGrid Email Provider initialized successfully');
      return { success: true, provider: 'SendGrid' };
    } catch (error) {
      strapi.log.error('❌ SendGrid initialization failed:', error.message);
      throw error;
    }
  }

  /**
   * Send a single email using SendGrid
   */
  async sendEmail(emailData) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const {
        to,
        from,
        subject,
        text,
        html,
        templateId,
        templateData = {},
        attachments = []
      } = emailData;

      // Validate required fields
      if (!to) {
        throw new Error('Recipient email is required');
      }

      if (!subject && !templateId) {
        throw new Error('Subject is required when not using a template');
      }

      // Prepare the message
      const msg = {
        to: Array.isArray(to) ? to : [to],
        from: {
          email: from || this.defaultFrom,
          name: this.defaultFromName
        },
        subject: subject,
        ...(text && { text }),
        ...(html && { html }),
        ...(attachments.length > 0 && { attachments })
      };

      // Handle template-based emails
      if (templateId) {
        msg.templateId = templateId;
        msg.dynamicTemplateData = {
          ...templateData,
          // Add common template variables
          email_subject: subject,
          sender_name: this.defaultFromName
        };
        
        // Remove subject if using template (template will handle it)
        delete msg.subject;
      }

      const response = await sgMail.send(msg);
      
      strapi.log.info(`✅ Email sent successfully via SendGrid to: ${Array.isArray(to) ? to.join(', ') : to}`);
      
      return {
        success: true,
        messageId: response[0].headers['x-message-id'],
        provider: 'SendGrid',
        statusCode: response[0].statusCode,
        recipients: Array.isArray(to) ? to : [to]
      };

    } catch (error) {
      strapi.log.error('❌ SendGrid send error:', error.message);
      
      // Extract useful error information
      let errorDetails = {
        success: false,
        provider: 'SendGrid',
        error: error.message
      };

      if (error.response && error.response.body) {
        errorDetails.details = error.response.body.errors || error.response.body;
      }

      throw errorDetails;
    }
  }

  /**
   * Send bulk emails using SendGrid
   */
  async sendBulkEmails(emails) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const results = [];
      const batchSize = 1000; // SendGrid batch limit
      
      for (let i = 0; i < emails.length; i += batchSize) {
        const batch = emails.slice(i, i + batchSize);
        const batchPromises = batch.map(emailData => 
          this.sendEmail(emailData).catch(error => ({
            success: false,
            error: error.message || error,
            emailData
          }))
        );
        
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      }

      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;

      strapi.log.info(`📊 Bulk email completed: ${successCount} sent, ${failureCount} failed`);

      return {
        success: true,
        totalEmails: emails.length,
        successCount,
        failureCount,
        results,
        provider: 'SendGrid'
      };

    } catch (error) {
      strapi.log.error('❌ SendGrid bulk send error:', error.message);
      throw error;
    }
  }

  /**
   * Get email delivery status (SendGrid specific implementation)
   */
  async getDeliveryStatus(messageId) {
    try {
      // Note: SendGrid doesn't provide a direct API for message status by message ID
      // This would typically require webhook setup or Event Webhook data
      strapi.log.warn('SendGrid delivery status check requires webhook setup');
      
      return {
        success: true,
        messageId,
        status: 'unknown',
        provider: 'SendGrid',
        note: 'Status tracking requires SendGrid webhook configuration'
      };
    } catch (error) {
      strapi.log.error('❌ SendGrid status check error:', error.message);
      throw error;
    }
  }

  /**
   * Validate SendGrid template
   */
  async validateTemplate(templateId) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      // SendGrid doesn't have a direct template validation API
      // We'll attempt to send a test email to validate
      const testEmail = {
        to: '<EMAIL>',
        templateId,
        templateData: {}
      };

      // This is a dry run - we're just validating the template structure
      try {
        const msg = {
          to: testEmail.to,
          from: this.defaultFrom,
          templateId: templateId,
          dynamicTemplateData: testEmail.templateData,
          mailSettings: {
            sandboxMode: {
              enable: true // Sandbox mode - won't actually send
            }
          }
        };

        await sgMail.send(msg);
        return true;
      } catch (error) {
        if (error.message.includes('template') || error.message.includes('not found')) {
          return false;
        }
        // Other errors might not be template-related
        return true;
      }
    } catch (error) {
      strapi.log.error('❌ SendGrid template validation error:', error.message);
      return false;
    }
  }

  /**
   * Get provider name
   */
  getProviderName() {
    return 'SendGrid';
  }

  /**
   * Test SendGrid connection
   */
  async testConnection() {
    try {
      // SendGrid doesn't have a dedicated ping endpoint
      // We'll use the API key validation approach
      const request = require('@sendgrid/client');
      request.setApiKey(this.apiKey);
      
      // Make a simple API call to verify connection
      const [response] = await request.request({
        url: '/v3/user/account',
        method: 'GET'
      });

      return response.statusCode === 200;
    } catch (error) {
      strapi.log.error('❌ SendGrid connection test failed:', error.message);
      return false;
    }
  }
}

module.exports = SendGridEmailProvider; 