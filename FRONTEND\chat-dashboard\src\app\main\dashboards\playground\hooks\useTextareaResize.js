import { useRef, useCallback } from 'react';

/**
 * Custom hook to manage textarea auto-resizing
 * @returns {Object} - Textarea ref and methods for resizing
 */
const useTextareaResize = () => {
  const textareaRef = useRef(null);
  
  // Reset height to default
  const resetHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = '24px';
    }
  };
  
  // Resize based on content
  const adjustHeight = useCallback((value) => {
    if (textareaRef.current) {
      // Reset height first to get accurate scrollHeight
      textareaRef.current.style.height = '24px';
      
      // Set new height based on content, with a maximum height
      textareaRef.current.style.height = !!value?.trim()
        ? `${Math.min(textareaRef.current.scrollHeight, 200)}px`
        : '24px';
        
      return textareaRef.current.scrollHeight;
    }
    return 24;
  }, []);
  
  return {
    textareaRef,
    resetHeight,
    adjustHeight
  };
};

export default useTextareaResize; 