/**
 * ChatApiService.js
 * Service responsible for handling all API interactions for the chat
 * Following Single Responsibility Principle by isolating API logic
 */

/**
 * Creates a request payload for the API
 * @param {string} question - User question
 * @param {string} sessionId - Session identifier
 * @param {Array} conversationHistory - Previous conversation history
 * @param {Object} userConfig - Custom user configuration
 * @param {boolean} isFirstMessage - Whether this is the first message
 * @returns {Object} Formatted payload
 */
const createPayload = (question, sessionId, conversationHistory, userConfig = {}, isFirstMessage = false) => {
  // Default values
  const defaults = {
    name: "<PERSON>",
    current_role: "Senior Software Engineer",
    years_of_experience: 10,
    target_role: "Architect",
    assessment_type: "technical",
    compulsory_questions: []
  };

  // Merge default values with user configuration and ensure it's not empty
  const config = { 
    ...defaults, 
    ...userConfig,
    // Make sure these values are not undefined
    name: userConfig.name || defaults.name,
    current_role: userConfig.current_role || defaults.current_role,
    years_of_experience: userConfig.years_of_experience || defaults.years_of_experience,
    target_role: userConfig.target_role || defaults.target_role,
    assessment_type: userConfig.assessment_type || defaults.assessment_type
  };

  console.log("Creating payload with userConfig:", userConfig);
  console.log("Merged config:", config);

  return {
    question,
    agent_id: "100",
    session_id: sessionId,
    search_type: "similarity",
    k_similarity: 10,
    fetch_k: 20,
    lamba_mul_mmr: 60,
    similarity_score_threshold: 20,
    ai_model_name: "gpt-3.5-turbo-1106",
    agent_name: "Naveen",
    agent_role: "Leadership Assessment Consultant",
    company_name: "ACME consultants",
    company_business: "ACME conducts personality assessments of managerial level employees of companies to assess them for future roles",
    company_values: "Integrity, Excellence, Innovation, and Respect",
    conversation_purpose: "Conduct an initial interview assessment",
    conversation_history: conversationHistory,
    conversation_type: "assessment",
    conversation_stage: "Introduction: Start the conversation by introducing yourself and explaining the assessment process.",
    use_tools: true,
    booking_link: "",
    agent_tone: "professional",
    agent_constraints: "Focus on leadership qualities, communication skills, decision-making abilities, and team management experience",
    agent_type_description: "Leadership Assessment Specialist",
    agent_first_message_is_goal: false,
    agent_first_message: `I'm Naveen, a Leadership Assessment Consultant with ACME consultants. I'll be conducting a ${config.assessment_type} assessment to evaluate your potential and capabilities. Let's start with a brief introduction about your experience.`,
    name: config.name,
    current_role: config.current_role,
    years_of_experience: config.years_of_experience,
    target_role: config.target_role,
    assessment_type: config.assessment_type,
    compulsory_questions: config.compulsory_questions || []
  };
};

/**
 * Asynchronously fetches a response from the AI.
 * @param {string} question - The user's question
 * @param {string} sessionId - The session ID
 * @param {Array} conversationHistory - The conversation history
 * @param {Object} userConfig - The user configuration
 * @param {boolean} isFirstMessage - Indicates if this is the first message in the conversation
 * @returns {Promise<Object>} - Promise resolving to the AI response, updated history, and assessment report
 */
async function fetchAIResponse(question, sessionId, conversationHistory = [], userConfig = {}, isFirstMessage = false) {
  try {
    // Construct the payload
    const payload = createPayload(question, sessionId, conversationHistory, userConfig, isFirstMessage);

    // Make the API request
    const response = await fetch(process.env.REACT_APP_AUTH_BASE_URL + '/api/assessment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    // Check if the request was successful
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `API request failed with status ${response.status}`);
    }

    // Parse the response
    const data = await response.json();

    // Extract the AI response
    const aiResponse = data.result.answer || 'Sorry, I couldn\'t generate a response.';

    // Extract the assessment report if available
    const assessmentReport = data.result.assessment_report || null;

    // Extract the current phase if available
    const currentPhase = data.result.phase || null;

    // Update conversation history based on whether it's the first message
    const updatedHistory = isFirstMessage
      ? [{ role: 'user', content: question }, { role: 'assistant', content: aiResponse }]
      : [...conversationHistory, { role: 'user', content: question }, { role: 'assistant', content: aiResponse }];

    // Return the AI response, updated history, and any assessment report
    return {
      aiResponse,
      updatedHistory,
      assessmentReport,
      currentPhase
    };
  } catch (error) {
    console.error('Error fetching AI response:', error);
    throw error;
  }
}

export default {
  fetchAIResponse
}; 