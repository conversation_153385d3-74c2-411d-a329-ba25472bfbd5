'use strict';

/**
 * Apple login controller
 */


const axios = require('axios');


const jwt = require("jsonwebtoken");
const appleSignin = require("apple-signin-auth");

module.exports = {
  async apple(ctx) {
    try {
      const { idToken } = ctx.request.body;

      // Verify Apple ID Token
      const appleResponse = await appleSignin.verifyIdToken(idToken, {
        audience: "com.imversion.podyc",
        ignoreExpiration: true, // Optional
      });

      const { email, sub } = appleResponse; // `sub` is the user's unique identifier

      // Find or create a user in Strapi
      let user = await strapi.query("plugin::users-permissions.user").findOne({ email });
      if (!user) {
        user = await strapi.query("plugin::users-permissions.user").create({
          data: {
            email,
            username: email,
            provider: "apple",
            password: sub, // Use a generated or static password
          },
        });
      }

      // Generate JWT token for the user
      const token = strapi.plugins["users-permissions"].services.jwt.issue({
        id: user.id,
      });

      return ctx.send({ jwt: token, user });
    } catch (error) {
      console.error("Apple Sign-In Error:", error);
      return ctx.badRequest("Invalid Apple Sign-In request");
    }
  },
};


