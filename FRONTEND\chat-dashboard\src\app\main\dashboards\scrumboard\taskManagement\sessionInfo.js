import React, { useEffect, useState } from "react";
import axios from "axios";
import LoadingSpinner from "app/shared-components/loading-spinner/loading-spinner";
import ComputerIcon from "@mui/icons-material/Computer";
import BrowserUpdatedIcon from "@mui/icons-material/BrowserUpdated";
import LanguageIcon from "@mui/icons-material/Language";
import AspectRatioIcon from "@mui/icons-material/AspectRatio";
import CookiesIcon from "@mui/icons-material/Cookie";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CancelIcon from "@mui/icons-material/Cancel";
import Tooltip from "@mui/material/Tooltip";
import { Skeleton } from "@mui/material";
import AssignmentIcon from "@mui/icons-material/Assignment";

export const SessionInfo = ({ sessionId }) => {
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const getSessionInfo = async (id) => {
    try {
      setLoading(true);
      const response = await axios.get(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/sessions/${id}?populate[0]=browserInfo,machineInfo`
      );
      const data = response.data.data.attributes;
      setSession(data);
    } catch (error) {
      setError("Failed to load session information.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (sessionId) {
      getSessionInfo(sessionId);
    }
  }, [sessionId]);

  if (loading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white bg-opacity-50">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) return <div className="text-red-500">{error}</div>;

  const { browserInfo, machineInfo } = session;
  const browserAttributes = browserInfo.data.attributes;
  const machineAttributes = machineInfo.data.attributes;

  // Extract browser and device name from userAgent
  const getBrowserName = (userAgent) => {
    if (userAgent.includes("Safari") && !userAgent.includes("Chrome")) {
      return "Safari";
    } else if (userAgent.includes("Chrome")) {
      return "Chrome";
    } else if (userAgent.includes("Firefox")) {
      return "Firefox";
    } else {
      return "Other";
    }
  };

  const getDeviceName = (userAgent) => {
    if (userAgent.includes("Macintosh")) {
      return "Mac";
    } else if (userAgent.includes("Windows")) {
      return "Windows PC";
    } else if (userAgent.includes("iPhone")) {
      return "iPhone";
    } else if (userAgent.includes("Android")) {
      return "Android Device";
    } else {
      return "Other";
    }
  };

  const deviceName = getDeviceName(machineAttributes.userAgent);
  const browserName = getBrowserName(browserAttributes.userAgent);

  return (
    <div className="bg-white transition-transform duration-300 ease-in-out">
      <h2 className="mr-4 text-base font-medium text-black my-4">
        <AssignmentIcon className="text-base-purple text-start text-md  mr-4" />
        Session Details
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="flex items-center bg-gray-50 p-4 rounded-lg transition-colors hover:bg-gray-100">
          <Tooltip title="Device Name">
            <ComputerIcon className="mx-8" />
          </Tooltip>
          <div>
            <p className="text-sm text-black">Device Name</p>
            <p className="text-md font-bold">{deviceName}</p>
          </div>
        </div>

        <div className="flex items-center bg-gray-50 p-4 rounded-lg transition-colors hover:bg-gray-100">
          <Tooltip title="Browser Name">
            <BrowserUpdatedIcon className="mx-8" />
          </Tooltip>
          <div>
            <p className="text-sm text-black">Browser Name</p>
            <p className="text-md font-bold">{browserName}</p>
          </div>
        </div>

        <div className="flex items-center bg-gray-50 p-4 rounded-lg transition-colors hover:bg-gray-100">
          <Tooltip title="Language">
            <LanguageIcon className="mx-8" />
          </Tooltip>
          <div>
            <p className="text-sm text-black">Language</p>
            <p className="text-md font-bold">{browserAttributes.language}</p>
          </div>
        </div>

        <div className="flex items-center bg-gray-50 p-4 rounded-lg transition-colors hover:bg-gray-100">
          <Tooltip title="Screen Resolution">
            <AspectRatioIcon className="mx-8" />
          </Tooltip>
          <div>
            <p className="text-sm text-black">Screen Width & Height</p>
            <p className="text-md font-bold">
              {browserAttributes.screenWidth} x {browserAttributes.screenHeight}
            </p>
          </div>
        </div>

        <div className="flex items-center bg-gray-50 p-4 rounded-lg transition-colors hover:bg-gray-100">
          <Tooltip title="Cookies Enabled">
            <CookiesIcon className="mx-8" />
          </Tooltip>
          <div>
            <p className="text-sm text-black">Cookies Enabled</p>
            <p className="text-md font-bold">
              {browserAttributes.cookiesEnabled ? (
                <CheckCircleIcon className="mx-8 text-base text-green" />
              ) : (
                <CancelIcon className="mx-8 text-base text-red" />
              )}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
