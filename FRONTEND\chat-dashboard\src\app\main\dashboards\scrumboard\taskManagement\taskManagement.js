import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { TaskItem } from "./taskItem";
import { TaskDetail } from "./taskDetail";
import { getLists, selectLists } from "../store/listsSlice";
import { useAppDispatch, useAppSelector } from "app/store";
import AssignmentIcon from '@mui/icons-material/Assignment';


export const TaskManagement = ({ boardId, tasks, onTaskUpdate }) => {
  const dispatch = useAppDispatch();
  const [selectedTask, setSelectedTask] = useState(null);
  const [ticketStates, setTicketStates] = useState([]);
  const lists = useSelector(selectLists);

  // Reset state when boardId changes
  useEffect(() => {
    setSelectedTask(null);
    setTicketStates([]);
  }, [boardId]);

  useEffect(() => {
    const url = new URL(window.location.href);
    const ticketId = url.searchParams.get('ticketId');

    if (ticketId) {
      const matchingTask = tasks.find(task => task.id === parseInt(ticketId, 10));
      if (matchingTask) {
        setSelectedTask(matchingTask);
      }
    } else if (tasks.length > 0 && !selectedTask) {
      setSelectedTask(tasks[0]);
    }

    dispatch(getLists(boardId));
  }, [tasks, selectedTask, boardId, dispatch]);

  useEffect(() => {
    if (lists.length > 0) {
      setTicketStates(lists);
    }
  }, [lists]);

  const handleTaskUpdate = (task) => {
    onTaskUpdate(task);
  };

  const handleTaskClick = (task) => {
    setSelectedTask(task);
  };

  return (
    <div className="flex p-4 h-[95%] w-full mx-32 py-16">
      {tasks.length > 0 ? (
        <>
          <div className="w-1/3 bg-[#FFFFF9] rounded-lg shadow-lg p-4 mr-4 overflow-y-auto">
            <div className="m-8">
              <span className="font-bold text-xl">{"Tickets"}</span>
            </div>
            {tasks.map((task) => (
              <TaskItem
                key={task.id}
                id={task.ticketId}
                title={task.title}
                ticketState={task.listId.title}
                onClick={() => handleTaskClick(task)}
                isSelected={selectedTask?.id === task.id}
                createdAt={task.createdAt}
              />
            ))}
          </div>
          <div className="w-full mx-16 h-full ">
            {selectedTask ? (
              <TaskDetail
                taskId={selectedTask.id}
                ticketStates={ticketStates}
                onUpdate={handleTaskUpdate}
              />
            ) : (
              <p>No task selected</p> // Optional: Add a fallback if no task is selected
            )}
          </div>
        </>
      ) : (
        <div className="w-full flex flex-col items-center justify-center">
          <AssignmentIcon style={{ fontSize: 96, color: '#9e9e9e', marginBottom: '1rem' }} />
          <h2 className="text-2xl font-semibold text-gray-700 mb-2">No Tasks Yet</h2>
          <p className="text-gray-500 text-center max-w-md">
            You have no tasks yet. Deploy your agents and engage with your customers to start collecting leads & creating tasks.
          </p>
        </div>
      )}
    </div>
  );
};
