// policies/OTPSentValidation.js

module.exports = async (policyContext, config, { strapi }) => {
	const { PolicyError } = require("@strapi/utils").errors;
	const params = policyContext.request.body;
	const otpReqObj = await strapi.db.query('api::otp-request.otp-request').findOne({ where: { phone: params.phone } })

	if ( ! otpReqObj ) {
		strapi.log.error('Please request for OTP first');
		return false;
	}
};
