.header-container {
	background-image: linear-gradient(70deg, rgb(27, 160, 206) 0%, rgb(101, 48, 193) 100%);
    width: 100%;
    max-width: 100%;
	padding-bottom: 200px;

}


.plans-container {
	position: relative;
	padding-top: 150px;
	margin-top: 150px;
}

.plan-card {
	padding: 25px;
}

.plan-title {
	font-weight: bold;
	color: rgb(101, 48, 193);

}

.plan-description{
	color: #535353;
}

/* Adjusting the autocomplete input padding */
.MuiAutocomplete-root .MuiOutlinedInput-root .MuiAutocomplete-input {
	padding-top: 0px;
}



/* Base styling for the toggle button group */
.MuiToggleButtonGroup-root {
	background-color: #F8F8F8;
	
	width: auto;
	font-size: 12px;
	max-width: 245px;
	height: 40px; /* Reduced height */
	border-radius: 4px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Added subtle shadow for depth */
	transition: box-shadow 0.3s; /* Smooth transition for interactions */
}

/* Hover effect for the toggle button group */
.MuiToggleButtonGroup-root:hover {
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Styling for each button inside the group */
.MuiToggleButtonGroup-root > .MuiToggleButtonGroup-grouped {
	color: #667085;
	background-color: 'transparent';
	font-size: 14px;
	text-transform: capitalize ;
	font-weight: 400;
	margin: 4px;
	
	/* Reduced padding for reduced height */
	/* Adjusted margin for reduced height */
	border-radius: 4px;
	height: 33px; /* Reduced height */
	transition: background-color 0.3s, transform 0.2s; /* Smooth transition for interactions */
}

/* Hover effect for each button */
.MuiToggleButtonGroup-root > .MuiToggleButtonGroup-grouped:hover {
	background-color: rgba(0, 0, 0, 0.05); /* Light hover effect */
	transform: translateY(-2px); /* Slight lift on hover */
}

/* Styling for the selected button */
.MuiToggleButtonGroup-root > .Mui-selected {
	background: #FFF;
	color: #7F56D9;
	border-radius: 4px;
	margin: 4px;
	height: 33px; /* Reduced height */
	 /* Added subtle shadow for depth */
}

/* Hover effect for the selected button */
.MuiToggleButtonGroup-root > .Mui-selected:hover {
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.card {
    width: 95%;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background-color: #fcf6ff;
}

.card-subtitle {
    text-align: center;
    margin: 0;
	font-size: 14px;
    font-weight: regular;
	color:#667085;

}

.card-title {
    font-size: 20px;
    font-weight: bold;
	text-align: center;
}


.card-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
	margin-top: 16px;
}

.card-list-item {
    display: flex;
	
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e0e0e0;
}

.card-list-item:last-child {
    border-bottom: none;
}

.features-item-icon {
    flex-shrink: 1;
    width: 15px;
    height: 15px;
    display: flex;
    align-items: center !important;
    font-size: 18px;
	justify-content: center !important;
}

.features-item-text {
    text-align: left;
	font-size: 14px;
	font-weight: regular;
	color: #303134;
	width: 40%;
	padding-top: 8px;

}





@media (max-width: 576px) {
	.plans-container {
		margin-top: -60px;
	}
}
