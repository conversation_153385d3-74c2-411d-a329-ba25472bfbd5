.meta-link-card-container{
    display: block;
    cursor: pointer;
    background: #FFFFFF;
    border-radius: 10px;
    
    margin: 4px;
    flex-flow: column;
    padding: 10px;
    flex-wrap: wrap;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
    /* Micro-interaction on hover */
  
}

.meta-link-card-container:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
}

/* Micro-interaction on active (clicked) */
.meta-link-card-container:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}


.link-data{
    display: flex;
    flex-flow: column;
    padding: 10px;
}

.meta-image{
    margin: auto;
    height: 150px;
    width: 100%;
    object-fit: cover;
}

.meta-description {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 5.3em;
}

.meta-link{
    background-color: #FFFFFF !important;
    border-bottom: 1px solid white !important;
    text-decoration: underline !important;
    color: #268ECC !important;
}
