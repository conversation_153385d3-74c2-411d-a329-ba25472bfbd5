import { createSlice } from '@reduxjs/toolkit';

/**
 * Redux slice for managing assessment state
 */
const assessmentSlice = createSlice({
  name: 'assessment',
  initialState: {
    isCompleted: false,
    report: null
  },
  reducers: {
    /**
     * Set assessment completed status
     * @param {Object} state - Current state
     * @param {Object} action - Action with boolean payload
     */
    setAssessmentCompleted: (state, action) => {
      state.isCompleted = action.payload;
    },
    
    /**
     * Set assessment report data
     * @param {Object} state - Current state
     * @param {Object} action - Action with report payload
     */
    setAssessmentReport: (state, action) => {
      state.report = action.payload;
    },
    
    /**
     * Reset assessment state
     * @param {Object} state - Current state
     */
    resetAssessment: (state) => {
      state.isCompleted = false;
      state.report = null;
    }
  }
});

// Action creators are generated for each case reducer function
export const { setAssessmentCompleted, setAssessmentReport, resetAssessment } = assessmentSlice.actions;

// Export the reducer as a default export
export default assessmentSlice.reducer; 