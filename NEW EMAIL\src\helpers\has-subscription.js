const _ = require( 'lodash' );

module.exports = {


	async  hasActiveSubscriptionPlan( organization ) {
		if(!( organization.subscription === 'subscribed' || organization.subscription === 'trial' )){
				return false;
			}

		// If monthly_usages is empty, fetch the organization with populated monthly_usages
		if ( _.isEmpty(organization.monthly_usages) || _.isEmpty(organization.plan) ) {
			organization = await strapi.query("api::organization.organization").findOne({
			where: { id: organization.id },
			populate: { monthly_usages: true, plan:true },
			});
		}

		// Check if the organization has monthly_usages
		if (_.isEmpty(organization.monthly_usages)) {
			console.log('Monthly usages not found for the organization.');
			return false;
		}
		// Check if the organization has plan
		if (_.isEmpty(organization.plan)) {
			console.log('Plan not found for the organization.');
			return false;
		}

		const currentDate = new Date();
		const currentMonthUsage = _.maxBy(organization.monthly_usages, 'createdAt');

		// Check if the currentMonthUsage is not found or has an invalid createdAt
		if (!currentMonthUsage || !currentMonthUsage.createdAt) {
			console.log('Invalid monthly usage record');
			return false;
		}

		const createdDate = new Date(currentMonthUsage.createdAt);
		const endDate = new Date(createdDate);
		if( organization.plan.type === 'monthly' ) {
			endDate.setMonth(createdDate.getMonth() + 1);
		} else {
			endDate.setFullYear(createdDate.getFullYear() + 1);
		}
		if (currentDate >= endDate) {
			await strapi.query("api::organization.organization").update({
			where: { id:  organization.id },
			data: {
				subscription: "renewFail",
			},
			});
			console.log('Subscription marked as renewFail.');
			return false;
		} else {
			console.log('Subscription is active.');
			return true;
		}
	},

	async hasSubscription( organization ) {
		if( organization.subscription === 'subscribed' || organization.subscription === 'trial' ){
			return true;
		} else {
			return false;
		}
   },
  }