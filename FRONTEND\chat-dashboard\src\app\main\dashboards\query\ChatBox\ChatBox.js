import React, { useState, useEffect } from "react";
import axios from "axios";
import { useSelector } from "react-redux";
import { selectUser, setUser } from "app/store/userSlice";
import { useDispatch } from "react-redux";
import { showMessage } from "app/store/fuse/messageSlice";
import { selectKb, getKb } from "app/store/kbSlice";
import { selectAiTasks, getAiTasks } from "../store/aiTaskSlice";
import { CustomDropdown } from "app/shared-components/dropdown/dropDown";
import "./chatbox.css";
import { v4 as uuidv4 } from "uuid";
import { logAskQuestionEvent } from "src/app/utils/analytics";
import { selectUsage, hasExcededCreditLimit , getUsage } from "app/store/usageSlice";

export default function ChatBox({
  onQuestionSubmit,
  setTimeout,
  loading,
  setLoading,
  setIsOpen,
  setLinksArray,
  setLinkMetaArray,
  setDocArray,
}) {
  const [question, setQuestion] = useState("");
  const dispatch = useDispatch();
  const [kbID, setKbId] = useState("");
  const maxLength = 300;
  const [seletectedChatbotType, setSeletectedChatbotType] = useState('')
  const user = useSelector(selectUser);
  const kbData = useSelector(selectKb);
  const AiTaskData = useSelector(selectAiTasks);
  const kbLoading = open && kbData.length === 0;
  const [agentId, setAgentId] = useState(13);
  const [waitingTime, setWaitingTime] = useState("5-7 seconds");
  const [sessionId, setSessionId] = useState("");
  const usage = useSelector(selectUsage);

  let isSendButtonDisabled =
    question.length === 0 ||
    question.length > maxLength ||
    question.trim() === "" ||
    (kbData.find((item) => item.kb_id === kbID)?.type === "datasource" &&
      agentId === "") ||
    (AiTaskData.find((item) => item.id === agentId)?.attributes.task_id !==
      10 &&
      kbID === "");

  const extractLinks = (text) => {
    const linkRegex = /(http:\/\/|https:\/\/)\S+/g;
    const extractedLinks = text.match(linkRegex) || [];
    const uniqueLinks = [...new Set(extractedLinks)];
    return uniqueLinks;
  };

  useEffect(() => {
    dispatch(getKb());
    dispatch(getAiTasks());
    dispatch(getUsage());
  }, [dispatch]);

  const handleQuestionChange = (event) => {
    const { value } = event.target;
    setQuestion(value);
  };

  const handleKeyDown = (event) => {
    const { value } = event.target;
    if (value.trim() === "") {
      isSendButtonDisabled = true;
      setQuestion("");
      return;
    }
    if (event.key === "Enter" && !isSendButtonDisabled) {
      handleSubmit(event);
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setLinksArray([]);
    setLinkMetaArray([]);
    setDocArray([]);
    setIsOpen(true);
    event.preventDefault();
    let session;
    if (_.isEmpty(sessionId)) {
      session = uuidv4();
      setSessionId(session);
    } else {
      session = sessionId;
    }

    try {
      setLoading(true);
      if (hasExcededCreditLimit(user.data.organization.plan ,  usage)) {
        dispatch(
          showMessage({ message: "You have run out of query credits. Please updgrade your plan to get more query credits" })
        );
      } else {
        setTimeout(waitingTime);
        const options = {
          method: "POST",
          timeout: 300000, // Set a timeout of 5mins/300seconds
          url: `${process.env.REACT_APP_AUTH_BASE_URL}/api/answers`,
          headers: {
            "Content-Type": "application/json",
          },
          data: {
            data: {
              question: question,
              kb_id: kbID,
              agent_id: agentId,
              session: session,
            },
          },
        };

        const response = await axios.request(options);
        logAskQuestionEvent(user.data.email, question);
        // Shallow copy of the user object
        var updatedUser = JSON.parse(JSON.stringify(user));
        updatedUser.data.organization.current_month_usage.query_count += 1;
        dispatch(setUser(updatedUser));
        const sources = response.data.data.attributes?.sources ?? [];
        const answer =
          response.data.data.attributes?.answer ?? "No answer returned";
        const answerSources = extractLinks(answer);
        const allSources = [...sources, ...answerSources];
        const querId = response.data.data.id;
        onQuestionSubmit({ answer, allSources, querId });
      }
    } catch (error) {
      var answer =
        error?.response?.data?.error?.message ?? "No answer returned";
      const source = [];
      const querId = "";
      onQuestionSubmit({ answer, source, querId });
    } finally {
      dispatch(getUsage());
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="chatbox-flex-container flex-start">
        <div>
          <label className="mb-8 component-title">Choose an agent</label>
          <CustomDropdown
            className="mt-8"
            disabled={
              AiTaskData.find((item) => item.id === agentId)?.attributes
                .task_id === 10
                ? true
                : false
            }
            options={kbData.map((item) => ({
              name: item.name,
              id: item.kb_id,
              type: item.type,
            }))}
            loading={kbLoading}
            onChange={(e) => {
              setKbId(e.target.value);
              const kb = kbData.find((item) => item.kb_id === e.target.value);
              if (kb.type === "shopify") {
                setAgentId(kb?.default_ai_task?.id);
                setWaitingTime("4-6seconds");
              }
              setSeletectedChatbotType(kb.type);
            }}
            value={kbID}
            placeholder="Select an agent"
            accessoryImage={"/assets/images/dropDown.png"}
          />
        </div>
        {/* <div>
          <label className="component-title">Choose an AI Task</label>
          <CustomDropdown
          disabled={seletectedChatbotType=='shopify'}
            className="mt-8"
            options={AiTaskData.map((item) => ({
              name: item.attributes.name,
              id: item.id,
            }))}
            loading={aiTasksLoadig}
            onChange={(e) => {

              if(AiTaskData.find(item => item.id === e.target.value)?.attributes.task_id === 10){
                //for youtube transcript
              setKbId('');
              }
              setAgentId(e.target.value);
              setWaitingTime(AiTaskData.find(item => item.id === e.target.value).attributes.waitingTime);
            }}
            value={agentId}
            placeholder="Select an AI Task"
            accessoryImage={"/assets/images/dropDown.png"}
          />
        </div> */}
        <div className="run-button-container">
          {!loading && (
            <button
              type="submit"
              className={` press-button shine-button ${
                isSendButtonDisabled
                  ? "run-button-inactive"
                  : "run-button-active"
              }`}
              disabled={isSendButtonDisabled}
            >
              Run
            </button>
          )}
        </div>
      </div>
      <h2 className="dashboard-title mt-32">Query</h2>
      <div className="chatbox-container mt-8">
        <textarea
          id="questionTextarea"
          placeholder="Go ahead, ask something, test your agent &#x1F525; &#x1F680;"
          value={question}
          label="Ask something about your knowledgebase..."
          onChange={handleQuestionChange}
          onKeyDown={handleKeyDown}
          className="chatbox-input shadow-base"
          rows={1}
          onInput={(e) => {
            const { scrollHeight, clientWidth } = e.target;
            const contentWidth = e.target.value.length * 10;
            if (contentWidth >= clientWidth * 0.9) {
              e.target.style.height = `${scrollHeight}px`;
            } else {
              e.target.style.height = "";
            }
          }}
        />
        <div
          className={`chatbox-char-count ${
            question.length > maxLength
              ? "char-count-text-red"
              : "char-count-text-grey"
          }`}
        >
          {question.length} / {maxLength}
        </div>
      </div>
    </form>
  );
}
