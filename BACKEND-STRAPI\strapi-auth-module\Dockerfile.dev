
# Creating multi-stage build for production
FROM node:18.16.1 as build
# RUN apk update && apk add --no-cache build-base gcc autoconf automake zlib-dev libpng-dev vips-dev git > /dev/null 2>&1
ARG NODE_ENV=production
ENV STRAPI_ADMIN_BACKEND_URL=https://auth-server-dev.talkbase.ai
ENV TALKBASE_BASE_URL=https://api-dev.talkbase.ai
ENV NODE_ENV=${NODE_ENV}

WORKDIR /opt/
COPY package.json ./
# RUN npm install -g node-gyp
RUN npm install
ENV PATH /opt/node_modules/.bin:$PATH
WORKDIR /opt/app
COPY . .
RUN npm run build

# Creating final production image
FROM node:18.16.1
# RUN apk add --no-cache vips-dev
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}
ENV STRAPI_ADMIN_BACKEND_URL=https://auth-server-dev.talkbase.ai
ENV TALKBASE_BASE_URL=https://api-dev.talkbase.ai
WORKDIR /opt/
COPY --from=build /opt/node_modules ./node_modules
WORKDIR /opt/app
COPY --from=build /opt/app ./
ENV PATH /opt/node_modules/.bin:$PATH

RUN chown -R node:node /opt/app
USER node
EXPOSE 1337
CMD ["npm", "run", "start"]
