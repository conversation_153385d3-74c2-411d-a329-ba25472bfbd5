.task-card-long {
	position: relative;
	background-color: rgba(137, 116, 186, 0.13);
	border-radius: 8px;
	border: 1px solid rgba(137, 116, 186, 0.6);
	max-height: 230px;
}
.active-task-card-long {
	position: relative;
	background-color: rgba(137, 116, 186, 0.13);
	border-radius: 8px;
	border: 1px solid rgba(137, 116, 186, 0.6);
	max-height: 230px;
}
.task-card {
	position: relative;
	background-color: rgba(137, 116, 186, 0.13);
	border-radius: 8px;
	border: 1px solid rgba(137, 116, 186, 0.6);
	max-height: 200px;
	max-width: 350px;
	min-width: 350px;
}
.active-task-card {
	position: relative;
	background-color: #effff1;
	border-radius: 8px;
	border: 1px solid rgba(38, 191, 25, 0.23);
	max-height: 200px;
	max-width: 350px;
	min-width: 350px;
}
.active-task-card #description {
	color: gray;
}
.active-task-card-long #description {
	color: gray;
}
.task-card-long #description {
	color: gray;
}
.task-card #description {
	color: gray;
}
.active-button {
	border-radius: 84px;
	background-color: #d2ffd6;
	width: 100px;
	height: 30px;
	display: flex;
	align-items: center;
	place-content: center;
	border: 1px solid rgba(38, 191, 25, 0.23);
	position: absolute;
	bottom: -15px;
	left: 50%;
	transform: translateX(-50%);
	/* cursor: pointer; */
	color:#26BF19;
}
.select-button {
	border-radius: 84px;
	background-color: rgba(255, 255, 255, 1);
	width: 100px;
	height: 30px;
	display: flex;
	align-items: center;
	place-content: center;
	border: 1px solid rgba(29, 157, 206, 1);
	position: absolute;
	bottom: -15px;
	left: 50%;
	transform: translateX(-50%);
	cursor: pointer;
	color:#1D9DCE;
}
.active-button-top-right {
	border-radius: 84px;
	background-color: #d2ffd6;
	width: 100px;
	height: 30px;
	display: flex;
	align-items: center;
	place-content: center;
	border: 1px solid rgba(38, 191, 25, 0.23);
	position: absolute;
	top: -15px;
	right: 25px;
	/* transform: translateX(-50%); */
	/* cursor: pointer; */
	color:#26BF19;
}
.select-button-top-right {
	border-radius: 84px;
	background-color: rgba(255, 255, 255, 1);
	width: 100px;
	height: 30px;
	display: flex;
	align-items: center;
	place-content: center;
	border: 1px solid rgba(29, 157, 206, 1);
	position: absolute;
	top: -15px;
	right: 25px;
	cursor: pointer;
	color:#1D9DCE;
}
@media only screen and (max-width: 600px) {
	.active-card {
		margin-bottom: 20px;
	}
}
