"use strict";

/**
 * train controller
 */

const { createCoreController } = require("@strapi/strapi").factories;
const axios = require("axios");

// module.exports = createCoreController('api::train.train');
module.exports = createCoreController("api::train.train", ({ strapi }) => ({
  async create(ctx) {
    var status = true;


    let { data } = await axios
      .post(
        `${process.env.TALKBASE_BASE_URL}/v4/train`,
        {
          url: ctx.request.body.data.link,
          kb_id: ctx.request.body.data.kb_id,
          org_id: ctx.state.user.organization?.org_id,
          force_train: true,
          document_id: document_model.id,
          remaining_quota:
            ctx.state.user.organization.plan.allowed_training_tokens -
            ctx.state.user.organization?.current_month_usage
              ?.training_tokens_count,
        },
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      )
      .catch((err) => {
        console.log(err);
        status = false;
      });
    if (!data?.result || data?.result === "False") {
      status = false;
    }
    await strapi.query("api::monthly-usage.monthly-usage").update({
      where: { id: ctx.state.user.organization.current_month_usage.id },
      data: {
        trained_count:
          ctx.state.user.organization.current_month_usage.trained_count + 1,
      },
    });
    // ctx.request.body['kb_name']=ctx.request.body.kb_name;
    ctx.request.body.data["train_status"] = status;
    // some logic here
    const response = await super.create(ctx);
    // some more logic
    return response.data;
  },
}));
