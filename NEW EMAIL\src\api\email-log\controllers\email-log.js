'use strict';

const { createCoreController } = require('@strapi/strapi').factories;

/**
 * Email Log Controller
 * Extended controller for email log management and analytics
 */
module.exports = createCoreController('api::email-log.email-log', ({ strapi }) => ({
  
  /**
   * Find email logs with organization filtering
   */
  async find(ctx) {
    try {
      const { query } = ctx;
      
      // Add organization filter if user has organization
      if (ctx.state.user?.organization) {
        query.filters = {
          ...query.filters,
          organization: ctx.state.user.organization.id
        };
      }

      const entity = await strapi.service('api::email-log.email-log').find({
        ...query,
        populate: ['organization', 'user', 'email_template']
      });

      ctx.send({
        success: true,
        data: entity,
        message: 'Email logs retrieved successfully'
      });

    } catch (error) {
      strapi.log.error('Email log find error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to retrieve email logs'
      });
    }
  },

  /**
   * Get email analytics
   */
  async getAnalytics(ctx) {
    try {
      const { startDate, endDate, provider, status } = ctx.query;
      
      // Build filter conditions
      const filters = {
        ...(ctx.state.user?.organization && { organization: ctx.state.user.organization.id }),
        ...(provider && { provider }),
        ...(status && { status }),
        ...(startDate && { 
          sent_at: { 
            $gte: new Date(startDate),
            ...(endDate && { $lte: new Date(endDate) })
          }
        })
      };

      // Get basic counts
      const totalEmails = await strapi.query('api::email-log.email-log').count({ where: filters });
      
      const sentEmails = await strapi.query('api::email-log.email-log').count({
        where: { ...filters, status: 'sent' }
      });
      
      const deliveredEmails = await strapi.query('api::email-log.email-log').count({
        where: { ...filters, status: 'delivered' }
      });
      
      const failedEmails = await strapi.query('api::email-log.email-log').count({
        where: { ...filters, status: 'failed' }
      });
      
      const bouncedEmails = await strapi.query('api::email-log.email-log').count({
        where: { ...filters, status: 'bounced' }
      });

      const openedEmails = await strapi.query('api::email-log.email-log').count({
        where: { ...filters, status: 'opened' }
      });

      // Get provider breakdown
      const providerStats = await strapi.db.connection.raw(`
        SELECT provider, status, COUNT(*) as count
        FROM email_logs 
        WHERE ${ctx.state.user?.organization ? 'organization = ?' : '1=1'}
        ${provider ? 'AND provider = ?' : ''}
        ${startDate ? 'AND sent_at >= ?' : ''}
        ${endDate ? 'AND sent_at <= ?' : ''}
        GROUP BY provider, status
      `, [
        ...(ctx.state.user?.organization ? [ctx.state.user.organization.id] : []),
        ...(provider ? [provider] : []),
        ...(startDate ? [new Date(startDate)] : []),
        ...(endDate ? [new Date(endDate)] : [])
      ]);

      // Get daily stats for the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const dailyStats = await strapi.db.connection.raw(`
        SELECT DATE(sent_at) as date, status, COUNT(*) as count
        FROM email_logs 
        WHERE sent_at >= ? 
        ${ctx.state.user?.organization ? 'AND organization = ?' : ''}
        GROUP BY DATE(sent_at), status
        ORDER BY date DESC
      `, [
        thirtyDaysAgo,
        ...(ctx.state.user?.organization ? [ctx.state.user.organization.id] : [])
      ]);

      // Calculate rates
      const deliveryRate = totalEmails > 0 ? ((deliveredEmails + sentEmails) / totalEmails * 100).toFixed(2) : 0;
      const openRate = (deliveredEmails + sentEmails) > 0 ? (openedEmails / (deliveredEmails + sentEmails) * 100).toFixed(2) : 0;
      const bounceRate = totalEmails > 0 ? (bouncedEmails / totalEmails * 100).toFixed(2) : 0;

      ctx.send({
        success: true,
        data: {
          summary: {
            totalEmails,
            sentEmails,
            deliveredEmails,
            failedEmails,
            bouncedEmails,
            openedEmails,
            deliveryRate: parseFloat(deliveryRate),
            openRate: parseFloat(openRate),
            bounceRate: parseFloat(bounceRate)
          },
          providerStats: providerStats.rows || providerStats,
          dailyStats: dailyStats.rows || dailyStats,
          period: {
            startDate,
            endDate: endDate || new Date().toISOString()
          }
        },
        message: 'Email analytics retrieved successfully'
      });

    } catch (error) {
      strapi.log.error('Email analytics error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to get email analytics'
      });
    }
  },

  /**
   * Retry failed email
   */
  async retryEmail(ctx) {
    try {
      const { id } = ctx.params;
      
      // Find the email log
      const emailLog = await strapi.query('api::email-log.email-log').findOne({
        where: { 
          id,
          status: 'failed',
          ...(ctx.state.user?.organization && { organization: ctx.state.user.organization.id })
        }
      });

      if (!emailLog) {
        return ctx.notFound('Failed email log not found');
      }

      // Prepare email data for retry
      const emailData = {
        to: emailLog.to_emails,
        subject: emailLog.subject,
        templateId: emailLog.template_id,
        templateData: emailLog.template_data || {}
      };

      // Retry sending
      const { emailProviderFactory } = require('../../../providers/email-provider-factory');
      const result = await emailProviderFactory.sendEmail(emailData, emailLog.provider);

      // Update the log
      await strapi.query('api::email-log.email-log').update({
        where: { id },
        data: {
          status: result.success ? 'sent' : 'failed',
          retry_count: emailLog.retry_count + 1,
          error_message: result.success ? null : result.error,
          provider_response: result,
          sent_at: result.success ? new Date() : emailLog.sent_at
        }
      });

      ctx.send({
        success: true,
        data: result,
        message: result.success ? 'Email retried successfully' : 'Email retry failed'
      });

    } catch (error) {
      strapi.log.error('Email retry error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to retry email'
      });
    }
  }
})); 