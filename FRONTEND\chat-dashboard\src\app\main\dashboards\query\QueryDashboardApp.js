import React, { useEffect, useState } from "react";
import { useSelector } from 'react-redux';
import withReducer from 'app/store/withReducer';
import reducer from './store';
import FusePageSimple from '@fuse/core/FusePageSimple';
import history from '@history';
import { selectUser } from 'app/store/userSlice';
import QueryDashboardAppHeader from './QueryDashboardAppHeader';
import AnswerSourcesContainer from './AnswerSourcesContainer/AnswerSourcesContainer'
import ChatBox from "./ChatBox/ChatBox";
import axios from 'axios';
import "./chat.css";

function QueryDashboardApp() {
  const [sources, setSources] = useState([]);
  const [querId, setQueryId] =  useState('');
  const [answer, setAnswer] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [waitingTime, setWaitingTime] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [linksArray, setLinksArray] = useState([]);
  const [linkMetaArray, setLinkMetaArray] = useState([]);
  const [docArray, setDocArray] = useState([]);
  const [isFetchingMeta, setIsFetchingMeta] = useState(true);
  const user = useSelector(selectUser);

  const handleQuestionSubmit = (response) => {
    setAnswer(response?.answer || '');
    setSources(response?.allSources || '');
    setQueryId(response?.querId??'');
  }

  const setTimeout = (timeout) => {
    setWaitingTime(timeout);
  }

  const segregateLinksandPdfs = () => {
    const httpRegex = /^(http:\/\/|https:\/\/|www\.)/;

    // Filter unique links
    const uniqueLinks = sources?.map((source) => {
      const cleanedSource = source.replace(/[,.]+$/, '');
      return cleanedSource;
    }).filter((source) => httpRegex.test(source));

    // Filter unique documents
    const uniqueDocs = Array.from(new Set(sources?.filter((source) => {
      const fileName = source.split('/').pop();
      return fileName.endsWith('.pdf') || fileName.endsWith('.csv');
    }).map((source) => {
      const fileName = source.split('/').pop();
      // return fileName;
      return {file: `https://talkbasefilestorage.s3.ap-south-1.amazonaws.com/${source.replace('doc/', '')}`,
        name:fileName
      };
    })));

    setDocArray([...uniqueDocs]);
    setLinksArray([...uniqueLinks]);
  };

  const fetchLinkMeta = async () => {
    setLinkMetaArray([]);
    setIsFetchingMeta(true);

    try {
      const options = {
        method: 'POST',
        url: `${process.env.REACT_APP_AUTH_BASE_URL}/api/url-meta`,
        headers: {'Content-Type': 'application/json'},
        data: {"urls": linksArray}
      };

      const response = await axios.request(options);
      setLinkMetaArray(response.data);
    } catch (error) {
      console.error('Error fetching meta information:', error);
    }

    setIsFetchingMeta(false);
  };
  useEffect(() => {
    if(!(user.data?.organization?.subscription==='subscribed'|| user.data?.organization?.subscription==='trial')){
      history.push('subscription');  
    }
  },[]);

  useEffect(() => {
    setDocArray([]);
    setLinksArray([]);
    if (sources?.length > 0) {
      segregateLinksandPdfs();
    }
  }, [sources, answer]);

  useEffect(() => {
    if (linksArray.length > 0) {
      setLinkMetaArray([]);
      fetchLinkMeta();
    }
  }, [linksArray]);

  return (
    <FusePageSimple
      header={<QueryDashboardAppHeader />}
      content={
      <div className="w-full px-24 md:px-32 pb-24 mt-16">
        <div className='chatbox-ui'>
          <ChatBox 
            onQuestionSubmit={handleQuestionSubmit} 
            setTimeout={setTimeout} 
            loading={isLoading} 
            setLoading={setIsLoading} 
            setIsOpen={setIsOpen}
            setLinksArray={setLinksArray}
            setLinkMetaArray={setLinkMetaArray}
            setDocArray={setDocArray}
          />
        </div>
          { isOpen && <AnswerSourcesContainer
            answer={answer}
            sources={sources}
            isLoading={isLoading}
            waitingTime={waitingTime}
            linksArray={linksArray}
            linkMetaArray={linkMetaArray}
            docArray={docArray}
            isFetchingMeta={isFetchingMeta}
            queryId={querId}
           
          /> }
      </div>
      }
    />
  );
}

export default withReducer('queryDashboardApp', reducer)(QueryDashboardApp);
