import React, { useState, useEffect } from 'react';

const ChatInput = ({ 
  input, 
  setInput, 
  handleSend, 
  textareaRef, 
  handleInputChange, 
  placeholder = "Message AI...",
  disabled = false
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [characterCount, setCharacterCount] = useState(0);
  const isInputEmpty = !input.trim();
  
  // Update character count when input changes
  useEffect(() => {
    setCharacterCount(input.length);
  }, [input]);

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey && !disabled) {
      e.preventDefault();
      if (!isInputEmpty) handleSend();
    }
  };

  return (
    <div className="chat-input-container">
      <div className={`chat-input-box ${isFocused ? 'focused' : ''} ${disabled ? 'disabled' : ''}`}>
        <button 
          className="chat-plus-button" 
          title="Add attachment"
          disabled={disabled}
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M12 5v14M5 12h14" />
          </svg>
        </button>
        
        <div className="textarea-container">
          <textarea
            ref={textareaRef}
            className="chat-input"
            placeholder={disabled ? "Please wait..." : placeholder}
            value={input}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => !disabled && setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            rows={1}
            disabled={disabled}
            style={{ 
              minHeight: "36px", 
              padding: "8px 12px 8px 0" 
            }}
          />
          
          {characterCount > 0 && !disabled && (
            <div 
              style={{ 
                position: 'absolute', 
                right: '8px', 
                bottom: '-18px', 
                fontSize: '11px', 
                color: '#8e8ea0',
                opacity: isFocused ? 0.8 : 0,
                transition: 'opacity 0.2s ease'
              }}
            >
              {characterCount} {characterCount === 1 ? 'character' : 'characters'}
            </div>
          )}
        </div>
        
        <button
          className="chat-send-button"
          onClick={handleSend}
          disabled={isInputEmpty || disabled}
          aria-label="Send message"
          title={disabled ? "Please wait..." : (isInputEmpty ? "Type a message to send" : "Send message (Enter)")}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth={2}
            className="chat-send-icon"
          >
            <path d="M22 2L11 13M22 2L15 22L11 13M11 13L2 9" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </button>
      </div>
      
      {!disabled && (
        <div className="chat-shortcuts" style={{ 
          textAlign: 'center', 
          fontSize: '12px', 
          color: '#8e8ea0', 
          marginTop: '8px',
          opacity: 0.8 
        }}>
          <span title="Press Enter to send, Shift+Enter for new line">
            Press <span className="keyboard-shortcut">Enter ↵</span> to send
          </span>
        </div>
      )}
    </div>
  );
};

export default ChatInput; 