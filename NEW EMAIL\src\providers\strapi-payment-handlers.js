const _ = require("lodash");

module.exports = {
  async updateOrgWithStripeCustomerId(email, stripeCustomerId) {
    try {
      console.log("****email", email);
      const user = await strapi
      .query("plugin::users-permissions.user")
      .findOne({
        where: { email: email },
        populate: {
          organization: {
            populate: {
              plan: true,
              monthly_usages: true,
              subscription_orders: true,
            },
          },
        },
      });
      console.log("****user", user);
      const organization = await strapi
        .query("api::organization.organization")
        .update({
          where: {
            id: user.organization.id,
          },
          data: {
            stripe_customer: stripeCustomerId,
          },
        });
      return organization;
    } catch (error) {
      console.error(
        "Error updating organization with Stripe customer ID:",
        error
      );
      throw error;
    }
  },

  async createSubscriptionOrder({
    ctx,
    email,
    plan,
    sessionId,
    subscriptionId,
    status,
    startDate,
    endDate,
    isTrial = false,
    trialDays = 14,
    trialStart = null,
    trialEnd = null,
  }) {
    try {
      const user = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: { email: email },
          populate: {
            organization: {
              populate: {
                plan: true,
                monthly_usages: true,
                subscription_orders: true,
              },
            },
          },
        });

      const subscriptionOrder = await strapi
        .service("api::subscription-order.subscription-order")
        .create({
          data: {
            type: "subscription",
            plan: plan.id,
            status: status,
            plantype: plan.type,
            stripeSessionId: sessionId,
            subscriptionId: subscriptionId,
            current_period_start: new Date(startDate * 1000),
            current_period_end: new Date(endDate * 1000),
            isPaid: false,
            isTrial: isTrial,
            trial_start_date: trialStart ? new Date(trialStart * 1000) : null,
            trial_end_date: trialEnd ? new Date(trialEnd * 1000) : null,
            organization: user.organization.id,
          },
        })
        .catch((err) => {
          console.log("subscription error ");
          console.log(err);
        });
      return subscriptionOrder;
    } catch (error) {
      console.error("Error initiating subscription order:", error);
      throw error;
    }
  },

  async updateSubscriptionOrder({
    ctx,
    email,
    status,
    subscriptionId = null,
    startDate = null,
    endDate = null,
    invoiceUrl = null,
    isTrial = false,
    trialDays = 14,
  }) {
    try {
      console.log("Updating subscription order");
      console.log("Email", email);
      const user = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: { email: email },
          populate: {
            organization: {
              populate: {
                plan: true,
                monthly_usages: true,
                subscription_orders: true,
              },
            },
          },
        });

      if (!user.organization.subscription_orders.length) {
        return ctx.throw(500, "No subscription orders found.");
      }

      const updatedSubscriptionOrder = await strapi
        .query("api::subscription-order.subscription-order")
        .update({
          where: { subscriptionId: subscriptionId },
          data: {
            status: status,
            current_period_start: new Date(parseInt(startDate) * 1000) ?? null,
            current_period_end: new Date(parseInt(endDate) * 1000) ?? null,
            invoice: invoiceUrl ?? null,
            isTrial: isTrial,
            trial_start_date: isTrial ? new Date() : null,
            trial_end_date: isTrial
              ? new Date(new Date().getTime() + trialDays * 24 * 60 * 60 * 1000)
              : null,
          },
        });
      return updatedSubscriptionOrder;
    } catch (error) {
      console.error("Error updating subscription order:", error);
      throw error;
    }
  },

  async addMonthlyUsage({ ctx, email, startDate, endDate, product_id }) {
    try {
      const user = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: { email: email },
          populate: {
            organization: {
              populate: {
                plan: true,
                monthly_usages: true,
                subscription_orders: true,
              },
            },
          },
        });
      const plan = await strapi.query("api::plan.plan").findOne({
        where: {
          stripe_prod_id: product_id,
        },
      });

      if (!plan) {
        throw new Error("Plan not found.");
      }

      const currentMonthlyUsage = await strapi.query("api::monthly-usage.monthly-usage").findOne({
        where: {
          organization: user.organization.id,
          period_start: { $lte: new Date(parseInt(startDate) * 1000) },
          period_end: { $gte: new Date(parseInt(startDate) * 1000) },
        },
      });


      if(currentMonthlyUsage){
        console.log("Found usage for currentMonthlyUsage", currentMonthlyUsage);
        await strapi.query("api::monthly-usage.monthly-usage").update({
          where: { id: currentMonthlyUsage.id },
          data: {
            is_active: false,
            period_end: new Date(parseInt(endDate) * 1000),
            period_start: new Date(parseInt(startDate) * 1000),
            usage_quota: parseInt(currentMonthlyUsage.usage_quota) + parseInt(plan.allowed_credits),
            period: plan.type === "yearly" ? "year" : "month",
          },
        });
      }
      else{
        console.log("No usage found creating new usage", currentMonthlyUsage);
        await strapi.query("api::monthly-usage.monthly-usage").create({
          data: {
            organization: user.organization.id,
            usage_quota: plan.allowed_credits,
            is_active: true,
            period: plan.type === "yearly" ? "year" : "month",
            period_start: new Date(parseInt(startDate) * 1000),
            period_end: new Date(parseInt(endDate) * 1000),
          },
        });
      }


    } catch (error) {
      console.error("Error adding monthly usage:", error);
      throw error;
    }
  },

  async updateMonthlyUsage({ ctx, email, startDate, endDate, product_id }) {
    try {
      const user = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: { email: email },
          populate: {
            organization: {
              populate: {
                plan: true,
                monthly_usages: true,
                subscription_orders: true,
              },
            },
          },
        });
      const newPlan = await strapi.query("api::plan.plan").findOne({
        where: {
          stripe_prod_id: product_id,
        },
      });

      if (!newPlan) {
        throw new Error("Plan not found.");
      }



      const monthlyUsage = await strapi.query("api::monthly-usage.monthly-usage").findOne({
        where: {
          organization: user.organization.id,
        },
        order: ["createdAt"],
      });

      console.log("used credits monthlyUsage", monthlyUsage.usage_quota);

      console.log("newPlan credits", newPlan.allowed_credits);

      // Calculate the remaining time in the current billing cycle
      console.log("Updating monthly usage");
      const currentPeriodEnd = new Date(monthlyUsage.period_end);
      const now = new Date();
      const remainingDays = Math.ceil((currentPeriodEnd - now) / (1000 * 60 * 60 * 24));

      console.log("remainingDays", remainingDays);

      // Determine the number of days in the current and new plan periods
      const currentDaysInPeriod = monthlyUsage.period === "year" ? 365 : 30;
      const newDaysInPeriod = newPlan.type === "yearly" ? 365 : 30;

      console.log("currentDaysInPeriod", currentDaysInPeriod);
      console.log("newDaysInPeriod", newDaysInPeriod);

      // Calculate daily credit rates based on plan type
      const currentDailyRate = monthlyUsage.usage_quota / currentDaysInPeriod;
      const newDailyRate = newPlan.allowed_credits / newDaysInPeriod;

      console.log("currentDailyRate", currentDailyRate);
      console.log("newDailyRate", newDailyRate);

      // Calculate pro-rated credits
      const proRatedCredits = (remainingDays * newDailyRate) - (remainingDays * currentDailyRate);

      console.log("proRatedCredits", proRatedCredits);

      // Calculate the new usage quota
      const newUsageQuota = monthlyUsage.usage_quota + Math.floor(proRatedCredits);

      console.log("newUsageQuota", newUsageQuota);

      // Ensure the usage quota does not go below zero
      const finalUsageQuota = Math.max(newUsageQuota, 0);

      console.log("finalUsageQuota", finalUsageQuota);

      // Update the usage quota with the pro-rated credits
      await strapi.query("api::monthly-usage.monthly-usage").update({
        where: {
          organization: user.organization.id,
        },
        data: {
          usage_quota: finalUsageQuota,
          is_active: true,
          period: newPlan.type === "yearly" ? "year" : "month",
          period_start: new Date(parseInt(startDate) * 1000),
          period_end: new Date(parseInt(endDate) * 1000),
        },
      });
    } catch (error) {
      console.error("Error adding monthly usage:", error);
      throw error;
    }
  },

  async addPlan({ ctx, email, product_id, isTrial }) {
    try {
      const user = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: { email: email },
          populate: {
            organization: {
              populate: {
                plan: true,
                monthly_usages: true,
                subscription_orders: true,
              },
            },
          },
        });
      const plan = await strapi.query("api::plan.plan").findOne({
        where: {
          stripe_prod_id: product_id,
        },
      });

      if (!plan) {
        throw new Error("Plan not found.");
      }

      // Update organization with the plan details
      await strapi.query("api::organization.organization").update({
        where: { id: user.organization?.id },
        data: {
          subscription: isTrial ? "trial" : "subscribed",
          plan: plan.id,
        },
      });
    } catch (error) {
      console.error("Error adding plan:", error);
      throw error;
    }
  },



  async updateOrganizationSubscription({ ctx, status, email }) {
    try {
      const user = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: { email: email },
          populate: {
            organization: {
              populate: {
                plan: true,
                monthly_usages: true,
                subscription_orders: true,
              },
            },
          },
        });

      await strapi.query("api::organization.organization").update({
        where: { id: user.organization.id },
        data: {
          subscription: status,
        },
      });
    } catch (error) {
      console.error("Error updating organization subscription:", error);
      throw error;
    }
  },


  async cancelSubscription(email) {
    try {

      const user = await strapi
      .query("plugin::users-permissions.user")
      .findOne({
        where: { email: email },
        populate: {
          organization: {
            populate: {
              plan: true,
              monthly_usages: true,
              subscription_orders: true,
            },
          },
        },
      });

      const latestSubscriptionOrder = _.maxBy(user.organization.subscription_orders, "createdAt");

      const updatedSubscription = await strapi
        .service("api::subscription-order.subscription-order")
        .update({
          where: {
            id: latestSubscriptionOrder.id,
          },
          data: {
            status: "cancelled",
          },
        });

       const updatedOrganization = await strapi.query("api::organization.organization").update({
        where: { id: user.organization.id },
        data: {
          subscription: "unsubscribed",
          plan: null,
        },
      });
      return { updatedSubscription, updatedOrganization };
    } catch (error) {
      console.error("Error canceling subscription:", error);
      throw error;
    }
  },

  // async handlePaymentSuccess(
  //   ctx,
  //   invoice_url,
  //   product_id,
  //   start_date,
  //   end_date,
  //   subscriptionId
  // ) {
  //   try {
  //     const user = await strapi
  //       .query("plugin::users-permissions.user")
  //       .findOne({
  //         where: { email: ctx.state.user.email },
  //         populate: {
  //           organization: {
  //             populate: {
  //               plan: true,
  //               monthly_usages: true,
  //               subscription_orders: true,
  //             },
  //           },
  //         },
  //       });

  //     if (!user.organization.subscription_orders.length) {
  //       return ctx.throw(500, "No subscription orders found.");
  //     }

  //     const order = _.maxBy(user.organization.subscription_orders, "createdAt");

  //     const startDate = new Date(parseInt(start_date) * 1000);
  //     const endDate = new Date(parseInt(end_date) * 1000);
  //     // Update the subscription model with payment status and invoice
  //     const subscriptionOrder = await strapi
  //       .query("api::subscription-order.subscription-order")
  //       .update({
  //         where: { id: order.id },
  //         data: {
  //           isPaid: true,
  //           invoice: invoice_url,
  //           status: "active",
  //           subscriptionId: subscriptionId,
  //           current_period_start: startDate,
  //           current_period_end: endDate,
  //         },
  //       });

  //     const plan = await strapi.query("api::plan.plan").findOne({
  //       where: {
  //         stripe_prod_id: product_id,
  //       },
  //     });

  //     // Update organization with the plan details
  //     await strapi.query("api::organization.organization").update({
  //       where: { id: user.organization?.id },
  //       data: {
  //         subscription: "subscribed",
  //         plan: plan.id,
  //       },
  //     });

  //     const monthlyUsage = await strapi
  //       .query("api::monthly-usage.monthly-usage")
  //       .create({
  //         data: {
  //           organization: user.organization.id,
  //           usage_quota: plan.allowed_credits,
  //           is_active: true,
  //           period: plan.type === "yearly" ? "year" : "month",
  //           period_start: startDate,
  //           period_end: endDate,
  //         },
  //       });
  //     return { plan, monthlyUsage, subscriptionOrder };
  //   } catch (error) {
  //     console.error("Error handling payment success:", error);
  //     throw error;
  //   }
  // },

  // async handlePaymentFailed(ctx) {
  //   try {
  //     const user = await strapi
  //       .query("plugin::users-permissions.user")
  //       .findOne({
  //         where: { email: ctx.state.user.email },
  //         populate: {
  //           organization: {
  //             populate: {
  //               plan: true,
  //               monthly_usages: true,
  //               subscription_orders: true,
  //             },
  //           },
  //         },
  //       });
  //     const activeSubscription =
  //       user.organization.subscription === "subscribed" ||
  //       user.organization.subscription === "trial";

  //     const latestMonthlyUsage = _.maxBy(
  //       user.organization.monthly_usages,
  //       "createdAt"
  //     );
  //     const latestSubscriptionOrder = _.maxBy(
  //       user.organization.subscription_orders,
  //       "createdAt"
  //     );

  //     const hasPlanExpired =
  //       Date.parse(latestMonthlyUsage.period_end) > Date.now();

  //     if (activeSubscription) {
  //       await strapi.query("api::organization.organization").update({
  //         where: { id: user.organization?.id },
  //         data: {
  //           subscription: "renewFail",
  //         },
  //       });
  //     }
  //     if (hasPlanExpired) {
  //       const subscriptionOrder = await strapi
  //         .query("api::subscription-order.subscription-order")
  //         .update({
  //           where: { id: latestSubscriptionOrder.id },
  //           data: {
  //             status: "renewFail",
  //             isPaid: false,
  //           },
  //         });

  //       return {
  //         organization: user.organization,
  //         subscriptionOrder: subscriptionOrder,
  //       };
  //     }
  //   } catch (error) {
  //     console.error("Error handling payment failed:", error);
  //     throw error;
  //   }
  // },

  // async handleSubscriptionDeleted(ctx) {
  //   try {
  //     const user = await strapi
  //       .query("plugin::users-permissions.user")
  //       .findOne({
  //         where: { email: ctx.state.user.email },
  //         populate: {
  //           organization: {
  //             populate: {
  //               plan: true,
  //               monthly_usages: true,
  //               subscription_orders: true,
  //             },
  //           },
  //         },
  //       });
  //     const latestSubscriptionOrder = _.maxBy(
  //       user.organization.subscription_orders,
  //       "createdAt"
  //     );

  //     const updatedSubscriptionOrder = await strapi
  //       .query("api::subscription-order.subscription-order")
  //       .update({
  //         where: { id: latestSubscriptionOrder.id },
  //         data: {
  //           status: "cancelled",
  //         },
  //       });

  //     const updatedOrganization = await strapi
  //       .query("api::organization.organization")
  //       .update({
  //         where: { id: user.organization.id },
  //         data: {
  //           subscription: "unsubscribed",
  //           plan: null,
  //         },
  //       });
  //     return { updatedOrganization, updatedSubscriptionOrder };
  //   } catch (error) {
  //     console.error("Error handling subscription deleted:", error);
  //     throw error;
  //   }
  // },
};
