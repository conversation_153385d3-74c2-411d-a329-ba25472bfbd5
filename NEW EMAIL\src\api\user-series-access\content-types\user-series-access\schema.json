{"kind": "collectionType", "collectionName": "user_series_access", "info": {"singularName": "user-series-access", "pluralName": "user-series-accesses", "displayName": "User Series Access", "description": "Individual user access grants for private series"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "series": {"type": "relation", "relation": "manyToOne", "target": "api::series.series"}, "access_type": {"type": "enumeration", "enum": ["paid", "trial", "admin", "granted", "purchased"], "default": "granted"}, "granted_at": {"type": "datetime", "required": true}, "expires_at": {"type": "datetime", "description": "Optional expiration for time-limited access"}, "granted_by": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "purchase_reference": {"type": "string", "description": "Reference to payment/order if purchased"}, "notes": {"type": "text"}}}