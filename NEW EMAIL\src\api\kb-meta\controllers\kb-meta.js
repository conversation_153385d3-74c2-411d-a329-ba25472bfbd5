'use strict';

/**
 * answer controller
 */


const axios = require('axios');
const { createCoreController } = require('@strapi/strapi').factories;


module.exports ={
	async find(ctx, next) { 

		let { data } = await axios.get(`${process.env.TALKBASE_BASE_URL}/v4/kb_list?org_id=${ctx.state.user.organization.org_id}`,
		).catch(err => {
			console.log(err);
			throw err;
		});

		return data;
	},

	async findOne(ctx, next) {

		// Make a GET request to the external API
		const response = await axios.get(`${process.env.TALKBASE_BASE_URL}/v4/kb_meta?kb_name=${ctx.params.id}&rg_id=${ctx.state.user.organization.org_id}`);

		// Return the response from the external API
		return response.data;
	},

	async create(ctx, next) {
		if(ctx.state.user.organization.current_month_usage.kb_count >= ctx.state.user.organization.allowed_kbs){
			return ctx.badRequest('You have exceeded the KB limit')
		}
		// Get the data from the request body
		const data = ctx.request.body;
		const populatedUser = await strapi.query('plugin::users-permissions.user').findOne({
			where: { id: ctx.state.user.id },
			populate: { organization: true },
		}).catch(err => {
			console.log(err);
		});
		if(!data.org_id){
			data.org_id =populatedUser.organization.org_id
		}
		if (!populatedUser.organization) {

			console.log("User doesn't have organization");

		}

		// Make a POST request to the external API with the request body
		const response = await axios.post(`${process.env.TALKBASE_BASE_URL}/v4/add_kb`, data, {
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded'
			}
		});
		await strapi.query('api::monthly-usage.monthly-usage').update({
			where: { id: ctx.state.user.organization.monthly_usages[ctx.state.user.organization.monthly_usages.length - 1].id },
			data: {
				kb_count: ctx.state.user.organization.monthly_usages[ctx.state.user.organization.monthly_usages.length - 1].kb_count+1,
				
			  },
		  });
		  ctx.request.body.data ={
			...ctx.request.body,
		  }

		// Return the response from the external API
		return response.data;
	},

	async update(ctx, next) {
		// Your own logic here
	},

	async delete(ctx) {
		// Your own logic here
	},
	async kbSource(ctx) {
		try {
			let result = await axios.get(`${process.env.TALKBASE_BASE_URL}/v4/kb_sources?page_num=${ctx.query.page_num}&page_size=${ctx.query.page_size}&kb_id=${ctx.query.kb_id}`
			).catch(err=>{
				console.log(err);

			});
			return result.data;
		} catch (err) {
			return ctx.throw(500,"Server error")
		}
	}
};

