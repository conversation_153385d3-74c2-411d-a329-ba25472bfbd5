{"kind": "collectionType", "collectionName": "organizations", "info": {"singularName": "organization", "pluralName": "organizations", "displayName": "organization", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "users_limit": {"type": "integer", "default": 10}, "paid": {"type": "decimal", "default": 0}, "org_id": {"type": "string"}, "users": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user", "mappedBy": "organization"}, "subscription": {"type": "enumeration", "enum": ["subscribed", "pendingUnsubscribed", "unsubscribed", "trial"], "default": "unsubscribed"}, "blocked": {"type": "boolean", "default": false}, "subscribedOn": {"type": "datetime"}, "subscriptionUpdatedOn": {"type": "datetime"}, "subscription_orders": {"type": "relation", "relation": "oneToMany", "target": "api::subscription-order.subscription-order", "mappedBy": "organization"}, "type": {"type": "enumeration", "enum": ["individual", "organization"], "default": "individual"}, "monthly_usages": {"type": "relation", "relation": "oneToMany", "target": "api::monthly-usage.monthly-usage", "mappedBy": "organization"}, "credit_balance": {"type": "relation", "relation": "oneToOne", "target": "api::credit-balance.credit-balance", "mappedBy": "organization"}, "one_time_orders": {"type": "relation", "relation": "oneToMany", "target": "api::one-time-order.one-time-order", "mappedBy": "organization"}, "series": {"type": "relation", "relation": "oneToMany", "target": "api::series.series", "mappedBy": "organization"}, "plan": {"type": "relation", "relation": "manyToOne", "target": "api::plan.plan", "inversedBy": "organizations"}, "api_access_keys": {"type": "relation", "relation": "oneToMany", "target": "api::api-access-key.api-access-key", "private": true, "mappedBy": "organization"}, "stripe_customer": {"type": "string"}}}