const admin = require('firebase-admin');
const serviceAccount = require('./firebase-service-account.json');

module.exports = ({ env }) => ({
  host: env('HOST', '0.0.0.0'),
  port: env.int('PORT', 1337),
  url: env('STRAPI_ADMIN_BACKEND_URL', 'http://localhost:1337'),
  app: {
    keys: env.array('APP_KEYS'),
    proxy: true,
  },
  proxy: true,
  maxFailedAttempt: env.int('MAX_FAILED_ATTEMPT', 3),
  maxResendAttempt: env.int('MAX_RESEND_ATTEMPT', 3),
  waitingTime: env.int('WAIT_TIME', 30),
  otpTimeToLive: env.int('OTP_TIME_TO_LIVE', 1.5),
  bootstrap({ strapi }) {
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });

    // Make firebase available through strapi global object
    strapi.firebase = admin;
  },
});
