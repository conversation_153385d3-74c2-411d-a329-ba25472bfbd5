import React, { useState, useEffect, useRef, useMemo } from 'react';
import './playground.css';
import { useDispatch } from 'react-redux';
import { setAssessmentCompleted, setAssessmentReport } from './store/assessmentSlice';
import jsPDF from 'jspdf'; // Import jsPDF

// Custom hooks
import useChatScroll from './hooks/useChatScroll';
import useTextareaResize from './hooks/useTextareaResize';
import useSession from './hooks/useSession';

// Components
import UserMessage from './components/UserMessage';
import AIMessage from './components/AIMessage';
import TypingIndicator from './components/TypingIndicator';
import ChatInput from './components/ChatInput';
import EmptyState from './components/EmptyState';
import AssessmentEmptyState from './components/AssessmentEmptyState';
import NewMessagesIndicator from './components/NewMessagesIndicator';
import ErrorMessage from './components/ErrorMessage';
import AssessmentReport from './components/AssessmentReport';
import AgentConfigForm from './components/AgentConfigForm';
import AssessmentCompletionAlert from './components/AssessmentCompletionAlert';

// Services
import MessageService from './services/MessageService';
import ChatApiService from './services/ChatApiService';

// Sample data
import sampleMessages from './data/sampleMessages';

/**
 * ChatUI Component - Main chat interface
 * Refactored to follow SOLID principles:
 * - Single Responsibility: Each component/service has one job
 * - Open-Closed: Extended via new components, not modifying existing ones
 * - Liskov Substitution: Components can be swapped with subclasses
 * - Interface Segregation: Components only know what they need to know
 * - Dependency Inversion: High-level modules don't depend on low-level modules
 */
export default function ChatUI() {
	// Configuration flag - Set to false for assessment mode
	const showTestData = false;
	
	// State to track if configuration is complete
	const [isConfigured, setIsConfigured] = useState(false);
	
	// State to track if assessment is completed
	const [isAssessmentCompleted, setIsAssessmentCompleted] = useState(false);
	
	// User configuration
	const [userConfig, setUserConfig] = useState({
		name: '',
		current_role: '',
		years_of_experience: 0,
		target_role: '',
		assessment_type: 'technical'
	});
	
	// State management
	const [messages, setMessages] = useState([]);
	const [input, setInput] = useState('');
	const [isTyping, setIsTyping] = useState(false);
	const [showNewMessageIndicator, setShowNewMessageIndicator] = useState(false);
	const [hasInteracted, setHasInteracted] = useState(false);
	const [isError, setIsError] = useState(false);
	const [assessmentReport, setAssessmentReport] = useState(null);
	const [isSending, setIsSending] = useState(false);
	const [error, setError] = useState(null);
	const [assessmentComplete, setAssessmentComplete] = useState(false);
	
	// Custom hooks
	const { textareaRef, resetHeight, adjustHeight } = useTextareaResize();
	const { chatRef, isAtBottom, scrollToBottomIfNeeded, scrollToBottom, adjustBottomPadding } = useChatScroll([messages.length]);
	const { sessionId, conversationHistory, updateConversationHistory, resetSession } = useSession();
	
	// Get dispatch function from Redux
	const dispatch = useDispatch();
	
	// Create action creators with proper type checking
	const safeDispatch = useMemo(() => {
		return (action) => {
			if (!action || typeof action !== 'object' || !action.type) {
				console.error('Attempted to dispatch an invalid action:', action);
				return;
			}
			try {
				dispatch(action);
			} catch (error) {
				console.error('Error dispatching action:', error);
			}
		};
	}, [dispatch]);
	
	// Refs for observers and animation frames
	const scrollObserverRef = useRef(null);
	const animationFrameRef = useRef(null);
	const lastInputRef = useRef('');
	
	// Handle exporting the chat
	const handleExportChat = () => {
		// PDF generation logic will go here
		console.log("Exporting chat..."); // Placeholder
		
		const doc = new jsPDF();
		const margin = 10;
		const pageHeight = doc.internal.pageSize.getHeight();
		const pageWidth = doc.internal.pageSize.getWidth();
		const usableWidth = pageWidth - margin * 2;
		let currentY = margin;
		
		// Add a title (optional)
		doc.setFontSize(16);
		doc.text("Chat Export", margin, currentY);
		currentY += 10;
		
		doc.setFontSize(12);
		
		messages.forEach(msg => {
			const prefix = msg.type === 'user' ? "User: " : "AI: ";
			const textLines = doc.splitTextToSize(`${prefix}${msg.text}`, usableWidth);
			
			// Check if the text fits on the current page
			if (currentY + (textLines.length * 5) > pageHeight - margin) { // Estimate line height
				doc.addPage();
				currentY = margin;
			}
			
			// Add text line by line
			doc.text(textLines, margin, currentY);
			currentY += (textLines.length * 5) + 5; // Add some padding between messages
		});
		
		doc.save("chat-export.pdf");
	};
	
	// Handle restarting the session
	const handleRestartSession = () => {
		// Reset configuration state
		setIsConfigured(false);
		
		// Reset assessment completed state
		setIsAssessmentCompleted(false);
		
		// Reset messages
		setMessages([]);
		
		// Reset user interaction state
		setHasInteracted(false);
		
		// Reset errors
		setIsError(false);
		
		// Reset assessment report
		setAssessmentReport(null);
		
		// Reset input
		setInput('');
		
		// Reset the session ID
		resetSession();
	};
	
	// Handle closing the completion alert
	const handleCloseCompletionAlert = () => {
		handleRestartSession();
	};
	
	// Handle starting a new session with user configuration
	const handleSessionStart = (config) => {
		// Update user configuration
		setUserConfig(config);
		
		// Mark as configured
		setIsConfigured(true);
		
		// Set the sessionId from the config
		resetSession();
		
		// Mark that the user has interacted
		setHasInteracted(true);
		
		// Don't make an initial API call
		// initiateSession(config);
	};
	
	// Initialize session with an empty first message
	const initiateSession = async (config) => {
		try {
			setIsTyping(true);
			
			// Make API request with empty message to get initial AI greeting
			const response = await ChatApiService.fetchAIResponse(
				'', 
				config.sessionId || sessionId, 
				[],
				config,
				true // This is the first message
			);
			
			// Extract the AI response and updated history
			const { aiResponse, updatedHistory } = response;
			
			// Add the AI greeting
			setMessages([{ type: 'ai', text: aiResponse }]);
			
			// Update conversation history
			const aiHistoryItem = { role: "assistant", content: aiResponse };
			updateConversationHistory(null, aiHistoryItem, true);
			
			// Hide typing indicator
			setIsTyping(false);
			
		} catch (error) {
			console.error('Error initiating session:', error);
			setIsTyping(false);
			setIsError(true);
		}
	};
	
	// Load initial message when component mounts
	useEffect(() => {
		if (showTestData && !hasInteracted) {
			// For test data mode, just show sample messages
			const timer = setTimeout(() => {
				setMessages(sampleMessages.slice(0, 1)); 
				
				// After another delay, show the rest of the sample messages
				setTimeout(() => {
					if (!hasInteracted) {
						setMessages(sampleMessages);
					}
				}, 1000);
			}, 300);
			
			return () => clearTimeout(timer);
		}
	}, [hasInteracted, showTestData]);
	
	// Set up scroll observer to detect when user isn't at the bottom
	useEffect(() => {
		const handleScroll = () => {
			if (chatRef.current) {
				// Check if we're not at the bottom and have messages
				if (!isAtBottom() && messages.length > 0) {
					setShowNewMessageIndicator(true);
				} else {
					setShowNewMessageIndicator(false);
				}
			}
		};
		
		// Throttle scroll events for better performance
		const throttledScrollHandler = () => {
			if (!animationFrameRef.current) {
				animationFrameRef.current = requestAnimationFrame(() => {
					handleScroll();
					animationFrameRef.current = null;
				});
			}
		};
		
		// Attach the event listener
		if (chatRef.current) {
			chatRef.current.addEventListener('scroll', throttledScrollHandler);
		}
		
		return () => {
			if (chatRef.current) {
				chatRef.current.removeEventListener('scroll', throttledScrollHandler);
			}
			
			if (animationFrameRef.current) {
				cancelAnimationFrame(animationFrameRef.current);
			}
		};
	}, [isAtBottom, messages.length]);

	// Handle sending a message and receiving a response
	const handleSend = async () => {
		if (input.trim() === '') return;
		
		// Store the current input then clear it
		const currentInput = input.trim();
		lastInputRef.current = currentInput;
		setInput('');
		resetHeight();
		
		// Prevent sending multiple messages simultaneously
		if (isTyping || isSending) return;
		
		setIsSending(true);
		
		// Mark that the user has interacted
		setHasInteracted(true);
		
		// Clear any previous errors
		setIsError(false);
		
		// Add user message immediately
		const userMessage = { type: 'user', text: currentInput };
		setMessages(prevMessages => [...prevMessages, userMessage]);
		
		// Debug log
		console.log("Sending message with userConfig:", userConfig);
		console.log("Is first message:", messages.length === 0);
		
		// Simulate AI response for test data
		if (showTestData) {
			// Generate random delay between 1-2 seconds
			const responseTime = Math.floor(Math.random() * 1000) + 1000;
			
			// Start the typing indicator
			setIsTyping(true);
			
			// Simulate network delay
			setTimeout(() => {
				// Generate a test response
				const testResponse = MessageService.generateTestResponse(currentInput);
				
				// Add AI message
				setMessages(prevMessages => [...prevMessages, { type: 'ai', text: testResponse }]);
				
				// Stop the typing indicator
				setIsTyping(false);
				setIsSending(false);
				
				// Scroll to the bottom
				scrollToBottomIfNeeded();
			}, responseTime);
			
			return;
		}
		
		// Start the typing indicator
		setIsTyping(true);
		
		try {
			// Make API request
			const response = await ChatApiService.fetchAIResponse(
				currentInput, 
				sessionId, 
				conversationHistory,
				userConfig,
				messages.length === 0 // This is the first message if there are no previous messages
			);
			
			// Extract the AI response, updated history, and assessment report
			const { aiResponse, updatedHistory, assessmentReport, currentPhase } = response;
			
			// Update conversation history
			const userHistoryItem = { role: "user", content: currentInput };
			const aiHistoryItem = { role: "assistant", content: aiResponse };
			updateConversationHistory(userHistoryItem, aiHistoryItem);
			
			// Store assessment report if available
			if (assessmentReport) {
				setAssessmentReport(assessmentReport);
				if (safeDispatch) {
					safeDispatch(setAssessmentReport(assessmentReport));
				}
			}
			
			// Add AI message
			setMessages(prevMessages => [...prevMessages, { type: 'ai', text: aiResponse }]);
			
			// Check if the assessment is in conclusion phase
			if (currentPhase === "conclusion") {
				// Mark assessment as completed
				setIsAssessmentCompleted(true);
				if (safeDispatch) {
					safeDispatch(setAssessmentCompleted(true));
				}
			}
			
			// Hide typing indicator
			setIsTyping(false);
			setIsSending(false);
			
			// Scroll to the bottom
			scrollToBottomIfNeeded();
			
		} catch (error) {
			console.error('Error fetching response:', error);
			setIsTyping(false);
			setIsSending(false);
			setIsError(true);
			setError(error.message || 'Something went wrong. Please try again.');
		}
	};

	// Handle retrying after error
	const handleRetry = async () => {
		setIsError(false);
		setIsTyping(true);
		
		try {
			// Retry the last request
			const response = await ChatApiService.fetchAIResponse(
				lastInputRef.current || '', 
				sessionId, 
				conversationHistory,
				userConfig
			);
			
			// Extract the AI response, updated history, and phase
			const { aiResponse, updatedHistory, assessmentReport: newReport, currentPhase } = response;
			
			// Check if response contains assessment report data
			if (newReport) {
				setAssessmentReport(newReport);
				if (safeDispatch) {
					safeDispatch(setAssessmentReport(newReport));
				}
			}
			
			// Update the messages with AI response
			setMessages(prev => MessageService.addAIMessage(prev, aiResponse));
			
			// Update conversation history
			const userHistoryItem = { role: "user", content: lastInputRef.current || '' };
			const aiHistoryItem = { role: "assistant", content: aiResponse };
			updateConversationHistory(userHistoryItem, aiHistoryItem);
			
			// Hide typing indicator
			setIsTyping(false);
			
			// Check if this is the conclusion phase
			if (currentPhase === 'conclusion') {
				setIsAssessmentCompleted(true);
				if (safeDispatch) {
					safeDispatch(setAssessmentCompleted(true));
				}
			}
			
		} catch (error) {
			setIsTyping(false);
			setIsError(true);
		}
	};

	// Handle input changes
	const handleInputChange = (e) => {
		setInput(e.target.value);
		
		// Adjust height and scroll position
		const textareaHeight = adjustHeight(e.target.value);
		adjustBottomPadding(textareaHeight);
		scrollToBottomIfNeeded();
	};

	// If not configured and not in test mode, show the config form
	if (!isConfigured && !showTestData) {
		return <AgentConfigForm onSessionStart={handleSessionStart} />;
	}

	return (
		<>
			{/* Display assessment completion modal when assessment is completed */}
			<AssessmentCompletionAlert 
				open={isAssessmentCompleted}
				onClose={handleCloseCompletionAlert}
			/>
			
			<div className="dashboard-container">
				<div className="chat-section">
					<div className="chat-container">
						<div className="chat-header">
							<div className="chat-title">
								{userConfig.name && (
									<div className="chat-participant">
										<span className="participant-name">{userConfig.name}</span>
										<span className="participant-role">{userConfig.current_role}</span>
									</div>
								)}
								<div className="chat-assessment-type">
									{userConfig.assessment_type && (
										<span className="assessment-badge">
											{userConfig.assessment_type.charAt(0).toUpperCase() + userConfig.assessment_type.slice(1)} Assessment
										</span>
									)}
								</div>
							</div>
							<div className="chat-actions">
								<button 
									className="restart-session-button"
									onClick={handleExportChat}
									title="Export Chat"
									style={{ marginRight: '8px' }}
								>
									<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
										<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
										<polyline points="7 10 12 15 17 10"></polyline>
										<line x1="12" y1="15" x2="12" y2="3"></line>
									</svg>
									<span>Export Chat</span>
								</button>
								<button 
									className="restart-session-button"
									onClick={handleRestartSession}
									title="Restart session"
								>
									<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
										<path d="M3 2v6h6"></path>
										<path d="M3 13a9 9 0 1 0 3-7.7L3 8"></path>
									</svg>
									<span>Restart</span>
								</button>
							</div>
						</div>
						<div
							ref={chatRef}
							className="chat-messages flex flex-col overflow-scroll pb-[120px] pt-4"
						>
							{/* Empty state when no messages */}
							{messages.length === 0 && !isTyping && (
								showTestData ? <EmptyState /> : <AssessmentEmptyState />
							)}
							
							{/* Display messages */}
							{messages.map((msg, index) => (
								<div key={index} className={`chat-message ${msg.type}`}>
									{msg.type === 'user' ? (
										<UserMessage content={msg.text} />
									) : (
										<AIMessage content={msg.text} />
									)}
								</div>
							))}
							
							{/* Typing indicator */}
							{isTyping && (
								<div className="chat-message ai">
									<div className="message-content">
										<div className="message-header">
											<div className="message-avatar ai-avatar">
												<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="16" height="16">
													<path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
												</svg>
											</div>
											<span>{showTestData ? "AI Assistant" : "Assessment AI"}</span>
										</div>
										<TypingIndicator />
									</div>
								</div>
							)}
							
							{/* Error message */}
							{isError && (
								<div className="chat-message ai error">
									<div className="message-content">
										<ErrorMessage onRetry={handleRetry} />
									</div>
								</div>
							)}
						</div>
						
						{/* New messages indicator */}
						{showNewMessageIndicator && (
							<NewMessagesIndicator onClick={scrollToBottom} />
						)}
					</div>

					<div className="chat-input-wrapper">
						<ChatInput
							input={input}
							setInput={setInput}
							handleSend={handleSend}
							textareaRef={textareaRef}
							handleInputChange={handleInputChange}
							placeholder={showTestData ? "Message AI..." : "Type your response here..."}
							disabled={isTyping || isError || isAssessmentCompleted} // Disable input also when assessment is completed
						/>
					</div>
				</div>
				
				{/* Assessment Report Section */}
				<div className="assessment-section">
					<AssessmentReport reportData={assessmentReport} />
				</div>
			</div>
		</>
	);
}
