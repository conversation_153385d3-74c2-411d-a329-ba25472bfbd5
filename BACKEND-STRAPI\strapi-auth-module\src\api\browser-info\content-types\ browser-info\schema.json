{"kind": "collectionType", "collectionName": "browser_info", "info": {"singularName": "browser-info", "pluralName": "browser-infos", "displayName": "Browser Info", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"userAgent": {"type": "text"}, "language": {"type": "string"}, "cookiesEnabled": {"type": "boolean"}, "screenWidth": {"type": "integer"}, "screenHeight": {"type": "integer"}, "colorDepth": {"type": "integer"}, "browseId": {"type": "string"}, "session": {"type": "relation", "relation": "oneToOne", "target": "api::session.session", "inversedBy": "browserInfo"}}}