import { useEffect } from 'react';
import Typography from '@mui/material/Typography';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import jwtService from 'src/app/auth/services/jwtService';
import { logCheckoutSuccessEvent } from 'src/app/utils/analytics';
import { useSelector } from 'react-redux';
import { selectUser } from 'app/store/userSlice';

function SuccessPage() {
  const user = useSelector(selectUser);
  useEffect(() => {
    logCheckoutSuccessEvent(user.data.email);
    jwtService.signInWithToken();
  }, []);
  return (
    <div className="flex flex-col flex-1 items-center justify-center p-16">
      <div className="w-full max-w-3xl text-center">
       

        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0, transition: { delay: 0.2 } }}
        >
          <Typography
            variant="h1"
            className="mt-16 sm:mt-96 text-4xl md:text-7xl font-extrabold tracking-tight leading-tight md:leading-none text-center"
          >
            Welcome to ajentic.
          </Typography>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0, transition: { delay: 0.2 } }}
        >
          <Typography
            variant="h5"
            // color="text.secondary"
            className="mt-32 text-lg md:text-xl font-regular tracking-tight text-center"
          >
            Thanks for subscribing. Your plan is now active.
          </Typography>
        </motion.div>

        <Link className="block font-normal mt-48" to="/">
          Back to Dashboard
        </Link>
      </div>
    </div>
  );
}

export default SuccessPage;
