{"kind": "collectionType", "collectionName": "inbuilt-tools", "info": {"singularName": "inbuilt-tool", "pluralName": "inbuilt-tools", "displayName": "inbuilt-tools", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "identifier": {"type": "string", "required": true}, "type": {"type": "enumeration", "required": true, "enum": ["inbuilt"], "default": "inbuilt"}, "enabled": {"type": "boolean", "default": true}, "description": {"type": "text", "required": true}, "tool_data": {"type": "json"}, "tool_image_url": {"type": "string"}, "knowledgebases": {"type": "relation", "relation": "manyToMany", "target": "api::knowledgebase.knowledgebase", "inversedBy": "inBuiltTools"}}}