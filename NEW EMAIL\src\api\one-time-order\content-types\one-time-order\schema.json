{"kind": "collectionType", "collectionName": "one_time_orders", "info": {"singularName": "one-time-order", "pluralName": "one-time-orders", "displayName": "One Time Order", "description": "Records individual one-time purchase transactions."}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"organization": {"type": "relation", "relation": "manyToOne", "target": "api::organization.organization", "inversedBy": "one_time_orders"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "plan": {"type": "relation", "relation": "manyToOne", "target": "api::plan.plan"}, "purchase_type": {"type": "enumeration", "enum": ["one-time", "coupon-one-time", "series-one-time"], "required": true, "default": "one-time"}, "credits_added": {"type": "integer"}, "platform": {"type": "enumeration", "enum": ["apple", "google", "stripe", "coupon"], "required": true}, "purchase_identifier": {"type": "string", "private": true}, "coupon_code": {"type": "string"}, "purchase_timestamp": {"type": "datetime"}, "processed_timestamp": {"type": "datetime", "required": true}, "series_purchased": {"type": "relation", "relation": "manyToOne", "target": "api::series.series", "description": "Series purchased with one-time payment"}, "amount_paid": {"type": "decimal", "description": "Amount paid for series purchase"}}}