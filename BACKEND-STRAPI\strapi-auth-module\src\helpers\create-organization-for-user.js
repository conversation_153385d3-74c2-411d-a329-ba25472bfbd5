const axios = require("axios");

module.exports = {
	async createOrgForuser( data ) {
		try{
		const result = await axios.post(
			`${process.env.TALKBASE_BASE_URL}/v4/add_org`,
			{"org_name":data.name},
			{
				headers: {
				  'Content-Type': 'multipart/form-data'
				}
			}
			).catch(err=>{
				throw new Error('Failed to create organization. Please try again later.');
			});
		data = {
			...data,
			type: data.name==='individual'?"individual":"organization",
			org_id: result.data.id,
			usage_quota: 20/2,
			users_limit: 10,
			tokens_used: result.data.tokens_used??0,
			paid: 20,
			cost_used: result.data.cost
		  };


		const response = await strapi.query('api::organization.organization').create({data:data});

	  
		return response;
		}
		catch(e){
			console.log(e);
		}
	}
}