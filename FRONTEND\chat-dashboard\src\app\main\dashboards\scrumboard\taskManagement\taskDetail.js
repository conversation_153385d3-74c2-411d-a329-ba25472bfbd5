import React, { useEffect, useState } from "react";
import axios from "axios";
import LoadingSpinner from "app/shared-components/loading-spinner/loading-spinner";
import AssignmentIcon from "@mui/icons-material/Assignment";
import DescriptionIcon from "@mui/icons-material/Description";
import EventIcon from "@mui/icons-material/Event";
import ScheduleIcon from "@mui/icons-material/Schedule";
import AssessmentIcon from "@mui/icons-material/Assessment";
import EmailIcon from "@mui/icons-material/Email";
import PhoneIcon from "@mui/icons-material/Phone";
import PersonIcon from "@mui/icons-material/Person";
import FingerprintIcon from "@mui/icons-material/Fingerprint";
import { LabelsList } from "./labelList";
import { SessionInfo } from "./sessionInfo";
import SelectDropdown from "app/shared-components/dropdown/selectDropDown";

export const TaskDetail = ({ taskId, ticketStates , onUpdate }) => {
  const [task, setTask] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [formValues, setFormValues] = useState({
    title: "",
    description: "",
    dueDate: "",
    aiAssessment: "",
    email: "",
    timezone: "",
    sessionId: "",
    labels: [],
    userName: "",
  });

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  const ticketStateOptions = () => {
    return ticketStates.map((state) => state.attributes.title); // Returns an array of ticketStates' titles
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const handleFormSubmit = (e) => {
    e.preventDefault();
    console.log("Form Values:", formValues);
    setIsEditing(false);
  };

  const updateTicketState = async (ticketState) => {
    try {
      const newListId = ticketStates.find(
        (state) => state.attributes.title === ticketState
      );
      const response = await axios.put(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/scrumboard-cards/${taskId}`,
        { data: { listId: newListId } }
      );
      console.log("Ticket state updated:", response.data);
      onUpdate(response.data);
    } catch (error) {
      console.error("Failed to update ticket state:", error);
    }
  };

  const getTaskInfo = async (id) => {
    try {
      setLoading(true);
      const response = await axios.get(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/scrumboard-cards/${id}?populate[0]=listId,memberIds,activities,labels,contact_booking,session`
      );
      const data = response.data.data.attributes;
      setTask(data);
      setFormValues({
        title: data.title,
        description: data.description,
        dueDate: new Date(data.dueDate).toLocaleDateString(),
        aiAssessment: data.ai_assesment,
        email: data.contact_booking?.data?.attributes.email || "",
        timezone: data.contact_booking?.data?.attributes.timezone || "",
        sessionId: data.session?.data?.id ?? -1,
        labels: data.labels.data,
        userName: data.contact_booking?.data?.attributes.user_name || null,
        phoneNumber: data.contact_booking?.data?.attributes.phone_number || null,
      });
    } catch (error) {
      setError("Failed to load task information.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (taskId) {
      getTaskInfo(taskId);
    }
  }, [taskId]);

  if (loading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white bg-opacity-50">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) return <div className="text-red-500">{error}</div>;

  return (
    <div className="flex flex-col overflow-y-auto bg-[#FFFFF9] p-8 px-16 h-full bg-white rounded-lg shadow-md transition-transform duration-300 ease-in-out">
      <div className="flex justify-between items-center mb-4">
        <div>
          <div className="flex flex-row">
            <AssignmentIcon className="text-base-purple text-start text-xl mt-4" />
            <h1 className="ml-4 text-xl font-bold text-black">
              {formValues.title}
            </h1>
          </div>

          <p className="mt-8 text-md text-black font-medium">
            Ticket ID: {task?.ticketId}
          </p>
          <div className="flex flex-row items-center">
            <SelectDropdown
              initialValue={task?.listId.data.attributes.title}
              options={ticketStateOptions()}
              onSelect={(option) => {
                updateTicketState(option);
              }}
            />
          </div>
        </div>
        {formValues.labels.length > 0 && (
          <LabelsList labels={formValues.labels} />
        )}
      </div>

      <form onSubmit={handleFormSubmit} className="space-y-16 my-16">
        <div>
          <label className="flex items-center text-base font-medium text-black">
            <DescriptionIcon className="mr-4 text-md text-base-purple" />{" "}
            Description
          </label>
          <textarea
            name="description"
            rows="3"
            value={formValues.description}
            onChange={handleInputChange}
            disabled={!isEditing}
            className={`mt-1 block w-full p-8 text-md border ${
              isEditing ? "border-gray-500" : "border-transparent"
            } rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 `}
          ></textarea>
        </div>

        <div className="grid grid-cols-2 gap-4 my-16">
          <div className="mr-8">
            <label className="flex items-center text-base font-medium text-black">
              <EventIcon className="mr-4 text-md text-base-purple" /> Due Date
            </label>
            <input
              type="text"
              name="dueDate"
              value={formValues.dueDate}
              disabled
              className="mt-1 block w-full p-8 border border-transparent rounded-md shadow-sm text-md text-black"
            />
          </div>

          <div>
            <label className="flex items-center text-base font-medium text-black">
              <ScheduleIcon className="mr-4 text-md text-base-purple" />{" "}
              Timezone
            </label>
            <input
              type="text"
              name="timezone"
              value={formValues.timezone}
              disabled
              className="mt-1 block w-full p-8 border border-transparent rounded-md shadow-sm text-md text-black"
            />
          </div>
        </div>

        <div>
          <label className="flex items-center text-base font-medium text-black">
            <AssessmentIcon className="mr-4 text-md text-base-purple" /> AI
            Assessment
          </label>
          <textarea
            name="aiAssessment"
            rows="2"
            value={formValues.aiAssessment}
            disabled
            className="mt-1 block w-full p-8 border border-transparent rounded-md shadow-sm text-md text-black"
          ></textarea>
        </div>

        {task?.contact_booking?.data?.attributes.user_name  && (
          <div>
            <label className="flex items-center text-base font-medium text-black">
              <PersonIcon className="mr-4 text-md text-base-purple" /> Contact Name
          </label>
          <input
            type="email"
            name="userName"
            value={formValues.userName}
            disabled
              className="mt-1 block w-full p-8 border border-transparent rounded-md shadow-sm text-md text-black"
            />
          </div>
        )}

        {task?.contact_booking?.data?.attributes.phone_number && (
          <div>
            <label className="flex items-center text-base font-medium text-black">
              <PhoneIcon className="mr-4 text-md text-base-purple" /> Contact
              Phone Number
            </label>
            <input
              type="text"
              name="phoneNumber"
              value={formValues.phoneNumber}
              disabled
              className="mt-1 block w-full p-8 border border-transparent rounded-md shadow-sm text-md text-black"
            />
          </div>
        )}

        {task?.contact_booking?.data?.attributes.email && (
          <div>
            <label className="flex items-center text-base font-medium text-black">
              <EmailIcon className="mr-4 text-md text-base-purple" /> Contact
            Email
          </label>
          <input
            type="email"
            name="email"
            value={formValues.email}
            disabled
              className="mt-1 block w-full p-8 border border-transparent rounded-md shadow-sm text-md text-black"
            />
          </div>
        )}

        <div>
          <label className="flex items-center text-base font-medium text-black">
            <FingerprintIcon className="mr-4 text-md text-base-purple" />{" "}
            Session ID
          </label>
          <input
            type="text"
            name="sessionId"
            value={formValues.sessionId}
            disabled
            className="mt-1 block w-full p-8 border border-transparent rounded-md shadow-sm text-md text-black"
          />
        </div>

        {/* <div className="flex justify-end mt-4 space-x-2">
          {isEditing ? (
            <>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Save Changes
              </button>
              <button
                type="button"
                onClick={handleEditToggle}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md shadow-sm hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400"
              >
                Cancel
              </button>
            </>
          ) : (
            <button
              type="button"
              onClick={handleEditToggle}
              className="px-8 py-4 bg-base-purple text-white rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Edit
            </button>
          )}
        </div> */}
      </form>
      {formValues.sessionId != -1 && (
        <SessionInfo sessionId={formValues.sessionId} />
      )}
    </div>
  );
};
