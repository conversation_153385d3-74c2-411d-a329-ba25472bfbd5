import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import * as yup from 'yup';
import _ from '@lodash';
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import jwtService from '../../auth/services/jwtService';

/**
 * Form Validation Schema
 */
const schema = yup.object().shape({
  password: yup
    .string()
    .required('Please enter your password.')
    .min(4, 'Password is too short - must be at least 4 chars.'),
  passwordConfirmation: yup
    .string()
    .required('Please enter your password.')
    .min(4, 'Password is too short - must be at least 4 chars.')
    .oneOf([yup.ref('password')], 'Passwords must match.')
});

const defaultValues = {
  password: '',
  passwordConfirmation: '',
};

export default function ResetPassword() {
  const { control, formState, handleSubmit, setError } = useForm({
    mode: 'onChange',
    defaultValues,
    resolver: yupResolver(schema),
  });
  const emailCode = new URLSearchParams(location.search).get('code');

  const { isValid, dirtyFields, errors } = formState;

  function onSubmit({ password, passwordConfirmation }) {
    jwtService
      .resetPassword( { password, passwordConfirmation, emailCode } )
      .then((user) => {
        // No need to do anything, user data will be set at app/auth/AuthContext
      })
      .catch((error) => {
        setError(error)
      });
  }

  return (
    <div className="flex flex-col sm:flex-row items-center md:items-start sm:justify-center md:justify-start flex-1 min-w-0">
      <Paper className="h-full sm:h-auto md:flex md:items-center md:justify-end w-full sm:w-auto md:h-full md:w-1/2 py-8 px-16 sm:p-48 md:p-64 sm:rounded-2xl md:rounded-none sm:shadow md:shadow-none ltr:border-r-1 rtl:border-l-1">
        <div className="w-full max-w-320 sm:w-320 mx-auto sm:mx-0">
          <div className="flex mb-24">
            <img className="logo-icon w-28" src="assets/images/logo/ajentic-logo.png" alt="logo" />
            <span className="logo-heading">Ajentic</span>
          </div>
          <div className="flex items-baseline mt-2 font-medium">
            <p className="MuiTypography-root MuiTypography-body1 muiltr-18q2h9t">Please enter your new password</p>
          </div>
          <form
            name="loginForm"
            noValidate
            className="flex flex-col justify-center w-full mt-32"
            onSubmit={handleSubmit(onSubmit)}
          >
            <Controller
              name="password"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  className="mb-24"
                  label={
                    <label htmlFor="email" style={{ color: "black" }}>
                      Password
                    </label>
                  }
                  type="password"
                  error={!!errors.password}
                  helperText={errors?.password?.message}
                  variant="outlined"
                  fullWidth
                />
              )}
            />

            <Controller
              name="passwordConfirmation"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  className="mb-24"
                  label={
                    <label htmlFor="email" style={{ color: "black" }}>
                      Confirm Password
                    </label>
                  }
                  type="password"
                  error={!!errors.passwordConfirmation}
                  helperText={errors?.passwordConfirmation?.message}
                  variant="outlined"
                  fullWidth
                />
              )}
            />

            <Button
              variant="contained"
              color="secondary"
              className=" w-full mt-16"
              aria-label="Forgot Password"
              disabled={_.isEmpty(dirtyFields) || !isValid}
              type="submit"
              size="large"
            >
              Reset Password
            </Button>
          </form>
        </div>
      </Paper>

      <Box
        className="relative hidden md:flex flex-auto items-center justify-center h-full p-64 lg:px-112 overflow-hidden"
        sx={{ backgroundColor: 'primary.main' }}
      >
        <svg
          className="absolute inset-0 pointer-events-none"
          viewBox="0 0 960 540"
          width="100%"
          height="100%"
          preserveAspectRatio="xMidYMax slice"
          xmlns="http://www.w3.org/2000/svg"
        >
          <Box
            component="g"
            sx={{ color: 'primary.light' }}
            className="opacity-20"
            fill="none"
            stroke="currentColor"
            strokeWidth="100"
          >
            <circle r="234" cx="196" cy="23" />
            <circle r="234" cx="790" cy="491" />
          </Box>
        </svg>
        <Box
          component="svg"
          className="absolute -top-64 -right-64 opacity-20"
          sx={{ color: 'primary.light' }}
          viewBox="0 0 220 192"
          width="220px"
          height="192px"
          fill="none"
        >
          <defs>
            <pattern
              id="837c3e70-6c3a-44e6-8854-cc48c737b659"
              x="0"
              y="0"
              width="20"
              height="20"
              patternUnits="userSpaceOnUse"
            >
              <rect x="0" y="0" width="4" height="4" fill="currentColor" />
            </pattern>
          </defs>
          <rect width="220" height="192" fill="url(#837c3e70-6c3a-44e6-8854-cc48c737b659)" />
        </Box>

        <div className="z-10 relative w-full max-w-2xl flex">
          <div className="flex-1 mr-20">
            <div className="text-6xl font-bold leading-none text-gray-100">
              <div>Welcome to Ajentic</div>
            </div>
            <div className="mt-24 text-lg tracking-tight leading-6 text-white">
              Create your <b style={{color: '#2394CD'}}>knowledge base</b>, train it with our <b style={{color: '#2394CD'}}>AI</b>, and let your team or clients ask it questions.
            </div>
          </div>
          <div className="flex-1">
            <img className="w-3/4 object-contain" src="assets/images/logo/ajentic-logo.png" alt="Ajentic Logo"/>
          </div>
        </div>
      </Box>
    </div>
  );
}
