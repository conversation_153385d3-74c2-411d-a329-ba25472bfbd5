{"kind": "collectionType", "collectionName": "content_progress", "info": {"singularName": "content-progress", "pluralName": "content-progresses", "displayName": "Content Progress", "description": "User progress tracking for all content - series episodes and standalone tracks"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "file_results_model": {"type": "relation", "relation": "manyToOne", "target": "api::file-results-model.file-results-model", "required": true}, "episode_id": {"type": "string", "description": "UUID reference to series_episodes.episode_id - only for series episodes"}, "series_id": {"type": "integer", "description": "Series reference - only for series episodes, null for standalone tracks"}, "progress_percentage": {"type": "integer", "default": 0, "min": 0, "max": 100, "description": "Progress as integer 0-100 for efficiency"}, "time_spent": {"type": "integer", "default": 0, "description": "Time spent in seconds"}, "last_position": {"type": "integer", "default": 0, "description": "Resume position in seconds"}, "completed": {"type": "boolean", "default": false}, "started_at": {"type": "datetime"}, "completed_at": {"type": "datetime"}, "last_accessed_at": {"type": "datetime"}}}