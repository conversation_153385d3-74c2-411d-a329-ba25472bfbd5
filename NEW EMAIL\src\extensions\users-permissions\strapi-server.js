const _ = require("lodash");
const emailService = require("../../helpers/email-service");

module.exports = (plugin) => {
  const register = plugin.controllers.auth.register;
  const callback = plugin.controllers.auth.callback;
  const forgotPassword = plugin.controllers.auth.forgotPassword;
  const resetPassword = plugin.controllers.auth.resetPassword;

  plugin.controllers.auth.register = async (ctx) => {
    try {
      const logData = {
        timestamp: new Date().toISOString(),
        endpoint: '/api/auth/local/register',
        userAgent: ctx.request.headers['user-agent'],
        ip: ctx.request.ip,
        requestBody: {
          username: ctx.request.body.username,
          email: ctx.request.body.email,
          organization: ctx.request.body.organization,
          provider: ctx.request.body.provider || 'local'
        }
      };
      
      console.log('🔄 SIGNUP FLOW START: Registration request received', logData);
      strapi.log.info('🔄 SIGNUP FLOW START: Registration request received', logData);

      if (typeof ctx.request.body.organization === "number") {
        const orgLogData = {
          organizationId: ctx.request.body.organization,
          accountType: 'organization'
        };
        console.log('📋 SIGNUP FLOW: Organization ID provided, setting account type to organization', orgLogData);
        strapi.log.info('📋 SIGNUP FLOW: Organization ID provided, setting account type to organization', orgLogData);
        
        ctx.request.body = {
          ...ctx.request.body,
          acc_type: "organization",
        };
      } else {
        const createOrgLogData = {
          organizationType: ctx.request.body.organization === "individual" ? "individual" : "organization",
          organizationName: ctx.request.body.organization === "individual" ? ctx.request.body.username : ctx.request.body.organization
        };
        console.log('🏢 SIGNUP FLOW: Creating new organization for user', createOrgLogData);
        strapi.log.info('🏢 SIGNUP FLOW: Creating new organization for user', createOrgLogData);

        let org;
        const userBody = ctx.request.body;
        if (ctx.request.body.organization === "individual") {
          const indOrgLogData = {
            name: ctx.request.body.username,
            type: 'individual'
          };
          console.log('👤 SIGNUP FLOW: Creating individual organization', indOrgLogData);
          strapi.log.info('👤 SIGNUP FLOW: Creating individual organization', indOrgLogData);
          
          ctx.request.body = {
            name: ctx.request.body.username,
            type: "individual",
          };
          org = await strapi.controllers[
            "api::organization.organization"
          ].create(ctx);
          
          const indOrgSuccessLogData = {
            organizationId: org.id,
            organizationName: org.name
          };
          console.log('✅ SIGNUP FLOW: Individual organization created successfully', indOrgSuccessLogData);
          strapi.log.info('✅ SIGNUP FLOW: Individual organization created successfully', indOrgSuccessLogData);
        } else {
          const bizOrgLogData = {
            name: ctx.request.body.organization,
            type: 'organization'
          };
          console.log('🏢 SIGNUP FLOW: Creating business organization', bizOrgLogData);
          strapi.log.info('🏢 SIGNUP FLOW: Creating business organization', bizOrgLogData);
          
          ctx.request.body = {
            name: ctx.request.body.organization,
            type: "organization",
          };
          org = await strapi.controllers[
            "api::organization.organization"
          ].create(ctx);
          
          const bizOrgSuccessLogData = {
            organizationId: org.id,
            organizationName: org.name
          };
          console.log('✅ SIGNUP FLOW: Business organization created successfully', bizOrgSuccessLogData);
          strapi.log.info('✅ SIGNUP FLOW: Business organization created successfully', bizOrgSuccessLogData);
        }
        ctx.request.body = {
          ...userBody,
          organization: org.id,
          acc_type:
            userBody.organization === "individual"
              ? "individual"
              : "organization",
        };
        
        const userDataLogData = {
          organizationId: org.id,
          accountType: userBody.organization === "individual" ? "individual" : "organization",
          username: userBody.username,
          email: userBody.email
        };
        console.log('🔗 SIGNUP FLOW: User data prepared for registration', userDataLogData);
        strapi.log.info('🔗 SIGNUP FLOW: User data prepared for registration', userDataLogData);
      }

      const coreRegLogData = {
        username: ctx.request.body.username,
        email: ctx.request.body.email,
        organizationId: ctx.request.body.organization,
        accountType: ctx.request.body.acc_type
      };
      console.log('🎯 SIGNUP FLOW: Calling core Strapi user registration', coreRegLogData);
      strapi.log.info('🎯 SIGNUP FLOW: Calling core Strapi user registration', coreRegLogData);

      // COMPLETELY BYPASS Strapi's default registration to prevent email sending
      // Create user manually without triggering email confirmation
      const user = await strapi.plugin('users-permissions').service('user').add({
        ...ctx.request.body,
        provider: 'local', // CRITICAL: Set provider to 'local' for login to work
        confirmed: false, // Keep as false so our lifecycle can send the custom email
        blocked: false,
        role: 1, // Set default authenticated role
      });

      // Return the user in the same format as the default registration
      const { sanitize } = require('@strapi/utils');
      const sanitizedUser = await sanitize.contentAPI.output(user, strapi.getModel('plugin::users-permissions.user'));
      ctx.send({ user: sanitizedUser });

      const successLogData = {
        username: ctx.request.body.username,
        email: ctx.request.body.email,
        timestamp: new Date().toISOString()
      };
      console.log('✅ SIGNUP FLOW: Core user registration completed successfully', successLogData);
      strapi.log.info('✅ SIGNUP FLOW: Core user registration completed successfully', successLogData);

    } catch (e) {
      const errorLogData = {
        error: e.message,
        stack: e.stack,
        timestamp: new Date().toISOString(),
        requestData: {
          username: ctx.request.body?.username,
          email: ctx.request.body?.email,
          organization: ctx.request.body?.organization
        }
      };
      console.error('❌ SIGNUP FLOW ERROR: Registration failed', errorLogData);
      strapi.log.error('❌ SIGNUP FLOW ERROR: Registration failed', errorLogData);
      ctx.throw(400, e);
    }
  };
  plugin.controllers.auth.callback = async (ctx) => {
    try {
      // Handle email confirmation properly like default Strapi flow
      const { confirmation } = ctx.query;
      
      if (confirmation) {
        // Find user by confirmation token and update confirmed status
        const user = await strapi.query('plugin::users-permissions.user').findOne({
          where: { confirmationToken: confirmation }
        });
        
        if (user && !user.confirmed) {
          await strapi.query('plugin::users-permissions.user').update({
            where: { id: user.id },
            data: { confirmed: true, confirmationToken: null }
          });
        }
      }
      
      await callback(ctx);
    } catch (error) {
      ctx.throw(400, error);
    }
  };

  plugin.routes['content-api'].routes.push({
    method: 'POST',
    path: '/auth/local/fcm',
    handler: 'auth.saveFCM',
    config: {
      prefix: '',
      policies: []
    }
  });

  plugin.controllers.auth.saveFCM = async (ctx) => {
    var res = await strapi.entityService.update('plugin::users-permissions.user', ctx.state.user.id, { data: { fcm_token: ctx.request.body.token } });
    ctx.body = res;
  };

  // Override forgot password to use SendGrid Dynamic Template
  plugin.controllers.auth.forgotPassword = async (ctx) => {
    try {
      const { email } = ctx.request.body;

      if (!email) {
        return ctx.badRequest('Email is required');
      }

      const logData = {
        timestamp: new Date().toISOString(),
        endpoint: '/api/auth/forgot-password',
        email: email,
        userAgent: ctx.request.headers['user-agent'],
        ip: ctx.request.ip
      };
      
      console.log('🔄 FORGOT PASSWORD FLOW START: Request received', logData);
      strapi.log.info('🔄 FORGOT PASSWORD FLOW START: Request received', logData);

      // Find user by email
      const user = await strapi.query('plugin::users-permissions.user').findOne({
        where: { email: email.toLowerCase() }
      });

      if (!user) {
        // For security reasons, don't reveal if email exists or not
        console.log('⚠️ FORGOT PASSWORD FLOW: User not found for email', { email });
        strapi.log.info('⚠️ FORGOT PASSWORD FLOW: User not found for email', { email });
        return ctx.send({ ok: true });
      }

      if (user.blocked) {
        console.log('⚠️ FORGOT PASSWORD FLOW: User is blocked', { email, userId: user.id });
        strapi.log.info('⚠️ FORGOT PASSWORD FLOW: User is blocked', { email, userId: user.id });
        return ctx.badRequest('This user is blocked');
      }

      // Generate reset password token
      const crypto = require('crypto');
      const resetPasswordToken = crypto.randomBytes(64).toString('hex');

      // Update user with reset token
      await strapi.query('plugin::users-permissions.user').update({
        where: { id: user.id },
        data: { resetPasswordToken }
      });

      console.log('🔑 FORGOT PASSWORD FLOW: Reset token generated', { 
        email, 
        userId: user.id,
        tokenLength: resetPasswordToken.length 
      });
      strapi.log.info('🔑 FORGOT PASSWORD FLOW: Reset token generated', { 
        email, 
        userId: user.id,
        tokenLength: resetPasswordToken.length 
      });

      // Create reset password link - point to frontend reset page
      const frontendUrl = process.env.DASHBOARD_URL || 'http://localhost:3000';
      const resetPasswordLink = `${frontendUrl}/reset-password?code=${resetPasswordToken}`;

      // Send email using SendGrid Dynamic Template (same pattern as confirmation email)
      try {
        const emailConnectorService = strapi.service('api::email-connector.email-connector');
        const result = await emailConnectorService.sendTemplateEmail({
          templateName: 'reset-password',
          to: user.email,
          templateData: {
            // Only provide dynamic values - defaults come from template
            name: user.username,
            username: user.username,
            action_url: resetPasswordLink,
      
          },
          provider: 'postmark'  // Using Postmark for email delivery
        })


        console.log('✅ FORGOT PASSWORD FLOW: Reset email sent via Postmark template', {
          email,
          userId: user.id,
          template: 'reset-password'
        });
       

      } catch (error) {
        console.error('❌ FORGOT PASSWORD FLOW ERROR: Failed to send reset email via Postmark', {
          email,
          userId: user.id,
          error: error.message,
          stack: error.stack
        });

        
        return ctx.badRequest('Unable to send reset email');
      }

      ctx.send({ ok: true });

    } catch (error) {
      const errorLogData = {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        requestData: {
          email: ctx.request.body?.email
        }
      };
      console.error('❌ FORGOT PASSWORD FLOW ERROR: Request failed', errorLogData);
      strapi.log.error('❌ FORGOT PASSWORD FLOW ERROR: Request failed', errorLogData);
      ctx.throw(400, error);
    }
  };

  // Override reset password to work with our custom token
  plugin.controllers.auth.resetPassword = async (ctx) => {
    try {
      const { code, password, passwordConfirmation } = ctx.request.body;

      if (!code) {
        return ctx.badRequest('Reset code is required');
      }

      if (!password) {
        return ctx.badRequest('Password is required');
      }

      if (!passwordConfirmation) {
        return ctx.badRequest('Password confirmation is required');
      }

      if (password !== passwordConfirmation) {
        return ctx.badRequest('Passwords do not match');
      }

      const logData = {
        timestamp: new Date().toISOString(),
        endpoint: '/api/auth/reset-password',
        userAgent: ctx.request.headers['user-agent'],
        ip: ctx.request.ip,
        codeLength: code.length
      };
      
      console.log('🔄 RESET PASSWORD FLOW START: Request received', logData);
      strapi.log.info('🔄 RESET PASSWORD FLOW START: Request received', logData);

      // Find user by reset token
      const user = await strapi.query('plugin::users-permissions.user').findOne({
        where: { resetPasswordToken: code }
      });

      if (!user) {
        console.log('⚠️ RESET PASSWORD FLOW: Invalid or expired reset token');
        strapi.log.info('⚠️ RESET PASSWORD FLOW: Invalid or expired reset token');
        return ctx.badRequest('Invalid or expired reset token');
      }

      if (user.blocked) {
        console.log('⚠️ RESET PASSWORD FLOW: User is blocked', { email: user.email, userId: user.id });
        strapi.log.info('⚠️ RESET PASSWORD FLOW: User is blocked', { email: user.email, userId: user.id });
        return ctx.badRequest('This user is blocked');
      }

      // Hash the new password
      const bcrypt = require('bcryptjs');
      const hashedPassword = await bcrypt.hash(password, 10);

      // Update user with new password and clear reset token
      await strapi.query('plugin::users-permissions.user').update({
        where: { id: user.id },
        data: { 
          password: hashedPassword,
          resetPasswordToken: null
        }
      });

      console.log('✅ RESET PASSWORD FLOW: Password updated successfully', { 
        email: user.email, 
        userId: user.id 
      });
      strapi.log.info('✅ RESET PASSWORD FLOW: Password updated successfully', { 
        email: user.email, 
        userId: user.id 
      });

      // Return user data (same format as login)
      const { sanitize } = require('@strapi/utils');
      const sanitizedUser = await sanitize.contentAPI.output(user, strapi.getModel('plugin::users-permissions.user'));
      
      // Generate JWT token for immediate login
      const jwt = strapi.plugin('users-permissions').service('jwt');
      const token = jwt.issue({ id: user.id });

      ctx.send({
        jwt: token,
        user: sanitizedUser,
      });

    } catch (error) {
      const errorLogData = {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        requestData: {
          hasCode: !!ctx.request.body?.code,
          hasPassword: !!ctx.request.body?.password
        }
      };
      console.error('❌ RESET PASSWORD FLOW ERROR: Request failed', errorLogData);
      strapi.log.error('❌ RESET PASSWORD FLOW ERROR: Request failed', errorLogData);
      ctx.throw(400, error);
    }
  };

  return plugin;
};
