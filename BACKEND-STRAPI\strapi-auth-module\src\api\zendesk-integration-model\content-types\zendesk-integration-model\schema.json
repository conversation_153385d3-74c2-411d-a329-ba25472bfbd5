{"kind": "collectionType", "collectionName": "zendesk_integration_models", "info": {"singularName": "zendesk-integration-model", "pluralName": "zendesk-integration-models", "displayName": "zendesk integration model", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"zendesk_email": {"type": "email", "required": true}, "zendesk_subdomain": {"type": "text", "required": true}, "zendesk_api_token": {"type": "text", "required": true}, "knowledgebase": {"type": "relation", "relation": "oneToOne", "target": "api::knowledgebase.knowledgebase", "inversedBy": "zendesk_integration_model"}}}