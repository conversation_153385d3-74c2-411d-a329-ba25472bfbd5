'use strict';
/**
 * url-tracker controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const { v4: uuidv4 } = require('uuid');

module.exports = createCoreController('api::url-tracker.url-tracker', ({ strapi }) => ({
        async create(ctx) {
            try {
                    ctx.request.body.data = {
                        ...ctx.request.body,
                        tracking_id: uuidv4()
                    };

                    const response = await super.create(ctx);

                    return response.data;
            } catch (err) {
                return ctx.throw(500,"Server error")
            }
        },
    })
);
