/**
 * This file was automatically generated by <PERSON><PERSON><PERSON>.
 * Any modifications made will be discarded.
 */
import i18N from "@strapi/plugin-i18n/strapi-admin";
import sentry from "@strapi/plugin-sentry/strapi-admin";
import usersPermissions from "@strapi/plugin-users-permissions/strapi-admin";
import importExportEntries from "strapi-plugin-import-export-entries/strapi-admin";
import strapiStripe from "strapi-stripe/strapi-admin";
import otpAuthentication from "../../src/plugins/otp-authentication/strapi-admin";
import { renderAdmin } from "@strapi/strapi/admin";

renderAdmin(document.getElementById("strapi"), {
  plugins: {
    i18n: i18N,
    sentry: sentry,
    "users-permissions": usersPermissions,
    "import-export-entries": importExportEntries,
    "strapi-stripe": strapiStripe,
    "otp-authentication": otpAuthentication,
  },
});
