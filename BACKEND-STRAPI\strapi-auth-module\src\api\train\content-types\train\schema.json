{"kind": "collectionType", "collectionName": "trains", "info": {"singularName": "train", "pluralName": "trains", "displayName": "Train", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"link": {"type": "string", "required": true}, "train_status": {"type": "boolean", "default": false}, "knowledgebase": {"type": "relation", "relation": "oneToOne", "target": "api::knowledgebase.knowledgebase", "inversedBy": "trains"}}}