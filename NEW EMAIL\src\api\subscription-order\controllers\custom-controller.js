"use strict";

/**
 * answer controller
 */

const { createCoreController } = require("@strapi/strapi").factories;
const axios = require("axios");
const stripePaymentProvider = require("../../../providers/stripe-payment-provider");

module.exports = createCoreController("api::subscription-order.subscription-order", ({ strapi }) => ({
    async unsubscribe(ctx) {
        try {
          console.log("in unsubscribe", ctx);
          const result = await stripePaymentProvider.cancelSubscription(ctx);
          return result;
        } catch (e) {
          console.log(e);
          return ctx.throw(500, "Something went wrong");
        }
    },
  })
);
