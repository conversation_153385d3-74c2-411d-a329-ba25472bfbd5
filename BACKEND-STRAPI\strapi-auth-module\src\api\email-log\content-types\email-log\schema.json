{"kind": "collectionType", "collectionName": "email_logs", "info": {"singularName": "email-log", "pluralName": "email-logs", "displayName": "<PERSON><PERSON>", "description": "Track email sending history and delivery status for all email flows including ticket notifications"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"message_id": {"type": "string", "unique": true, "description": "Unique message ID from email provider"}, "provider": {"type": "enumeration", "enum": ["postmark", "sendgrid", "mailgun", "ses"], "default": "postmark", "required": true, "description": "Email provider used to send the email"}, "to_emails": {"type": "json", "required": true, "description": "Array of recipient email addresses"}, "from_email": {"type": "email", "required": true, "description": "Sender email address"}, "from_name": {"type": "string", "description": "Sender display name"}, "subject": {"type": "string", "description": "Email subject line"}, "template_name": {"type": "string", "description": "Name of the email template used"}, "template_id": {"type": "string", "description": "Provider-specific template ID"}, "template_data": {"type": "json", "description": "Template variables and data used for rendering"}, "status": {"type": "enumeration", "enum": ["pending", "sent", "delivered", "opened", "clicked", "bounced", "failed", "spam", "unsubscribed"], "default": "pending", "required": true, "description": "Current delivery status of the email"}, "email_type": {"type": "enumeration", "enum": ["confirmation", "welcome", "reset-password", "ticket-notification", "system", "marketing", "transactional"], "default": "transactional", "description": "Type/category of email sent"}, "error_message": {"type": "text", "description": "Error message if email failed to send"}, "provider_response": {"type": "json", "description": "Full response from email provider"}, "sent_at": {"type": "datetime", "description": "Timestamp when email was sent"}, "delivered_at": {"type": "datetime", "description": "Timestamp when email was delivered"}, "opened_at": {"type": "datetime", "description": "Timestamp when email was first opened"}, "clicked_at": {"type": "datetime", "description": "Timestamp when email links were first clicked"}, "bounced_at": {"type": "datetime", "description": "Timestamp when email bounced"}, "organization": {"type": "relation", "relation": "manyToOne", "target": "api::organization.organization", "description": "Organization associated with this email"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "description": "User who triggered or received this email"}, "email_template": {"type": "relation", "relation": "manyToOne", "target": "api::email-template.email-template", "description": "Email template used (if managed through database)"}, "retry_count": {"type": "integer", "default": 0, "description": "Number of retry attempts for failed emails"}, "max_retries": {"type": "integer", "default": 3, "description": "Maximum number of retry attempts allowed"}, "next_retry_at": {"type": "datetime", "description": "Scheduled time for next retry attempt"}, "metadata": {"type": "json", "default": {}, "description": "Additional metadata and tracking information"}, "ticket_id": {"type": "string", "description": "Associated ticket ID for ticket notification emails"}, "agent_name": {"type": "string", "description": "Agent name for ticket notification emails"}, "customer_email": {"type": "email", "description": "Customer email for ticket notification emails"}, "priority": {"type": "enumeration", "enum": ["low", "normal", "high", "urgent"], "description": "Email priority level"}, "tags": {"type": "json", "default": [], "description": "Tags for categorizing and filtering emails"}}}