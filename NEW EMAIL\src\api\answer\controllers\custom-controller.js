"use strict";

/**
 * answer controller
 */

const { createCoreController } = require("@strapi/strapi").factories;
const axios = require("axios");

module.exports = createCoreController("api::answer.answer", ({ strapi }) => ({
  async askquestion(ctx) {
    try {
      const kb = await strapi
        .query("api::knowledgebase.knowledgebase")
        .findOne({
          where: { kb_id: ctx.request.body.kb_id },
          populate: {
            default_ai_task: true,
            shopify_model: true,
            ai_agent: true,
          },
        });
      let promptQuestion;
      if (kb.prompt_prefix) {
        promptQuestion = `Use these instructions to answer the question
        Instructions :${kb.prompt_prefix}
        Question :${ctx.request.body.question}`;
      } else {
        promptQuestion = ctx.request.body.question;
      }
      var status = true;
      let answerResult;
      if (kb.type === "shopify") {
        const body = {
          question: promptQuestion,
          org_id: ctx.request.body.org_id,
          base_url: kb.shopify_model.url,
          headers: {
            "X-Shopify-Storefront-Access-Token":
              kb.shopify_model.store_front_api_key,
          },
        };
        answerResult = await axios
          .post(`${process.env.TALKBASE_BASE_URL}/v4/api_task`, body)
          .catch((err) => {
            console.log(err);
            ctx.badRequest(err);
          });
      }
      if (
        kb.ai_agent &&
        kb.default_ai_task.task_id >= 20 &&
        kb.default_ai_task.task_id < 30
      ) {
        answerResult = await axios
          .post(
            `${process.env.TALKBASE_BASE_URL}/v4/agent`,
            {
              question: promptQuestion,
              kb_id: ctx.request.body.kb_id,
              org_id: ctx.request.body.org_id,
              agent_id: kb.default_ai_task.task_id ?? 16,
              session_id:
                ctx.request.body.session ?? ctx.request.body?.session_id ?? "",
              search_type: kb.search_type ?? "similarity",
              k_similarity: kb.k_similarity ?? 3,
              fetch_k: kb.fetch_k ?? 20,
              lamba_mul_mmr: kb.lamba_mul_mmr ?? 60,
              similarity_score_threshold: kb.similarity_score_threshold ?? 0.0,
              ai_model_name: kb.ai_model_name ?? "gpt-3.5-turbo",
              agent_name: kb.ai_agent.agent_name,
              agent_role: kb.ai_agent.agent_role,
              company_name: kb.ai_agent.company_name,
              company_business: kb.ai_agent.company_business,
              company_values: kb.ai_agent.company_values,
              conversation_purpose: kb.ai_agent.conversation_purpose,
              booking_link: kb.ai_agent.booking_base_url,
            },
            {
              timeout: 300000, // Set a timeout of 5mins/300seconds
              headers: {
                "Content-Type": "application/json",
              },
            }
          )
          .catch((err) => {
            console.log(err);
            status = false;
            ctx.badRequest(err.toString());
            // throw err;
          });
      } else {
        answerResult = await axios
          .post(
            `${process.env.TALKBASE_BASE_URL}/v4/answer`,
            {
              kb_id: ctx.request.body.kb_id,
              org_id: ctx.request.body.org_id,
              question: ctx.request.body.question,
              prefix_prompt: kb.prompt_prefix,
              agent_id: kb.default_ai_task?.task_id ?? 2,
              session:
                ctx.request.body?.session ?? ctx.request.body?.session_id ?? "",
              search_type: kb.search_type ?? "similarity",
              k_similarity: kb.k_similarity ?? 3,
              fetch_k: kb.fetch_k ?? 20,
              lamba_mul_mmr: kb.lamba_mul_mmr ?? 60,
              similarity_score_threshold: kb.similarity_score_threshold ?? 0.0,
              ai_model_name: kb.ai_model_name ?? "gpt-3.5-turbo",
            },
            {
              headers: {
                "Content-Type": "application/json",
              },
            }
          )
          .catch((err) => {
            console.log(err);
            ctx.badRequest(err);
          });
      }
      if (answerResult.data?.result === "False") {
        status = false;
      }

      ctx.request.body.data = {};
      ctx.request.body.data["question"] = ctx.request.body.question;
      ctx.request.body.data["kb_id"] = ctx.request.body.kb_id;
      ctx.request.body.data["org_id"] = ctx.request.body.org_id;
      ctx.request.body.data["answer"] = answerResult.data.answer;
      ctx.request.body.data["sources"] = answerResult.data.sources ?? [];
      ctx.request.body.data["did_find_answer"] =
        answerResult.data?.did_find_answer === "false" ? false : true ?? null;
      ctx.request.body.data["session"] =
        ctx.request.body?.session ?? ctx.request.body?.session_id ?? "";
      ctx.request.body.data["answer_status"] = status;
      ctx.request.body.data["knowledgebase"] = kb.id;
      ctx.request.body.data["api_source"] =
        ctx.request.body.api_source ?? "chatBot";
      const response = await super.create(ctx);
      try {
        const isWhatsapp =
          ctx.request.body?.api_source &&
          ctx.request.body.api_source.toLowerCase().startsWith("whatsapp");
        if (isWhatsapp) {
          response.data.attributes.sources =
            response.data?.attributes?.sources.map(
              (e) =>
                `${process.env.TALKBASE_TRACKER_URL}?url=${e}&query_id=${response.data.id}`
            );
          switch (kb.collect_leads) {
            case "Always":
              response.data.attributes["collect_lead"] = true;
              var message;
              if (answerResult.data?.did_find_answer === "false") {
                message = `We couldn't find the answer to this question, our team is committed to helping you out! You can request for further assistance here…\n${
                  process.env.MEETING_SCHEDULE_URL
                }?queryId=${response?.data?.id}&sessionId=${
                  response?.data?.attributes.session
                }${isWhatsapp ? "&isWhatsapp=true" : ""}`;
              } else {
                message = `${
                  response.data.attributes.answer
                }\n\nHope that helped! You can ask us any other question and we will help out. If you require additional help from a human, you can raise a request here :${
                  process.env.MEETING_SCHEDULE_URL
                }?queryId=${response?.data?.id}&sessionId=${
                  response?.data?.attributes.session
                }${isWhatsapp ? "&isWhatsapp=true" : ""}`;
              }
              response.data.attributes.answer = message;

              break;
            case "TriggeredWhenUnanswered":
              if (answerResult.data?.did_find_answer === "false") {
                response.data.attributes["collect_lead"] = true;
                var message;
                if (answerResult.data?.did_find_answer === "false") {
                  message = `We couldn't find the answer to this question, our team is committed to helping you out! You can request for further assistance here…\n${
                    process.env.MEETING_SCHEDULE_URL
                  }?queryId=${response?.data?.id}&sessionId=${
                    response?.data?.attributes.session
                  }${isWhatsapp ? "&isWhatsapp=true" : ""}`;
                } else {
                  message = `${response.data.attributes.answer}\n${
                    process.env.MEETING_SCHEDULE_URL
                  }?queryId=${response?.data?.id}&sessionId=${
                    response?.data?.attributes.session
                  }${isWhatsapp ? "&isWhatsapp=true" : ""}`;
                }
                response.data.attributes.answer = message;
              }
              break;
            case "NoLeads":
              break;

            default:
              break;
          }
        } else {
          switch (kb.collect_leads) {
            case "Always":
              response.data.attributes["collect_lead"] = true;

              break;
            case "TriggeredWhenUnanswered":
              if (answerResult.data?.did_find_answer === "false") {
                response.data.attributes["collect_lead"] = true;
              }
              break;
            case "NoLeads":
              break;

            default:
              break;
          }
        }
      } catch (e) {
        console.log(e);
      }
      return response;
    } catch (err) {
      console.log(err);
      return ctx.throw(500, "Server error");
    }
  },
  async runanswer(ctx) {
    try {
      let answerResult = await axios
        .post(
          `${process.env.TALKBASE_BASE_URL}/v4/run_answer`,
          {
            kb_id: ctx.request.body.kb_id,
            org_id: ctx.request.body.org_id,
            question: ctx.request.body.question,
          },
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        )
        .catch((err) => {
          console.log(err);
        });
      return answerResult.data;
    } catch (err) {
      return ctx.throw(500, "Server error");
    }
  },

  async sessionanswer(ctx) {
    try {
      // const sessions = await strapi.db.connection
      // .table('answers')
      // .select('session')
      // .distinct('session');

      const answers = await strapi.db.query("api::answer.answer").findMany({
        where: {
          org_id: ctx.state.user.organization.org_id,
        },
        populate: {
          knowledgebase: true,
        },
        sort: { createdAt: "desc" },
      });
      const groupedAnswers = answers.reduce((acc, answer) => {
        if (!acc[answer.session]) {
          acc[answer.session] = [];
        }
        acc[answer.session].push(answer);
        return acc;
      }, {});
      const page = ctx.query.page || 1;
      const pageSize = ctx.query.pageSize || 10;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      // Slice the groupedAnswers object to get paginated results
      const paginatedAnswers = Object.keys(groupedAnswers)
        .slice(startIndex, endIndex)
        .reduce((acc, key) => {
          acc[key] = groupedAnswers[key];
          return acc;
        }, {});

      return paginatedAnswers;
    } catch (e) {
      console.log(e);
      throw e;
    }
  },
  async multiagent(ctx) {
    try {

      // const currentUsage = await strapi.query("api::monthly-usage.monthly-usage").findOne({
      //   where: { id: ctx.state.user.organization.current_month_usage.id },
      // });

      const kb = await strapi
        .query("api::knowledgebase.knowledgebase")
        .findOne({
          where: { id: ctx.request.body.kb_id },
          populate: {
            default_ai_task: true,
          },
        });
      const body = {
        user_id: ctx.state.user.id,
        org_id: ctx.state.user.organization?.org_id,
        kb_id: kb.kb_id,
        objective: ctx.request.body.objective,
        max_sections: ctx.request.body.max_sections,
        guidelines: ctx.request.body.guidelines,
        publish_markdown: true,
        publish_pdf: true,
        publish_docx: true,
        follow_guidelines: true,
        ai_model_name: kb.ai_model_name,
        agent_id: 30,
        session_id: ctx.request.body.session_id,
        search_type: kb.search_type,
        k_similarity: kb.k_similarity,
        fetch_k: kb.fetch_k,
        lambda_mul_mmr: kb.lambda_mul_mmr,
        similarity_score_threshold: kb.similarity_score_threshold,
        usage_id: "",
      };
      const answerResult = await axios
        .post(`${process.env.TALKBASE_BASE_URL}/v4/long_agent`, body,{ timeout: 6000000 })
        .catch((err) => {
          console.log(err);
          ctx.badRequest(err);
        });
        return answerResult.data;
    } catch (err) {
      console.log(err);
      return ctx.throw(500, "Server error");
    }
  },
}));
