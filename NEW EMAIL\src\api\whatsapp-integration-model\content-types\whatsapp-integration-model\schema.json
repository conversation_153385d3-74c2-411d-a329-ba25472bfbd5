{"kind": "collectionType", "collectionName": "whatsapp_integration_models", "info": {"singularName": "whatsapp-integration-model", "pluralName": "whatsapp-integration-models", "displayName": "whatsapp integration model", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"phone_number": {"type": "biginteger", "unique": true, "required": true}, "phone_number_id": {"type": "biginteger", "required": true, "unique": true}, "access_token": {"type": "text"}, "knowledgebase": {"type": "relation", "relation": "oneToOne", "target": "api::knowledgebase.knowledgebase", "inversedBy": "whatsapp_integration_model"}}}