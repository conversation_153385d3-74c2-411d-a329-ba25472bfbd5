.answer-sources-conatiner {
  display: flex;
  flex-wrap: wrap;
  flex-flow: row;
  gap: 40px;  /* Increased the gap for better spacing */
  padding:4px; /* Add padding to the container  /* Add a subtle background color */
}


.meta-links {
  flex: 1 0 30%;

}

.no-meta {
  background: #F2F4F6; /* Lighter shade of gray */
  border-radius: 10px;
  margin-bottom: 15px;
  padding: 20px; /* Increase padding */
  color: #268ECC !important;
  font-weight: 500; /* Slight boldness */
}

.source-links {
  overflow-y: scroll;
  max-height: 500px;
  border-radius: 10px;
  max-width: 350px;
}

.fieldset {
  border-radius: 10px;
 
  padding: 24px; /* Increased padding */
  min-height: 160px;
  
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
}

/* Micro-interaction on active (clicked) */
&:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
}

.info-tip {
  border-radius: 10px;
  box-shadow: 0px 0px 15px rgba(227, 50, 50, 0.08); 
  padding: 20px;
  margin-bottom: 25px; /* Slightly increased margin */
  align-items: center;
}

.info-tip span {
  font-size: 13px; /* Slightly bigger font-size */
  color: #030229;
  opacity: 0.85; /* Less transparent */
}

.leading-normal {
  overflow-y: scroll;
  border-radius: 10px;
  margin-bottom: 10px;
}

.answer-sources-separator {
  margin-top: 25px; /* Slightly increased margin */
  margin-bottom: 25px;
  border-top: 2px solid #E5E8ED; /* Slightly thicker and lighter border */
}

.sources-container {
  margin-top: 25px;
}

.sources-title {
  font-weight: bold;
  margin-bottom: 12px; /* Adjusted margin */
  color: #323A4B; /* Color adjusted for better readability */
}

.source-bubble {
  background-color: #E5E8ED;
  border-radius: 25px; /* Increase radius for rounded appearance */
  display: inline-block;
  font-size: 15px; /* Increased font size */
  margin-right: 12px; /* Increased margin */
  margin-bottom: 12px;
  padding: 6px 12px; /* Adjusted padding */
  white-space: nowrap;
}

.sources-row {
  display: flex;
  flex-wrap: wrap;
}

.legend {
  padding-inline: 12px; /* Adjusted padding */
  color: #323A4B;
  font-weight: 500;
}

.loading-div {
  display: flex;
  flex-flow: column;
  align-items: start; /* Center the loading div */
}

.line {
  height: 5px; /* Increased height */
  margin: 10px; /* Increased margin */
  background-color: #c7c9cc; /* Adjusted color */
  animation: typing 1.5s infinite;
}

.line:nth-child(1) {
  width: 50%;
}

.line:nth-child(2) {
  width: 44%;
}

.line:nth-child(3) {
  width: 60%;
}

.line:nth-child(4) {
  width: 55%;
}

.line:nth-child(5) {
  width: 45%;
}

.line:nth-child(6) {
  width: 40%;
}

@keyframes typing {
  0% {
    opacity: 0;
    transform: scaleX(0);
    transform-origin: left;
  }
  100% {
    opacity: 0.4;
    transform: scaleX(1);
    transform-origin: left;
  }
}

@media only screen and (max-width: 600px) {
  .fieldset {
    min-width: 200px;
    min-height: 170px;
  }

  .answer-sources-conatiner{
    flex-flow: column;
  }

  .info-tip{
    width: 100%;
  }

  .line:nth-child(1) {
    width: 96%;
  }
  
  .line:nth-child(2) {
    width: 90%;
  }
  
  .line:nth-child(3) {
    width: 80%;
  }
  
  .line:nth-child(4) {
    width: 60%;
  }
  
  .line:nth-child(5) {
    width: 70%;
  }
  
  .line:nth-child(6) {
    width: 85%;
  }
}
