'use strict';

const { createCoreController } = require('@strapi/strapi').factories;

/**
 * Email Template Controller
 * Extended controller for managing email templates
 */
module.exports = createCoreController('api::email-template.email-template', ({ strapi }) => ({
  
  /**
   * Create email template with validation
   */
  async create(ctx) {
    const { data } = ctx.request.body;

    try {
      // Validate template based on type
      if (data.template_type === 'provider_template' || data.template_type === 'hybrid') {
        if (!data.template_id) {
          return ctx.badRequest('Template ID is required for provider templates');
        }
        
        // Validate template with provider
        if (data.template_id && data.provider) {
          const { emailProviderFactory } = require('../../../providers/email-provider-factory');
          const provider = emailProviderFactory.getProvider(data.provider);
          
          if (provider) {
            const isValid = await provider.validateTemplate(data.template_id);
            if (!isValid) {
              return ctx.badRequest(`Template ID '${data.template_id}' is not valid for provider '${data.provider}'`);
            }
          }
        }
      }
      
      if (data.template_type === 'html_content' || data.template_type === 'hybrid') {
        if (!data.html_content) {
          return ctx.badRequest('HTML content is required for HTML content templates');
        }
        
        // Validate HTML template
        const TemplateRenderer = require('../../../services/template-renderer');
        const validation = TemplateRenderer.validateTemplate(data.html_content);
        
        if (!validation.valid) {
          return ctx.badRequest(`Template HTML validation failed: ${validation.errors.join(', ')}`);
        }
        
        // Auto-extract variables from HTML if not provided
        if (!data.variables || Object.keys(data.variables).length === 0) {
          const extractedVars = TemplateRenderer.extractVariables(data.html_content);
          data.variables = extractedVars.reduce((acc, varName) => {
            acc[varName] = '';
            return acc;
          }, {});
        }
      }

      // Validate template based on type
      if (data.template_type === 'provider_template' || data.template_type === 'hybrid') {
        if (!data.template_id) {
          return ctx.badRequest('Template ID is required for provider templates');
        }
        
        // Validate template with provider
        if (data.template_id && data.provider) {
          const { emailProviderFactory } = require('../../../providers/email-provider-factory');
          const provider = emailProviderFactory.getProvider(data.provider);
          
          if (provider) {
            const isValid = await provider.validateTemplate(data.template_id);
            if (!isValid) {
              return ctx.badRequest(`Template ID '${data.template_id}' is not valid for provider '${data.provider}'`);
            }
          }
        }
      }
      
      if (data.template_type === 'html_content' || data.template_type === 'hybrid') {
        if (!data.html_content) {
          return ctx.badRequest('HTML content is required for HTML content templates');
        }
        
        // Validate HTML template
        const TemplateRenderer = require('../../../services/template-renderer');
        const validation = TemplateRenderer.validateTemplate(data.html_content);
        
        if (!validation.valid) {
          return ctx.badRequest(`Template HTML validation failed: ${validation.errors.join(', ')}`);
        }
        
        // Auto-extract variables from HTML if not provided
        if (!data.variables || Object.keys(data.variables).length === 0) {
          const extractedVars = TemplateRenderer.extractVariables(data.html_content);
          data.variables = extractedVars.reduce((acc, varName) => {
            acc[varName] = '';
            return acc;
          }, {});
        }
      }

      // Set defaults for simplified system
      // No organization field needed - templates are scope-based only

      // Check for duplicate names in the same scope
      const existingTemplate = await strapi.query('api::email-template.email-template').findOne({
        where: {
          name: data.name,
          scope: data.scope
        }
      });

      if (existingTemplate) {
        return ctx.badRequest(`Template with name '${data.name}' already exists in ${data.scope} scope`);
      }

      const entity = await strapi.service('api::email-template.email-template').create({
        data,
        populate: ['parent_template']
      });

      ctx.send({
        success: true,
        data: entity,
        message: 'Email template created successfully'
      });

    } catch (error) {
      strapi.log.error('Email template creation error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to create email template'
      });
    }
  },

  /**
   * Update email template with validation
   */
  async update(ctx) {
    const { id } = ctx.params;
    const { data } = ctx.request.body;

    try {
      // Find existing template
      const existingTemplate = await strapi.query('api::email-template.email-template').findOne({
        where: { 
          id
        }
      });

      if (!existingTemplate) {
        return ctx.notFound('Email template not found');
      }

      // Validate template with provider if changed
      if (data.template_id && data.provider) {
        const { emailProviderFactory } = require('../../../providers/email-provider-factory');
        const provider = emailProviderFactory.getProvider(data.provider);
        
        if (provider) {
          const isValid = await provider.validateTemplate(data.template_id);
          if (!isValid) {
            return ctx.badRequest(`Template ID '${data.template_id}' is not valid for provider '${data.provider}'`);
          }
        }
      }

      const entity = await strapi.service('api::email-template.email-template').update(id, {
        data,
        populate: ['parent_template']
      });

      ctx.send({
        success: true,
        data: entity,
        message: 'Email template updated successfully'
      });

    } catch (error) {
      strapi.log.error('Email template update error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to update email template'
      });
    }
  },

  /**
   * Find email templates (simplified)
   */
  async find(ctx) {
    try {
      const { query } = ctx;
      
      // Use standard Strapi service with scope filtering
      const entity = await strapi.service('api::email-template.email-template').find({
        ...query,
        filters: {
          ...query.filters,
          scope: ['global', 'system'] // Only global and system templates
        },
        populate: ['parent_template']
      });

      ctx.send({
        success: true,
        data: entity,
        message: 'Email templates retrieved successfully'
      });

    } catch (error) {
      strapi.log.error('Email template find error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to retrieve email templates'
      });
    }
  },

  /**
   * Test email template
   */
  async testTemplate(ctx) {
    const { id } = ctx.params;
    const { testEmail, testData = {} } = ctx.request.body;

    try {
      if (!testEmail) {
        return ctx.badRequest('Test email address is required');
      }

      // Find template
      const template = await strapi.query('api::email-template.email-template').findOne({
        where: { 
          id,
          is_active: true
        }
      });

      if (!template) {
        return ctx.notFound('Email template not found or inactive');
      }

      let emailData = {};

      // Handle different template types
      if (template.template_type === 'provider_template') {
        // Use provider template
        emailData = {
          to: testEmail,
          templateId: template.template_id,
          templateData: {
            ...template.variables,
            ...testData,
            test_mode: true,
            test_timestamp: new Date().toISOString()
          },
          from: template.default_from_email,
          subject: `[TEST] ${template.subject || template.name}`
        };
      } else if (template.template_type === 'html_content') {
        // Use HTML content
        const TemplateRenderer = require('../../../services/template-renderer');
        const renderedHtml = TemplateRenderer.renderAdvancedTemplate(
          template.html_content,
          { ...template.variables, ...testData }
        );
        const renderedText = template.text_content ? 
          TemplateRenderer.renderTemplate(template.text_content, { ...template.variables, ...testData }) : null;

        emailData = {
          to: testEmail,
          html: renderedHtml,
          text: renderedText,
          from: template.default_from_email,
          subject: `[TEST] ${template.subject || template.name}`
        };
      } else if (template.template_type === 'hybrid') {
        // Use provider template but with fallback to HTML
        emailData = {
          to: testEmail,
          templateId: template.template_id,
          templateData: {
            ...template.variables,
            ...testData,
            test_mode: true,
            test_timestamp: new Date().toISOString()
          },
          from: template.default_from_email,
          subject: `[TEST] ${template.subject || template.name}`
        };
      }

      // Send test email
      const { emailProviderFactory } = require('../../../providers/email-provider-factory');
      const result = await emailProviderFactory.sendEmail(emailData, template.provider);

      // Log test email
      if (result.success) {
        await strapi.service('api::email-log.email-log').create({
          data: {
            message_id: result.messageId,
            provider: result.provider,
            to_emails: [testEmail],
            from_email: emailData.from || process.env.SENDGRID_FROM_EMAIL,
            subject: emailData.subject,
            template_id: template.template_id,
            template_data: testData,
            status: 'sent',
            provider_response: result,
            sent_at: new Date(),
            email_template: template.id,
    
            user: ctx.state.user?.id,
            metadata: { is_test: true }
          }
        });
      }

      ctx.send({
        success: true,
        data: result,
        template: {
          id: template.id,
          name: template.name,
          provider: template.provider,
          type: template.template_type
        },
        message: 'Test email sent successfully'
      });

    } catch (error) {
      strapi.log.error('Template test error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to send test email'
      });
    }
  },

  /**
   * Preview email template HTML
   */
  async previewTemplate(ctx) {
    const { id } = ctx.params;
    const { previewData = {} } = ctx.request.body;

    try {
      // Find template
      const template = await strapi.query('api::email-template.email-template').findOne({
        where: { 
          id
        }
      });

      if (!template) {
        return ctx.notFound('Email template not found');
      }

      if (template.template_type === 'provider_template') {
        return ctx.badRequest('Preview not available for provider templates. Use test email instead.');
      }

      if (!template.html_content) {
        return ctx.badRequest('No HTML content available for preview');
      }

      // Generate preview
      const TemplateRenderer = require('../../../services/template-renderer');
      const previewHtml = TemplateRenderer.generatePreview(
        template.html_content,
        { ...template.variables, ...previewData }
      );

      ctx.send({
        success: true,
        data: {
          html: previewHtml,
          template: {
            id: template.id,
            name: template.name,
            type: template.template_type
          }
        },
        message: 'Template preview generated successfully'
      });

    } catch (error) {
      strapi.log.error('Template preview error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to generate template preview'
      });
    }
  },

  /**
   * Validate template HTML
   */
  async validateTemplateHtml(ctx) {
    const { html_content } = ctx.request.body;

    try {
      if (!html_content) {
        return ctx.badRequest('HTML content is required');
      }

      const TemplateRenderer = require('../../../services/template-renderer');
      const validation = TemplateRenderer.validateTemplate(html_content);
      const extractedVars = TemplateRenderer.extractVariables(html_content);

      ctx.send({
        success: true,
        data: {
          valid: validation.valid,
          errors: validation.errors,
          extractedVariables: extractedVars,
          variableCount: extractedVars.length
        },
        message: validation.valid ? 'Template is valid' : 'Template has validation errors'
      });

    } catch (error) {
      strapi.log.error('Template validation error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to validate template'
      });
    }
  },

  /**
   * Clone template (simplified)
   */
  async cloneTemplate(ctx) {
    const { id } = ctx.params;
    const { name, overrides = {} } = ctx.request.body;

    try {
      // Find source template
      const sourceTemplate = await strapi.query('api::email-template.email-template').findOne({
        where: { id }
      });

      if (!sourceTemplate) {
        return ctx.notFound('Source template not found');
      }

      // Create new template name if not provided
      const newName = name || `${sourceTemplate.name}-copy`;

      // Check for duplicate names
      const existingTemplate = await strapi.query('api::email-template.email-template').findOne({
        where: {
          name: newName,
          scope: overrides.scope || sourceTemplate.scope
        }
      });

      if (existingTemplate) {
        return ctx.badRequest(`Template with name '${newName}' already exists`);
      }

      // Clone template data
      const clonedData = {
        ...sourceTemplate,
        id: undefined,
        name: newName,
        parent_template: sourceTemplate.id,
        created_at: undefined,
        updated_at: undefined,
        // No organization field - templates are scope-based only
        ...overrides
      };

      const clonedTemplate = await strapi.service('api::email-template.email-template').create({
        data: clonedData,
        populate: ['parent_template']
      });

      strapi.log.info(`Cloned template ${sourceTemplate.name} to ${newName}`);

      ctx.send({
        success: true,
        data: clonedTemplate,
        message: 'Template cloned successfully'
      });

    } catch (error) {
      strapi.log.error('Template clone error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to clone template'
      });
    }
  },

  /**
   * Get template inheritance chain (simplified)
   */
  async getTemplateHierarchy(ctx) {
    const { id } = ctx.params;

    try {
      const chain = [];
      let currentTemplate = await strapi.query('api::email-template.email-template').findOne({
        where: { id },
        populate: ['parent_template']
      });

      // Build inheritance chain
      while (currentTemplate) {
        chain.unshift(currentTemplate);
        
        if (currentTemplate.parent_template) {
          currentTemplate = await strapi.query('api::email-template.email-template').findOne({
            where: { id: currentTemplate.parent_template.id },
            populate: ['parent_template']
          });
        } else {
          currentTemplate = null;
        }
      }

      ctx.send({
        success: true,
        data: {
          chain,
          depth: chain.length,
          rootTemplate: chain[0] || null,
          currentTemplate: chain[chain.length - 1] || null
        },
        message: 'Template hierarchy retrieved successfully'
      });

    } catch (error) {
      strapi.log.error('Template hierarchy error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to get template hierarchy'
      });
    }
  },

  /**
   * Get available template scopes for current user
   */
  async getTemplateScopes(ctx) {
    try {
      const userRole = ctx.state.user?.role?.type;

      let availableScopes = ['global']; // Everyone can create global templates

      // Only admins can create system templates
      if (userRole === 'admin' || userRole === 'super-admin') {
        availableScopes.push('system');
      }

      ctx.send({
        success: true,
        data: {
          availableScopes,
          userRole,
          recommendations: {
            global: 'Templates available to everyone',
            system: 'System-wide templates (admin only)'
          }
        },
        message: 'Available template scopes retrieved'
      });

    } catch (error) {
      strapi.log.error('Template scopes error:', error);
      ctx.badRequest({
        success: false,
        error: error.message || 'Failed to get template scopes'
      });
    }
  }
})); 