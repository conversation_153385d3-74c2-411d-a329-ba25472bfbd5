'use strict';

/**
 * series controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::series.series', ({ strapi }) => ({
  
  async create(ctx) {
    const user = ctx.state.user;
    if (!user) {
      return ctx.unauthorized('You must be logged in to create a series.');
    }

    // Add user organization and created_by_user to the data
    ctx.request.body.data.organization = user.organization?.id;
    ctx.request.body.data.created_by_user = user.id;

    const response = await super.create(ctx);
    return response;
  },

  async find(ctx) {
    // Only show published series or series owned by the user
    const user = ctx.state.user;
    
    if (!user) {
      // Public access - only show published public series
      ctx.query.filters = {
        ...ctx.query.filters,
        public: true,
        publishedAt: { $notNull: true }
      };
    } else {
      // Authenticated user - show public series or their own series
      ctx.query.filters = {
        ...ctx.query.filters,
        $or: [
          { public: true, publishedAt: { $notNull: true } },
          { created_by_user: user.id }
        ]
      };
    }

    const response = await super.find(ctx);
    return response;
  },

  async findOne(ctx) {
    const user = ctx.state.user;
    const { id } = ctx.params;

    // Get the series
    const series = await strapi.entityService.findOne('api::series.series', id, {
      populate: {
        series_episodes: {
          populate: {
            file_results_model: {
              fields: ['id', 'title', 'audio_url', 'feature_image_url', 'excerpt', 'estimated_duration']
            }
          },
          sort: 'position:asc'
        },
        organization: true,
        created_by_user: true
      }
    });

    if (!series) {
      return ctx.notFound('Series not found');
    }

    // Check access permissions
    if (!series.public && (!user || (series.created_by_user?.id !== user.id && series.organization?.id !== user.organization?.id))) {
      return ctx.forbidden('You do not have access to this series');
    }

    // If user is authenticated, get their progress
    let userProgress = null;
    if (user && series.series_episodes?.length > 0) {
      userProgress = await this.getUserSeriesProgress(user.id, series.id, series.series_episodes);
      
      // Add unlock status and progress to episodes
              series.series_episodes = await this.addContentProgressInfo(user.id, series.series_episodes, userProgress);
    }

    return {
      data: {
        ...series,
        user_progress: userProgress
      }
    };
  },

  // Helper method to get user's series progress
  async getUserSeriesProgress(userId, seriesId, episodes) {
    const seriesProgress = await strapi.db.query('api::series-progress.series-progress').findOne({
      where: { user: userId, series: seriesId }
    });

    if (!seriesProgress) {
      // Create initial progress if it doesn't exist
      return await strapi.entityService.create('api::series-progress.series-progress', {
        data: {
          user: userId,
          series: seriesId,
          started_at: new Date(),
          last_accessed_at: new Date()
        }
      });
    }

    return seriesProgress;
  },

  // Helper method to add progress info to episodes
  async addContentProgressInfo(userId, episodes, seriesProgress) {
    const contentProgressData = await strapi.db.query('api::content-progress.content-progress').findMany({
      where: { user: userId, series_id: episodes[0]?.series?.id || seriesProgress.series }
    });

    const progressMap = {};
    contentProgressData.forEach(cp => {
      progressMap[cp.episode_id] = cp;
    });

    return episodes.map(episode => {
      const progress = progressMap[episode.episode_id];
      
      // Progressive unlocking logic - check if previous episodes are completed
      let isUnlocked = episode.is_preview || episode.position === 1;
      
      if (!isUnlocked && episode.position > 1) {
        // Find previous episode
        const previousEpisode = episodes.find(ep => ep.position === episode.position - 1);
        if (previousEpisode) {
          const previousProgress = progressMap[previousEpisode.episode_id];
          isUnlocked = previousProgress?.completed || false;
        }
      }

      return {
        ...episode,
        unlocked: isUnlocked,
        completed: progress?.completed || false,
        progress_percentage: progress?.progress_percentage || 0,
        time_spent: progress?.time_spent || 0,
        last_position: progress?.last_position || 0
      };
    });
  }

})); 