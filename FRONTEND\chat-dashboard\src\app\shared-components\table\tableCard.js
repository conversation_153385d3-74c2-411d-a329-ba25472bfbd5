// src/TableComponent.js
import { Paper } from "@mui/material";
import React from "react";
import "../../../styles/tables.css";
import LottieControl from "src/app/main/dashboards/home/<USER>/ctaAnimation";
import "../../../styles/globals.css";
const TableCardComponent = ({ data }) => {
  const openInNewTab = (url) => {
    const newWindow = window.open(url, '_blank', 'noopener,noreferrer')
    if (newWindow) newWindow.opener = null
  }

  return (
    <div className="p-16 border-1 bg-base-purple h-[420px] rounded-lg shadow-md transform hover:-translate-y-0.5 hover:shadow-xl">
      <table className="table-fixed w-full" style={{ border: "none" }}>
        <thead>
          <tr>
            <th className="w-5/6 text-left py-3 px-4 widget-title text-white">
              Top sources
            </th>
            <th className="w-1/6"></th>
          </tr>
        </thead>
        <tbody>
          {data.length > 0 ? (
            data.map((item, index) => (
              <tr key={index} className="rounded-md hover:bg-[#6142a6] my-24">
                <td className="flex items-start border-gray-100 hover:border-none">
                  <img
                    src={"/assets/images/link-small.png"}
                    alt={item.name}
                    className="h-20 w-20 rounded-full mr-4"
                  />
                  <div className="ml-8 h-max-96 flex-grow overflow-hidden max-w-[calc(100%-5rem)]">
                    <p onClick={
                        (e) => {
                          openInNewTab(item.url);
                        }
                    } className="line-clamp-3 text-white cursor-pointer ">
                      {item.url}
                    </p>
                  </div>
                </td>
                <td className="pl-16 text-white items-center justify-end py-4 px-4 text-xl font-semibold border-none">
                  {item.count}
                </td>
              </tr>
            ))
          ) : (
            <tr className="h-full">
              <td
                colSpan="2"
                className="flex border-none justify-center items-center py-96 px-4 text-white text-base h-full"
              >
                The agents have not produced any links yet. Train your agent with URLs to start recommending products and links to your customers.
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default TableCardComponent;
