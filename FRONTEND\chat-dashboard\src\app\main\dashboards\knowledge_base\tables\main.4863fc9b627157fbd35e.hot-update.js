"use strict";
globalThis["webpackHotUpdatetalkbase_dashboard"]("main",{

/***/ "./src/app/main/dashboards/knowledge_base/tables/DatasourceTable.js":
/*!**************************************************************************!*\
  !*** ./src/app/main/dashboards/knowledge_base/tables/DatasourceTable.js ***!
  \**************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DatasourceTable: () => (/* binding */ DatasourceTable),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! axios */ "./node_modules/axios/lib/axios.js");
/* harmony import */ var src_app_utils_date_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/utils/date-utils */ "./src/app/utils/date-utils.js");
/* harmony import */ var src_app_shared_components_pagination_PaginationComponent__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/shared-components/pagination/PaginationComponent */ "./src/app/shared-components/pagination/PaginationComponent.js");
/* harmony import */ var src_app_shared_components_pill_StatusPill__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/app/shared-components/pill/StatusPill */ "./src/app/shared-components/pill/StatusPill.js");
/* harmony import */ var src_app_utils_validations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/app/utils/validations */ "./src/app/utils/validations.js");
/* harmony import */ var app_shared_components_pill_pill__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! app/shared-components/pill/pill */ "./src/app/shared-components/pill/pill.js");
/* harmony import */ var qs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! qs */ "./node_modules/qs/lib/index.js");
/* harmony import */ var qs__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(qs__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react/jsx-dev-runtime */ "./node_modules/react/jsx-dev-runtime.js");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");
__webpack_require__.$Refresh$.runtime = __webpack_require__(/*! ./node_modules/react-refresh/runtime.js */ "./node_modules/react-refresh/runtime.js");

var _jsxFileName = "/Users/<USER>/Desktop/Imversion.nosync/ai/chat-dashboard/chat-dashboard/src/app/main/dashboards/knowledge_base/tables/DatasourceTable.js",
  _s = __webpack_require__.$Refresh$.signature();











function DatasourceTable({
  datastoreId,
  onAddClick,
  onDeleteAllClick,
  onDocumentClick
}) {
  _s();
  const dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useDispatch)();
  const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);
  const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);
  const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);
  const itemsPerPage = 5;
  const handleToggle = () => {
    setIsOpen(!isOpen);
  };
  const getDatasource = async page => {
    try {
      const query = {
        populate: {
          documents: {
            sort: "createdAt:asc",
            limit: itemsPerPage,
            start: (page - 1) * itemsPerPage
          }
        }
      };
      const queryToString = qs__WEBPACK_IMPORTED_MODULE_7___default().stringify(query, {
        encode: false
      });
      const datasource = await axios__WEBPACK_IMPORTED_MODULE_9__["default"].get(`${"https://auth-server-dev.talkbase.ai"}/api/datasources/${datastoreId}?${queryToString}`);
      setDocuments(prev => [...prev, ...datasource.data.data.attributes.documents.data]);
      // setDocuments([
      //   ...documents,
      //   datasource.data.data.attributes.documents.data,
      // ]);
      setLoading(false);
    } catch (e) {
      console.log(e);
    }
  };
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {
    getDatasource();
  }, [datastoreId]);
  const totalPages = documents ? Math.ceil(documents.length / itemsPerPage) + 1 : 1;
  const onPageChange = page => {
    setCurrentPage(page);
    getDatasource();
  };
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
    className: "m-5 mt-32 border border-gray-300 shadow-lg rounded-lg overflow-hidden",
    children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
      className: "bg-white text-black p-4 flex flex-col cursor-pointer",
      onClick: handleToggle,
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
        className: "flex justify-between items-center",
        children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
          className: "m-16 flex flex-row",
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("img", {
            src: "assets/images/datasource-icon.png",
            className: "w-32 h-32 p-4 bg-[#EBEFF9] rounded-2xl"
          }, void 0, false, {
            fileName: _jsxFileName,
            lineNumber: 74,
            columnNumber: 13
          }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
            className: "ml-8 mt-6 font-semibold",
            children: ""
          }, void 0, false, {
            fileName: _jsxFileName,
            lineNumber: 78,
            columnNumber: 13
          }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
            className: "mx-16 mt-4",
            children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(app_shared_components_pill_pill__WEBPACK_IMPORTED_MODULE_6__["default"], {
              text: "New"
            }, void 0, false, {
              fileName: _jsxFileName,
              lineNumber: 80,
              columnNumber: 15
            }, this)
          }, void 0, false, {
            fileName: _jsxFileName,
            lineNumber: 79,
            columnNumber: 13
          }, this)]
        }, void 0, true, {
          fileName: _jsxFileName,
          lineNumber: 73,
          columnNumber: 11
        }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
          className: "flex mr-16 mt-4",
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("button", {
            onClick: onAddClick,
            className: "flex flex-row py-6 px-16 align-center justify-center shine-button bg-white-500 border-1 border-[#D6C8FF] hover:bg-base-purple hover:text-white text-base-purple font-medium  rounded h-36",
            children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("img", {
              src: "assets/images/add-icon.png",
              className: "mr-8 p-4 w-24 h-24 bg-grey-200 rounded-full hover:bg-white"
            }, void 0, false, {
              fileName: _jsxFileName,
              lineNumber: 89,
              columnNumber: 15
            }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("span", {
              className: "mt-2",
              children: "Add Data"
            }, void 0, false, {
              fileName: _jsxFileName,
              lineNumber: 93,
              columnNumber: 15
            }, this)]
          }, void 0, true, {
            fileName: _jsxFileName,
            lineNumber: 85,
            columnNumber: 13
          }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("button", {
            onClick: onDeleteAllClick,
            className: "ml-16 flex flex-row py-6 px-16 align-center justify-center shine-button bg-white-500 border-1 border-[#D6C8FF] hover:bg-base-purple hover:text-white text-base-purple font-medium  rounded h-36",
            children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("img", {
              src: "assets/images/delete-icon.png ",
              className: "mr-8 p-4 w-24 h-24 bg-grey-200 rounded-full hover:bg-white"
            }, void 0, false, {
              fileName: _jsxFileName,
              lineNumber: 99,
              columnNumber: 15
            }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("span", {
              className: "mt-2",
              children: "Delete All Data"
            }, void 0, false, {
              fileName: _jsxFileName,
              lineNumber: 103,
              columnNumber: 15
            }, this)]
          }, void 0, true, {
            fileName: _jsxFileName,
            lineNumber: 95,
            columnNumber: 13
          }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("span", {
            className: `mb-4 mt-12 ml-32 transform transition-transform duration-300 ${isOpen ? "rotate-180" : ""}`,
            style: {
              transformOrigin: "center",
              display: "inline-block",
              width: "25px",
              height: "25px"
            },
            children: "\u25BC"
          }, void 0, false, {
            fileName: _jsxFileName,
            lineNumber: 105,
            columnNumber: 13
          }, this)]
        }, void 0, true, {
          fileName: _jsxFileName,
          lineNumber: 84,
          columnNumber: 11
        }, this)]
      }, void 0, true, {
        fileName: _jsxFileName,
        lineNumber: 72,
        columnNumber: 9
      }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
        className: "mt-2 w-1/2 ml-16 mb-16 text-base text-grey-700 font-regular",
        children: "A datasource is where you will add your data to be used for training your agents. Whenever you add your data, it will be stored here in a datasource."
      }, void 0, false, {
        fileName: _jsxFileName,
        lineNumber: 120,
        columnNumber: 9
      }, this)]
    }, void 0, true, {
      fileName: _jsxFileName,
      lineNumber: 68,
      columnNumber: 7
    }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
      className: `transition-max-height duration-500 ease-in-out ${isOpen ? "max-h-screen" : "max-h-0 overflow-hidden"}`,
      children: isOpen && /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.Fragment, {
        children: documents && documents.length > 0 ? /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.Fragment, {
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
            className: "flex bg-[#F9FBFC] p-16 border-grey-300 border-t-1 border-b-1",
            children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
              className: "w-96 font-semibold p-2 pl-16",
              children: "SL No"
            }, void 0, false, {
              fileName: _jsxFileName,
              lineNumber: 136,
              columnNumber: 19
            }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
              className: "w-1/3 font-semibold p-2",
              children: "Document Name"
            }, void 0, false, {
              fileName: _jsxFileName,
              lineNumber: 137,
              columnNumber: 19
            }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
              className: "w-128 font-semibold p-2",
              children: "Status"
            }, void 0, false, {
              fileName: _jsxFileName,
              lineNumber: 138,
              columnNumber: 19
            }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
              className: "flex-1 font-semibold p-2",
              children: "Document Type"
            }, void 0, false, {
              fileName: _jsxFileName,
              lineNumber: 139,
              columnNumber: 19
            }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
              className: "flex-1 font-semibold p-2",
              children: "Date Added"
            }, void 0, false, {
              fileName: _jsxFileName,
              lineNumber: 140,
              columnNumber: 19
            }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
              className: "flex-1 font-semibold p-2",
              children: "Tokens Used"
            }, void 0, false, {
              fileName: _jsxFileName,
              lineNumber: 141,
              columnNumber: 19
            }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
              className: "flex-1 font-semibold p-2",
              children: "Actions"
            }, void 0, false, {
              fileName: _jsxFileName,
              lineNumber: 142,
              columnNumber: 19
            }, this)]
          }, void 0, true, {
            fileName: _jsxFileName,
            lineNumber: 135,
            columnNumber: 17
          }, this), documents.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage).map((_, index) => {
            var _documents$attributes;
            return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
              onClick: () => {
                onDocumentClick(documents[(currentPage - 1) * itemsPerPage + index].id);
              },
              className: "flex p-4 text-md border-b bg-[#faf9fb] border-gray-200 last:border-none hover:bg-white transition-colors",
              children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
                className: "w-96 p-16 px-32 text-grey-900",
                children: (currentPage - 1) * itemsPerPage + index + 1
              }, void 0, false, {
                fileName: _jsxFileName,
                lineNumber: 159,
                columnNumber: 23
              }, this), (0,src_app_utils_validations__WEBPACK_IMPORTED_MODULE_5__.validateUrl)(documents[(currentPage - 1) * itemsPerPage + index].attributes.document_name) && /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
                className: "w-1/3 p-16  text-start text-grey-900 bg-transparent",
                children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("a", {
                  href: documents[(currentPage - 1) * itemsPerPage + index].attributes.document_name,
                  target: "_blank",
                  rel: "noopener noreferrer",
                  style: {
                    color: "blue",
                    textDecoration: "underline",
                    backgroundColor: "transparent",
                    textAlign: "start",
                    justifyContent: "start"
                  },
                  children: documents[(currentPage - 1) * itemsPerPage + index].attributes.document_name
                }, void 0, false, {
                  fileName: _jsxFileName,
                  lineNumber: 167,
                  columnNumber: 27
                }, this)
              }, void 0, false, {
                fileName: _jsxFileName,
                lineNumber: 166,
                columnNumber: 25
              }, this), !(0,src_app_utils_validations__WEBPACK_IMPORTED_MODULE_5__.validateUrl)(documents[(currentPage - 1) * itemsPerPage + index].attributes.document_name) && /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
                className: "w-1/3 p-16 text-grey-900 ",
                children: documents[(currentPage - 1) * itemsPerPage + index].attributes.document_name
              }, void 0, false, {
                fileName: _jsxFileName,
                lineNumber: 195,
                columnNumber: 25
              }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
                className: "w-128 py-16",
                children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(src_app_shared_components_pill_StatusPill__WEBPACK_IMPORTED_MODULE_4__["default"], {
                  status: documents[(currentPage - 1) * itemsPerPage + index].attributes.status
                }, void 0, false, {
                  fileName: _jsxFileName,
                  lineNumber: 203,
                  columnNumber: 25
                }, this)
              }, void 0, false, {
                fileName: _jsxFileName,
                lineNumber: 202,
                columnNumber: 23
              }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
                className: "flex-1 py-16 text-grey-900",
                children: (_documents$attributes = documents[(currentPage - 1) * itemsPerPage + index].attributes.document_type) !== null && _documents$attributes !== void 0 ? _documents$attributes : "url"
              }, void 0, false, {
                fileName: _jsxFileName,
                lineNumber: 211,
                columnNumber: 23
              }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
                className: "flex-1 py-16 text-grey-900",
                children: (0,src_app_utils_date_utils__WEBPACK_IMPORTED_MODULE_2__.formatDateToHumanReadable)(documents[(currentPage - 1) * itemsPerPage + index].attributes.createdAt)
              }, void 0, false, {
                fileName: _jsxFileName,
                lineNumber: 215,
                columnNumber: 23
              }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
                className: "flex-1 py-16 text-grey-900",
                children: documents[(currentPage - 1) * itemsPerPage + index].attributes.tokens_used
              }, void 0, false, {
                fileName: _jsxFileName,
                lineNumber: 221,
                columnNumber: 23
              }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
                className: "flex-1 py-16 text-grey-800",
                children: "Actions"
              }, void 0, false, {
                fileName: _jsxFileName,
                lineNumber: 227,
                columnNumber: 23
              }, this)]
            }, index, true, {
              fileName: _jsxFileName,
              lineNumber: 150,
              columnNumber: 21
            }, this);
          }), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
            className: "",
            children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(src_app_shared_components_pagination_PaginationComponent__WEBPACK_IMPORTED_MODULE_3__["default"], {
              currentPage: currentPage,
              totalPages: totalPages,
              onPageChange: onPageChange
            }, void 0, false, {
              fileName: _jsxFileName,
              lineNumber: 231,
              columnNumber: 19
            }, this)
          }, void 0, false, {
            fileName: _jsxFileName,
            lineNumber: 230,
            columnNumber: 17
          }, this)]
        }, void 0, true) : /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("div", {
          className: "flex flex-col items-center justify-center p-10",
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("img", {
            src: "https://via.placeholder.com/150",
            alt: "No data",
            className: "mb-4"
          }, void 0, false, {
            fileName: _jsxFileName,
            lineNumber: 240,
            columnNumber: 17
          }, this), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)("p", {
            className: "text-gray-500",
            children: "No data"
          }, void 0, false, {
            fileName: _jsxFileName,
            lineNumber: 245,
            columnNumber: 17
          }, this)]
        }, void 0, true, {
          fileName: _jsxFileName,
          lineNumber: 239,
          columnNumber: 15
        }, this)
      }, void 0, false)
    }, void 0, false, {
      fileName: _jsxFileName,
      lineNumber: 126,
      columnNumber: 7
    }, this)]
  }, void 0, true, {
    fileName: _jsxFileName,
    lineNumber: 67,
    columnNumber: 5
  }, this);
}
_s(DatasourceTable, "YdPb9M4xOo2CGi4JHJMNDqphRf8=", false, function () {
  return [react_redux__WEBPACK_IMPORTED_MODULE_1__.useDispatch];
});
_c = DatasourceTable;
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DatasourceTable);
var _c;
__webpack_require__.$Refresh$.register(_c, "DatasourceTable");

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("af64995b57345a98f6b1")
/******/ })();
/******/
/******/ }
);
//# sourceMappingURL=main.4863fc9b627157fbd35e.hot-update.js.map