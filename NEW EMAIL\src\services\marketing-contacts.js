const axios = require('axios');

/**
 * Marketing Contacts Service
 * Handles adding users to marketing contact lists
 * Currently uses SendGrid for marketing contacts while Postmark handles transactional emails
 */
class MarketingContactsService {
  constructor() {
    this.sendGridApiKey = process.env.SENDGRID_API_KEY;
    this.enabled = !!this.sendGridApiKey;
  }

  /**
   * Add a user to marketing contacts
   * @param {Object} userData - User data to add to marketing contacts
   * @param {string} userData.email - User email
   * @param {string} userData.firstName - User first name
   * @param {string} userData.lastName - User last name (optional)
   * @param {Object} userData.customFields - Custom fields to add
   * @returns {Promise<Object>} Result of the operation
   */
  async addContact(userData) {
    const logPrefix = '[MARKETING-CONTACTS]';
    
    if (!this.enabled) {
      strapi.log.warn(`${logPrefix} SendGrid API key not configured - skipping marketing contact addition`);
      return {
        success: false,
        error: 'SendGrid API key not configured',
        skipped: true
      };
    }

    try {
      const { email, firstName, lastName, customFields = {} } = userData;

      if (!email) {
        throw new Error('Email is required');
      }

      const contactData = {
        contacts: [
          {
            email: email,
            first_name: firstName,
            ...(lastName && { last_name: lastName }),
            custom_fields: customFields
          }
        ]
      };

      strapi.log.info(`${logPrefix} Adding contact to SendGrid marketing list`, {
        email,
        firstName,
        customFields: Object.keys(customFields)
      });

      const response = await axios.put(
        'https://api.sendgrid.com/v3/marketing/contacts',
        contactData,
        {
          headers: {
            'Authorization': `Bearer ${this.sendGridApiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      strapi.log.info(`${logPrefix} ✅ Contact added successfully`, {
        email,
        statusCode: response.status,
        jobId: response.data?.job_id
      });

      return {
        success: true,
        data: response.data,
        email,
        provider: 'sendgrid'
      };

    } catch (error) {
      strapi.log.error(`${logPrefix} ❌ Failed to add contact to marketing list`, {
        email: userData.email,
        error: error.message,
        statusCode: error.response?.status,
        responseData: error.response?.data
      });

      return {
        success: false,
        error: error.message,
        email: userData.email,
        provider: 'sendgrid',
        statusCode: error.response?.status,
        responseData: error.response?.data
      };
    }
  }

  /**
   * Add multiple contacts to marketing list
   * @param {Array} contacts - Array of contact objects
   * @returns {Promise<Object>} Result of the operation
   */
  async addContacts(contacts) {
    const logPrefix = '[MARKETING-CONTACTS]';
    
    if (!this.enabled) {
      strapi.log.warn(`${logPrefix} SendGrid API key not configured - skipping marketing contacts addition`);
      return {
        success: false,
        error: 'SendGrid API key not configured',
        skipped: true
      };
    }

    try {
      if (!Array.isArray(contacts) || contacts.length === 0) {
        throw new Error('Contacts array is required and cannot be empty');
      }

      const contactsData = {
        contacts: contacts.map(contact => ({
          email: contact.email,
          first_name: contact.firstName,
          ...(contact.lastName && { last_name: contact.lastName }),
          custom_fields: contact.customFields || {}
        }))
      };

      strapi.log.info(`${logPrefix} Adding ${contacts.length} contacts to SendGrid marketing list`);

      const response = await axios.put(
        'https://api.sendgrid.com/v3/marketing/contacts',
        contactsData,
        {
          headers: {
            'Authorization': `Bearer ${this.sendGridApiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      strapi.log.info(`${logPrefix} ✅ Bulk contacts added successfully`, {
        count: contacts.length,
        statusCode: response.status,
        jobId: response.data?.job_id
      });

      return {
        success: true,
        data: response.data,
        count: contacts.length,
        provider: 'sendgrid'
      };

    } catch (error) {
      strapi.log.error(`${logPrefix} ❌ Failed to add bulk contacts to marketing list`, {
        count: contacts?.length || 0,
        error: error.message,
        statusCode: error.response?.status,
        responseData: error.response?.data
      });

      return {
        success: false,
        error: error.message,
        count: contacts?.length || 0,
        provider: 'sendgrid',
        statusCode: error.response?.status,
        responseData: error.response?.data
      };
    }
  }

  /**
   * Check if marketing contacts service is enabled
   * @returns {boolean} Whether the service is enabled
   */
  isEnabled() {
    return this.enabled;
  }

  /**
   * Get service status
   * @returns {Object} Service status information
   */
  getStatus() {
    return {
      enabled: this.enabled,
      provider: 'sendgrid',
      apiKeyConfigured: !!this.sendGridApiKey
    };
  }
}

// Create singleton instance
const marketingContactsService = new MarketingContactsService();

module.exports = {
  MarketingContactsService,
  marketingContactsService
};
