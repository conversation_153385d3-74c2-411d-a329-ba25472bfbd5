import React from 'react';
import IconButton from '@mui/material/IconButton';
import DeleteIcon from '@mui/icons-material/Delete';
import Tooltip from '@mui/material/Tooltip';
import MemberComp from './MemberComp';
import './organisation.css';

function GridComponent({name, email, onClick}) {
  return (
    <div className="grid-container">
      <div className="grid-item">
        <MemberComp name={name} email={email}/>
      </div>
      <div className="leave-btn ml-64">
        <Tooltip title="Remove">
          <IconButton
            size="small"
            aria-label="remove"
            onClick={onClick}
            sx={{
              backgroundColor: '#7666CE',
              color: 'white',
              '&:hover': {
                color: 'error.main',
                transform: 'scale(1.1)',
              },
              transition: 'color 0.3s, transform 0.2s',
            }}
          >
            <DeleteIcon />
          </IconButton>
        </Tooltip>
      </div>
    </div>
  );
}

export default GridComponent;
