{"kind": "collectionType", "collectionName": "plans", "info": {"singularName": "plan", "pluralName": "plans", "displayName": "Plan", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "description": {"type": "string"}, "stripe_prod_id": {"type": "string"}, "paid": {"type": "decimal", "default": 0}, "purchase_type": {"type": "enumeration", "enum": ["subscription", "one-time"], "default": "subscription", "required": true}, "allowed_kbs": {"type": "integer", "default": 5}, "organizations": {"type": "relation", "relation": "oneToMany", "target": "api::organization.organization", "mappedBy": "plan"}, "allowed_training_tokens": {"type": "integer", "default": 100000}, "key_features": {"type": "json", "default": []}, "type": {"type": "enumeration", "enum": ["monthly", "yearly"], "default": "monthly"}, "allowed_credits": {"type": "biginteger"}, "allowed_scrape_train_count": {"type": "integer", "default": 5}, "series_access_level": {"type": "enumeration", "enum": ["none", "basic", "premium", "all"], "default": "basic", "description": "Level of series access included in this plan"}, "allowed_premium_series": {"type": "integer", "default": 0, "description": "Number of premium series accessible with this plan (0 = unlimited for premium+ plans)"}, "series_credit_cost": {"type": "integer", "default": 0, "description": "Credits required per premium series access (0 = no additional cost)"}}}