'use strict';
const admin = require('firebase-admin');
const serviceAccount = require('../../../../config/firebase-service-account.json');
/**
 * url-meta controller
 */


// const axios = require('axios');
// const cheerio = require( 'cheerio' );
// const _ = require( 'lodash' );

module.exports = {

	async fcmTest(ctx) {
		try {
			// Send message with both notification and data payloads
			await admin.messaging().send({
				notification: {
					title: 'Hello',
					body: 'World',
				},
				data: {
					route: "/track_detail",
					track_id: "132",
					click_action: "FLUTTER_NOTIFICATION_CLICK",
				},
				token: ctx.request.body.token
			});
			
			ctx.body = { success: true, message: 'Message sent successfully' };
		} catch (error) {
			console.error('Error sending message:', error);
			ctx.body = { success: false, error: error.message };
		}
	}
};

