import { Button, CircularProgress } from "@mui/material";
import SettingsTextField from "app/shared-components/forms/settingsTextField";
import { useState } from "react";

const PrefixPrompt = ({
  onSubmit,
  isLoading = false,
  disabled = false,
  value,
  title,
  description,
  icon,
  examples,

}) => {
  const [prompt, setPrompt] = useState();

  return (
    <div className="mt-24 settigs-grid-container bg-white">
      <SettingsTextField
        id="prompt-prefix"
        isMultilineMode={true}
        finalValue={value}
        validate={(value) => {
          return value != null;
        }}
        title={title}
        icon={icon}
        description={
          description
        }
        placeholder={
          examples
        }
        onChange={(id, value, isValid) => {
          setPrompt(value);
        }}
        className="w-4/5"
      ></SettingsTextField>
      <Button
        disabled={disabled}
        className="purple-styled-button mr-10 w-fit"
        variant="outlined"
        onClick={(e) => onSubmit(prompt)}
      >
        {isLoading ? (
          <CircularProgress
            thickness={5}
            size={18}
            className="mr-8 text-white"
          />
        ) : (
          <div></div>
        )}
        Update
      </Button>
    </div>
  );
};

export default PrefixPrompt;
