'use strict';

const chance = new require( 'chance' )();
const utils = require('@strapi/utils');
const { sanitize } = utils;
const { ApplicationError } = utils.errors;

const getService = (name) => {
  return strapi.plugin('users-permissions').service(name);
};

const sanitizeUser = (user, ctx) => {
  const { auth } = ctx.state;
  const userSchema = strapi.getModel('plugin::users-permissions.user');

  return sanitize.contentAPI.output(user, userSchema, { auth });
};

module.exports = ({ strapi }) => ({
  async signup(ctx, next) {
    return await this.send( ctx, next );
  },
  async signin(ctx, next) {
    return await this.send( ctx, next );
  },
  async send(ctx, next) {
    try {
      const params = ctx.request.body;

      const otpRequestObj = await strapi.db.query('api::otp-request.otp-request').findOne({ where: { phone: params.phone } })

      // Disallow user to request for OTP during cool down period.
      if ( otpRequestObj !== null && ( otpRequestObj.failedAttemptTime + ( strapi.config.get('server.waitingTime')*60*1000 ) ) > Date.now()) { 
          return ctx.throw(400, 'Your account is blocked. Try again after '+ strapi.config.get('server.waitingTime')+ ' mins');
      }

      if ( otpRequestObj !== null ){
        await strapi.entityService.delete('api::otp-request.otp-request', otpRequestObj.id);
      }

      const otpGenerated = await strapi.entityService.create('api::otp-request.otp-request', {
        data: {
          phone: params.phone,
        },
      });

      //Send OTP using PLIVO messaging services to the requested phone number.
      strapi
      .plugin("otp-authentication")
      .service("otp")
      .sendSms(
        {
          'dst':  otpGenerated.phone, // Receiver's phone Number with country code.
          'text': otpGenerated.otp, // Your SMS Text Message - English.
        }
      ).then( () => {
        return ctx.send( otpGenerated );
      } ).catch( err => {
          strapi.log.error(err);
          return ctx.throw(400, 'Could not sent OTP! Please check the entry fields.', err);
      } );

    } catch (err) {
      strapi.log.error(err);
      ctx.badRequest( "Send otp controller error", err );
    } 
  },
  async verify(ctx, next) {
    const params = ctx.request.body;
		const otpReqObj = await strapi.db.query('api::otp-request.otp-request').findOne({ where: { phone: params.phone } });

		let failedCount = otpReqObj.failedAttemptCount;

		//Check if verification failed for more than maxFailedAttempt or if resend attempts has exceeded its limit.
		if ( failedCount >= strapi.config.get('server.maxFailedAttempt') || otpReqObj.resentCount >= strapi.config.get('server.maxResendAttempt') ) {
			failedCount++;

      await strapi.entityService.update('api::otp-request.otp-request', otpReqObj.id, {
        data: {
          failedAttemptTime: Date.now(),
          failedAttemptCount: failedCount
        },
      });

      return ctx.throw( 400, 'Your account is blocked. Try again after '+ strapi.config.get('server.waitingTime') + ' mins' );
		}

		// If timeout event occurs destroy the current OtpRequest record and throw "OTP EXPIRED" error.
		if ( ( otpReqObj.createdAt + ( otpReqObj.timeToLive*1000 ) ) < Date.now() ) {
      await strapi.entityService.delete('api::otp-request.otp-request', otpReqObj.id);

			return ctx.throw( 400, 'OTP EXPIRED' );
		}

		// If OTP mismatches increment failedCount and update OtpRequest record.
		if ( otpReqObj.otp !== parseInt( params.otp, 10 ) ) {
			failedCount++;
      await strapi.entityService.update('api::otp-request.otp-request', otpReqObj.id, {
        data: {
          phone: otpReqObj.phone,
          failedAttemptCount: failedCount
        },
      });
			return ctx.throw( 400, 'INCORRECT OTP' );
		}

    // When OTP matches.
    await strapi.entityService.delete('api::otp-request.otp-request', otpReqObj.id);

    let existingUser = await strapi.db.query('plugin::users-permissions.user').findOne({ where: { phone: params.phone } })

		// If user newly registers then Create user and also the profile for user.
		if ( ! existingUser ) {
			let dummyPswd = chance.hash( { length: 8 } );
      let dummyEmail = chance.email({domain: "example.com"})
			let dummyUsername = params.phone + chance.hash( { length: 5 } );

      ctx.request.body = {
          "username": dummyUsername,
          "email": dummyEmail,
          "password": dummyPswd,
          "phone": params.phone,
          "provider": 'otp'
      }

      return await strapi.plugin("users-permissions").controller("auth").register(ctx);
		}
    else {

      if (existingUser.blocked === true) {
        throw new ApplicationError('Your account has been blocked by an administrator');
      }

      return ctx.send({
        jwt: getService('jwt').issue({ id: existingUser.id }),
        user: await sanitizeUser(existingUser, ctx),
      });
		}
	},
  async resend(ctx, next) {
		try {
			const params = ctx.request.body;
		  const otpReqObj = await strapi.db.query('api::otp-request.otp-request').findOne({ where: { phone: params.phone } });

      if(otpReqObj==null){
        return ctx.throw(400, 'Request an OTP first');
      }

			//Get number of resend attempt counts and failed verification counts.
			let resentCount = otpReqObj.resendAttemptCount;

			// Increment resent attempt resentCount by one.
			resentCount++;

			//Check if resend attempt count and failure attempt count is out of limit
			if ( resentCount > strapi.config.get('server.maxResendAttempt') || otpReqObj.failedCount > strapi.config.get('server.maxFailedAttempt') ) {
        await strapi.entityService.update('api::otp-request.otp-request', otpReqObj.id, {
          data: {
            failedAttemptTime: Date.now(),
            resendAttemptCount: resentCount
          },
        });
				return ctx.throw(400, 'Your account is blocked. Try again after '+ strapi.config.get('server.waitingTime')+ ' mins');
			}

			//Destroy existing record.
      await strapi.entityService.delete('api::otp-request.otp-request', otpReqObj.id);

			// Create new record for same phone number.
      const otpGenerated = await strapi.entityService.create('api::otp-request.otp-request', {
        data: {
          phone: params.phone
        },
      });

			//Update the verification failure count and resend attempt count.
      await strapi.entityService.update('api::otp-request.otp-request', otpGenerated.id, {
        data: {
          resendAttemptCount: resentCount
        },
      });

      strapi
      .plugin("otp-authentication")
      .service("otp")
      .sendSms(
        {
          'dst':  otpGenerated.phone, // Receiver's phone Number with country code.
          'text': otpGenerated.otp, // Your SMS Text Message - English.
        }
      ).then( () => {
        return ctx.send( otpGenerated );
      } ).catch( err => {
        if ( err ) {
          strapi.log.error(err);
          return ctx.throw(400, 'Could not sent OTP! Please check the entry fields.',err.response.statusText);
        }
      } );

		} catch ( err ) {
      strapi.log.error(err);
      return ctx.throw(400, 'Could not sent OTP! Please check the entry fields.',err);
		}
	},
});
