'use strict';

/**
 * content-progress controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::content-progress.content-progress', ({ strapi }) => ({
  
  // Track progress for any content (series episode or standalone track)
  async updateProgress(ctx) {
    const { user } = ctx.state;
    const { fileResultsModelId, progressPercentage, timeSpent, lastPosition, completed } = ctx.request.body.data;
    
    // Check if this content is part of a series
    const seriesEpisode = await strapi.db.query('api::series-episode.series-episode').findOne({
      where: { file_results_model: fileResultsModelId },
      populate: ['series']
    });
    
    const progressData = {
      user: user.id,
      file_results_model: fileResultsModelId,
      progress_percentage: progressPercentage,
      time_spent: timeSpent,
      last_position: lastPosition,
      completed,
      last_accessed_at: new Date(),
    };
    
    // Add series-specific data if this content is part of a series
    if (seriesEpisode) {
      progressData.episode_id = seriesEpisode.episode_id;
      progressData.series_id = seriesEpisode.series.id;
    }
    
    // Upsert progress record
    const existingProgress = await strapi.db.query('api::content-progress.content-progress').findOne({
      where: {
        user: user.id,
        file_results_model: fileResultsModelId
      }
    });
    
    let progress;
    if (existingProgress) {
      progress = await strapi.entityService.update('api::content-progress.content-progress', existingProgress.id, {
        data: progressData
      });
    } else {
      progressData.started_at = new Date();
      if (completed) {
        progressData.completed_at = new Date();
      }
      progress = await strapi.entityService.create('api::content-progress.content-progress', {
        data: progressData
      });
    }
    
    // If this is a series episode and it's completed, update series progress
    if (seriesEpisode && completed) {
      await this.updateSeriesProgress(user.id, seriesEpisode.series.id, seriesEpisode.episode_id);
    }
    
    return { data: progress };
  },
  
  // Get user's progress for specific content
  async getProgress(ctx) {
    const { user } = ctx.state;
    const { fileResultsModelId } = ctx.params;
    
    const progress = await strapi.db.query('api::content-progress.content-progress').findOne({
      where: {
        user: user.id,
        file_results_model: fileResultsModelId
      },
      populate: ['file_results_model']
    });
    
    return { data: progress };
  },
  
  // Get user's recent activity across all content
  async getRecentActivity(ctx) {
    const { user } = ctx.state;
    const { limit = 20 } = ctx.query;
    
    const recentProgress = await strapi.db.query('api::content-progress.content-progress').findMany({
      where: { user: user.id },
      populate: ['file_results_model', 'series'],
      orderBy: { last_accessed_at: 'desc' },
      limit: parseInt(limit)
    });
    
    return { data: recentProgress };
  },
  
  // Helper to update series progress when episode completed
  async updateSeriesProgress(userId, seriesId, completedEpisodeId) {
    // Update series progress record
    const seriesProgress = await strapi.db.query('api::series-progress.series-progress').findOne({
      where: { user: userId, series: seriesId }
    });
    
    const updateData = {
      last_episode_id: completedEpisodeId,
      last_accessed_at: new Date()
    };
    
    // Check if series is fully completed
    const totalEpisodes = await strapi.db.query('api::series-episode.series-episode').count({
      where: { series: seriesId, is_optional: false }
    });
    
    const completedEpisodes = await strapi.db.query('api::content-progress.content-progress').count({
      where: { user: userId, series_id: seriesId, completed: true }
    });
    
    if (completedEpisodes >= totalEpisodes) {
      updateData.completed_at = new Date();
    }
    
    if (seriesProgress) {
      await strapi.entityService.update('api::series-progress.series-progress', seriesProgress.id, {
        data: updateData
      });
    } else {
      await strapi.entityService.create('api::series-progress.series-progress', {
        data: {
          user: userId,
          series: seriesId,
          started_at: new Date(),
          ...updateData
        }
      });
    }
  }
  
})); 