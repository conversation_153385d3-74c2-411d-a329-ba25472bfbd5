import "./plan-card.css";
import { Card } from "@mui/material";
import { Divider } from "@mui/material";
import clsx from "clsx";
import FuseSvgIcon from "@fuse/core/FuseSvgIcon/FuseSvgIcon";
import CircleWithCheckmark from "../circle-with-checkmark/circle-with-checkmark";
import { PricingPill } from "../pill/pill";
import { capitalize } from "lodash";
const PlanCard = ({
  plan,
  color = "#ffffff",
  textColor = "#1e2227",
  buttonColor = "#ffffff",
  borderColor = "#ebebeb",
  borderWidth = "1px",
  buttonTextColor = "#1e2227",
  isPopular = false,
  perMonth = false,
  disableButton = false,
  buttonBorderColor = "#C5C5C5",
  buttonText = "Subscribe",
  isCurrentPlan = false,
  onClick,
}) => {
  return (
    <Card
      key={plan.id}
      className="plan-card flex flex-col gap-8 text-start relative items-start shadow-md"
      style={{
        backgroundColor: color,
        color: textColor,
        borderRadius: "20px",
        border: `${borderWidth} solid ${borderColor}`,

      }}
    >
      {/* {isCurrentPlan?<div className='bg-base-purple rounded text-white w-fit p-6 absolute top-0 right-0'>
			Active
		</div>:<></>} */}
      <div className="flex w-full justify-between">
        <PricingPill text={plan.attributes.name.toUpperCase()} />
        {/* <div className="text-start font-bold text-2xl" style={{color: isPopular?'#6530c1':'black'}}>{plan.attributes.name}</div> */}
        {isPopular && (
          <div className="rounded-full self-center bg-base-purple px-12 py-4 text-white">
            Popular
          </div>
        )}
        {isCurrentPlan && (
          <div className="rounded-full self-center bg-base-purple px-12 py-4 text-[#00C681] bg-[#00C68140]">
            Active
          </div>
        )}
      </div>

      <div className="flex flex-row">
        <div className="flex justify-center items-center">
          <span className="plan-currency-label">$</span>
          <span className="plan-price-label ml-2">{plan.attributes.paid}</span>
          <span className="ml-4 align-bottom mt-16 font-regular text-md">
            {perMonth ? " /month" : " /year"}
          </span>
        </div>
      </div>
      <div className="text-md text-light-text">
        {plan.attributes.description}
      </div>

        <button
          className={clsx(
            "text-center rounded-md p-10 w-full mt-16",
            disableButton || plan.attributes?.name === "Trial" ? "opacity-50 cursor-not-allowed" : ""
          )}
          size="sm"
          variant="contained"
          disabled={disableButton}
          onClick={(e) => onClick(plan)}
          style={{
            backgroundColor: buttonColor,
            color: buttonTextColor,
            border: `1px solid ${buttonBorderColor}`,
          }}
        >
          {buttonText}
        </button>


      <Divider
        className="mt-10 mb-6"
        style={{
          width: "100%",
          borderColor: "#d4d4d4",
          borderWidth: "0.1px",
        }}
      />
      <div>
        <div className="font-regular text-md text-body-text-color">
          {plan.attributes.name + " plan features"}
        </div>
      </div>

      {plan.attributes.key_features?.slice(0, 3).map((value) => (
        <div key={value} className="flex my-4 flex-row items-center">
          <CircleWithCheckmark />
          <div
            key={value}
            className="text-md text-light-text grow flex-col items-start gap-x-8 w-full"
          >
            {value}
            {/* {value !=
					plan.attributes.key_features[
						plan.attributes.key_features.length - 1
					] ? (
						<Divider
							className="mt-10 mb-6"
							style={{ width: '100%', borderColor: '#d4d4d4' }}
						/>
					) : (
						<></>
					)} */}
          </div>
        </div>
      ))}

      {/* {disableButton ? (
				<></>
			) : (
				<div className="flex-row justify-center items-center w-full">
					<Divider
						className="mt-10 mb-6"
						style={{ width: '100%', borderColor: '#d4d4d4' }}
					/>
					<></>

				</div>
			)} */}
    </Card>
  );
};
export default PlanCard;
