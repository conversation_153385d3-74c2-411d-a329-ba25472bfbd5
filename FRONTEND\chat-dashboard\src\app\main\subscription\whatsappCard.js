import React from 'react';
import './whatsappCard.css';

const WhatsappCard = ({ icon }) => {
    return (
        <div className="whatsapp-container">
            <img src={icon} alt="leading-icon" className="leading-icon" />
            <div className="text-container">
                <h1 className="title">{"Whatsapp Integration (Whatsapp messages incur an extra charge as per meta's pricing model)"}</h1>
                <div className="flex flex-row w-full">
                    <p className="description">{'1000 chat sessions per month @ 30$'}</p>
                    <p className="ml-32 description">{"2000 chat sessions month @ 75$"}</p>
                    <p className="ml-32 description">{"5000 chat sessions month @ 210$"}</p>
                </div>
               
            </div>
        </div>
    );
}

export default WhatsappCard;
