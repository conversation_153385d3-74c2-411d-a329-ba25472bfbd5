import { PillLabel } from 'app/shared-components/pill/pill';
import React from 'react';
// Label component
const Label = ({ title }) => {
    return (
      <div className="inline-block mx-4 py-4 m-1">
           <PillLabel text={title}></PillLabel>
      </div>
    );
  };

  // LabelsList component
  export const LabelsList = ({ labels }) => {
    return (
      <div className="p-4 rounded-lg max-w-md">
        {labels.map(label => (
          <Label key={label.id} title={label.attributes.title} />
        ))}
      </div>
    );
  };

