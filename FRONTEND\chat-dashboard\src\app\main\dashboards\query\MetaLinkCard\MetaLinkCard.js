import React from 'react';
import './metaLinkCard.css';

export default function MetaLinkCard({url, title, description, image, queryId}){

    const handleClick = (event) =>{
        window.open(`${process.env.REACT_APP_TRACKER_PAGE}?url=${url}&query_id=${queryId}`, '_blank')
		event.stopPropagation();
    }

    return(
        <div onClick={handleClick} className='meta-link-card-container'>
            {image && <img className="meta-image p-2" src={image} alt='logo'/>}
            <div className='link-data'>
                { url &&  <b className="meta-link">{url}</b>}
                { title && <div> <b>{title}</b> </div> }
                { description && <div className="meta-description">{description}</div> }
            </div>
        </div>
    )
}
