{"kind": "collectionType", "collectionName": "org_series_access", "info": {"singularName": "org-series-access", "pluralName": "org-series-accesses", "displayName": "Organization Series Access", "description": "Organization-level access grants for private series"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"organization": {"type": "relation", "relation": "manyToOne", "target": "api::organization.organization"}, "series": {"type": "relation", "relation": "manyToOne", "target": "api::series.series"}, "access_type": {"type": "enumeration", "enum": ["paid", "trial", "admin", "granted", "purchased"], "default": "granted"}, "granted_at": {"type": "datetime", "required": true}, "expires_at": {"type": "datetime", "description": "Optional expiration for time-limited access"}, "granted_by": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "purchase_reference": {"type": "string", "description": "Reference to payment/order if purchased"}, "max_users": {"type": "integer", "description": "Maximum users that can access (0 = unlimited)"}, "notes": {"type": "text"}}}