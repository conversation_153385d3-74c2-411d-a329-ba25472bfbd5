{"kind": "collectionType", "collectionName": "email_templates", "info": {"singularName": "email-template", "pluralName": "email-templates", "displayName": "<PERSON>ail Te<PERSON>late", "description": "Manage email templates for different providers and use cases"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "description": {"type": "text"}, "provider": {"type": "enumeration", "enum": ["sendgrid", "mailgun", "postmark", "ses"], "default": "sendgrid", "required": true}, "template_id": {"type": "string", "required": false}, "html_content": {"type": "text"}, "text_content": {"type": "text"}, "template_type": {"type": "enumeration", "enum": ["provider_template", "html_content", "hybrid"], "default": "provider_template", "required": true}, "category": {"type": "enumeration", "enum": ["welcome", "verification", "reset-password", "notification", "marketing", "transactional", "system"], "default": "transactional"}, "subject": {"type": "string"}, "default_from_email": {"type": "email"}, "default_from_name": {"type": "string"}, "variables": {"type": "json", "default": {}, "description": "Template variables schema and default values"}, "is_active": {"type": "boolean", "default": true}, "scope": {"type": "enumeration", "enum": ["global", "system"], "default": "global", "required": true, "description": "Template scope: global (everyone), system (admin only)"}, "parent_template": {"type": "relation", "relation": "manyToOne", "target": "api::email-template.email-template", "description": "Parent template for inheritance/overrides"}, "usage_count": {"type": "integer", "default": 0}, "last_used": {"type": "datetime"}, "tags": {"type": "json", "default": []}}}