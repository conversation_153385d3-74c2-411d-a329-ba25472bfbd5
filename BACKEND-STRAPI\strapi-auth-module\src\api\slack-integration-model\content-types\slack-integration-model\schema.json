{"kind": "collectionType", "collectionName": "slack_integration_models", "info": {"singularName": "slack-integration-model", "pluralName": "slack-integration-models", "displayName": "slack integration model", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"access_token": {"type": "text"}, "slack_channel_id": {"type": "text"}, "knowledgebase": {"type": "relation", "relation": "oneToOne", "target": "api::knowledgebase.knowledgebase", "inversedBy": "slack_integration_model"}}}