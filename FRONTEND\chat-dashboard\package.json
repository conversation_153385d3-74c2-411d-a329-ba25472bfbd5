{"name": "talkbase-dashboard", "version": "8.3.8", "private": true, "dependencies": {"@amplitude/analytics-browser": "^2.3.8", "@emotion/cache": "11.10.5", "@emotion/react": "11.10.5", "@emotion/styled": "11.10.5", "@fullcalendar/core": "6.0.0", "@fullcalendar/daygrid": "6.0.0", "@fullcalendar/interaction": "6.0.0", "@fullcalendar/react": "6.0.0", "@fullcalendar/timegrid": "6.0.0", "@hookform/resolvers": "2.9.10", "@mui/base": "5.0.0-alpha.110", "@mui/icons-material": "5.11.0", "@mui/lab": "5.0.0-alpha.112", "@mui/material": "5.11.0", "@mui/material-next": "6.0.0-alpha.66", "@mui/styles": "5.14.14", "@mui/system": "5.11.0", "@mui/utils": "5.11.0", "@mui/x-data-grid": "5.17.14", "@mui/x-date-pickers": "5.0.10", "@reduxjs/toolkit": "1.8.6", "@stripe/react-stripe-js": "^2.1.0", "@stripe/stripe-js": "^1.53.0", "apexcharts": "3.44.0", "autosuggest-highlight": "3.3.4", "axios": "^1.3.4", "axios-mock-adapter": "1.21.2", "clipboard-copy": "^4.0.1", "clsx": "1.2.1", "core-js": "3.26.1", "crypto-js": "^4.1.1", "date-fns": "^2.29.3", "date-fns-jalali": "2.21.3-1", "draft-js": "0.11.7", "draftjs-to-html": "0.9.1", "fetch": "^1.1.0", "file-saver": "^2.0.5", "framer-motion": "7.9.1", "google-map-react": "2.2.0", "history": "5.3.0", "i18next": "22.4.5", "jspdf": "^3.0.1", "jwt-decode": "3.1.2", "keycode": "2.2.1", "lodash": "4.17.21", "lucide-react": "^0.513.0", "marked": "4.2.4", "material-ui-popup-state": "2.0.1", "mobile-detect": "1.4.5", "mobx": "^6.7.0", "moment": "2.29.4", "notistack": "1.0.6-next.3", "open-graph-scraper": "^6.0.1", "perfect-scrollbar": "1.5.5", "prismjs": "1.29.0", "prop-types": "15.8.1", "qs": "^6.11.0", "raw-loader": "4.0.2", "react": "18.2.0", "react-apexcharts": "1.4.1", "react-autosuggest": "10.1.0", "react-beautiful-dnd": "13.1.1", "react-circular-progressbar": "^2.1.0", "react-color": "^2.19.3", "react-dom": "18.2.0", "react-draft-wysiwyg": "1.15.0", "react-draggable": "4.4.5", "react-ga": "^3.3.1", "react-hook-form": "7.40.0", "react-i18next": "12.1.1", "react-icons": "^5.5.0", "react-imask": "6.4.3", "react-lottie": "^1.2.3", "react-markdown": "^8.0.6", "react-masonry-css": "1.0.16", "react-number-format": "4.9.3", "react-popper": "2.3.0", "react-redux": "8.0.5", "react-router-dom": "^6.8.2", "react-spring": "8.0.27", "react-svg": "^16.1.21", "react-swipeable": "7.0.0", "react-swipeable-views": "0.14.0", "react-swipeable-views-utils": "0.14.0", "react-syntax-highlighter": "^15.6.1", "react-table": "7.8.0", "react-transition-group": "4.4.5", "react-virtualized": "9.22.3", "react-window": "1.8.8", "redoc": "2.0.0-rc.77", "redux-logger": "4.0.0", "rehype-katex": "^6.0.2", "rehype-raw": "^6.1.1", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "styled-components": "5.3.6", "stylis": "4.1.3", "stylis-plugin-rtl": "2.1.1", "uuid": "^9.0.1", "validator": "^13.11.0", "web-vitals": "2.1.4", "yup": "0.32.11"}, "peerDependencies": {"autoprefixer": "10.4.7", "postcss": "8.4.20", "react": "18.2.0", "react-dom": "18.2.0"}, "resolutions": {"react": "18.2.0", "react-dom": "18.2.0", "babel-loader": "8.1.0"}, "devDependencies": {"@babel/core": "7.20.5", "@babel/eslint-parser": "7.19.1", "@babel/node": "7.20.5", "@babel/preset-env": "7.20.2", "@babel/preset-react": "7.18.6", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.8", "@typescript-eslint/eslint-plugin": "^5.46.1", "@typescript-eslint/parser": "^4.33.0", "autoprefixer": "9.8.8", "cross-env": "7.0.3", "eslint": "7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^8.5.0", "eslint-config-react-app": "^6.0.0", "eslint-plugin-flowtype": "^5.10.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^2.0.0", "immutable": "4.0.0", "js-beautify": "1.14.7", "postcss": "8.4.20", "prettier": "^2.8.7", "promise": "8.3.0", "react-app-alias": "2.2.2", "react-app-rewired": "2.2.1", "react-scripts": "5.0.1", "source-map-explorer": "2.5.3", "tailwindcss": "3.2.4", "typescript": "4.6.4", "util": "^0.12.5"}, "scripts": {"start": "react-app-rewired  start", "build": "cross-env GENERATE_SOURCEMAP=false react-app-rewired build", "test": "react-app-rewired test --env=node", "eject": "react-app-rewired eject", "build-docs": "babel-node --presets @babel/preset-env src/app/main/documentation/material-ui-components/build.js", "analyze": "react-app-rewired build && source-map-explorer 'build/static/js/*.js' --html analyze-result.html", "lint": "eslint"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 3 safari version"]}}