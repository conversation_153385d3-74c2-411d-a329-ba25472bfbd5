import { Fragment, useEffect } from "react";
import Box from "@mui/material/Box";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemText from "@mui/material/ListItemText";
import { Avatar, ListItemAvatar, ListSubheader } from "@mui/material";
import { useSearchParams } from "react-router-dom";
import { GoDatabase } from "react-icons/go";
import { VscTools } from "react-icons/vsc";
import { MdOutlineIntegrationInstructions } from "react-icons/md";
import { IoSettingsOutline } from "react-icons/io5";
import { MdOutlineEditNote } from "react-icons/md";
import { BsPlugin } from "react-icons/bs";

export default function SidebarMenu() {
  const [searchParams, setSearchParams] = useSearchParams();
  const menus = [
    {
      name: "Datasource #1",
      icon: GoDatabase,
      slug: "datasource",
    },
    {
      name: "APITools",
      icon: VscTools,
      slug: "api-tools",
    },
    {
      name: "Built In Tools",
      icon: VscTools,
      slug: "built-in-tools",
    },

    {
      name: "Additional Agent Instructions",
      icon: MdOutlineIntegrationInstructions,
      slug: "additional-agent-instructions",
    },
    {
      name: "Tone & Persona",
      icon: MdOutlineEditNote,
      slug: "tone-persona",
    },
    {
      name: "Agent Settings",
      icon: IoSettingsOutline,
      slug: "agent-settings",
    },

    {
      name: "Integrations",
      icon: BsPlugin,
      slug: "integrations",
    },
  ];

  function handleListItemClick(slug) {
    searchParams.set("tab", slug);
    setSearchParams(searchParams);
  }

  useEffect(() => {
    const currentTab = searchParams.get("tab");
    if (!currentTab) {
      searchParams.set("tab", "datasource");
      setSearchParams(searchParams);
    }
  }, []);
  return (
    <Box className="border-r  h-full bg-white max-w-[360px] w-full">
      <nav>
        <List
          subheader={
            <ListSubheader className="text-black">Settings</ListSubheader>
          }
        >
          {menus.map((menu) => (
            <Fragment key={menu.name}>
              <ListItem disablePadding>
                <ListItemButton
                  selected={searchParams.get("tab") === menu.slug}
                  onClick={() => handleListItemClick(menu.slug)}
                  className="flex gap-8"
                  sx={{
                    "&.Mui-selected": {
                      backgroundColor: "#7C53FE", // Change selected background color
                      color: "white",
                      "&:hover": {
                        backgroundColor: "#7C53FE", // Keep the same color for selected on hover
                      },
                    },
                  }}
                >
                  <Avatar className="w-28 h-28 bg-black text-white">
                    <menu.icon className="w-16 h-16" />
                  </Avatar>

                  <ListItemText
                    primary={<span className="text-[12px]">{menu.name}</span>}
                  />
                </ListItemButton>
              </ListItem>
            </Fragment>
          ))}
        </List>
      </nav>
    </Box>
  );
}
