import React, { useState, useRef } from "react";
import CircularProgress from "@mui/material/CircularProgress";
import './dropDown.css';

/**
 * Represents a custom dropdown component.
 *
 * @param {Object} props - The props object containing the following properties:
 * @param {Array} props.options - The array of dropdown options.
 * @param {boolean} props.loading - Indicates whether the dropdown options are currently loading.
 * @param {function} props.onChange - The function to be called when the dropdown value is changed.
 * @param {string} props.value - The current selected value of the dropdown.
 * @param {string} [props.placeholder="Choose a Chatbot"] - The placeholder text displayed when no value is selected.
 * @param {string} [props.accessoryImage="/path/to/your/local/image.png"] - The path to the accessory image of the dropdown button.
 * @param {boolean} [props.disabled=false] - flag to disable the dropdown
 * @returns {JSX.Element} - The rendered custom dropdown component.
 */
export function CustomDropdown({
  options,
  loading,
  onChange,
  value,
  placeholder = "Choose a Agent",
  accessoryImage = "/path/to/your/local/image.png",
  disabled=false
}) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const hasValue = value !== undefined && value !== null && value !== "";

  /**
   * Handles outside click to close the dropdown.
   *
   * @param {Event} event - The event object for the outside click.
   */
  const handleOutsideClick = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setIsOpen(false);
      document.removeEventListener("mousedown", handleOutsideClick);
    }
  };

  /**
   * Toggles the dropdown visibility.
   */
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      // if going to open dropdown
      document.addEventListener("mousedown", handleOutsideClick);
    } else {
      document.removeEventListener("mousedown", handleOutsideClick);
    }
  };

  return (
    <div className="dropdown-container" ref={dropdownRef}>
      <button type="button" onClick={toggleDropdown} className={hasValue ? 'dropdown-button selected' : 'dropdown-button'} disabled={ disabled }>
        {value ? options.find((opt) => opt.id === value).name : placeholder}
        <img
          src={accessoryImage}
          alt="Dropdown Arrow"
          className="dropdown-arrow"
          
        />
      </button>
      {isOpen && (
        <div className="backdrop" onClick={() => setIsOpen(false)}></div>
      )}
      {isOpen && (
        <div className="dropdown-options">
          {loading
            ? "Loading..."
            : options.map((opt) => (
                <div
                  key={opt.id}
                  onClick={() => {
                    onChange({ target: { value: opt.id } });
                    setIsOpen(false);
                  }}
                  className="option-item"
                >
                  {opt.name}
                </div>
              ))}
        </div>
      )}
      {loading && <CircularProgress size={20} className="circular-progress" />}
    </div>
  );
}
