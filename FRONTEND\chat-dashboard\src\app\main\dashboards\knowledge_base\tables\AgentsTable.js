import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import axios from "axios";
import { formatDateToHumanReadable } from "src/app/utils/date-utils";
import PaginationComponent from "src/app/shared-components/pagination/PaginationComponent";
import CircularButton from "app/shared-components/buttons/circular-button";
import { selectUser, setUser } from "app/store/userSlice";
import history from "@history";
import aesEncrypt from "src/app/utils/encryptions";
import clipboardCopy from "clipboard-copy";
import { showMessage } from "app/store/fuse/messageSlice";
import ConfirmModal from "app/shared-components/modals/ConfirmModal";
import LoadingSpinner from "app/shared-components/loading-spinner/loading-spinner";
import { Typography } from "@mui/material";
import { getImageForAgentType } from "src/app/main/factory/ImageFactory";

export function AgentsTable({ onDeleteClick, isInitiallyOpen = true }) {
  const dispatch = useDispatch();
  const [isOpen, setIsOpen] = useState(true);
  const [agents, setAgents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [lastPage, setLastPage] = useState(1);
  const itemsPerPage = 5;
  const user = useSelector(selectUser);
  const [deletingRecord, setDeletingRecord] = useState("");
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [error, setError] = useState("");

  const agent = (index) => {
    return agents[(currentPage - 1) * itemsPerPage + index];
  };

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };



  const getAgents = async (page) => {
    try {
      setLoading(true);
      const agents = await axios.get(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/knowledgebases?`
      );
      setAgents((prev) => [...prev, ...agents.data]);
      setLastPage(agents.data.length < itemsPerPage ? page : 1);
      setLoading(false);
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    getAgents(1);
  }, []);

  useEffect(() => {
    setIsOpen(isInitiallyOpen);
  }, [isInitiallyOpen]);

  const totalPages =
    lastPage != 1
      ? lastPage
      : agents
      ? Math.ceil(agents.length / itemsPerPage)
      : 1;

  const onPageChange = (page) => {
    setCurrentPage(page);
    // if (lastPage != page && agents.length < page * itemsPerPage) {
    //   getAgents(page);
    // }
  };

  const onKbDeleteCancel = () => {
    setConfirmDialogOpen(false);
    setDeletingRecord("");
  };

  const openKbDeleteConfirmDialog = (kbId) => {
    setConfirmDialogOpen(true);
    setDeletingRecord(kbId);
  };

  async function HandleDeleteKB() {
    setConfirmDialogOpen(false);
    setLoading(true);
    try {
      const response = await axios.delete(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/knowledgebases/${deletingRecord}`
      );
      const kbData = await response.data;
      setDeletingRecord("");
      setError("");
      dispatch(showMessage({ message: "Your agent has been deleted" }));
    } catch (error) {
      setError("Something went wrong. Please try again later.");
      dispatch(showMessage({ message: "Your agent has been deleted" }));
      console.log(error);
    } finally {
      setLoading(false);
      setAgents([]);
      getAgents(1);
    }
  }

  return (
    <div className="m-5 mt-32 border border-gray-300 shadow-lg rounded-lg overflow-hidden">
      <div
        className="bg-white text-black p-4 flex flex-col cursor-pointer"
        onClick={handleToggle}
      >
        <div className="flex justify-between items-center">
          <div className="m-16 flex flex-row">
            <img
              src="assets/images/agent-icon.svg"
              className="w-32 h-32 p-4 bg-[#EBEFF9] rounded-2xl"
            ></img>
            <div className="ml-8 mt-6 font-semibold tex-title-text-color">
              {"Agents"}
            </div>
          </div>

          <div className="flex mr-16 mt-4">
            <span
              className={`mb-4 mt-12 ml-32 transform transition-transform duration-300 ${
                isOpen ? "rotate-180" : ""
              }`}
              style={{
                transformOrigin: "center",
                display: "inline-block",
                width: "25px",
                height: "25px",
              }}
            >
              ▼
            </span>
          </div>
        </div>
        <div className="mt-2 w-1/2 ml-16 mb-16 text-base text-body-text-color font-regular">
          A agent is where you will add your data to be used for training your
          agents. Whenever you add your data, it will be stored here in a
          datasource.
        </div>
      </div>
      <div
        className={`transition-max-height duration-500 ease-in-out ${
          isOpen ? "max-h-screen" : "max-h-0 overflow-hidden"
        }`}
      >
        {
          <>
            {loading && (
              <div className="flex place-content-center my-64">
                <LoadingSpinner className={"w-60 h-60"} color={"#6200ea"} />
                <Typography className={"mt-16 text-lg font-regular"}>
                  Loading your Agents
                </Typography>
              </div>
            )}
            {agents && agents.length && !loading > 0 ? (
              <>
                <div className="flex bg-[#F9FBFC] p-16 border-grey-300 border-t-1 border-b-1">
                  <div className="w-96 font-semibold p-2 pl-16">SL No</div>
                  <div className="w-1/4 font-semibold p-2">Agent Name</div>
                  <div className="w-1/5 font-semibold p-2">Agent Type</div>
                  <div className="w-1/6 font-semibold p-2">Created Date</div>
                  <div className="flex-1 font-semibold p-2">Deploy</div>
                  <div className="flex-1 font-semibold p-2">Share</div>
                  <div className="flex-1 font-semibold p-2">Delete</div>
                </div>
                {agents
                  .slice(
                    (currentPage - 1) * itemsPerPage,
                    currentPage * itemsPerPage
                  )
                  .map((_, index) => (
                    <div
                      onClick={() => {
                        const url = `/dashboards/knowledge_base/items_detail_page?kbID=${
                          agent(index).kb_id
                        }&kbName=${agent(index).name}&kbrID=${agent(index).id}`;
                        history.push(url);
                      }}
                      key={index}
                      className="flex p-4 text-md border-b bg-[#faf9fb] border-gray-200 last:border-none hover:bg-white transition-colors hover:bg-white hover:shadow-md hover:cursor-pointer"
                    >
                      <div className="w-96 p-16 px-32 mt-8 text-grey-900">
                        {(currentPage - 1) * itemsPerPage + index + 1}
                      </div>

                      <div className="w-1/4 p-16 mt-8  font-semibold text-start text-grey-900 bg-transparent">
                        {agent(index).name}
                      </div>

                      <div className="w-1/5 flex py-16 text-grey-900">
                        <img
                          src={getImageForAgentType(
                            agent(index).default_ai_task.name
                          )}
                          className="w-32 h-32 p-4  bg-base-purple rounded-2xl"
                        ></img>
                        <div className="text-start  mt-[5px] mx-16 text-grey-900 bg-transparent">
                          {agent(index).default_ai_task.name}
                        </div>
                      </div>
                      <div className="w-1/6 flex py-16 text-grey-900">
                        {formatDateToHumanReadable(agent(index).createdAt)}
                      </div>
                      <div className="flex-1 py-16 text-grey-900">
                        <CircularButton
                          image={"assets/images/deploy-icon.svg"}
                          onPress={(event) => {
                            event.stopPropagation();
                            history.push(`/dashboards/knowledge_base/chat_bot?kbID=${
                                agent(index).kb_id
                              }`)
                            ;
                          }}
                        />
                      </div>
                      <div className="flex-1 py-16 text-grey-900">
                        <CircularButton
                          image={"assets/images/share-icon.svg"}
                          onPress={(event) => {
                            event.stopPropagation();
                            const link = `${
                              process.env.REACT_APP_PREVIEW_BASE_URL
                            }?orgId=${aesEncrypt(
                              user.data.organization.org_id,
                              process.env.REACT_APP_ENCRYPTION_KEY
                            )}&chatbotId=${aesEncrypt(
                              agent(index).kb_id,
                              process.env.REACT_APP_ENCRYPTION_KEY
                            )}`;
                            clipboardCopy(link);
                            dispatch(
                              showMessage({
                                message: "Sharable link copied to clipboard",
                              })
                            );
                          }}
                        />
                      </div>
                      <div className="flex-1 py-16 text-grey-900">
                        <CircularButton
                          image={"assets/images/delete-icon.svg"}
                          onPress={(event) => {
                            event.stopPropagation();
                            openKbDeleteConfirmDialog(agent(index).id);
                          }}
                        />
                      </div>
                    </div>
                  ))}
                <div className="">
                  <PaginationComponent
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={onPageChange}
                  />
                </div>
              </>
            ) : (
              !loading && (
                <div className="flex flex-col items-center justify-center p-10">
                  <img
                    src="/assets/images/no-data.jpg"
                    alt="No data"
                    className="mb-4 h-[150px]"
                  />
                  <p className="text-body-text-color mx-64 w-[300px] text-center my-16">
                    Looks like you haven't added any data to this datasource
                    yet. Click the add button to add your data
                  </p>
                </div>
              )
            )}
          </>
        }
      </div>
      <ConfirmModal
        open={confirmDialogOpen}
        handleClose={() => setConfirmDialogOpen(false)}
        onConfirm={() => {
          HandleDeleteKB();
        }}
        onCancel={onKbDeleteCancel}
        title="Delete Agent"
        message="Are you sure you want to delete this agent?"
      />
    </div>
  );
}

export default AgentsTable;
