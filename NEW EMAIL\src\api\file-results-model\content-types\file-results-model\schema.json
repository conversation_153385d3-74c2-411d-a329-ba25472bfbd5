{"kind": "collectionType", "collectionName": "file_results_models", "info": {"singularName": "file-results-model", "pluralName": "file-results-models", "displayName": "file result model", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"py_id": {"type": "string"}, "ai_model_name": {"type": "string"}, "ai_tokens_used_prompt": {"type": "integer", "default": 0}, "ai_tokens_used_completion": {"type": "integer", "default": 0}, "base_cost_per_token": {"type": "float", "default": 0}, "credits_used": {"type": "integer", "default": 0}, "docx_url": {"type": "text"}, "pdf_url": {"type": "text"}, "audio_url": {"type": "text"}, "mp3_url": {"type": "text"}, "script": {"type": "text"}, "feature_image_url": {"type": "text"}, "excerpt": {"type": "text"}, "title": {"type": "string"}, "color": {"type": "string"}, "markdown_url": {"type": "text"}, "query": {"type": "text"}, "start_time": {"type": "datetime"}, "end_time": {"type": "datetime"}, "total_cost": {"type": "float", "default": 0}, "status": {"type": "enumeration", "enum": ["pending", "processing", "completed", "failed"]}, "knowledgebase": {"type": "relation", "relation": "manyToOne", "target": "api::knowledgebase.knowledgebase", "inversedBy": "file_results_models"}, "playlist": {"type": "relation", "relation": "manyToOne", "target": "api::playlist.playlist", "inversedBy": "file_results_models"}, "bookmarks": {"type": "relation", "relation": "oneToMany", "target": "api::bookmark.bookmark", "mappedBy": "file_results_models"}, "series_episodes": {"type": "relation", "relation": "oneToMany", "target": "api::series-episode.series-episode", "mappedBy": "file_results_model"}}}