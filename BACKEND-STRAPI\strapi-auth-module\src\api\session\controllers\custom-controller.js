"use strict";

/**
 * session controller
 */

const axios = require("axios");

const chance = new require("chance")();
module.exports = {
  async start(ctx, next) {
    let { data } = await axios
      .post(
        `http://api.ipapi.com/api/${ctx.request.ip}?access_key=${process.env.IPAPIKEY}`
      )
      .catch((err) => {
        console.log(err);
        throw err;
      });
    const response = {
      session: chance.guid(),
      country_code: data.country_code,
      calling_code: data.location.calling_code,
    };

    return response;
  },

  async getRecentSessions(ctx, next) {
    var result = [];
    var sessions = [];
    strapi.log.info(`getRecentSessions: ${ctx.state.user.organization.id}`);
    const knowledgebases = await strapi.query("api::knowledgebase.knowledgebase").findMany({
      where: {
        organization: ctx.state.user.organization.id,
      },
    });
    strapi.log.info(`knowledgebases: ${JSON.stringify(knowledgebases, null, 2)}`);
    for (const knowledgebase of knowledgebases) {
      const knowledgebaseSessions = await strapi.query("api::session.session").findMany({
        where: {
          knowledgebase: knowledgebase.id,
        },
        orderBy: { createdAt: 'desc' }, // Sort sessions by creation date, most recent first
      });
      sessions = sessions.concat(knowledgebaseSessions);
    }
    strapi.log.info(`sessions: ${JSON.stringify(sessions, null, 2)}`);
    sessions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    // Pagination parameters
    const page = parseInt(ctx.query.page) || 1;
    const pageSize = parseInt(ctx.query.pageSize) || 10;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    // Slice the sessions array for pagination
    const paginatedSessions = sessions.slice(startIndex, endIndex);

    for (const session of paginatedSessions) {
      const oldestAnswer = await strapi.db.query("api::answer.answer").findOne({
        where: {
          sessionModel: session.id,
        },
        orderBy: { createdAt: 'asc' },
        populate: ['sessionModel.knowledgebase'],
      });
      strapi.log.info(`oldestAnswer: ${JSON.stringify(oldestAnswer, null, 2)}`);
      if (oldestAnswer) {
        // Extract knowledgebase_name before removing sessionModel
        const knowledgebase_name = oldestAnswer.sessionModel.knowledgebase.name;

        // Remove the sessionModel key
        const { sessionModel, ...answerWithoutSession } = oldestAnswer;
        strapi.log.info(`answerWithoutSession: ${JSON.stringify(answerWithoutSession, null, 2)}`);
        result.push({
          session: session,
          query: answerWithoutSession,
          knowledgebase_name: knowledgebase_name,
        });
      }
    }

    // Prepare the response with pagination metadata
    const response = {
      data: result,
      pagination: {
        page,
        pageSize,
        totalPages: Math.ceil(sessions.length / pageSize),
        totalItems: sessions.length,
      },
    };

    return response;
  },
};
