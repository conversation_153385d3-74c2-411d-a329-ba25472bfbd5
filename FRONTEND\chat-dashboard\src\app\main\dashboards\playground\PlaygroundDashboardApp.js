import React, { useEffect, useState } from "react";
import FusePageSimple from "@fuse/core/FusePageSimple";
import ChatUI from "./chat-ui";
import withReducer from 'app/store/withReducer';
import reducer from './store';
import axios from "axios";
import "./playground.css";
import PlaygroundDashboardAppHeader from "./PlaygroundDashboardAppHeader";

function PlaygroundDashboardApp() {
  return (
    <FusePageSimple
      header={<></>}
      content={
        <div className="w-full px-24 md:px-32 relative h-[90vh]">
          <ChatUI />
        </div>
      }
    />
  );
}

export default withReducer('playgroundApp', reducer)(PlaygroundDashboardApp);
