# Sample Email Templates

This document shows examples of different template types and approaches.

## 1. Welcome Email (HTML Content)

### Template Data
```json
{
  "name": "welcome-email-html",
  "template_type": "html_content",
  "category": "welcome",
  "subject": "Welcome to {{app_name}}, {{username}}!",
  "html_content": "<!-- See HTML below -->",
  "variables": {
    "username": "User Name",
    "app_name": "Your App",
    "confirmation_link": "",
    "features": []
  }
}
```

### HTML Content
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {{app_name}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .email-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
        }
        .welcome-message {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .cta-button {
            display: inline-block;
            background: #4CAF50;
            color: white !important;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 20px 0;
        }
        .features-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .feature-item {
            margin: 10px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #666;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">{{app_name}}</div>
        </div>
        
        <div class="content">
            <h1 class="welcome-message">Welcome aboard, {{username}}! 🎉</h1>
            
            <p>We're thrilled to have you join our community. Your account has been successfully created and you're ready to get started!</p>
            
            {{#if confirmation_link}}
            <div style="text-align: center;">
                <a href="{{confirmation_link}}" class="cta-button">Confirm Your Email</a>
            </div>
            {{/if}}
            
            <div class="features-list">
                <h3>Here's what you can do now:</h3>
                {{#each features}}
                <div class="feature-item">
                    <strong>{{title}}</strong> - {{description}}
                </div>
                {{/each}}
            </div>
            
            <p>If you have any questions, feel free to reach out to our support team. We're here to help!</p>
            
            <p>Best regards,<br>The {{app_name}} Team</p>
        </div>
        
        <div class="footer">
            <p>© {{current_year}} {{app_name}}. All rights reserved.</p>
            <p>You received this email because you signed up for {{app_name}}.</p>
        </div>
    </div>
</body>
</html>
```

## 2. Password Reset (Simple HTML)

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .alert { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; }
        .button { display: inline-block; background: #007bff; color: white !important; 
                 padding: 10px 20px; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h2>Password Reset Request</h2>
        
        <div class="alert">
            <p>Hi {{username}},</p>
            <p>We received a request to reset your password for {{app_name}}.</p>
        </div>
        
        <p>Click the button below to reset your password:</p>
        
        <p style="text-align: center;">
            <a href="{{reset_url}}" class="button">Reset Password</a>
        </p>
        
        <p><small>This link will expire in {{expires_in}} for security reasons.</small></p>
        
        <p>If you didn't request this reset, please ignore this email.</p>
        
        <hr>
        <p><small>© {{app_name}} - Automated message, please do not reply</small></p>
    </div>
</body>
</html>
```

## 3. Newsletter Template (Advanced Features)

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: 'Helvetica Neue', Arial, sans-serif; margin: 0; padding: 0; background: #f4f4f4; }
        .newsletter { max-width: 600px; margin: 0 auto; background: white; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .article { margin-bottom: 30px; padding-bottom: 20px; border-bottom: 1px solid #eee; }
        .article h3 { color: #333; margin-bottom: 10px; }
        .read-more { color: #667eea; text-decoration: none; font-weight: bold; }
        .footer { background: #333; color: white; padding: 20px; text-align: center; font-size: 12px; }
    </style>
</head>
<body>
    <div class="newsletter">
        <div class="header">
            <h1>{{newsletter_title}}</h1>
            <p>{{newsletter_date}}</p>
        </div>
        
        <div class="content">
            <p>Hello {{username}},</p>
            <p>{{intro_message}}</p>
            
            {{#each articles}}
            <div class="article">
                <h3>{{title}}</h3>
                <p>{{excerpt}}</p>
                {{#if read_more_url}}
                <a href="{{read_more_url}}" class="read-more">Read More →</a>
                {{/if}}
            </div>
            {{/each}}
            
            {{#if featured_product}}
            <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; text-align: center;">
                <h3>Featured: {{featured_product.name}}</h3>
                <p>{{featured_product.description}}</p>
                <a href="{{featured_product.url}}" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Learn More</a>
            </div>
            {{/if}}
        </div>
        
        <div class="footer">
            <p>{{company_name}} | {{company_address}}</p>
            <p><a href="{{unsubscribe_url}}" style="color: #ccc;">Unsubscribe</a></p>
        </div>
    </div>
</body>
</html>
```

## 4. API Usage Examples

### Creating HTML Template
```javascript
const template = await strapi.service('api::email-template.email-template').create({
  data: {
    name: 'welcome-email-html',
    template_type: 'html_content',
    category: 'welcome',
    subject: 'Welcome to {{app_name}}, {{username}}!',
    html_content: '<html>...</html>', // Full HTML
    text_content: 'Welcome {{username}}! Thanks for joining {{app_name}}.',
    variables: {
      username: '',
      app_name: 'My App',
      confirmation_link: '',
      current_year: new Date().getFullYear().toString()
    },
    is_active: true
  }
});
```

### Sending HTML Template Email
```javascript
const result = await strapi.emailProviderFactory.sendEmail({
  to: '<EMAIL>',
  templateName: 'welcome-email-html',
  templateData: {
    username: 'John Doe',
    confirmation_link: 'https://myapp.com/confirm/abc123',
    features: [
      { title: 'Dashboard', description: 'View your analytics' },
      { title: 'API Access', description: 'Integrate with our API' }
    ]
  }
});
```

### Preview Template
```javascript
// POST /api/email-template/123/preview
{
  "previewData": {
    "username": "Preview User",
    "app_name": "My App",
    "features": [
      { "title": "Feature 1", "description": "Description 1" }
    ]
  }
}
```

### Validate HTML
```javascript
// POST /api/email-template/validate-html
{
  "html_content": "<html><body>Hello {{username}}!</body></html>"
}

// Response:
{
  "success": true,
  "data": {
    "valid": true,
    "errors": [],
    "extractedVariables": ["username"],
    "variableCount": 1
  }
}
```

## 5. Template Types Comparison

| Feature | Provider Template | HTML Content | Hybrid |
|---------|------------------|--------------|--------|
| Design Control | Limited | Full | Full |
| Provider Features | Full | None | Full |
| Strapi Preview | No | Yes | Yes |
| Variable Extraction | Manual | Auto | Auto |
| Fallback Options | No | No | Yes |
| Migration Effort | Low | Medium | Medium |

## 6. Best Practices

1. **Use HTML Content** for:
   - Custom designs
   - Advanced logic (conditionals, loops)
   - Full control over template
   - Preview requirements

2. **Use Provider Templates** for:
   - Complex designs with provider tools
   - A/B testing features
   - Advanced analytics
   - Team collaboration in provider dashboard

3. **Use Hybrid** for:
   - Migration scenarios
   - Backup/fallback strategies
   - Testing new approaches 