{"kind": "collectionType", "collectionName": "ai_tasks", "info": {"singularName": "ai-task", "pluralName": "ai-tasks", "displayName": "AI Task"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "task_id": {"type": "integer"}, "detailsFactor": {"type": "decimal"}, "speedFactor": {"type": "decimal"}, "tokensUsageFactor": {"type": "decimal"}, "description": {"type": "text"}, "waitingTime": {"type": "string"}, "organizations": {"type": "relation", "relation": "oneToMany", "target": "api::organization.organization", "mappedBy": "ai_task"}}}