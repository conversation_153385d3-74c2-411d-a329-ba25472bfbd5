/* Main Chat Container */
.chat-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  flex: 1;
  overflow-y: auto;
  background-color: #ffffff;
  position: relative;
}

/* Chat Messages */
.chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  scroll-behavior: smooth;
  padding: 0;
  scrollbar-width: thin;
  scrollbar-color: #e1e1e1 transparent;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background-color: #e1e1e1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background-color: #d1d1d1;
}

/* Chat Message Styles */
.chat-message {
  padding: 20px 28px;
  margin-bottom: 0;
  font-size: 16px;
  line-height: 1.6;
  width: 100%;
  position: relative;
  transition: background-color 0.3s ease;
}

.chat-message:hover {
  background-color: rgba(0, 0, 0, 0.01);
}

.chat-message.user {
  background-color: #f9fafb;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.chat-message.ai {
  background-color: #ffffff;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
}

.chat-message.error {
  background-color: #fff9f9;
}

/* Message Content */
.message-content {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.message-content p {
  margin: 0;
  padding: 0;
  white-space: pre-wrap;
}

/* User and AI Icons */
.message-avatar {
  width: 34px;
  height: 34px;
  margin-right: 16px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
  transform: translateY(0);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  flex-shrink: 0;
}

.message-header:hover .message-avatar {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.user-avatar {
  background-color: #7857ff;
  color: white;
}

.ai-avatar {
  background-color: #10a37f;
  color: white;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
  font-weight: 600;
  color: #202123;
  position: relative;
}

.message-header span {
  font-size: 15px;
  letter-spacing: 0.2px;
}

/* Chat Input Wrapper */
.chat-input-wrapper {
  width: 100%;
  background-color: #ffffff;
  padding: 16px 20px;
  box-sizing: border-box;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

/* Chat Input Container */
.chat-input-container {
  width: 100%;
  background-color: #ffffff;
  box-sizing: border-box;
  max-width: 900px;
  margin: 0 auto;
}

/* Chat Input Box */
.chat-input-box {
  display: flex;
  align-items: center;
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  width: 100%;
  margin: 0 auto;
  transition: border-color 0.2s ease, box-shadow 0.2s ease, transform 0.2s ease;
  box-sizing: border-box;
}

.chat-input-box:focus-within {
  border-color: rgba(16, 163, 127, 0.4);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08), 0 0 0 2px rgba(16, 163, 127, 0.1);
  transform: translateY(-1px);
}

.chat-input-box.disabled {
  opacity: 0.7;
  background-color: #f9f9f9;
  cursor: not-allowed;
}

/* Plus Button */
.chat-plus-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  margin-right: 12px;
  color: #6e6e80;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background-color 0.2s ease, color 0.2s ease, transform 0.1s ease;
}

.chat-plus-button:hover {
  background-color: rgba(110, 110, 128, 0.1);
  color: #10a37f;
  transform: scale(1.05);
}

.chat-plus-button:active {
  transform: scale(0.95);
}

.chat-plus-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Chat Input (Expanding Textarea) */
.chat-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 16px;
  resize: none;
  overflow-y: hidden;
  max-height: 200px;
  line-height: 1.5;
  padding: 8px 12px 8px 0;
  color: #353740;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
  width: 100%;
  box-sizing: border-box;
  min-height: 36px;
}

.chat-input::placeholder {
  color: #8e8ea0;
  opacity: 1;
  transition: opacity 0.2s ease;
  line-height: 1.5;
  vertical-align: middle;
}

.chat-input:focus::placeholder {
  opacity: 0.7;
}

.chat-input:disabled {
  background-color: transparent;
  cursor: not-allowed;
}

/* Send Button */
.chat-send-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  transition: background-color 0.2s ease, transform 0.1s ease;
  width: 36px;
  height: 36px;
}

.chat-send-button:not(:disabled):hover {
  background-color: rgba(16, 163, 127, 0.1);
  transform: scale(1.05);
}

.chat-send-button:not(:disabled):active {
  transform: scale(0.95);
}

.chat-send-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.chat-send-icon {
  width: 18px;
  height: 18px;
  color: #10a37f;
  transition: color 0.2s ease;
}

.chat-send-button:not(:disabled):hover .chat-send-icon {
  color: #0d8c6c;
}

/* Error message */
.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fef2f2;
  border-radius: 8px;
  padding: 16px;
  margin: 10px 0;
  border: 1px solid #fee2e2;
  text-align: center;
}

.error-icon {
  margin-bottom: 10px;
  color: #ef4444;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #fef2f2;
  border: 1px solid #fee2e2;
}

.error-message p {
  color: #b91c1c;
  margin-bottom: 14px;
  font-weight: 500;
}

.retry-button {
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.retry-button:hover {
  background-color: #dc2626;
  transform: translateY(-1px);
}

.retry-button:active {
  transform: translateY(1px);
}

/* Code styling enhancements */
pre {
  border-radius: 8px;
  margin: 16px 0;
  background-color: #f8f8f8 !important;
  border: 1px solid #e5e5e5;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  overflow: auto;
  transition: box-shadow 0.2s ease;
}

pre:hover {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
}

code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 14px;
  line-height: 1.45;
}

/* Copy code button */
.code-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f1f1f1;
  padding: 8px 12px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  border-bottom: 1px solid #e5e5e5;
  font-size: 13px;
  color: #666;
}

.copy-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
  transition: color 0.2s;
  padding: 4px 8px;
  border-radius: 4px;
}

.copy-button:hover {
  color: #10a37f;
  background-color: rgba(16, 163, 127, 0.1);
}

.copy-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* Message typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10a37f;
  margin-right: 6px;
  animation: typing-animation 1.4s infinite ease-in-out;
  opacity: 0.6;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-animation {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.6;
  }
  30% {
    transform: translateY(-4px);
    opacity: 1;
  }
}

/* Message actions (appear on hover) */
.message-actions {
  position: absolute;
  top: 10px;
  right: 20px;
  display: flex;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.chat-message:hover .message-actions {
  opacity: 1;
}

.message-action-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #8e8ea0;
  width: 30px;
  height: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
  transition: background-color 0.2s, color 0.2s;
}

.message-action-button:hover {
  background-color: rgba(142, 142, 160, 0.1);
  color: #353740;
}

/* Empty state when no messages */
.empty-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
  text-align: center;
  color: #8e8ea0;
}

.empty-chat-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #10a37f;
  opacity: 0.8;
}

.assessment-icon {
  color: #4d69fa;
}

.empty-chat-title {
  font-size: 24px;
  font-weight: 600;
  color: #353740;
  margin-bottom: 12px;
}

.empty-chat-subtitle {
  font-size: 16px;
  max-width: 500px;
  line-height: 1.5;
  margin-bottom: 30px;
}

/* Assessment instructions */
.assessment-instructions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 500px;
  background-color: #f9fafb;
  border-radius: 12px;
  padding: 20px;
  margin-top: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  text-align: left;
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.assessment-instruction-item {
  display: flex;
  align-items: flex-start;
  gap: 14px;
}

.instruction-icon {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  background-color: #4d69fa;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 3px;
}

.instruction-text h3 {
  font-size: 16px;
  font-weight: 600;
  color: #202123;
  margin: 0 0 4px 0;
}

.instruction-text p {
  font-size: 14px;
  color: #6e6e80;
  margin: 0;
  line-height: 1.5;
}

/* Link styling in messages */
.message-content a {
  color: #10a37f;
  text-decoration: none;
  border-bottom: 1px solid rgba(16, 163, 127, 0.3);
  transition: border-color 0.2s, color 0.2s;
}

.message-content a:hover {
  color: #0d8c6c;
  border-bottom-color: #0d8c6c;
}

/* New message indicator */
.new-messages-indicator {
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  background-color: white;
  color: #10a37f;
  padding: 8px 16px;
  border-radius: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  cursor: pointer;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s, transform 0.3s;
  z-index: 5;
}

.new-messages-indicator.visible {
  opacity: 1;
  pointer-events: auto;
  transform: translate(-50%, -5px);
}

.new-messages-indicator:hover {
  background-color: #f9f9f9;
}

.new-messages-indicator svg {
  margin-left: 6px;
  width: 16px;
  height: 16px;
}

/* Animation for new messages */
@keyframes message-appear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-message {
  animation: message-appear 0.3s ease-out;
}

/* Keyboard shortcuts tooltip */
.keyboard-shortcut {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #f1f1f1;
  font-size: 12px;
  color: #666;
  margin-left: 8px;
}

/* Pulse animation for assessment icon */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(77, 105, 250, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(77, 105, 250, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(77, 105, 250, 0);
  }
}

.assessment-icon svg {
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Loading animation for the first message */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-message:first-child {
  animation: fadeInUp 0.5s ease-out;
}

/* Dashboard Layout */
.dashboard-container {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.chat-section {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-right: 1px solid #e5e7eb;
}

.assessment-section {
  width: 380px;
  height: 100%;
  overflow-y: auto;
  background-color: #f9fafb;
  padding: 1rem;
}

/* Assessment Report Styles */
.assessment-report {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  color: #374151;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.assessment-report-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
  font-style: italic;
  text-align: center;
  padding: 2rem;
}

.report-header {
  padding: 1.25rem;
  border-bottom: 1px solid #e5e7eb;
}

.report-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.report-timestamp {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.report-overview {
  padding: 1.25rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.key-value {
  margin-bottom: 0.5rem;
  display: flex;
  align-items: baseline;
}

.key-value .key {
  font-weight: 500;
  margin-right: 0.5rem;
  min-width: 140px;
  color: #4b5563;
}

.key-value .value {
  font-weight: 400;
  color: #111827;
}

.report-section {
  padding: 1.25rem;
  border-bottom: 1px solid #e5e7eb;
}

.report-section-title {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
}

.bullet-list {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.bullet-list li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.phase-summaries {
  margin-top: 1rem;
}

.phase-summary {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 6px;
}

.phase-name {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: capitalize;
  color: #111827;
}

.detailed-assessment {
  margin: 0.5rem 0;
  line-height: 1.5;
}

.phase-strengths, 
.phase-development, 
.phase-insights, 
.phase-recommendation {
  margin-top: 0.75rem;
}

.phase-strengths h5, 
.phase-development h5, 
.phase-insights h5, 
.phase-recommendation h5 {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
}

.overall-evaluation,
.overall-recommendation {
  line-height: 1.6;
}

.report-footer {
  padding: 1.25rem;
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #6b7280;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .dashboard-container {
    flex-direction: column;
  }
  
  .chat-section {
    height: 60%;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .assessment-section {
    width: 100%;
    height: 40%;
  }
}

/* Textarea container */
.textarea-container {
  position: relative;
  flex: 1;
  width: 100%;
  min-width: 0;
}

/* Agent Configuration Form Styles */
.agent-config-form {
  max-width: 800px;
  margin: 40px auto;
  padding: 30px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.form-header p {
  color: #666;
  font-size: 16px;
}

/* Form Sections */
.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eaeaea;
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.section-title svg {
  margin-right: 8px;
  color: #10a37f;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group label svg {
  margin-right: 8px;
  color: #10a37f;
}

.form-group input, 
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-group input:focus, 
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #10a37f;
  box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.2);
}

.form-group input.error, 
.form-group select.error,
.form-group textarea.error {
  border-color: #e74c3c;
}

.error-message {
  color: #e74c3c;
  font-size: 14px;
  margin-top: 5px;
}

/* Compulsory Questions Styles */
.questions-list {
  margin-bottom: 20px;
}

.question-item {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 12px;
  border-left: 3px solid #10a37f;
  transition: box-shadow 0.2s ease, transform 0.2s ease;
}

.question-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.question-content {
  position: relative;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.question-phase {
  background-color: rgba(16, 163, 127, 0.1);
  color: #10a37f;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: capitalize;
}

.question-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  background: none;
  border: none;
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.action-button.edit {
  color: #4d69fa;
}

.action-button.edit:hover {
  background-color: rgba(77, 105, 250, 0.1);
}

.action-button.delete {
  color: #e74c3c;
}

.action-button.delete:hover {
  background-color: rgba(231, 76, 60, 0.1);
}

.question-text {
  font-weight: 500;
  margin-bottom: 8px;
}

.question-expected, .question-criteria {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.no-questions {
  text-align: center;
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
  color: #666;
  font-style: italic;
}

/* Question Form */
.question-form {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #eaeaea;
}

.question-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.question-form-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.close-form-button {
  background: none;
  border: none;
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  color: #666;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.close-form-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.criteria-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.criteria-item input {
  flex-grow: 1;
  margin-right: 8px;
}

.remove-criteria-button {
  background: none;
  border: none;
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  color: #e74c3c;
  transition: background-color 0.2s ease;
}

.remove-criteria-button:hover {
  background-color: rgba(231, 76, 60, 0.1);
}

.add-criteria-button, .add-question-button {
  background: none;
  border: 1px dashed #ddd;
  color: #10a37f;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease, border-color 0.2s ease;
  width: 100%;
  margin-top: 10px;
}

.add-criteria-button svg, .add-question-button svg {
  margin-right: 6px;
}

.add-criteria-button:hover, .add-question-button:hover {
  background-color: rgba(16, 163, 127, 0.05);
  border-color: #10a37f;
}

.question-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.cancel-button, .save-button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.cancel-button {
  background-color: #f1f1f1;
  color: #666;
  border: none;
}

.cancel-button:hover {
  background-color: #e5e5e5;
}

.save-button {
  background-color: #10a37f;
  color: white;
  border: none;
}

.save-button:hover {
  background-color: #0d8c6c;
  transform: translateY(-1px);
}

.save-button:active {
  transform: translateY(1px);
}

.start-session-button {
  width: 100%;
  padding: 14px;
  background-color: #10a37f;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.start-session-button svg {
  margin-right: 8px;
}

.start-session-button:hover {
  background-color: #0d8c6c;
  transform: translateY(-2px);
}

.start-session-button:active {
  transform: translateY(1px);
}

/* Assessment type pill styles */
.assessment-type-pills {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

.assessment-pill {
  background-color: #f1f1f1;
  border: 2px solid transparent;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  user-select: none;
}

.assessment-pill:hover {
  background-color: #e9e9e9;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.assessment-pill.active {
  background-color: rgba(16, 163, 127, 0.1);
  border-color: #10a37f;
  color: #10a37f;
  box-shadow: 0 2px 8px rgba(16, 163, 127, 0.2);
}

.pill-icon {
  display: flex;
  align-items: center;
  margin-right: 8px;
  color: #10a37f;
}

/* Assessment info icon */
.assessment-info {
  display: flex;
  align-items: center;
  margin-top: 10px;
  color: #666;
  font-size: 14px;
  background-color: rgba(16, 163, 127, 0.05);
  padding: 10px;
  border-radius: 6px;
}

.assessment-info svg {
  margin-right: 8px;
  color: #10a37f;
  flex-shrink: 0;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .agent-config-form {
    max-width: 100%;
    margin: 20px;
    padding: 20px;
  }
}

/* Chat Header Styles */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background-color: #ffffff;
}

.chat-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chat-participant {
  display: flex;
  flex-direction: column;
}

.participant-name {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.participant-role {
  font-size: 13px;
  color: #666;
}

.chat-assessment-type {
  margin-left: 10px;
}

.assessment-badge {
  background-color: rgba(16, 163, 127, 0.1);
  color: #10a37f;
  padding: 4px 10px;
  border-radius: 14px;
  font-size: 13px;
  font-weight: 500;
}

.chat-actions {
  display: flex;
  gap: 10px;
}

.restart-session-button {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f1f1f1;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.restart-session-button:hover {
  background-color: #e5e5e5;
  color: #333;
  transform: translateY(-1px);
}

.restart-session-button:active {
  transform: translateY(1px);
}

.restart-session-button svg {
  margin-right: 6px;
  color: #10a37f;
}

/* Assessment Completion Alert Styles */
.assessment-completion-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.assessment-completion-modal {
  background-color: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  padding: 30px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  text-align: center;
  animation: slideUp 0.4s ease-out;
}

.completion-icon {
  width: 80px;
  height: 80px;
  background-color: rgba(16, 163, 127, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: #10a37f;
}

.completion-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.completion-message {
  font-size: 16px;
  line-height: 1.5;
  color: #666;
  margin-bottom: 16px;
}

.completion-next-steps {
  font-size: 15px;
  line-height: 1.5;
  color: #666;
  background-color: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  text-align: left;
  border-left: 3px solid #10a37f;
}

.completion-actions {
  display: flex;
  justify-content: center;
}

.completion-close-button {
  background-color: #10a37f;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.completion-close-button:hover {
  background-color: #0d8c6c;
  transform: translateY(-2px);
}

.completion-close-button:active {
  transform: translateY(1px);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
