{"kind": "collectionType", "collectionName": "monthly_usages", "info": {"singularName": "monthly-usage", "pluralName": "monthly-usages", "displayName": "Monthly Usage", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"training_char_count": {"type": "integer", "default": 0}, "training_tokens_count": {"type": "integer", "default": 0}, "query_tokens_count": {"type": "integer", "default": 0}, "query_count": {"type": "integer", "default": 0}, "usage_quota": {"type": "integer", "default": 0}, "total_tokens_used": {"type": "integer", "default": 0}, "trained_count": {"type": "integer", "default": 0}, "kb_count": {"type": "integer", "default": 0}, "cost_training": {"type": "decimal", "default": 0}, "cost_query": {"type": "decimal", "default": 0}, "cost_used": {"type": "decimal", "default": 0}, "organization": {"type": "relation", "relation": "manyToOne", "target": "api::organization.organization", "inversedBy": "monthly_usages"}, "period": {"type": "enumeration", "enum": ["year", "month"], "default": "month"}, "credits_used": {"type": "biginteger", "default": "0"}, "period_start": {"type": "datetime"}, "period_end": {"type": "datetime"}}}