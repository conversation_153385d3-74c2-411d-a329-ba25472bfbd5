import React from "react";

export default function PaginationComponent({
  currentPage,
  totalPages,
  onPageChange,
  showPageNumbers = true,
}) {
  const canGoPrev = currentPage > 1;
  const canGoNext = currentPage < totalPages;

  const activeTextColor = "text-[#7C53FE] font-bold";
  const inactiveTextColor = "text-gray-700 hover:text-[#7C53FE]";

  return (
    <div className="flex justify-center items-center space-x-4 py-4">
      <button
        onClick={() => canGoPrev && onPageChange(currentPage - 1)}
        disabled={!canGoPrev}
        className={`text-sm px-2 py-1 ${
          canGoPrev
            ? "text-[#7C53FE] hover:underline"
            : "text-gray-400 cursor-not-allowed"
        }`}
      >
        Prev
      </button>

      {showPageNumbers && (
        <div className="flex space-x-3">
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => onPageChange(page)}
              className={`text-sm ${
                currentPage === page ? activeTextColor : inactiveTextColor
              }`}
            >
              {page}
            </button>
          ))}
        </div>
      )}

      <button
        onClick={() => canGoNext && onPageChange(currentPage + 1)}
        disabled={!canGoNext}
        className={`text-sm px-2 py-1 ${
          canGoNext
            ? "text-[#7C53FE] hover:underline"
            : "text-gray-400 cursor-not-allowed"
        }`}
      >
        Next
      </button>
    </div>
  );
}
