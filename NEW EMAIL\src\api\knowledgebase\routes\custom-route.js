'use strict';

/**
 * knowledgebase custom router
 */
 module.exports = {
	routes: [

		{
			method: 'GET',
			path: '/kb_source',
			handler: 'custom-controller.kbSource',
			config: {
				policies: [

					'global::user-details-populate',
					'global::update-org-before-api-call',

				]
			}
		  },
		  {
			method: 'DELETE',
			path: '/delete_kb_source/:id',
			handler: 'custom-controller.deleteKbSource',
			config: {
				policies: [

					'global::user-details-populate',
					'global::update-org-before-api-call',
				]
			}
		  },
		  {
			method: 'POST',
			path: '/whatsapp-url',
			handler: 'custom-controller.whatsappUrl',
			// config: {
			// 	policies: [

			// 		'global::user-details-populate',
			// 		'global::update-org-before-api-call',
			// 	]
			// }
		  },
		  {
			method: 'GET',
			path: '/public_kb',
			handler: 'custom-controller.publicKb',

		  },
		  {
			method: 'GET',
			path: '/public_file_results_model/:id',
			handler: 'custom-controller.getFileResultsModel',
		  }

	]
	};
