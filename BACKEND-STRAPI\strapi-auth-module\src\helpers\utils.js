module.exports = {
  isGenericEmail(email) {
    const emailDomains = ["gmail", "yahoo"];
    const domain = email.split("@")[1].split(".")[0]; // Extract domain from email

    // Check if the domain is in the generic domains array or is a specific organization
    return emailDomains.includes(domain) || !email.includes("@"); // Add more conditions for specific organizations
  },

  isYouTubeUrl(url) {
    const regex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.?be)\/.+$/;
    return regex.test(url);
  },
};
