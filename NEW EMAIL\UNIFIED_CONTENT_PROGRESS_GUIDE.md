# 🎯 **Unified Content Progress System**

## 📋 **Overview**

The unified content progress system tracks user progress for **ALL** content types in a single table:

1. **📺 Series Episodes** - Tracks within series context  
2. **🎵 Standalone Tracks** - Individual content not in series
3. **🔗 Unified API** - Same endpoints for both content types
4. **📊 Comprehensive Analytics** - Cross-content insights

---

## 🏗 **Architecture**

### **Single Table Design**
```sql
content_progress {
  user_id                  -- Who is progressing
  file_results_model_id    -- What content (ALWAYS required)
  episode_id               -- Series episode UUID (NULL for standalone)
  series_id                -- Series reference (NULL for standalone)
  progress_percentage      -- 0-100 integer
  time_spent               -- Seconds spent
  last_position            -- Resume position
  completed                -- Boolean completion
  -- timestamps...
}
```

### **Content Type Detection**
```javascript
// Automatic detection when tracking progress
if (episode_id && series_id) {
  // This is a series episode
} else {
  // This is standalone content
}
```

---

## 🎯 **Use Cases**

### **1. Series Episode Progress**
```javascript
// User watches episode 3 of "AI Fundamentals" series
PUT /api/content-progress/456
{
  "data": {
    "fileResultsModelId": 456,        // The audio/video file
    "progressPercentage": 75,
    "timeSpent": 1800,               // 30 minutes
    "lastPosition": 1350,            // Resume at 22:30
    "completed": false
  }
}

// System automatically detects this is part of series and:
// 1. Populates episode_id and series_id
// 2. Updates series progress when episode completed
// 3. Unlocks next episode if prerequisites met
```

### **2. Standalone Track Progress**
```javascript
// User listens to a standalone podcast
PUT /api/content-progress/789
{
  "data": {
    "fileResultsModelId": 789,        // The audio file
    "progressPercentage": 100,
    "timeSpent": 2400,               // 40 minutes
    "lastPosition": 2400,            // Completed
    "completed": true
  }
}

// System automatically detects standalone content:
// 1. episode_id and series_id remain NULL
// 2. No series progress updates
// 3. Just tracks individual content progress
```

---

## 🔄 **API Endpoints**

### **Universal Progress Tracking**
```http
PUT /api/content-progress/{fileResultsModelId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "data": {
    "progressPercentage": 85,
    "timeSpent": 1200,
    "lastPosition": 1020,
    "completed": false
  }
}
```

### **Get Progress for Any Content**
```http
GET /api/content-progress/{fileResultsModelId}
Authorization: Bearer {token}

Response:
{
  "data": {
    "id": 123,
    "progress_percentage": 85,
    "time_spent": 1200,
    "last_position": 1020,
    "completed": false,
    "series_id": 5,           // Present if part of series
    "episode_id": "uuid-123", // Present if part of series
    "file_results_model": {
      "title": "Episode 3: Neural Networks",
      "audio_url": "https://...",
      "duration": 1440
    }
  }
}
```

### **Recent Activity Across All Content**
```http
GET /api/my-progress/recent?limit=20
Authorization: Bearer {token}

Response:
{
  "data": [
    {
      "progress_percentage": 75,
      "last_accessed_at": "2024-01-15T10:30:00Z",
      "file_results_model": {
        "title": "Episode 3: Neural Networks"
      },
      "series_id": 5,           // Series episode
      "episode_id": "uuid-123"
    },
    {
      "progress_percentage": 100,
      "last_accessed_at": "2024-01-14T15:45:00Z", 
      "file_results_model": {
        "title": "Standalone Podcast: Future of AI"
      },
      "series_id": null,        // Standalone content
      "episode_id": null
    }
  ]
}
```

---

## 📊 **Analytics & Insights**

### **User Progress Statistics**
```javascript
// Get user's overall progress stats
const stats = await strapi.service('api::content-progress.content-progress')
  .getUserProgressStats(userId);

/*
Returns:
{
  total_content_accessed: 45,
  total_completed: 32,
  total_time_spent: 72000,        // 20 hours
  series_accessed: 5,             // 5 different series
  standalone_tracks_accessed: 15  // 15 standalone tracks
}
*/
```

### **Series-Specific Analytics**
```javascript
// Get user's progress within a specific series
const seriesStats = await strapi.service('api::content-progress.content-progress')
  .getSeriesProgressStats(userId, seriesId);

/*
Returns:
{
  episodes_accessed: 8,
  episodes_completed: 6,
  total_time_spent: 14400,    // 4 hours
  last_activity: "2024-01-15T10:30:00Z",
  total_episodes: 10          // Series has 10 episodes
}
*/
```

### **Content Discovery**
```javascript
// Find what to play next in a series
const nextEpisode = await strapi.service('api::content-progress.content-progress')
  .getNextEpisodeInSeries(userId, seriesId);

// Check if user can access specific episode  
const canAccess = await strapi.service('api::content-progress.content-progress')
  .canAccessEpisode(userId, episodeId);
```

---

## 🎮 **Smart Features**

### **1. Progressive Unlocking (Series Only)**
```javascript
// Episodes unlock only after previous completion
const episode3 = await getEpisode("uuid-episode-3");
const canAccess = await canAccessEpisode(userId, "uuid-episode-3");

if (!canAccess) {
  return "Complete Episode 2 to unlock Episode 3";
}
```

### **2. Smart Resume**
```javascript
// Resume from last position for any content
const progress = await getProgress(userId, fileResultsModelId);
const resumePosition = progress?.last_position || 0;

return {
  audioUrl: file.audio_url,
  startAt: resumePosition,  // Resume from last position
  duration: file.duration
};
```

### **3. Cross-Content Recommendations**
```javascript
// Recommend based on combined activity
const recentActivity = await getRecentActivity(userId);
const preferences = analyzeContentPreferences(recentActivity);

// Mix of series episodes and standalone content
const recommendations = await getRecommendations(preferences);
```

---

## 🔍 **Query Patterns**

### **Efficient Queries with Proper Indexes**

#### **User's Active Content (Mixed)**
```sql
-- Get all user's in-progress content (series + standalone)
SELECT 
  cp.file_results_model_id,
  cp.progress_percentage,
  cp.last_accessed_at,
  frm.title,
  cp.series_id,
  s.title as series_title
FROM content_progress cp
JOIN file_results_models frm ON frm.id = cp.file_results_model_id
LEFT JOIN series s ON s.id = cp.series_id
WHERE cp.user_id = ? 
  AND cp.completed = false
ORDER BY cp.last_accessed_at DESC;

-- Uses: idx_user_active_content
```

#### **Popular Content Analysis**
```sql
-- Find most popular content (series + standalone)
SELECT 
  frm.title,
  COUNT(DISTINCT cp.user_id) as unique_users,
  AVG(cp.progress_percentage) as avg_progress,
  COUNT(*) FILTER (WHERE cp.completed) as completions,
  CASE WHEN cp.series_id IS NULL THEN 'standalone' ELSE 'series' END as content_type
FROM content_progress cp
JOIN file_results_models frm ON frm.id = cp.file_results_model_id
GROUP BY frm.id, frm.title, (cp.series_id IS NULL)
ORDER BY unique_users DESC;

-- Uses: idx_content_popularity
```

#### **Series Completion Analytics**
```sql
-- Series episode completion rates
SELECT 
  s.title,
  se.position,
  frm.title as episode_title,
  COUNT(DISTINCT cp.user_id) as users_accessed,
  COUNT(*) FILTER (WHERE cp.completed) as completions,
  ROUND(COUNT(*) FILTER (WHERE cp.completed) * 100.0 / COUNT(DISTINCT cp.user_id), 2) as completion_rate
FROM content_progress cp
JOIN series_episodes se ON se.episode_id = cp.episode_id
JOIN series s ON s.id = cp.series_id
JOIN file_results_models frm ON frm.id = cp.file_results_model_id
WHERE cp.series_id = ?
GROUP BY s.id, s.title, se.position, frm.title
ORDER BY se.position;

-- Uses: idx_series_episode_completion
```

---

## ⚡ **Performance Optimizations**

### **1. Efficient Data Types**
```sql
-- Optimized storage
progress_percentage INTEGER      -- 2 bytes vs 8 bytes decimal
time_spent INTEGER              -- Seconds, efficient for calculations
last_position INTEGER           -- Resume position in seconds
completed BOOLEAN               -- 1 byte boolean
```

### **2. Smart Indexing**
```sql
-- Separate indexes for different query patterns
idx_content_progress_user_content     -- User + specific content
idx_content_progress_user_series      -- User + series episodes
idx_content_progress_standalone       -- Standalone content only
idx_content_progress_recent           -- Recent activity feed
```

### **3. Partitioning Strategy**
```sql
-- Partition by user activity for large scale
-- Hot partition: Active users (last 30 days)
-- Warm partition: Regular users (30-180 days)  
-- Cold partition: Inactive users (>180 days)
```

---

## 🚀 **Migration from Old System**

### **If You Had Separate Tables Before:**
```sql
-- Migrate existing episode progress
INSERT INTO content_progress (
  user_id, file_results_model_id, episode_id, series_id,
  progress_percentage, time_spent, last_position, completed,
  started_at, completed_at, last_accessed_at
)
SELECT 
  ep.user_id, 
  se.file_results_model_id,
  se.episode_id,
  se.series_id,
  ep.progress_percentage,
  ep.time_spent,
  ep.last_position,
  ep.completed,
  ep.started_at,
  ep.completed_at,
  ep.last_accessed_at
FROM old_episode_progress ep
JOIN series_episodes se ON se.id = ep.series_episode_id;

-- Migrate standalone track progress (if any)
INSERT INTO content_progress (
  user_id, file_results_model_id, 
  progress_percentage, time_spent, last_position, completed,
  started_at, completed_at, last_accessed_at
)
SELECT 
  user_id, file_results_model_id,
  progress_percentage, time_spent, last_position, completed,
  started_at, completed_at, last_accessed_at  
FROM old_track_progress;
```

---

## 🎯 **Benefits Summary**

### **For Developers:**
- **Single API** for all content progress
- **Unified logic** for tracking, analytics, recommendations
- **Consistent data model** across content types
- **Efficient queries** with proper indexing

### **For Users:**
- **Seamless experience** across series and standalone content
- **Universal progress tracking** and resume functionality
- **Consistent UI** regardless of content organization
- **Smart recommendations** based on all activity

### **For Analytics:**
- **Complete user journey** across all content
- **Cross-content insights** and patterns
- **Unified reporting** for business intelligence
- **Efficient data processing** with single table

This unified approach scales from thousands to millions of users while providing rich insights into user behavior across all content types! 🚀 