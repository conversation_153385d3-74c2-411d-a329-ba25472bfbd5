import React, {useState, useEffect} from 'react';
import { DataGrid } from '@mui/x-data-grid';
import Stack from '@mui/material/Stack';
import { useSelector } from 'react-redux';
import { selectUser } from 'app/store/userSlice';
import axios from "axios";

const columns = [
  { field: 'guid', headerName: 'ID', width: 150 },
  { field: 'source_type', headerName: 'Type', width: 70 },
  { field: 'source_name', headerName: 'Source', width: 230 },
  

];



export default function DataTable() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const user = useSelector(selectUser);
  const fetchData = () => {
    const options = {
      method: 'GET',
      url: `${process.env.REACT_APP_AUTH_BASE_URL}/api/kb_meta`,
    };
    axios.request(options).then((response) => {
      if(response.data.kb_data){
      setData(response.data.kb_data);
      }
      setLoading(false);
    }).catch((error) => {
      setLoading(false);
      // setFailureMessage("File Upload Failed !!")
    })
  }
  useEffect(() => {
    fetchData();
  },[])
  return (
    <div style={{ height: 400}} className="bg-gray-100 w-full border rounded-lg mt-10">
    <div className="justify-self-center"/>
      <DataGrid
      getRowId={(row) => row.guid}
        rows={data}
        columns={columns}
        pageSize={10}
        rowsPerPageOptions={[10]}
        loading={loading}
        components={{NoRowsOverlay: () => 
      <Stack height="100%" alignItems="center" justifyContent="center">
        No data has bean trained
      </Stack>
  }}
 
     />
    </div>
  );
}
