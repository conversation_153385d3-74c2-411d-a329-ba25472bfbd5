'use strict';

/**
 * answer controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const axios = require('axios');

module.exports = createCoreController('api::monthly-usage.monthly-usage', ({ strapi }) =>  ({
	async currentMonth(ctx) {
		const now = new Date();
		try {
			let result = await strapi.query('api::monthly-usage.monthly-usage').findOne({
				where: { 
					organization: ctx.state.user.organization.id,
					createdAt:{$startsWith: `${now.getFullYear()}-${now.getMonth()<9?'0':''}${now.getMonth() + 1}`,
				}},
				
			  }).catch(err=>{
				console.log(err);

			});
			return result;
		} catch (err) {
			return ctx.throw(500,"Server error")
		}
	  },
	}));