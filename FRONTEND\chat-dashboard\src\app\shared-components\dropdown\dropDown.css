/** 
 * The Dropdown Container class represents a container for a dropdown menu.
 * It provides styling for the dropdown button, arrow, options, and backdrop.
 */
.dropdown-container {
    position: relative;
    width: 280px;
    display: flex;
    align-items: center;
    padding-top: 8px;
}

/**
 * The Dropdown Button class represents a button for the dropdown menu.
 * It provides styling for the button appearance, background, borders, padding, and text alignment.
 */
 .dropdown-button {
    z-index: 1000;
    flex: 1;
    height: 42px;
    color:#667085;
    background-color: white;
    border: 1px solid #D1D1D1;
    border-radius: 4px;
    padding: 0 12px;
    font-weight: 500;
    text-align: left;
    appearance: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: box-shadow 0.3s ease, transform 0.3s ease; /* Smooth transition for the raised effect */
}

/* Hover effect for border color and raised appearance */
.dropdown-button:hover {
    background-color: #8576B5 !important;
    color : white;
    border-color: transparent;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Adds shadow beneath the button */
    transform: translateY(-2px); /* Moves the button up slightly for the raised effect */
}
.dropdown-button:disabled {
    background-color: #cccccc !important;
}
.dropdown-button:disabled:hover {
    background-color: #cccccc !important;
    transform: none;
}
/* Selected effect */
.dropdown-button.selected {
    z-index: 1000;
    flex: 1;
    height: 42px;
    background-color: #8576B5 !important;
    color : white;
    border-color: transparent;
    border-radius: 4px;
    padding: 0 12px;
    text-align: left;
    appearance: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: box-shadow 0.3s ease, transform 0.3s ease;
}

/**
 * The Dropdown Arrow class represents an arrow icon for the dropdown menu.
 * It provides styling for the arrow height, width, and margin.
 */
.dropdown-arrow {
    height: 6px;
    width: auto;
    margin-left: 8px;
}



/**
 * The Backdrop class represents a backdrop for the dropdown menu.
 * It provides styling for the backdrop position, size, blur, transition, and background color.
 */
.backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    backdrop-filter: blur(1px);
    transition: all 0.2s ease-in-out;
    background-color: rgba(255, 255, 255, 0.4);
    z-index: 999;
}

/**
 * The DropdownOptions class represents the dropdown container.
 * It provides absolute positioning, box-shadow, and scroll properties.
 */
 .dropdown-options {
    position: absolute;
    top: 56px;
    width: 100%;
    border: none;
    border-radius: 6px;
    background-color: #f8f8f8; /* Slightly off-white for depth */
    background-image: linear-gradient(to bottom, #fff, #f2f2f2); /* Subtle gradient for a more professional look */
    z-index: 1000;
    max-height: 240px;
    overflow-y: auto;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.2s; /* Smooth transitions */
}

/**
 * The OptionItem class represents an individual option in the dropdown menu.
 * It provides styling for the option item padding, hover, and active state.
 */
.option-item {
    padding: 10px 15px; /* Slightly increased padding for better aesthetics */
    cursor: pointer;
    color: #333; /* Darker text color for better contrast */
    transition: background-color 0.2s; /* Smooth transitions for hover effects */
}

.option-item:hover {
    background-color: #e6e6e6; /* A light gray color on hover */
}

.option-item:active {
    background-color: #cccccc; /* A slightly darker gray color on active/click */
}

/**
 * The Circular Progress class represents a circular progress indicator for the dropdown menu.
 * It provides styling for the progress indicator position and z-index.
 */
.circular-progress {
    position: absolute;
    top: 18px;
    right: 30px;
    z-index: 1;
}
