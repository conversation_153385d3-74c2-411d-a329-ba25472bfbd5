'use strict';

/**
 * file-results-model custom router
 */
 module.exports = {
	routes: [
        {
            method: 'GET',
            path: '/random-public-tracks',
            handler: 'custom-controller.getRandomPublicFileResultsModel',
        },
        {
            method: 'GET',
            path: '/random-public-tracks-preview',
            handler: 'custom-controller.getRandomPublicFileResultsModelPreview',
        },
        {
            method: 'GET',
            path: '/featured',
            handler: 'custom-controller.getFeaturedFileResultsModel',
        },
        {
            method: 'GET',
            path: '/share-track/:id',
            handler: 'custom-controller.shareTrack',
            config: {
                auth: false, // Make this endpoint public (no authentication required)
            }
        }
    ]
};
