import React, { useState, useEffect } from "react";
import axios from "axios";
import FusePageSimple from "@fuse/core/FusePageSimple";
import OrganisationHeader from "./OrganisationHeader";
import { selectUser } from "app/store/userSlice";
import { useSelector, useDispatch } from "react-redux";
import Divider from "@mui/material/Divider";
import Button from "@mui/material/Button";
import GridComponent from "./GridComponent";
import PlanCard from "app/shared-components/plan-card/plan-card";
import Typography from "@mui/material/Typography";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import history from "@history";
import { showMessage } from "app/store/fuse/messageSlice";
import LoadingSpinner from "app/shared-components/loading-spinner/loading-spinner";
import { ButtonBase, Stack } from "@mui/material";
import AddMemberDialog from "./AddMemberDialog";
import ConfirmDialog from "app/shared-components/dialog/confirm-dialog";
import SettingsCopyTextField from "app/shared-components/forms/SettingsCopyTextField";
import { makeStyles } from "@mui/styles";

// Add this useStyles hook outside of your componen

const useStyles = makeStyles({
  customBackground: {
    backgroundColor: "#76acda !important",
  },
});


function OrganisationDashboardApp() {
  const classes = useStyles();
  const [organisation, setOrganisation] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [openUnsubscribeConfirm, setOpenUnsubscribeConfirm] = useState(false);
  const user = useSelector(selectUser);
  const dispatch = useDispatch();
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [deletingUser, setDeletingUser] = useState(null);
  async function fetchOrganisations() {
    try {
      const response = await axios
        .get(
          `${process.env.REACT_APP_AUTH_BASE_URL}/api/organizations/${user.data.organization.id}?populate=*`
        )
        .catch((error) => {
          console.log(error);
        });
      setOrganisation(response.data.data);
    } catch (error) {
      dispatch(
        showMessage({ message: "Something went worng. Try after some time" })
      );
      console.error(error);
    }
  }
  useEffect(() => {
    fetchOrganisations();
  }, []);
  useEffect(() => {
    if (
      !(
        user.data?.organization?.subscription === "subscribed" ||
        user.data?.organization?.subscription === "trial"
      )
    ) {
      history.push("subscription");
    }
  }, []);

  const users = organisation?.attributes?.users?.data;

  const gridItems = users?.map((user, index) => (
    <div key={index} style={{ marginBottom: "15px", marginTop: "16px" }}>
      <Divider></Divider>
      <GridComponent
        name={user.attributes.username}
        email={user.attributes.email}
      />
      <Divider></Divider>
    </div>
  ));
  const onUnsubscribeClick = () => {
    setOpenUnsubscribeConfirm(true);
  };

  const handleUnsubscribeDialogClose = () => {
    setOpenUnsubscribeConfirm(false);
  };
  const onPlanUpgrageSelect = () => {
    history.push("/subscription", "update");
  };
  const onUnsubscribeConfirm = async () => {
    try {
      setIsLoading(true);
      const response = await axios
        .get(
          `${process.env.REACT_APP_AUTH_BASE_URL}/api/subscriptions/unsubscribe`
        )
        .catch((error) => {
          console.log(error);
          throw error;
        });
      setOpenUnsubscribeConfirm(false);
      history.push("/sign-out");
      dispatch(showMessage({ message: "You have successfully unsubscribed" }));
    } catch (error) {
      console.log(error);
      dispatch(
        showMessage({ message: "Something went worng. Try after some time" })
      );
    } finally {
      setIsLoading(false);
    }
  };
  const handleRemoveUser = (user) => {
    if (organisation?.attributes?.users.data.length <= 1) {
      dispatch(showMessage({ message: `You can not delete all users` }));
    } else {
      setConfirmDialogOpen(true);
      setDeletingUser(user);
    }
  };
  const onUserDeleteCancel = () => {
    setConfirmDialogOpen(false);
    setDeletingUser(undefined);
  };
  const deleteUser = async () => {
    setConfirmDialogOpen(false);
    setIsLoading(true);
    try {
      const response = await axios.delete(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/users/${deletingUser.id}`
      );
      const users = _.reject(organisation?.attributes?.users.data, {
        id: deletingUser.id,
      });
      const updatedUOrg = { ...organisation };
      updatedUOrg.attributes.users.data = users;
      setOrganisation(updatedUOrg);
      dispatch(
        showMessage({
          message: `${deletingUser.attributes.username} has been deleted`,
        })
      );
    } catch (error) {
      console.log(error);
    } finally {
      setDeletingUser(undefined);
      setIsLoading(false);
    }
  };

  const handleCopy = (text) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        dispatch(showMessage({ message: "Copied to Clipboard" }));
      })
      .catch((err) => {
        dispatch(showMessage({ message: "Could not copy to Clipboard" }));
      });
  };

  return (
    <FusePageSimple
      header={
        <div style={{ marginLeft: "10%" }}  className="bg-transparent">
          <OrganisationHeader />
        </div>
      }
      content={
        <div
          className="flex flex-col w-full bg-transparent px-24 items-start md:px-32 pb-24 "
          style={{ marginLeft: "10%" }}
        >
          <div className="rounded-md bg-transparent">
            <SettingsCopyTextField
              organizationId={user.data.organization.org_id}
              onCopy={handleCopy}
            />
            <div className="mt-64 flex flex-col w-full bg-transparent">
              <Stack direction={"row"} justifyContent={"space-between"}>
                <div className="text-lg font-semibold text-black tracking-tight leading-8 mr-4">
                  Members
                </div>
                <div className="mt-[-20px]">
                  <AddMemberDialog fetchOrganisations={fetchOrganisations} />
                </div>
              </Stack>
              {users?.map((user, index) => (
                <div
                  key={index}
                  style={{ marginBottom: "15px", marginTop: "16px" }}
                >
                  <GridComponent
                    name={user.attributes.username}
                    email={user.attributes.email}
                    onClick={() => {
                      handleRemoveUser(user);
                    }}
                  />
                </div>
              ))}
            </div>
            <div className="mt-64">
              <Typography className="text-lg font-semibold text-black tracking-tight leading-8 mr-4">
                Current plan
              </Typography>
              <div className="flex mt-20 w-[53%]">
                {_.isEmpty(organisation) ||
                _.isEmpty(organisation.attributes.plan.data) ? (
                  <></>
                ) : (
                  <PlanCard
                    plan={organisation.attributes.plan.data}
                    disableButton={true}
                    isCurrentPlan={true}
                    onClick={()=>{}}
                  ></PlanCard>
                )}
              </div>
            </div>
            <Button
              style={{
                width: "200px",
              }}
              className="shine-button leave-btn text-center text-l text-grey p-10 mt-20"
              size="sm"
              variant="outlined"
              onClick={(e) => onUnsubscribeClick()}
            >
              Unsubscribe
            </Button>

            <Button
              style={{
                width: "200px",
              }}
              className="press-button shine-button filled-btn-black text-center text-l p-10 mt-20 ml-20"
              size="sm"
              variant="outlined"
              onClick={(e) => onPlanUpgrageSelect()}
            >
              Upgrade
            </Button>
          </div>
          <ConfirmDialog
            open={confirmDialogOpen}
            title="Delete User"
            message={`Are you sure want to delete User: ${deletingUser?.attributes.username}?`}
            onCancel={() => {
              onUserDeleteCancel();
            }}
            onc
            onConfirm={() => {
              deleteUser();
            }}
          />
          <Dialog
            open={openUnsubscribeConfirm}
            onClose={handleUnsubscribeDialogClose}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
          >
            <DialogTitle id="alert-dialog-title">
              {"Are you sure want to unsubscribe?!"}
            </DialogTitle>
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                className="text-black"
              >
                You will loose access to Ajentic.
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <button
                className="text-center bg-base-purple text-l rounded text-white p-10"
                onClick={handleUnsubscribeDialogClose}
              >
                Cancel
              </button>
              <Button
                style={{
                  width: "200px",
                }}
                onClick={onUnsubscribeConfirm}
                autoFocus
              >
                Unsubscribe
              </Button>
            </DialogActions>
          </Dialog>
        </div>
      }
    />
  );
}

export default OrganisationDashboardApp;
