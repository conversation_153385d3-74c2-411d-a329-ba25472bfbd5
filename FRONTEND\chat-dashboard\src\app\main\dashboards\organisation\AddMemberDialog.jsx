import DialogTitle from "@mui/material/DialogTitle";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import { Fragment, useState } from "react";
import { showMessage } from "app/store/fuse/messageSlice";
import axios from "axios";
import { yupResolver } from "@hookform/resolvers/yup";
import { Controller, useForm } from "react-hook-form";
import * as yup from "yup";
import _ from "@lodash";
import { useSelector } from "react-redux";
import Button from "@mui/material/Button";
import FormHelperText from "@mui/material/FormHelperText";
import FormControl from "@mui/material/FormControl";
import TextField from "@mui/material/TextField";
import CircularProgress from "@mui/material/CircularProgress";
import { selectUser } from "app/store/userSlice";
import LoadingSpinner from "app/shared-components/loading-spinner/loading-spinner";
import { useDispatch } from "react-redux";

/**
 * Form Validation Schema
 */
const schema = yup.object().shape({
  userName: yup.string().required("You must enter display name"),
  email: yup
    .string()
    .email("You must enter a valid email")
    .required("You must enter a email"),

  password: yup
    .string()
    .required("Please enter your password.")
    .min(8, "Password is too short - should be 8 chars minimum."),
  passwordConfirm: yup
    .string()
    .oneOf([yup.ref("password"), null], "Passwords must match"),
});

const defaultValues = {
  userName: "",
  email: "",
  password: "",
  passwordConfirm: "",
};

export default function AddMemberDialog({ fetchOrganisations }) {
  const [open, setOpen] = useState(false);
  const dispatch = useDispatch();
  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const [isVerifyingEmail, setIsVerifyingEmail] = useState(false);
  const [isVerifyingUsername, setIsVerifyingUsername] = useState(false);
  const [loading, setLoading] = useState(false);
  const user = useSelector(selectUser);

  const { control, formState, handleSubmit, watch, setError, setValue } =
    useForm({
      mode: "onChange",
      defaultValues,
      resolver: yupResolver(schema),
    });

  const { isValid, dirtyFields, errors } = formState;
  async function onSubmit({ userName, password, email }) {
    setLoading(true);

    try {
      const response = await axios.post(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/users`,
        {
          confirmed: false,
          blocked: false,
          role: {
            connect: [
              {
                id: 1,
              },
            ],
          },
          organization: {
            connect: [
              {
                id: user.data.organization.id,
              },
            ],
          },
          username: userName,
          email,
          password,
        },

        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      handleClose();
      fetchOrganisations();
    } catch (error) {
      dispatch(
        showMessage({ message: "Something went worng. Try after some time" })
      );
      console.error(error);
    } finally {
      setLoading(false);
    }
  }
  const verifyEmail = async (email) => {
    setIsVerifyingEmail(true);
    try {
      // Make the API call to verify email
      const response = await axios.post(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/user/verify-email`,
        {
          email: email,
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      if (response.data === "invalid") {
        setError("email", {
          type: "manual",
          message: "Email already exists",
        });
        setValue("email", email, { shouldDirty: false });
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsVerifyingEmail(false);
    }
  };

  const verifyUsername = async (username) => {
    setIsVerifyingUsername(true);
    try {
      // Make the API call to verify email
      const response = await axios.post(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/user/verify-username`,
        {
          username: username,
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      if (response.data === "invalid") {
        setError("userName", {
          type: "manual",
          message: "Username already exists",
        });
        setValue("userName", username, { shouldDirty: false });
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsVerifyingUsername(false);
    }
  };

  return (
    <Fragment>
      <div>
        <Button variant="outlined" className="press-button shine-button w-full mt-16 rounded-xs bg-primary hover:bg-primary text-white" onClick={handleClickOpen}>
          Add Member
        </Button>
      </div>

      <Dialog open={open} onClose={handleClose} maxWidth="xs" fullWidth>
        <DialogTitle>Add Member</DialogTitle>
        <DialogContent>
          <form
            name="registerForm"
            noValidate
            className="flex flex-col justify-center w-full mt-32"
            onSubmit={handleSubmit(onSubmit)}
          >
            <Controller
              name="userName"
              control={control}
              render={({ field }) => (
                <div className="flex">
                  <TextField
                    {...field}
                    className="mb-12"
                    autoFocus
                    type="name"
                    error={!!errors.userName}
                    helperText={errors?.userName?.message}
                    variant="outlined"
                    required
                    fullWidth
                    onChange={(e) => {
                      field.onChange(e);
                      verifyUsername(e.target.value);
                    }}
                    style={{
                      backgroundColor: "transparent",
                      borderRadius: "2px",
                    }}
                    placeholder="Enter your username"
                    inputProps={{
                      disableUnderline: true, // Disables the default underline to use modern border
                      style: {
                        backgroundColor: "transparent",
                        paddingRight: "4px",
                        paddingBottom: "4px",
                        paddingTop: "2px", // Updated padding for a better look and feel
                        fontWeight: 400,
                        height: "4rem", // Fix height of text field
                        fontSize: "1.5rem", // Base font size
                      },
                    }}
                  />
                  {isVerifyingUsername && (
                    <CircularProgress className="ml-4 mt-8" size={24} />
                  )}
                </div>
              )}
            />

            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <div className="flex">
                  <TextField
                    {...field}
                    className="mb-24"
                    type="email"
                    error={!!errors.email}
                    helperText={errors?.email?.message}
                    variant="outlined"
                    borderRadius="2px"
                    required
                    fullWidth
                    onChange={(e) => {
                      field.onChange(e);
                      verifyEmail(e.target.value);
                    }}
                    style={{
                      backgroundColor: "transparent",
                    }}
                    placeholder="Enter your email"
                    inputProps={{
                      disableUnderline: true, // Disables the default underline to use modern border
                      style: {
                        backgroundColor: "transparent",
                        paddingRight: "4px",
                        paddingBottom: "4px",
                        paddingTop: "2px", // Updated padding for a better look and feel
                        fontWeight: 400,
                        height: "4rem", // Fix height of text field
                        fontSize: "1.5rem", // Base font size
                      },
                    }}
                  />
                  {isVerifyingEmail && (
                    <CircularProgress className="ml-4 mt-8" size={24} />
                  )}
                </div>
              )}
            />

            <Controller
              name="password"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  className="mb-24"
                  type="password"
                  error={!!errors.password}
                  helperText={errors?.password?.message}
                  variant="outlined"
                  required
                  fullWidth
                  style={{
                    backgroundColor: "transparent",
                  }}
                  placeholder="Enter your password"
                  inputProps={{
                    disableUnderline: true, // Disables the default underline to use modern border
                    style: {
                      backgroundColor: "transparent",
                      paddingRight: "4px",
                      paddingBottom: "4px",
                      paddingTop: "2px", // Updated padding for a better look and feel
                      fontWeight: 400,
                      height: "4rem", // Fix height of text field
                      fontSize: "1.5rem", // Base font size
                    },
                  }}
                />
              )}
            />

            <Controller
              name="passwordConfirm"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  className="mb-24 cta-text-style"
                  style={{
                    backgroundColor: "transparent",
                  }}
                  placeholder="Confirm your password"
                  inputProps={{
                    disableUnderline: true, // Disables the default underline to use modern border
                    style: {
                      backgroundColor: "transparent",
                      paddingRight: "4px",
                      paddingBottom: "4px",
                      paddingTop: "2px", // Updated padding for a better look and feel
                      fontWeight: 400,
                      height: "4rem", // Fix height of text field
                      fontSize: "1.5rem", // Base font size
                    },
                  }}
                  type="password"
                  error={!!errors.passwordConfirm}
                  helperText={errors?.passwordConfirm?.message}
                  variant="outlined"
                  required
                  fullWidth
                />
              )}
            />

            <Controller
              name="acceptTermsConditions"
              control={control}
              render={({ field }) => (
                <FormControl
                  className="items-center"
                  error={!!errors.acceptTermsConditions}
                >
                  <FormHelperText>
                    {errors?.acceptTermsConditions?.message}
                  </FormHelperText>
                </FormControl>
              )}
            />
            {loading && (
              <div className="mt-4 flex align-center justify-center items-center">
                <LoadingSpinner size={24} />
              </div>
            )}
            {!loading && (
              <Button
                variant="outlined"
                className="press-button shine-button w-full mt-16 rounded-xs bg-primary hover:bg-primary text-white"
                aria-label="Register"
                disabled={
                  _.isEmpty(dirtyFields) ||
                  !isValid ||
                  Object.keys(errors).length > 0
                }
                type="submit"
                size="large"
              >
                Add member
              </Button>
            )}
          </form>
        </DialogContent>
      </Dialog>
    </Fragment>
  );
}
