.chatbox-input {
  /* Sizing and Overflow */
  width: 100%;
  min-height: 80px;
  max-height: 180px;
  overflow-y: auto;
  resize: none;
  padding: 10px;
  

  font-size: 14px;
  font-weight: 400;
  color: #333;

  /* Styling */
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius:6px;

  /* Transitions for Smooth Effects */
  transition: all 0.3s ease;
}

.chatbox-input::placeholder {
  color: #999;
}

 /* Hover Effect */
 .chatbox-input:hover {
  box-shadow: 0px 5px 12px rgba(0, 0, 0, 0.08);
}

/* Focus Effect */
.chatbox-input:focus {
  outline: none;
  border-color: #8576B5 ;
  box-shadow: 0px 5px 12px rgba(0, 0, 0, 0.1);
}

.chatbox-container .MuiOutlinedInput-notchedOutline {
  border: none;
}

.chatbox-input:focus {
  outline: none;
}

.chatbox-char-count {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 5px;
  transition: color 200ms ease;  /* Smooth transition for color changes */
}

.char-count-text-grey {
  color: #777;
}

.char-count-text-red {
  color: #D32F2F; /* A softer shade of red */
}

.chatbox-flex-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  gap: 40px;
}

.run-button-container {
  margin-top: 25px;
  margin-left: auto;
  display: flex;
  flex-flow: column;
}

.chatbox-flex-container .muiltr-73lb2f-MuiPaper-root-MuiDialog-paper {
  max-width: 600px !important;
}

.chatbox-flex-container .muiltr-hgpioi-MuiSvgIcon-root {
  fill: #FFFFFF;
  opacity: 0.5;
}

.muiltr-sbmv4m-MuiInputBase-input-MuiOutlinedInput-input{
  color: white;
  padding: 0 !important;
  margin-left: 10px;
}

.chatbox-send-button {
  cursor: pointer;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 10px;
  width: 100px;
}

.muiltr-72806-MuiInputBase-root-MuiOutlinedInput-root{
  border-radius: 10px;
  border-color: grey !important;
  outline: none;
  padding: 0;
}

@media only screen and (max-width: 600px) {
  .run-button-container {
    margin-left: 30%;
    align-items: center;
    justify-content: center;
  }

  .run-button-active{
    background-color : #6530C1 !important;

    border: 0px !important;
   
  }

  .run-button-inactive{
    background-color : #999696 !important ;
    border-color: #999696 !important;
    border: 0px !important;
  }
}
