'use strict';

/**
 * knowledgebase controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const axios = require('axios');

module.exports = createCoreController('api::knowledgebase.knowledgebase', ({ strapi }) =>  ({
	async create(ctx, next) {
		const kbCount= await strapi.db.query('api::knowledgebase.knowledgebase').count({
			where: {
				organization: {
					id: ctx.state.user.organization.id,
				},
				publishedAt:{
					$notNull: true,
				}
			},
		}
		  );
		if(kbCount >= ctx.state.user.organization.allowed_kbs){
			ctx.throw(400, 'You have exceeded the Agents limit');
			return ctx.badRequest('You have exceeded the Agents limit');
		}

		// Get the data from the request body
		const data = ctx.request.body;
		if(!data.org_id){
			data.org_id =ctx.state.user.organization.org_id;
		}
		data["kb_type"]=ctx.request.body.type;
		data["agent_type"]=ctx.request.body.agent_type ?? 'multiAgent';
		data["openAI_assistant_id"]='';
		data['vs_source']= ctx.request.body?.vs_source ?? 'supabase';
		data['vs_table_name']= ctx.request.body?.vs_table_name ?? 'documents';

		// Make a POST request to the external API with the request body
		const response = await axios.post(`${process.env.TALKBASE_BASE_URL}/v4/add_kb`, data, {
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded'
			}
		}).catch(e=>{
			console.log(e);
		});
		if(response.data?.status==='failure'){
			return ctx.badRequest(response.data.error??'Something went wrong');
		}

		await strapi.query('api::monthly-usage.monthly-usage').update({
			where: { id: ctx.state.user.organization.monthly_usages[ctx.state.user.organization.monthly_usages.length - 1].id },
			data: {
				kb_count: ctx.state.user.organization.monthly_usages[ctx.state.user.organization.monthly_usages.length - 1].kb_count+1,

			  },
		  });
		  ctx.request.body.data ={
			name: ctx.request.body.kb_name,
			organization: ctx.state.user.organization.id,
			kb_id: response.data.id,
			type: ctx.request.body.type,
			default_ai_task: ctx.request.body.default_ai_task,
			publishedAt: new Date()
		  }
		  const result = await super.create(ctx);

		return result.data;

	},

	 async find(ctx) {
		try{
		const results= await strapi.db.query('api::knowledgebase.knowledgebase').findMany({
			where: {
				organization: {
					id: ctx.state.user.organization.id,
				},
				publishedAt:{
					$notNull: true,
				}
			},
			populate:{
				default_ai_task:true,
				shopify_model:true,
			}
		}
		  );
		  return results ;
	}catch(e){
		console.log(e);
		throw e;
	}

	  },
	  async delete(ctx) {
		try{
		const data = {
			org_id: ctx.state.user.organization.id,
			kb_id: ctx.params.id
		};
		const deletedRecord = await axios.delete(`${process.env.TALKBASE_BASE_URL}/v4/delete_kb`, data, {
			headers: {
				'Content-Type': 'multipart/form-data'
			}
		}).catch(e=>{
			console.log(e);
		});
		const response = await strapi.db.query('api::knowledgebase.knowledgebase').update({
			where: { id: ctx.params.id },
			data: {
				publishedAt: null
			},
		  });

		// const response = await super.delete(ctx);


		return response;
	}catch(e){
		return ctx.badRequest('Something went wrong')
	}
	  }
}));
