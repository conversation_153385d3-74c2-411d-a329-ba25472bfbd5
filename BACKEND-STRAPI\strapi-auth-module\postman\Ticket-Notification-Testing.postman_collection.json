{"info": {"name": "Ticket Notification Email Testing", "description": "Comprehensive testing collection for ticket creation and email notification flows", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:1337", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"identifier\": \"<EMAIL>\",\n  \"password\": \"your-password\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/local", "host": ["{{base_url}}"], "path": ["api", "auth", "local"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('auth_token', response.jwt);", "    console.log('Authentication successful');", "} else {", "    console.log('Authentication failed');", "}"]}}]}]}, {"name": "Email Testing", "item": [{"name": "Test Email Service - Confirmation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"name\": \"Test User\",\n  \"confirmationUrl\": \"https://podycy.com/confirm/test123\"\n}"}, "url": {"raw": "{{base_url}}/api/test-email/confirmation", "host": ["{{base_url}}"], "path": ["api", "test-email", "confirmation"]}}}, {"name": "Test Email Service - Welcome", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"name\": \"Test User\"\n}"}, "url": {"raw": "{{base_url}}/api/test-email/welcome", "host": ["{{base_url}}"], "path": ["api", "test-email", "welcome"]}}}, {"name": "Test Email Service - Password Reset", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"name\": \"Test User\",\n  \"resetUrl\": \"https://podycy.com/reset/test123\"\n}"}, "url": {"raw": "{{base_url}}/api/test-email/reset-password", "host": ["{{base_url}}"], "path": ["api", "test-email", "reset-password"]}}}, {"name": "Test Ticket Notification - Basic", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"emails\": [\"<EMAIL>\", \"<EMAIL>\"],\n  \"name\": \"Admin User\",\n  \"title\": \"Test Support Ticket\",\n  \"description\": \"This is a test ticket created for email flow validation.\",\n  \"agent_name\": \"Test Agent\",\n  \"customer_email\": \"<EMAIL>\",\n  \"ticket_url\": \"https://podycy.com/tickets/test123\"\n}"}, "url": {"raw": "{{base_url}}/api/test-email/ticket-notification", "host": ["{{base_url}}"], "path": ["api", "test-email", "ticket-notification"]}}}, {"name": "Test Ticket Notification - Enhanced", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"emails\": [\"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\"],\n  \"name\": \"Organization Admin\",\n  \"title\": \"High Priority Customer Issue\",\n  \"description\": \"Customer is experiencing critical login issues that need immediate attention.\",\n  \"agent_name\": \"Support Bot AI\",\n  \"customer_email\": \"<EMAIL>\",\n  \"ticket_url\": \"https://podycy.com/tickets/urgent123\",\n  \"organization_name\": \"Podycy Technologies\",\n  \"priority\": \"High\",\n  \"assignee\": \"Senior Support Engineer\"\n}"}, "url": {"raw": "{{base_url}}/api/test-email/ticket-notification-enhanced", "host": ["{{base_url}}"], "path": ["api", "test-email", "ticket-notification-enhanced"]}}}]}, {"name": "Scrumboard Ticket Creation", "item": [{"name": "Create Ticket - Email Integration Users", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": {\n    \"knowledgebase\": {\n      \"id\": \"test-kb-id\",\n      \"name\": \"Test Knowledge Base\",\n      \"organization\": {\n        \"id\": \"test-org-id\",\n        \"name\": \"Podycy Technologies\",\n        \"users\": [\n          {\n            \"id\": \"user1\",\n            \"email\": \"<EMAIL>\",\n            \"username\": \"Admin User\"\n          },\n          {\n            \"id\": \"user2\",\n            \"email\": \"<EMAIL>\",\n            \"username\": \"Manager User\"\n          }\n        ]\n      },\n      \"email_integration\": {\n        \"users\": [\n          {\n            \"email\": \"<EMAIL>\",\n            \"username\": \"Support Team\"\n          },\n          {\n            \"email\": \"<EMAIL>\",\n            \"username\": \"Notification Center\"\n          }\n        ]\n      }\n    }\n  },\n  \"ticketJson\": {\n    \"title\": \"Customer Login Issue\",\n    \"description\": \"Customer cannot access their account after password reset\",\n    \"priority\": \"High\",\n    \"assignee\": \"Support Team\"\n  },\n  \"contactInfo\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/scrumboard/create-ticket", "host": ["{{base_url}}"], "path": ["api", "scrumboard", "create-ticket"]}}}, {"name": "Create Ticket - Organization Users Only", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"query\": {\n    \"knowledgebase\": {\n      \"id\": \"test-kb-id\",\n      \"name\": \"Test Knowledge Base\",\n      \"organization\": {\n        \"id\": \"test-org-id\",\n        \"name\": \"Podycy Technologies\",\n        \"users\": [\n          {\n            \"id\": \"user1\",\n            \"email\": \"<EMAIL>\",\n            \"username\": \"Admin User\"\n          },\n          {\n            \"id\": \"user2\",\n            \"email\": \"<EMAIL>\",\n            \"username\": \"Manager User\"\n          },\n          {\n            \"id\": \"user3\",\n            \"email\": \"<EMAIL>\",\n            \"username\": \"Support Agent 1\"\n          },\n          {\n            \"id\": \"user4\",\n            \"email\": \"<EMAIL>\",\n            \"username\": \"Support Agent 2\"\n          }\n        ]\n      }\n    }\n  },\n  \"ticketJson\": {\n    \"title\": \"General Support Request\",\n    \"description\": \"Customer needs help with feature configuration\",\n    \"priority\": \"Medium\",\n    \"assignee\": \"Support Team\"\n  },\n  \"contactInfo\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/scrumboard/create-ticket", "host": ["{{base_url}}"], "path": ["api", "scrumboard", "create-ticket"]}}}]}, {"name": "System Validation", "item": [{"name": "Check Email Templates", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/email-templates", "host": ["{{base_url}}"], "path": ["api", "email-templates"]}}}, {"name": "Check Postmark Configuration", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/email-config", "host": ["{{base_url}}"], "path": ["api", "email-config"]}}}, {"name": "Run Comprehensive Email Test", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/test-email-system", "host": ["{{base_url}}"], "path": ["api", "test-email-system"]}}}]}]}