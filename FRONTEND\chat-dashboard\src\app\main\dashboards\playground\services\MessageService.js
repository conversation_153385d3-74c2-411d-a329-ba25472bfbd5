/**
 * MessageService.js
 * Service responsible for message handling operations
 * Following Single Responsibility Principle by isolating message operations
 */

/**
 * Creates a user message object
 * @param {string} text - Message content
 * @returns {Object} Formatted user message
 */
const createUserMessage = (text) => {
  return {
    type: 'user',
    text
  };
};

/**
 * Creates an AI message object
 * @param {string} text - Message content
 * @returns {Object} Formatted AI message
 */
const createAIMessage = (text) => {
  return {
    type: 'ai',
    text
  };
};

/**
 * Adds a user message to the message list
 * @param {Array} messages - Current messages
 * @param {string} text - Message content
 * @returns {Array} Updated message list
 */
const addUserMessage = (messages, text) => {
  return [...messages, createUserMessage(text)];
};

/**
 * Adds an AI message to the message list
 * @param {Array} messages - Current messages
 * @param {string} text - Message content
 * @returns {Array} Updated message list
 */
const addAIMessage = (messages, text) => {
  return [...messages, createAIMessage(text)];
};

/**
 * Creates test response content for demo mode
 * @param {string} userInput - User input to respond to
 * @returns {string} Generated response
 */
const createTestResponse = (userInput) => {
  return `I've received your message: "${userInput.substring(0, 50)}${userInput.length > 50 ? '...' : ''}"\n\nThank you for sharing your thoughts. Could you elaborate on how you would apply this approach in a real-world scenario?`;
};

export default {
  addUserMessage,
  addAIMessage,
  createTestResponse
}; 