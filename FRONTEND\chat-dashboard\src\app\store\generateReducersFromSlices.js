const { combineReducers } = require('@reduxjs/toolkit');
const { SlicesType } = require('app/store/lazyWithSlices');

const generateReducersFromSlices = (slices) => {
  const reducerGroups = {};

  slices?.forEach((slice) => {
    const [primary, secondary] = slice.name.split('/');
    if (secondary) {
      if (!reducerGroups[primary]) {
        reducerGroups[primary] = {};
      }
      reducerGroups[primary][secondary] = slice.reducer;
    } else {
      reducerGroups[primary] = slice.reducer;
    }
  });

  const combinedReducers = {};

  Object.entries(reducerGroups).forEach(([key, reducerGroup]) => {
    if (typeof reducerGroup === 'function') {
      combinedReducers[key] = reducerGroup;
    } else if (typeof reducerGroup === 'object') {
      combinedReducers[key] = combineReducers(reducerGroup);
    }
  });

  return combinedReducers;
};

module.exports = generateReducersFromSlices;
