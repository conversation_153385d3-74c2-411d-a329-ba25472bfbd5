'use strict';

/**
 * Template Renderer Service
 * Handles rendering of HTML templates with variable substitution
 */

class TemplateRenderer {
  
  /**
   * Render template with variables
   * @param {string} template - HTML template string
   * @param {Object} variables - Variables to substitute
   * @returns {string} - Rendered HTML
   */
  static renderTemplate(template, variables = {}) {
    if (!template) return '';
    
    let rendered = template;
    
    // Handle different template syntax patterns
    // {{variable}} - Standard handlebars style
    // {variable} - Simple brackets
    // %variable% - Percentage style
    
    Object.keys(variables).forEach(key => {
      const value = variables[key] || '';
      
      // Replace various patterns
      const patterns = [
        new RegExp(`{{\\s*${key}\\s*}}`, 'g'),
        new RegExp(`{\\s*${key}\\s*}`, 'g'),
        new RegExp(`%${key}%`, 'g')
      ];
      
      patterns.forEach(pattern => {
        rendered = rendered.replace(pattern, value);
      });
    });
    
    return rendered;
  }
  
  /**
   * Render template with advanced features (conditional blocks, loops)
   * @param {string} template - HTML template string
   * @param {Object} variables - Variables to substitute
   * @returns {string} - Rendered HTML
   */
  static renderAdvancedTemplate(template, variables = {}) {
    if (!template) return '';
    
    let rendered = template;
    
    // Handle conditional blocks: {{#if variable}}content{{/if}}
    rendered = this.processConditionals(rendered, variables);
    
    // Handle loops: {{#each items}}{{name}}{{/each}}
    rendered = this.processLoops(rendered, variables);
    
    // Handle basic variable substitution
    rendered = this.renderTemplate(rendered, variables);
    
    return rendered;
  }
  
  /**
   * Process conditional blocks in template
   * @param {string} template - Template string
   * @param {Object} variables - Variables
   * @returns {string} - Processed template
   */
  static processConditionals(template, variables) {
    // Handle {{#if variable}}content{{/if}}
    const ifRegex = /{{#if\s+(\w+)}}(.*?){{\/if}}/gs;
    
    return template.replace(ifRegex, (match, variable, content) => {
      const value = variables[variable];
      return (value && value !== 'false' && value !== '0') ? content : '';
    });
  }
  
  /**
   * Process loop blocks in template
   * @param {string} template - Template string
   * @param {Object} variables - Variables
   * @returns {string} - Processed template
   */
  static processLoops(template, variables) {
    // Handle {{#each arrayVariable}}{{property}}{{/each}}
    const eachRegex = /{{#each\s+(\w+)}}(.*?){{\/each}}/gs;
    
    return template.replace(eachRegex, (match, arrayName, itemTemplate) => {
      const array = variables[arrayName];
      if (!Array.isArray(array)) return '';
      
      return array.map(item => {
        return this.renderTemplate(itemTemplate, item);
      }).join('');
    });
  }
  
  /**
   * Validate template syntax
   * @param {string} template - Template to validate
   * @returns {Object} - Validation result
   */
  static validateTemplate(template) {
    if (!template) {
      return { valid: false, errors: ['Template is empty'] };
    }
    
    const errors = [];
    
    // Check for unclosed handlebars
    const openBraces = (template.match(/{{/g) || []).length;
    const closeBraces = (template.match(/}}/g) || []).length;
    
    if (openBraces !== closeBraces) {
      errors.push('Mismatched handlebars braces');
    }
    
    // Check for unclosed conditional blocks
    const ifBlocks = (template.match(/{{#if/g) || []).length;
    const endIfBlocks = (template.match(/{{\/if}}/g) || []).length;
    
    if (ifBlocks !== endIfBlocks) {
      errors.push('Unclosed conditional blocks');
    }
    
    // Check for unclosed loop blocks
    const eachBlocks = (template.match(/{{#each/g) || []).length;
    const endEachBlocks = (template.match(/{{\/each}}/g) || []).length;
    
    if (eachBlocks !== endEachBlocks) {
      errors.push('Unclosed loop blocks');
    }
    
    // Check for basic HTML validity
    if (!this.isValidHTML(template)) {
      errors.push('Invalid HTML structure');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Basic HTML validation
   * @param {string} html - HTML to validate
   * @returns {boolean} - Is valid HTML
   */
  static isValidHTML(html) {
    try {
      // Basic check for common HTML issues
      const openTags = html.match(/<[^\/][^>]*>/g) || [];
      const closeTags = html.match(/<\/[^>]*>/g) || [];
      
      // This is a basic check - for production, consider using a proper HTML parser
      return true; // For now, assume valid
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Extract variables from template
   * @param {string} template - Template string
   * @returns {Array} - Array of variable names found in template
   */
  static extractVariables(template) {
    if (!template) return [];
    
    const variables = new Set();
    
    // Extract {{variable}} style
    const handlebarsMatches = template.match(/{{(?!#|\/)\s*(\w+)\s*}}/g) || [];
    handlebarsMatches.forEach(match => {
      const variable = match.replace(/[{}]/g, '').trim();
      variables.add(variable);
    });
    
    // Extract {variable} style
    const simpleMatches = template.match(/{(?!{)\s*(\w+)\s*}/g) || [];
    simpleMatches.forEach(match => {
      const variable = match.replace(/[{}]/g, '').trim();
      variables.add(variable);
    });
    
    // Extract %variable% style
    const percentMatches = template.match(/%(\w+)%/g) || [];
    percentMatches.forEach(match => {
      const variable = match.replace(/%/g, '');
      variables.add(variable);
    });
    
    return Array.from(variables);
  }
  
  /**
   * Generate preview HTML with sample data
   * @param {string} template - Template string
   * @param {Object} sampleData - Sample data for preview
   * @returns {string} - Preview HTML
   */
  static generatePreview(template, sampleData = {}) {
    const defaultSampleData = {
      username: 'John Doe',
      email: '<EMAIL>',
      app_name: 'Your App',
      confirmation_link: 'https://yourapp.com/confirm/sample',
      reset_url: 'https://yourapp.com/reset/sample',
      company_name: 'Your Company',
      support_email: '<EMAIL>',
      current_year: new Date().getFullYear().toString()
    };
    
    const previewData = { ...defaultSampleData, ...sampleData };
    return this.renderAdvancedTemplate(template, previewData);
  }
}

module.exports = TemplateRenderer; 