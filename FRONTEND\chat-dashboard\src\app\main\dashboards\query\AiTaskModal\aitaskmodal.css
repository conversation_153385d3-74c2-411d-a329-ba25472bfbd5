.ai-task-conatiner{
    display: flex;
    flex-flow: column;
    gap: 25px;
}

.row {
    display: flex;
    padding: 20px;
    border-radius: 20px;
    cursor: pointer;
    background-color: rgb(249, 249, 249);
}

.row.selected {
    background-color: lightgrey;
    color: white;
}

.row:hover{
    background-color: lightgrey;
}

.row:nth-child(odd) {
    background-color:#F3F9FF;
}

.row:nth-child(odd):hover {
    background-color:lightgrey;
}
 
.left-column {
    flex: 70%;
    margin-right: 25px;
}
  
.right-column {
    flex: 30%;
    display: flex;
    flex-direction: column;
}
  
.name {
    font-weight: bold;
}
  
.description {
    margin-top: 5px;
    font-size: 14px;
    color: #555;
}

.circle-container {
    display: flex;
    align-items: center;
    flex-flow: row;
    margin-bottom: 10px; 
}

.label-heading {
    flex: 20%;
    font-weight: bold;
}

.circles {
    flex: 50%;
    display: flex;
} 

.circle {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 4px;
    background-color: #B3B3BF;
    box-shadow: 0px 4px 4px rgba(96, 91, 255, 0.25);
}

.filled {
    background-color: #2696CD;
}
