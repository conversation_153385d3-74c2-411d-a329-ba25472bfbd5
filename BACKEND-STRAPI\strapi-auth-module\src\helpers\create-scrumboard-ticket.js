const _ = require("lodash");
const axios = require("axios");
const { scrumboardStartingFlow } = require("./defaults");
const emailService = require("./email-service");
const { sendSlackMessage } = require("./slack-service");
const { add_answer_data_to_db, update_monthly_usage } = require("./database-operations");

module.exports = {
  async createScrumboardTicket({ query, session, booking }) {
    try {
      var isWhatsapp;
      if (query) {
        query = await strapi.query("api::answer.answer").findOne({
          where: { id: query },
          populate: {
            knowledgebase: {
              populate: {
                scrumboard: { populate: { scrumboard_automations: true } },
                organization: { populate: { users: true } },
                slack_integration_model: true,
                email_integration: { populate: { users: true } },
              },
            },
          },
        });
        isWhatsapp =
          query.api_source &&
          query.api_source.toLowerCase().startsWith("whatsapp");
      } else {
        isWhatsapp = !isNaN(session);
        query = await strapi.query("api::answer.answer").findOne({
          where: { session: session },
          populate: {
            knowledgebase: {
              populate: {
                scrumboard: { populate: { scrumboard_automations: true } },
                organization: { populate: { users: true } },
                slack_integration_model: true,
                email_integration: { populate: { users: true } },
              },
            },
          },
          orderBy: { id: "desc" },
        });
      }
      // if(_.isEmpty(query)){
      //   const bookingData = await strapi.query("api::contact-booking.contact-booking").findOne({where:{ id: booking},populate:{}})
      //   const ticket = await strapi
      //   .query("api::scrumboard-card.scrumboard-card")
      //   .create({
      //     data: {
      //       title: "Customer ticket",
      //       description: !_.isEmpty(ticketJson?.description?.value)?ticketJson.description.value:`A ticket was created from ${query.knowledgebase.name}`,
      //       ai_assesment: "This ticket was raised without any conversation history",
      //       scrumboard: query.knowledgebase.scrumboard.id,
      //       contact_booking: booking,
      // labels:{
      // 	disconnect: [],
      // 	connect: labels,
      //   }
      //     },
      //   });
      // console.log(ticket);

      // }

      var queryWhere;
      if (isWhatsapp) {
        var date = new Date(query.createdAt);
        date.setMinutes(date.getMinutes() - 2);
        queryWhere = { session: query.session, createdAt: { $gt: date } };
      } else {
        queryWhere = { session: query.session };
      }
      const queries = await strapi.query("api::answer.answer").findMany({
        where: queryWhere,
      });
      const list = await strapi
        .query("api::scrumboard-list.scrumboard-list")
        .findOne({
          where: {
            $and: [
              { title: scrumboardStartingFlow },
              { boardId: query.knowledgebase.scrumboard.id },
            ],
          },
        });
      let conversation = "";
      queries.forEach((item) => {
        if (item.question) {
          conversation += `Customer: ${item.question}\n`;
        }
        if (item.answer) {
          conversation += `Help Desk: ${item.answer}\n`;
        }
      });
      var ticketJson;
      let labels = [];
      try {
        let { data } = await axios
          .post(`${process.env.TALKBASE_BASE_URL}/v4/pipe`, {
            question: `${
              query.knowledgebase.scrumboard?.scrumboard_automations?.find(
                (e) => e.automation_identifier === "ticket_create"
              )?.prompt
            }\n${conversation}`,
            org_id: query.org_id,
            chat_history: "",
            agent_id: 17,
          })
          .catch((err) => {
            console.log(err);
            throw err;
          });
        ticketJson = JSON.parse(data.answer);
        // let botLabels=_.chain(ticketJson).values().filter({ field_type: "Label" }).map('value').value();
        let botLabels = [];
        if (!_.isEmpty(ticketJson.nature_label)) {
          botLabels.push(
            ticketJson.nature_label?.value || ticketJson.nature_label
          );
        }
        if (!_.isEmpty(ticketJson.lead_potential)) {
          botLabels.push(
            ticketJson.lead_potential?.value || ticketJson.lead_potential
          );
        }
        if (!_.isEmpty(ticketJson.service_interest_level)) {
          botLabels.push(
            ticketJson.service_interest_level?.value ||
              ticketJson.service_interest_level
          );
        }

        // Removing duplicates
        botLabels = _.uniq(botLabels);
        const stringsToRemove = ["Unknown", "Not Applicable"];
        // Filtering the unique array to remove specified strings
        botLabels = botLabels.filter((item) => !stringsToRemove.includes(item));

        if (botLabels && botLabels.length > 0) {
          botLabels.map(async (l) => {
            if (l && l.length > 0) {
              var label = await strapi
                .query("api::label.label")
                .findOne({ where: { title: l } });
              if (!label) {
                label = await strapi
                  .query("api::label.label")
                  .create({ data: { title: l } });
              }

              labels.push(label.id);
            }
          });
        }
      } catch (e) {
        console.log(e);
      }

      // const ticket = await strapi
      //   .query("api::scrumboard-card.scrumboard-card")
      //   .create({
      //     data: {
      //       title: !_.isEmpty(ticketJson?.title?.value)?ticketJson.title.value:"Customer ticket",
      //       description: !_.isEmpty(ticketJson?.description?.value)?ticketJson.description.value:`A ticket was created from ${query.knowledgebase.name}`,
      //       ai_assesment: !_.isEmpty(ticketJson?.suggested_actions?.value)?ticketJson.suggested_actions.value:"The AI did not have enough conversation context or information to provide an assessment",
      //       scrumboard: query.knowledgebase.scrumboard.id,
      //       listId: list.id,
      //       contact_booking: booking,
      //       ticket_info:ticketJson,
      // labels:{
      // 	disconnect: [],
      // 	connect: labels,
      //   }
      //     },
      //   });
      const sessionModel = await strapi.query("api::session.session").findOne({
        where: {
          session_id: session,
        },
      });

      const ticket = await strapi
        .query("api::scrumboard-card.scrumboard-card")
        .create({
          data: {
            title:
              ticketJson?.title?.value || ticketJson?.title || "Customer ticket",
            description:
              ticketJson?.description?.value ||
              ticketJson?.description ||
              `A ticket was created from ${query.knowledgebase.name}`,
            ai_assesment:
              ticketJson?.suggested_actions?.value ||
              ticketJson?.suggested_actions ||
              "The AI did not have enough conversation context or information to provide an assessment",
            scrumboard: query.knowledgebase.scrumboard.id,
            listId: list.id,
            contact_booking: booking,
            ticket_info: ticketJson,
            labels: {
              disconnect: [],
              connect: labels,
            },
            session:sessionModel.id
          },
          populate: { contact_booking: true },
        });
      if (!query.knowledgebase.organization) {
        query = await strapi.query("api::answer.answer").findOne({
          where: { id: query.id },
          populate: {
            knowledgebase: {
              populate: {
                scrumboard: { populate: { scrumboard_automations: true } },
                organization: { populate: { users: true } },
              },
            },
          },
        });
      }

      var email_contact = ticket.contact_booking.email || "";
      var phone = ticket.contact_booking.phone_number || "";
      var separator = email_contact && phone ? ", " : "";
      var contactInfo = `${email_contact}${separator}${phone}`;

      try {
        if(query.knowledgebase.email_integration){
          const emails = query.knowledgebase.email_integration.users.map((user)=>user.email);
          await emailService.sendTicketNotificationEmail({
            emails: emails,
            name: query.knowledgebase.organization.users[0].username,
            title: ticket.title,
            description: ticket.description,
            agent_name: query.knowledgebase.name,
            customer_email: contactInfo,
            ticket_url: ticket.contact_booking.channel_link,
          });
        } else{
          await emailService.sendTicketNotificationEmail({
            emails: [query.knowledgebase.organization.users[0].email],
            name: query.knowledgebase.organization.users[0].username,
            title: ticket.title,
            description: ticket.description,
            agent_name: query.knowledgebase.name,
            customer_email: contactInfo,
            ticket_url: ticket.contact_booking.channel_link,
          });
        }
      } catch (emailError) {
        console.error('Failed to send notification email:', emailError.message);
      }

      if(query.knowledgebase.slack_integration_model){

        sendSlackMessage({
          slackAuthToken: query.knowledgebase.slack_integration_model.access_token,
          slackChannelId: query.knowledgebase.slack_integration_model.slack_channel_id,
          name: query.knowledgebase.organization.users[0].username,
          subject: ticket.title,
          title: ticket.title,
          description: ticket.description,
          agent_name: query.knowledgebase.name,
          contact_info: contactInfo,
          ticket_url: ticket.contact_booking.channel_link

        });
      }

      if(email_contact){
        try {
          await emailService.sendTicketNotificationEmail({
            emails: [email_contact],
            name: email_contact,
            title: ticket.title,
            description: ticket.description,
            agent_name: query.knowledgebase.name,
            customer_email: contactInfo,
            ticket_url: ticket.contact_booking.channel_link,
          });
        } catch (emailError) {
          console.error('Failed to send customer email:', emailError.message);
        }
      }

      console.log(ticket);
    } catch (e) {
      console.log("Ticket create failed", e);
    }
  },
};
