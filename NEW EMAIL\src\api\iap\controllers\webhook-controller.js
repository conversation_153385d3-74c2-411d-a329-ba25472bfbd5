'use strict';

/**
 * IAP Webhook Controller
 * Handles subscription status updates from Apple and Google
 */

const _ = require('lodash');
const crypto = require('crypto');

module.exports = {
  async handleAppleWebhook(ctx) {
    try {
      const { body } = ctx.request;
      
      // Verify the webhook's authenticity
      // Apple uses a signed payload - verification depends on how Apple implemented your webhook
      
      // The notification type determines what action to take
      const notificationType = body.notification_type;
      const subtype = body.subtype || '';
      const data = body.data || {};
      
      // Get Apple original transaction ID which serves as our subscriptionId
      const originalTransactionId = data.original_transaction_id;
      
      if (!originalTransactionId) {
        console.error('Missing original transaction ID in Apple webhook');
        return ctx.badRequest('Missing transaction data');
      }
      
      // Find the matching subscription in our system
      const subscription = await strapi.query("api::subscription-order.subscription-order").findOne({
        where: { subscriptionId: originalTransactionId },
        populate: ['organization', 'plan'],
      });
      
      if (!subscription) {
        console.error(`Subscription not found for transaction ${originalTransactionId}`);
        return ctx.badRequest('Subscription not found');
      }
      
      // Get user from organization
      const user = await strapi.query("plugin::users-permissions.user").findOne({
        where: { organization: subscription.organization.id },
        limit: 1,
      });
      
      if (!user) {
        console.error(`User not found for subscription ${subscription.id}`);
        return ctx.badRequest('User not found');
      }
      
      // Process the notification based on type
      let newStatus;
      
      switch (notificationType) {
        case 'RENEWAL':
          // Subscription was renewed, update the subscription dates
          newStatus = 'active';
          await this.handleSubscriptionRenewal(subscription, data, user.email);
          break;
          
        case 'CANCEL':
          // Subscription was cancelled
          newStatus = 'cancelled';
          await this.handleSubscriptionCancellation(subscription, user.email);
          break;
          
        case 'DID_CHANGE_RENEWAL_PREF':
          // Subscription renewal preference changed
          if (subtype === 'AUTO_RENEW_DISABLED') {
            await this.handleAutoRenewDisabled(subscription, user.email);
          } else if (subtype === 'AUTO_RENEW_ENABLED') {
            await this.handleAutoRenewEnabled(subscription, user.email);
          }
          break;
          
        case 'DID_CHANGE_RENEWAL_STATUS':
          // Auto-renewal status changed
          if (data.auto_renew_status === false) {
            await this.handleAutoRenewDisabled(subscription, user.email);
          } else {
            await this.handleAutoRenewEnabled(subscription, user.email);
          }
          break;
          
        case 'REFUND':
          // Subscription was refunded
          newStatus = 'refunded';
          await this.handleSubscriptionRefund(subscription, user.email);
          break;
          
        case 'EXPIRED':
          // Subscription expired
          newStatus = 'expired';
          await this.handleSubscriptionExpiration(subscription, user.email);
          break;
          
        default:
          console.log(`Unhandled Apple notification type: ${notificationType}`);
      }
      
      // Update subscription status if needed
      if (newStatus) {
        await strapi.entityService.update('api::subscription-order.subscription-order', subscription.id, {
          data: { status: newStatus }
        });
      }
      
      return { success: true };
    } catch (error) {
      console.error('Error handling Apple webhook:', error);
      return ctx.badRequest(`Failed to process Apple webhook: ${error.message}`);
    }
  },
  
  async handleGoogleWebhook(ctx) {
    try {
      const { body } = ctx.request;
      
      // Verify the webhook's authenticity using your Google Cloud project number
      // This depends on how Google implemented your webhook
      
      // Extract the notification data
      const message = body.message;
      const data = message?.data ? JSON.parse(Buffer.from(message.data, 'base64').toString()) : null;
      
      if (!data) {
        console.error('Missing data in Google webhook');
        return ctx.badRequest('Missing notification data');
      }
      
      // Get subscription ID from the notification
      const subscriptionId = data.subscriptionId;
      const packageName = data.packageName;
      const eventType = data.eventType || data.notificationType;
      
      if (!subscriptionId) {
        console.error('Missing subscription ID in Google webhook');
        return ctx.badRequest('Missing subscription ID');
      }
      
      // Find the matching subscription in our system
      // Google subscriptionId might be stored in a different format, adjust as needed
      const googleSubId = `iap_google_${subscriptionId}`;
      
      // We might need to search by a pattern if the subscription ID format is not exact
      const subscriptions = await strapi.query("api::subscription-order.subscription-order").findMany({
        where: { 
          subscriptionId: { $contains: subscriptionId },
          purchase_platform: 'google'
        },
        populate: ['organization', 'plan'],
      });
      
      if (!subscriptions || subscriptions.length === 0) {
        console.error(`Subscription not found for Google ID ${subscriptionId}`);
        return ctx.badRequest('Subscription not found');
      }
      
      // Use the most recent subscription
      const subscription = _.maxBy(subscriptions, 'createdAt');
      
      // Get user from organization
      const user = await strapi.query("plugin::users-permissions.user").findOne({
        where: { organization: subscription.organization.id },
        limit: 1,
      });
      
      if (!user) {
        console.error(`User not found for subscription ${subscription.id}`);
        return ctx.badRequest('User not found');
      }
      
      // Process the notification based on event type
      let newStatus;
      
      switch (eventType) {
        case 'SUBSCRIPTION_RENEWED':
          // Subscription was renewed
          newStatus = 'active';
          await this.handleSubscriptionRenewal(subscription, data, user.email);
          break;
          
        case 'SUBSCRIPTION_CANCELED':
          // Subscription was cancelled
          newStatus = 'cancelled';
          await this.handleSubscriptionCancellation(subscription, user.email);
          break;
          
        case 'SUBSCRIPTION_PURCHASED':
          // This is typically handled by the initial purchase flow
          // But we can update if needed
          newStatus = 'active';
          break;
          
        case 'SUBSCRIPTION_RECOVERED':
          // Subscription was recovered from payment failure
          newStatus = 'active';
          await this.handleSubscriptionRecovery(subscription, user.email);
          break;
          
        case 'SUBSCRIPTION_PAUSED':
          // Subscription was paused
          newStatus = 'paused';
          break;
          
        case 'SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED':
          // Pause schedule was changed
          break;
          
        case 'SUBSCRIPTION_RESTARTED':
          // Subscription was restarted after pause
          newStatus = 'active';
          break;
          
        case 'SUBSCRIPTION_PRICE_CHANGE_CONFIRMED':
          // Price change was confirmed by user
          break;
          
        case 'SUBSCRIPTION_DEFERRED':
          // Subscription renewal was deferred
          break;
          
        case 'SUBSCRIPTION_REVOKED':
          // User has revoked their subscription
          newStatus = 'cancelled';
          await this.handleSubscriptionCancellation(subscription, user.email);
          break;
          
        case 'SUBSCRIPTION_EXPIRED':
          // Subscription expired
          newStatus = 'expired';
          await this.handleSubscriptionExpiration(subscription, user.email);
          break;
          
        default:
          console.log(`Unhandled Google notification type: ${eventType}`);
      }
      
      // Update subscription status if needed
      if (newStatus) {
        await strapi.entityService.update('api::subscription-order.subscription-order', subscription.id, {
          data: { status: newStatus }
        });
      }
      
      return { success: true };
    } catch (error) {
      console.error('Error handling Google webhook:', error);
      return ctx.badRequest(`Failed to process Google webhook: ${error.message}`);
    }
  },
  
  // Common handlers for subscription events
  
  async handleSubscriptionRenewal(subscription, data, email) {
    try {
      // Update subscription dates
      const startDate = Math.floor(Date.now() / 1000);
      const endDate = subscription.plan.type === "yearly"
        ? Math.floor(Date.now() / 1000) + 365 * 24 * 60 * 60
        : Math.floor(Date.now() / 1000) + 30 * 24 * 60 * 60;
      
      const strapiPaymentHandlers = require('../../../providers/strapi-payment-handlers');
      
      // Update subscription order
      await strapiPaymentHandlers.updateSubscriptionOrder({
        email,
        status: 'active',
        subscriptionId: subscription.subscriptionId,
        startDate,
        endDate,
        isPaid: true,
      });
      
      // Add monthly usage for the new period
      await strapiPaymentHandlers.addMonthlyUsage({
        email,
        startDate,
        endDate,
        product_id: subscription.plan.stripe_prod_id,
      });
      
      // Update organization subscription status
      await strapiPaymentHandlers.updateOrganizationSubscription({
        email,
        status: 'subscribed',
      });
      
    } catch (error) {
      console.error('Error handling subscription renewal:', error);
      throw error;
    }
  },
  
  async handleSubscriptionCancellation(subscription, email) {
    try {
      const strapiPaymentHandlers = require('../../../providers/strapi-payment-handlers');
      
      // Update subscription order
      await strapiPaymentHandlers.updateSubscriptionOrder({
        email,
        status: 'cancelled',
        subscriptionId: subscription.subscriptionId,
      });
      
      // Update organization subscription status to pending cancellation
      // The subscription is still valid until the end of the current period
      await strapiPaymentHandlers.updateOrganizationSubscription({
        email,
        status: 'pendingUnsubscribed',
      });
      
    } catch (error) {
      console.error('Error handling subscription cancellation:', error);
      throw error;
    }
  },
  
  async handleAutoRenewDisabled(subscription, email) {
    try {
      const strapiPaymentHandlers = require('../../../providers/strapi-payment-handlers');
      
      // Update organization subscription status to pending cancellation
      await strapiPaymentHandlers.updateOrganizationSubscription({
        email,
        status: 'pendingUnsubscribed',
      });
      
      // Update subscription status
      await strapi.entityService.update('api::subscription-order.subscription-order', subscription.id, {
        data: { status: 'pendingCancellation' }
      });
      
    } catch (error) {
      console.error('Error handling auto-renew disabled:', error);
      throw error;
    }
  },
  
  async handleAutoRenewEnabled(subscription, email) {
    try {
      const strapiPaymentHandlers = require('../../../providers/strapi-payment-handlers');
      
      // Update organization subscription status back to subscribed
      await strapiPaymentHandlers.updateOrganizationSubscription({
        email,
        status: 'subscribed',
      });
      
      // Update subscription status
      await strapi.entityService.update('api::subscription-order.subscription-order', subscription.id, {
        data: { status: 'active' }
      });
      
    } catch (error) {
      console.error('Error handling auto-renew enabled:', error);
      throw error;
    }
  },
  
  async handleSubscriptionRefund(subscription, email) {
    try {
      const strapiPaymentHandlers = require('../../../providers/strapi-payment-handlers');
      
      // Update subscription order
      await strapiPaymentHandlers.updateSubscriptionOrder({
        email,
        status: 'refunded',
        subscriptionId: subscription.subscriptionId,
      });
      
      // Update organization subscription status
      await strapiPaymentHandlers.updateOrganizationSubscription({
        email,
        status: 'unsubscribed',
      });
      
      // Clear plan from organization
      await strapi.entityService.update('api::organization.organization', subscription.organization.id, {
        data: { plan: null }
      });
      
    } catch (error) {
      console.error('Error handling subscription refund:', error);
      throw error;
    }
  },
  
  async handleSubscriptionExpiration(subscription, email) {
    try {
      const strapiPaymentHandlers = require('../../../providers/strapi-payment-handlers');
      
      // Update subscription order
      await strapiPaymentHandlers.updateSubscriptionOrder({
        email,
        status: 'expired',
        subscriptionId: subscription.subscriptionId,
      });
      
      // Update organization subscription status
      await strapiPaymentHandlers.updateOrganizationSubscription({
        email,
        status: 'unsubscribed',
      });
      
      // Clear plan from organization
      await strapi.entityService.update('api::organization.organization', subscription.organization.id, {
        data: { plan: null }
      });
      
    } catch (error) {
      console.error('Error handling subscription expiration:', error);
      throw error;
    }
  },
  
  async handleSubscriptionRecovery(subscription, email) {
    try {
      const strapiPaymentHandlers = require('../../../providers/strapi-payment-handlers');
      
      // Update subscription order
      await strapiPaymentHandlers.updateSubscriptionOrder({
        email,
        status: 'active',
        subscriptionId: subscription.subscriptionId,
      });
      
      // Update organization subscription status
      await strapiPaymentHandlers.updateOrganizationSubscription({
        email,
        status: 'subscribed',
      });
      
    } catch (error) {
      console.error('Error handling subscription recovery:', error);
      throw error;
    }
  }
}; 