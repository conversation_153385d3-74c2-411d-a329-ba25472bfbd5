'use strict';

/**
 * api-access-key controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::api-access-key.api-access-key',({ strapi }) =>  ({
	async create(ctx) {
		
		const accessKey = await strapi.query('api::api-access-key.api-access-key').findOne({
			where: { name: ctx.request.body.data.name, organization: ctx.state.user.organization?.id},
			populate: { organization: true },
		}).catch(err => {
			console.log(err);
		});
		if(accessKey){
			throw Error("Access key is already added");
		}
		ctx.request.body.data.organization=ctx.state.user.organization?.id;

		const response = await super.create(ctx);
	  
		return response;
	},
}
));
