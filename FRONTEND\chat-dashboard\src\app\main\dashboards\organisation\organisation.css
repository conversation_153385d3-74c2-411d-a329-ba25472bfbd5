.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30%;
}

.leave-btn {
  text-align: center;
  background-color: transparent !important;
  border-radius: 8px !important;
  width: 30%;
  color: #000000 !important;
}

.filled-btn-black {
  text-align: center;
  color: #ffffff !important;
  background-color: #000000 !important;
  border-radius: 8px !important;
}

.grid-item{
  justify-items: center;
  text-align: center;
}

.org-id-title{
  font-size: 14px !important;
  font-weight: 'regular';
  color: #101828;
}

.org-id{
  font-size: 14px !important;
  font-weight: 300 !important;
  color: #667085;
}



.grid-item .MuiGrid-root {
  flex-wrap: nowrap;
}

.i-circle {
  align-items: center;
  background: #7666CE;
  color: #fff;
  display: flex;
  font-size: 16px;
  border-radius: 100px;
  height: 40px;
  justify-content: center;
  line-height: 20px;
  width: 40px;
}

.you-button{
  display: inline;
  font-weight: regular;
  font-size: 10px !important;
  line-height: 13px;
  background: #d2f4d3;
  color: green;
  padding: 2px 8px 2px;
  border-radius: 20px;
  white-space: nowrap;
}

.member-comp {
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.member-comp:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.member-avatar {
  width: 40px;
  height: 40px;
  font-size: 1.2rem;
  background-color: #1976d2;
  color: white;
}

.member-name {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #101828;

}

.current-user-chip {
  font-size: 0.8rem;
  height: 18px;
  padding: 0 4px;
  font-weight: 700;
  background-color: #d2f4d3;
}

.member-email {
  margin-top: 4px;
}
