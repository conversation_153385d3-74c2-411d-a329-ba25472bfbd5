'use strict';

const { validate } = require('uuid');

/**
 * contact-booking controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::contact-booking.contact-booking',({ strapi }) => ({
	async create(ctx) {
		try{
			var query;
			if (!ctx.request.body.data.query && !ctx.request.body.data.session_id) {
				// Both query and session_id are empty
				ctx.badRequest( new Error("Both query and session_id can't be empty"));
			  }
		if(ctx.request.body.data.query){
			query = await strapi.query("api::answer.answer").findOne({
				where: { id: ctx.request.body.data.query },
				populate: { knowledgebase: true, },
			});
			const isWhatsapp = query.api_source && query.api_source.toLowerCase().startsWith("whatsapp");
			if(isWhatsapp){
			var number = query.api_source.match(/\d+/g);
			number = parseInt(number.join(''));
			ctx.request.body.data.phone_number=number;
			}else{
				if(!ctx.request.body.data.email){
					ctx.badRequest(Error("Email is required"));
				}

			}
		}
		if(ctx.request.body.data.session_id){
			query = await strapi.query("api::answer.answer").findOne({
				where: { session: ctx.request.body.data.session_id },
				populate: { knowledgebase: true, },
				orderBy: { id: 'desc' },
			}).catch((e)=>{
				console.log(e);
			});
			if(query){
			const isWhatsapp = query.api_source && query.api_source.toLowerCase().startsWith("whatsapp");
			if(isWhatsapp){
			var number = query.api_source.match(/\d+/g);
			number = parseInt(number.join(''));
			ctx.request.body.data.phone_number=number;
			}else{
				if(
					!ctx.request.body.data.email &&
					!ctx.request.body.data.phone_number
				){
					ctx.badRequest(Error("Email or phone_number is required"));
				}

				if(ctx.request.body.data.phone_number){
					var number = ctx.request.body.data.phone_number.match(/\d+/g);
					number = parseInt(number.join(''));
					ctx.request.body.data.phone_number = number;
				}

			}
		}

		}
		const response = await super.create(ctx);
		return response;
	}catch(e){
		throw e;
	}
	}
}));
