import React, { useState, useRef, useEffect } from "react";
import { getTicketStatePillColors } from "src/app/utils/colors";

const SelectDropdown = ({
  options = [],
  onSelect,
  placeholder = "Select an option",
  unselectedBgColor = "#ffffff",
  unselectedTextColor = "#4a4a4a",
  selectedBgColor = "#1d4ed8",
  selectedTextColor = "#ffffff",
  initialValue = null, // {{ edit_1 }} Accept initial value prop
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selected, setSelected] = useState(initialValue); // {{ edit_2 }} Set initial selected value
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSelect = (option) => {
    setSelected(option);
    onSelect(option);
    setIsOpen(false);
  };

  return (
    <div
      className="relative inline-block w-full max-w-xs py-4"
      ref={dropdownRef}
    >
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`w-full flex justify-between items-center px-4 py-2 text-left border border-gray-300 rounded-md shadow-sm transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
          isOpen ? "ring-2 ring-blue-500 border-blue-500" : ""
        }`}
        style={{
          backgroundColor: getTicketStatePillColors(selected).background,
          color: getTicketStatePillColors(selected).label,
        }}
      >
        <span
          className={`${
            selected
              ? "inline-flex items-center px-2.5 py-2 rounded-md text-sm font-medium"
              : ""
          }`}
        >
          {selected || placeholder}
        </span>
        <svg
          className={`w-16 h-16 ml-2 transition-transform duration-200 ${
            isOpen ? "transform rotate-180" : ""
          }`}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
          aria-hidden="true"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>
      {isOpen && (
        <ul className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg overflow-auto focus:outline-none">
          {options.map((option, index) => (
            <li
              key={index}
              className={`cursor-pointer px-4 py-8 hover:bg-gray-100 transition-colors flex items-center ${
                option === selected ? "bg-gray-100" : ""
              }`}
              onClick={() => handleSelect(option)}
            >
              <div className="flex items-center mr-4">
              <span
                className="w-6 h-6 ml-4 rounded-full mr-2"
                style={{
                  backgroundColor: getTicketStatePillColors(option).label,
                }}
              ></span>
              </div>
              {option}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default SelectDropdown;
