'use strict';

/**
 * Usage controller
 */

const axios = require('axios');
const _ = require('lodash');

module.exports = {

	async tokenusage(ctx, next) {
		const range = ctx.query.range??7;
		const endDate = new Date();
		const startDate = new Date();
		const dateRange = [];
		startDate.setDate(startDate.getDate() - range);
		let currentDate = new Date(startDate);
		while (currentDate <= endDate) {
			dateRange.push(currentDate.toISOString().substring(0, 10));
			currentDate.setDate(currentDate.getDate() + 1);
		  }


			let data = {};
			data.organization= ctx.state.user.organization;
			try{
				const queries = await strapi.query('api::answer.answer').findMany({ where: {
					createdAt: {
						$gte: startDate,
						$lte: endDate,
					  },
					org_id: ctx.state.user.organization.org_id
				}});
				// const tracks = await strapi.query('api::url-tracker.url-tracker').findMany({ 
				// 	populate: true,
				// 	where: {
				// 	$and: [
				// 	{ createdAt: { $gte: startDate } },
				// 	{ createdAt: { $lte: endDate } },
				// 	{'query_id.org_id': ctx.state.user.organization.org_id }
				// 	],
					
				// }});
				const tracks = await strapi.query('api::url-tracker.url-tracker').findMany({
					populate: ['query_id'], // Populate the associated query_id
					where: {
						createdAt: {
							$gte: startDate,
							$lte: endDate,
						  },
						query_id: { org_id: ctx.state.user.organization.org_id }
					//   $and: [
					// 	{ createdAt: {
					// 		$gte: startDate,
					// 		$lte: endDate,
					// 	  }, },
						
					// 	{ query_id: { org_id: ctx.state.user.organization.org_id } }
					//   ]
					}
				  });
				const dayWiseQueryCounts = {};
				const dayWiseLinksCounts = {};
				const dayWiseLinksClickTrackCounts = {};
				for (const record of queries) {
					const createdAtDate = new Date(record.createdAt).toISOString().substring(0, 10);
					dayWiseQueryCounts[createdAtDate] = (dayWiseQueryCounts[createdAtDate] || 0) + 1;
					dayWiseLinksCounts[createdAtDate] = (dayWiseLinksCounts[createdAtDate] || 0) + record.sources.length;
				}
				for (const record of tracks) {
					const createdAtDate = new Date(record.createdAt).toISOString().substring(0, 10);
					dayWiseLinksClickTrackCounts[createdAtDate] = (dayWiseLinksClickTrackCounts[createdAtDate] || 0) + 1;
				}
				for (const date of dateRange) {
					if (!(date in dayWiseQueryCounts)) {
					  dayWiseQueryCounts[date] = 0;
					}
					if (!(date in dayWiseLinksCounts)) {
					  dayWiseLinksCounts[date] = 0;
					}
					if (!(date in dayWiseLinksClickTrackCounts)) {
						dayWiseLinksClickTrackCounts[date] = 0;
					  }
				  }

				const groupedTracks = _.groupBy(tracks, 'url');
				const result = _.map(groupedTracks, (group, url) => ({
					url,
					count: group.length,
				  }));
		
				data.top_reffered_links = result;
				data.query_counts = dayWiseQueryCounts;
				data.links_produced_counts = dayWiseLinksCounts;
				data.links_track_counts = dayWiseLinksClickTrackCounts;
			}catch (e){
				console.log(e);
			}
				
		
	return data;
	}
  };

