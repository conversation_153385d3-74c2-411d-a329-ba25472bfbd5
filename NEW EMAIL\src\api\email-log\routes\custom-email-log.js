'use strict';

/**
 * Email Log Custom Router
 * Custom routes for email log analytics and operations
 */
module.exports = {
  routes: [
    // Get email analytics
    {
      method: 'GET',
      path: '/email-log/analytics',
      handler: 'email-log.getAnalytics',
      config: {
        policies: [
          'global::user-details-populate',
          'global::update-org-before-api-call',
        ]
      }
    },

    // Retry failed email
    {
      method: 'POST',
      path: '/email-log/:id/retry',
      handler: 'email-log.retryEmail',
      config: {
        policies: [
          'global::user-details-populate',
          'global::update-org-before-api-call',
        ]
      }
    }
  ]
}; 