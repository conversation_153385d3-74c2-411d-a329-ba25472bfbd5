// api/webhook/controllers/webhook.js
"use strict";
const admin = require('firebase-admin');


module.exports = {
  pushMessages: [
    {
      "title": "🎙️ Your Podcast is Ready!",
      "body": "Tap to listen to your personalized podcast now and enjoy the experience!"
    },
    {
      "title": "🎧 Tune In: Your Podcast is Here!",
      "body": "Your podcast is all set! Click to start listening and immerse yourself."
    },
    {
      "title": "✨ Your Audio Adventure Awaits!",
      "body": "Your podcast has been created! Tap here to explore your custom journey."
    },
    {
      "title": "🎙️ Your Podcast is Live!",
      "body": "Ready to listen? Click here and jump into your latest episode now!"
    },
    {
      "title": "🔊 It's Time to Listen!",
      "body": "Your podcast is ready. Tap to enjoy your unique audio experience!"
    }
  ],

  async webhook(ctx, next) {
    console.log(" webhook trigger");
    try {
      const { action_type, data } = ctx.request.body;

      switch (action_type) {
        case "add_files_result_model":
          try {
            if (typeof data.knowledgebase == "string") {
              const kb = await strapi
                .query("api::knowledgebase.knowledgebase")
                .findOne({
                  where: { kb_id: data.knowledgebase },
                });
              data.knowledgebase = kb.id;
            }
            data["py_id"] = data.id;
            delete data.id;
            await strapi
              .query("api::file-results-model.file-results-model")
              .create({
                data: data,
              });
          } catch (e) {
            console.log("Webhook create error", e);
            throw e;
          }

          break;

        case "update_files_result_model":
          try {
            console.log("Webhook update Payload", data);
            if (data.knowledgebase) {
              delete data.knowledgebase;
            }
            if (data.source) {
              delete data.source;
            }
            const id = data.id;
            delete data.id;
            console.log("Webhook update Payload", data);
            const updated_file = await strapi
              .query("api::file-results-model.file-results-model")
              .update({ where: { py_id: id }, data: data });

            if (data.status === "completed") {
              // --- Robust Credit Deduction Logic ---
              const creditsToDeduct = 1; // Cost for this operation
              let organization;
              let user;

              try {
                // 1. Fetch User to get Organization ID
                user = await strapi.db.query('plugin::users-permissions.user').findOne({
                    where: { id: data.user_id },
                    populate: { organization: { fields: ['id'] } } // Only need org ID here
                });

                if (!user || !user.organization || !user.organization.id) {
                    strapi.log.error(`Webhook Error (update_files_result_model): Could not find user ${data.user_id} or their associated organization.`);
                    throw new Error(`User ${data.user_id} or their organization not found.`);
                }
                const orgId = user.organization.id;

                // 2. Fetch Organization with all necessary details
                organization = await strapi.db.query('api::organization.organization').findOne({
                    where: { id: orgId },
                    populate: { credit_balance: true, monthly_usages: true, plan: true }
                });

                if (!organization) {
                    strapi.log.error(`Webhook Error (update_files_result_model): Could not find organization ${orgId} (from user ${data.user_id}).`);
                    throw new Error(`Organization ${orgId} not found.`);
                }

                // --- Find the CURRENT monthly usage record from the array --- 
                const now = new Date();
                const currentMonthlyUsageRecord = organization.monthly_usages?.find(usage => {
                    // Ensure period_start and period_end exist and are valid dates
                    const startDate = usage && usage.period_start ? new Date(usage.period_start) : null;
                    const endDate = usage && usage.period_end ? new Date(usage.period_end) : null;
                    return startDate instanceof Date && !isNaN(startDate) && 
                           endDate instanceof Date && !isNaN(endDate) && 
                           startDate <= now && now < endDate;
                }) || null; // Default to null if not found or array is empty/null
                // ---------------------------------------------------------------

                // Use the found record (or null) in subsequent logic
                const creditBalance = organization.credit_balance; // Might be null
                const plan = organization.plan; // Might be null

                // 3. Calculate available credits (handling nulls for currentMonthlyUsageRecord)
                const periodQuota = Number(currentMonthlyUsageRecord?.usage_quota || plan?.allowed_credits || 0);
                const periodUsed = Number(currentMonthlyUsageRecord?.query_count || 0);
                const periodCreditsRemaining = currentMonthlyUsageRecord ? Math.max(0, periodQuota - periodUsed) : 0;

                const oneTimeCreditsAvailable = creditBalance ? Number(creditBalance.available_credits || 0) : 0;

                const totalAvailableCredits = periodCreditsRemaining + oneTimeCreditsAvailable;

                // 4. Check if sufficient total credits
                if (totalAvailableCredits < creditsToDeduct) {
                    strapi.log.error(`Webhook Error (update_files_result_model): Insufficient total credits (${totalAvailableCredits}) for Org ${organization.id}. Cannot deduct ${creditsToDeduct} for completed task.`);
                    throw new Error(`Insufficient credits for Org ${organization.id} to deduct for completed task.`);
                }

                // 5. Perform Deduction - Prepare updates
                let updates = [];

                // Check if the current usage record exists and has enough credits
                if (currentMonthlyUsageRecord && periodCreditsRemaining >= creditsToDeduct) {
                    // Deduct entirely from subscription credits (monthly usage exists and has enough)
                    const newPeriodUsed = periodUsed + creditsToDeduct;
                    strapi.log.info(`Webhook (update_files_result_model): Deducting ${creditsToDeduct} from current subscription period. Usage ID: ${currentMonthlyUsageRecord.id}. Org: ${organization.id}. New used: ${newPeriodUsed}`);
                    updates.push(strapi.db.query('api::monthly-usage.monthly-usage').update({
                        where: { id: currentMonthlyUsageRecord.id },
                        data: { query_count: newPeriodUsed },
                    }));
                } else { 
                    // Need to use one-time credits (either partially or fully)
                    // Calculate credits needed from period (0 if no current record)
                    const creditsFromPeriod = currentMonthlyUsageRecord ? periodCreditsRemaining : 0;
                    const creditsFromOneTime = creditsToDeduct - creditsFromPeriod;

                    strapi.log.info(`Webhook (update_files_result_model): Deducting ${creditsFromPeriod} from subscription (Usage ID: ${currentMonthlyUsageRecord?.id}) and ${creditsFromOneTime} from one-time balance. Org: ${organization.id}`);

                    // Max out period usage if it exists and credits were taken from it
                    if (currentMonthlyUsageRecord && creditsFromPeriod > 0 && periodUsed < periodQuota) {
                         updates.push(strapi.db.query('api::monthly-usage.monthly-usage').update({
                            where: { id: currentMonthlyUsageRecord.id },
                            data: { query_count: periodQuota }, // Update to the quota limit
                         }));
                    }

                    // Deduct from one-time balance (check if it exists first)
                    if (oneTimeCreditsAvailable >= creditsFromOneTime) {
                        if (creditBalance) {
                            const newOneTimeBalance = oneTimeCreditsAvailable - creditsFromOneTime;
                            updates.push(strapi.db.query('api::credit-balance.credit-balance').update({
                                where: { id: creditBalance.id },
                                data: { available_credits: newOneTimeBalance },
                            }));
                        } else {
                             // This case means we needed one-time credits, but the balance record doesn't exist.
                             strapi.log.error(`Webhook Error (update_files_result_model): Org ${organization.id} needs ${creditsFromOneTime} from one-time credits but has no credit balance record.`);
                             throw new Error('Credit balance record not found when required for deduction.');
                        }
                    } else {
                        // Should not happen if initial total check passed
                        strapi.log.error(`Webhook Error (update_files_result_model): Org ${organization.id} insufficient one-time credits (${oneTimeCreditsAvailable}) for remaining cost (${creditsFromOneTime}). Logic error or race condition.`);
                        throw new Error('Insufficient one-time credits during deduction logic.');
                    }
                }

                // Execute all updates
                await Promise.all(updates);
                strapi.log.info(`Webhook (update_files_result_model): Credit deduction successful for org ${organization.id}.`);

              } catch (deductionError) {
                  strapi.log.error(`Webhook Error (update_files_result_model): Failed to process credits for user ${data.user_id} / org ${organization?.id}. Error: ${deductionError.message}`, deductionError.stack);
                  // Re-throw the error to be caught by the outer try-catch, preventing push notification if deduction fails
                  throw deductionError;
              }
              // --- End Credit Deduction Logic ---

              // Fetch user again (or use the one from above if full details needed) for push notification
              // If user object from above is sufficient, reuse it. Otherwise fetch again.
              // const userForPush = user; // Reuse if sufficient
              const userForPush = await strapi.query("plugin::users-permissions.user")
                .findOne({
                  where: { id: data.user_id }, // Fetch fresh user details if needed (like fcm_token)
                });

              if (!userForPush) {
                  strapi.log.warn(`Webhook Warning (update_files_result_model): Could not re-fetch user ${data.user_id} for push notification after credit deduction.`);
                  // Continue without push notification if user fetch fails here
              } else {
                  // --- Push Notification Logic (existing code) ---
                  console.log("user found for push", userForPush);
              const pushMessageIndex = Math.floor(Math.random() * this.pushMessages.length);
                  if (userForPush.fcm_token) {
                try {
                  const pushMessage = this.pushMessages[pushMessageIndex];
                  await admin.messaging().send({
                    notification: {
                      title: pushMessage.title,
                      body: pushMessage.body,
                    },
                    data: {
                      route: "/track_detail",
                      track_id: updated_file.id.toString(),
                      click_action: "FLUTTER_NOTIFICATION_CLICK",
                    },
                              token: userForPush.fcm_token,
                  });
                  console.log('Successfully sent push notification to user');
                } catch (error) {
                  console.error('Error sending push notification:', error);
                }
              }
                  // --- End Push Notification Logic ---
              }
            }
          } catch (e) {
            console.log("Webhook update error", e);
            throw e;
          }

          break;

        case "create_chunk":
          try {
            const id = data.id;
            delete data.id;
            await strapi.query("api::chunk.chunk").create({
              data: {
                content: data.content,
                embedding_id: data.embedding_id,
                kb_id: data.kb_id,
                document: data.document_id,
                status: data.status,
                chunk_index: data.chunk_index,
              },
            });
          } catch (e) {
            console.log("Webhook create error", e);
            throw e;
          }
          break;

        case "update_document":
          try {
            console.log("Webhook update Payload", data);
            if (data.knowledgebase) {
              delete data.knowledgebase;
            }
            if (data.source) {
              delete data.source;
            }
            const id = data.id;
            delete data.id;
            await strapi.query("api::document.document").update({
              where: { id: data.document_id },
              data: {
                status: data.status,
                tokens_used: data.tokens_used,
                character_count: data.character_count,
                vector_store_name: data.vector_store_name,
                index_name: data.index_name,
                namespace_prefix: data.namespace_prefix,
                embedding_model: data.embedding_model,
                document_store_url: data?.document_store_url ?? null,
              },
            });
            //TODO check this
            const currentUsage = await strapi
              .query("api::monthly-usage.monthly-usage")
              .findOne({
                where: { id: data.usage_id },
              });

            if (currentUsage === null) {
              throw new Error("No usage found");
            }
            await strapi.query("api::monthly-usage.monthly-usage").update({
              where: { id: data.usage_id },
              data: {
                training_tokens_count:
                  currentUsage.training_tokens_count + data.tokens_used,
              },
            });
          } catch (e) {
            console.log("Webhook update error", e);
            throw e;
          }
          break;

        // Add more cases for other events you want to handle
        default:
          console.log(`Unhandled event type: ${type}`);
      }
      return "webhook success";
    } catch (e) {
      console.error("Error handling webhook event:", e);
      return "webhook error";
    }
  },
};
