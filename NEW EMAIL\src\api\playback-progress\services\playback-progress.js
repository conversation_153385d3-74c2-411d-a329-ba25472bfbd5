"use strict";

module.exports = {
  async updatePosition(userId, trackId, position, duration, deviceId) {
    const status = position >= duration - 1 ? "completed" : "in-progress";

    const existing = await strapi.entityService.findMany(
      "api::playback-progress.playback-progress",
      {
        filters: { user: userId, track: trackId },
        limit: 1,
      }
    );

    if (existing.length > 0) {
      const entry = existing[0];
      return await strapi.entityService.update(
        "api::playback-progress.playback-progress",
        entry.id,
        {
          data: {
            positionSec: position,
            durationSec: duration,
            deviceId,
            status,
          },
        }
      );
    } else {
      return await strapi.entityService.create(
        "api::playback-progress.playback-progress",
        {
          data: {
            user: userId,
            track: trackId,
            positionSec: position,
            durationSec: duration,
            deviceId,
            status,
          },
        }
      );
    }
  },

  async listByUser(userId, query) {
    const pageSize = parseInt(query.pageSize, 10) || 20;
    const page = parseInt(query.page, 10) || 1;
    const start =
      query.start !== undefined
        ? parseInt(query.start, 10)
        : (page - 1) * pageSize;
    const status = query.status || "in-progress";

    const [results, total] = await Promise.all([
      strapi.entityService.findMany(
        "api::playback-progress.playback-progress",
        {
          filters: { user: userId, status },
          sort: { updatedAt: "DESC" },
          limit: pageSize,
          start,
          populate: ["track"],
        }
      ),
      strapi.entityService.count("api::playback-progress.playback-progress", {
        filters: { user: userId, status },
      }),
    ]);

    return {
      results,
      pagination: {
        page,
        pageSize,
        total,
        pageCount: Math.ceil(total / pageSize),
      },
    };
  },

  async clear(userId, trackId) {
    const entries = await strapi.entityService.findMany(
      "api::playback-progress.playback-progress",
      {
        filters: { user: userId, track: trackId },
        limit: 1,
      }
    );

    if (!entries || entries.length === 0) {
      throw new Error("No playback progress found for this user and track");
    }

    const entry = entries[0];

    await strapi.entityService.update(
      "api::playback-progress.playback-progress",
      entry.id,
      {
        data: {
          status: "completed",
          positionSec: entry.durationSec,
        },
      }
    );
  },
};
