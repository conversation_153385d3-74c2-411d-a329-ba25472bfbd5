import React, { useState, useRef } from "react";
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import LoadingText from "./LoadingText";
import FileViewerList from "./FileViewerList";

function FileUploadArea({setFiles}) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [dragOver, setDragOver] = useState(false);
  const [files, setUploadedFiles] = useState([]);
  // const dropzoneRef = useRef<HTMLLabelElement>(null);

  const handleFileChange = async (droppedFiles) => {
    setLoading(true);
    setError("");

    try {
      if (droppedFiles.length > 1) {
        setError("Please select only one file to upload.");
      }
  
      const newFiles = Array.from(droppedFiles);
      setUploadedFiles(newFiles);
      setFiles(newFiles);
    } catch (err) {
      setError(err.message);
    }
  
    setLoading(false);
  };

  const handleDragEnter = (event) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragOver = (event) => {
    event.preventDefault();
  };

  const handleDragLeave = (event) => {
    event.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (event) => {
    event.preventDefault();
    setDragOver(false);
    const droppedFiles = event.dataTransfer.files;
    handleFileChange(droppedFiles);
  };

  const handleRemoveFile = (index) => {
    const updatedFiles = [...files];
    updatedFiles.splice(index, 1);
    setUploadedFiles(updatedFiles);
    setFiles(updatedFiles);
  };

  return (
    <div style={{ marginTop: "30px" }} className="flex items-center justify-center w-full flex-col">
      <label
        htmlFor="dropzone-file"
        className={`flex p-8 flex-col shadow items-center justify-center w-full border rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 relative ${
          dragOver ? "border-blue-500 bg-blue-50" : "border-green"
        }`}
        // ref={dropzoneRef}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center justify-center pt-5 pb-6">
          {loading ? (
            <LoadingText />
          ) : (
            <div className="flex flex-col items-center text-center">
              <FuseSvgIcon className="text-48" size={24} color="action">
                heroicons-outline:upload
              </FuseSvgIcon>
              <p className="mb-2 text-sm">
                <span className="font-semibold">Click to upload</span> or drag and drop
              </p>
              <p className="text-xs">Only PDF(max 5MB per file)</p>
              <input
                id="dropzone-file"
                key={files.length}
                type="file"
                className="hidden"
                accept="application/pdf"
                onChange={(event) => handleFileChange(event.target.files)}
              />
            </div>
          )}
        </div>
      </label>

      {error && (
        <div className="flex items-center justify-center w-full mt-4">
          <p className="text-sm text-red-500">{error}</p>
        </div>
      )}

      {files.length > 0 && <FileViewerList files={files} title="Uploaded Files" onRemove={handleRemoveFile} />}
    </div>
  );
}

export default FileUploadArea;
