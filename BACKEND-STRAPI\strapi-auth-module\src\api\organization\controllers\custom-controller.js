"use strict";

/**
 * organization controller
 */

const { createCoreController } = require("@strapi/strapi").factories;
const axios = require("axios");

module.exports = createCoreController(
  "api::organization.organization",
  ({ strapi }) => ({
    async org(ctx) {
      try {
        // Check if user is authenticated
        if (!ctx.state.user) {
          console.log('🔓 Unauthenticated request to /api/org - returning empty response');
          return ctx.send({
            message: 'Authentication required',
            authenticated: false
          });
        }

        // Check if user has organization
        if (!ctx.state.user.organization) {
          console.log('⚠️ User has no organization');
          return ctx.send({
            message: 'No organization found',
            authenticated: true,
            hasOrganization: false
          });
        }

        return ctx.state.user.organization;
      } catch (err) {
        console.error('❌ Organization endpoint error:', err);
        return ctx.throw(500, "Server error");
      }
    },
    async getOrganizationInfo(ctx) {
      try {
        const org_id = ctx.query.id;
        const organization = await strapi
          .query("api::organization.organization")
          .findOne({
            where: {
              id: org_id,
            },
            populate: {
              subscription_orders: {
                populate: {
                  plan: true,
                },
              },
              monthly_usages: true,
            },
          });

        if (!organization) {
          return ctx.notFound("Organization not found");
        }

        const currentDate = new Date();
        const createdAt = new Date(organization.createdAt);
        const lifetimeInMonths = Math.floor(
          (currentDate - createdAt) / (1000 * 60 * 60 * 24 * 30)
        );

        var currentSubscription = null;
        var totalAmountPaid = 0;
        var trainingTokensUsed = 0;
        var queryTokensUsed = 0;
        var creditsUsed = 0;

        if (organization.subscription_orders.length > 0) {
          const sortedOrders = organization.subscription_orders.sort(
            (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
          );

          currentSubscription = sortedOrders[0];

          // Calculate total amount paid
          totalAmountPaid = sortedOrders.reduce(
            (sum, order) => sum + (order.plan.paid || 0),
            0
          );
        }
        if (organization.monthly_usages.length > 0) {
          organization.monthly_usages.forEach((usage , index) => {
            trainingTokensUsed += usage.training_tokens_count;
            queryTokensUsed += usage.query_tokens_count;
            creditsUsed += parseInt(usage.credits_used);
          });
        }


        const organizationInfo = {
          total_amount_paid: totalAmountPaid,
          life_time_in_months: lifetimeInMonths,
          plan_amount: currentSubscription?.plan?.price || 0,
          plan_period: currentSubscription?.plan?.billing_cycle || "N/A",
          query_tokens_used: queryTokensUsed,
          training_tokens_used: trainingTokensUsed,
          creditsUsed: creditsUsed,
        };

        return ctx.send(organizationInfo);
      } catch (error) {
        console.error("Error in getOrganizationInfo:", error);
        return ctx.badRequest("Error retrieving organization information");
      }
    },
  })
);
