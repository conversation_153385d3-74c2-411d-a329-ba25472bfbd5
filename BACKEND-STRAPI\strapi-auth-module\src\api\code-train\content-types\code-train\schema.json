{"kind": "collectionType", "collectionName": "code_trains", "info": {"singularName": "code-train", "pluralName": "code-trains", "displayName": "Code Train", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"file": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["files"]}, "train_status": {"type": "boolean", "default": false}, "file_name": {"type": "string"}, "knowledgebase": {"type": "relation", "relation": "oneToOne", "target": "api::knowledgebase.knowledgebase", "inversedBy": "file_trains"}}}