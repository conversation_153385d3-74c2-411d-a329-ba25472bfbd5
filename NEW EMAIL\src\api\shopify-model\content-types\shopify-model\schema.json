{"kind": "collectionType", "collectionName": "shopify_models", "info": {"singularName": "shopify-model", "pluralName": "shopify-models", "displayName": "shopify_model", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"url": {"type": "string"}, "store_front_api_key": {"type": "text"}, "knowledgebase": {"type": "relation", "relation": "oneToOne", "target": "api::knowledgebase.knowledgebase", "inversedBy": "shopify_model"}}}