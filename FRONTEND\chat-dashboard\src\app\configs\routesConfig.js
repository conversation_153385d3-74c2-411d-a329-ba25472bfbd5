import FuseUtils from "@fuse/utils";
import FuseLoading from "@fuse/core/FuseLoading";
import { Navigate } from "react-router-dom";
import settingsConfig from "app/configs/settingsConfig";
import SignInConfig from "../main/sign-in/SignInConfig";
import SignUpConfig from "../main/sign-up/SignUpConfig";
import SignOutConfig from "../main/sign-out/SignOutConfig";
import EmailConfirmationConfig from "../main/email-confirmation/EmailConfirmationConfig";
import dashboardsConfigs from "../main/dashboards/dashboardsConfigs";
import ForgotPasswordConfig from "../main/forgot-password/ForgotPasswordConfig";
import ResetPasswordConfig from "../main/reset-password/ResetPasswordConfig";
import pagesConfigs from "../main/pages/pagesConfigs";
import SubscriptionConfig from "../main/subscription/SubscriptionConfig";
import ChatbotDetailPage from "../main/dashboards/knowledge_base/chatbot-details/";
import ChunksListPage from "../main/dashboards/knowledge_base/ChunksListPage";
import ChatBotIntegration from "../main/dashboards/knowledge_base/ChatBotIntegration";
import ChatBotStylePage from "../main/dashboards/knowledge_base/chatbotStyle/chatbotStylePage";
import GoogleAuthCallback from "../main/google-auth/Google-auth-page";
import { element } from "prop-types";
import TurboWriterPage from "../main/dashboards/turboWriter/turboWriterPage";
const routeConfigs = [
  ...dashboardsConfigs,
  ...pagesConfigs,
  SubscriptionConfig,
  SignOutConfig,
  SignInConfig,
  SignUpConfig,
  EmailConfirmationConfig,
  ForgotPasswordConfig,
  ResetPasswordConfig,
];

const routes = [
  ...FuseUtils.generateRoutesFromConfigs(
    routeConfigs,
    settingsConfig.defaultAuth
  ),
  {
    path: "/",
    element: <Navigate to="dashboards/home" />,
    auth: settingsConfig.defaultAuth,
  },
  {
    path: "/dashboards/home",
    element: <Navigate to="dashboards/home" />,
    auth: settingsConfig.defaultAuth,
  },
  {
    path: "/dashboards/query",
    element: <Navigate to="dashboards/query" />,
    auth: settingsConfig.defaultAuth,
  },
  {
    path: '/dashboards/playground',
    element: <Navigate to="dashboards/playground" />,
    auth: settingsConfig.defaultAuth,
  },
  {
    path: '/dashboards/knowledge_base',
    element: <Navigate to="dashboards/knowledge_base" />,
    auth: settingsConfig.defaultAuth,
  },
  {
    path: "/dashboards/kanban",
    element: <Navigate to="dashboards/kanban" />,
    auth: settingsConfig.defaultAuth,
  },
  {
    path: "/dashboards/upload",
    element: <Navigate to="dashboards/upload" />,
    auth: settingsConfig.defaultAuth,
  },
  {
    path: "/dashboards/knowledge_base/items_detail_page",
    element: <ChatbotDetailPage />,
  },
  {
    path: "/dashboards/knowledge_base/items_detail_page/:itemId",
    element: <ChatbotDetailPage />,
  },
  {
    path: "/dashboards/knowledge_base/items_detail_page/document/:documentId",
    element: <ChunksListPage />,
  },
  {
    path: "/dashboards/knowledge_base/turbo-writer/:itemId",
    element: <TurboWriterPage />,
  },
  {
    path: "/dashboards/knowledge_base/chat_bot",
    element: <ChatBotIntegration />,
  },
  {
    path: "/dashboards/knowledge_base/chat_bot_style",
    element: <ChatBotStylePage />,
  },
  {
    path: "/dashboards/organisation",
    element: <Navigate to="dashboards/organisation" />,
    auth: settingsConfig.defaultAuth,
  },
  {
    path: "/dashboards/turbo-writer",
    element: <TurboWriterPage />,
  },
  {
    path: "/auth/callback/google",
    element: <GoogleAuthCallback />,
  },
  {
    path: "loading",
    element: <FuseLoading />,
  },
  {
    path: "*",
    element: <Navigate to="pages/error/404" />,
  },
];

export default routes;
