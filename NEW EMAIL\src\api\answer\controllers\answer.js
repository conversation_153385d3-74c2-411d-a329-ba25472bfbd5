"use strict";

/**
 * answer controller
 */

const { createCoreController } = require("@strapi/strapi").factories;
const axios = require("axios");
const CryptoJS = require("crypto-js");

module.exports = createCoreController("api::answer.answer", ({ strapi }) => ({
  async create(ctx) {
    var status = true;
    let answerResult;
    const agent = await strapi
      .query("api::ai-task.ai-task")
      .findOne({ where: { id: ctx.request.body.data.agent_id ?? 1 } });
    const kb = await strapi.query("api::knowledgebase.knowledgebase").findOne({
      where: { kb_id: ctx.request.body.data.kb_id },
      populate: { default_ai_task: true, shopify_model: true, ai_agent:true },
    });
    let promptQuestion;
    if (kb.prompt_prefix) {
      promptQuestion = `
      Question :${ctx.request.body.data.question}
      Use the following Persona to answer this question
      Persona :${kb.prompt_prefix}
        `;
    } else {
      promptQuestion = ctx.request.body.data.question;
    }
    if (agent?.task_id == 10) {
      const body = {
        question: promptQuestion,
        org_id: ctx.state.user.organization?.org_id,
        agent_id: agent.task_id,
        chat_history: "",
      };
      answerResult = await axios
        .post(`${process.env.TALKBASE_BASE_URL}/v4/pipe`, body, {
          timeout: 300000, // Set a timeout of 5mins/300seconds
          headers: {
            "Content-Type": "application/json",
          },
        })
        .catch((err) => {
          console.log(err);
          ctx.badRequest(err);
        });
    } else {
      if (kb.type === "shopify") {
        const body = {
          question: promptQuestion,
          org_id: ctx.state.user.organization?.org_id,
          base_url: kb.shopify_model.url,
          agent_id: agent?.task_id ?? kb?.default_ai_task.task_id,
          chat_history: "",
          session_id: ctx.request.body.data.session ?? "",
          headers: {
            "X-Shopify-Storefront-Access-Token": CryptoJS.AES.decrypt(
              kb.shopify_model.store_front_api_key,
              process.env.ENCRYPTION_KEY
            ).toString(CryptoJS.enc.Utf8),
          },
        };
        answerResult = await axios
          .post(`${process.env.TALKBASE_BASE_URL}/v4/api_task`, body, {
            timeout: 300000, // Set a timeout of 5mins/300seconds
            headers: {
              "Content-Type": "application/json",
            },
          })
          .catch((err) => {
            console.log(err);
            ctx.badRequest(err);
          });
      } else {
        if( kb.ai_agent && ( kb.default_ai_task.task_id>=20 && kb.default_ai_task.task_id<30 ) ){
          answerResult = await axios
          .post(
            `${process.env.TALKBASE_BASE_URL}/v4/agent`,
            {
              question: promptQuestion,
              kb_id: ctx.request.body.data.kb_id,
              org_id: ctx.state.user.organization?.org_id,
              agent_id: kb.default_ai_task.task_id ?? 16,
              session_id: ctx.request.body.data.session ?? "",
              search_type: kb.search_type ?? "similarity",
              k_similarity: kb.k_similarity ?? 3,
              fetch_k: kb.fetch_k ?? 20,
              lamba_mul_mmr: kb.lamba_mul_mmr ?? 60,
              similarity_score_threshold: kb.similarity_score_threshold ?? 0.0,
              ai_model_name: kb.ai_model_name ?? "gpt-3.5-turbo",
              agent_name : kb.ai_agent.agent_name,
              agent_role :  kb.ai_agent.agent_role,
              company_name :  kb.ai_agent.company_name,
              company_business :  kb.ai_agent.company_business,
              company_values:  kb.ai_agent.company_values,
              conversation_purpose: kb.ai_agent.conversation_purpose,
              booking_link: kb.ai_agent.booking_base_url
            },
            {
              timeout: 300000, // Set a timeout of 5mins/300seconds
              headers: {
                "Content-Type": "application/json",
              },
            }
          )
          .catch((err) => {
            console.log(err);
            status = false;
            ctx.badRequest(err.toString());
            // throw err;
          });
        }else{
        answerResult = await axios
          .post(
            `${process.env.TALKBASE_BASE_URL}/v4/answer`,
            {
              question: ctx.request.body.data.question,
              prefix_prompt: kb.prompt_prefix,
              kb_id: ctx.request.body.data.kb_id,
              org_id: ctx.state.user.organization?.org_id,
              agent_id: kb.default_ai_task.task_id ?? 16,
              session: ctx.request.body.data.session ?? "",
              search_type: kb.search_type ?? "similarity",
              k_similarity: kb.k_similarity ?? 3,
              fetch_k: kb.fetch_k ?? 20,
              lamba_mul_mmr: kb.lamba_mul_mmr ?? 60,
              similarity_score_threshold: kb.similarity_score_threshold ?? 0.0,
              ai_model_name: kb.ai_model_name ?? "gpt-3.5-turbo",
            },
            {
              timeout: 300000, // Set a timeout of 5mins/300seconds
              headers: {
                "Content-Type": "application/json",
              },
            }
          )
          .catch((err) => {
            console.log(err);
            status = false;
            ctx.badRequest(err);
            // throw err;
          });
        }
      }
    }
    const data = answerResult.data;
    await strapi
      .query("api::monthly-usage.monthly-usage")
      .update({
        where: { id: ctx.state.user.organization.current_month_usage.id },
        data: {
          query_count:
            ctx.state.user.organization.current_month_usage.query_count + 1,
        },
      })
      .catch((e) => {
        console.log(e);
      });

    if (data?.result === "False") {
      status = false;
    }
    ctx.request.body.data["kb_id"] = ctx.request.body.data.kb_id;
    ctx.request.body.data["knowledgebase"] = kb.id;
    ctx.request.body.data["org_id"] = ctx.state.user.organization?.org_id;
    ctx.request.body.data["answer"] = data.answer;
    ctx.request.body.data["sources"] = data.sources ?? [];
    ctx.request.body.data["did_find_answer"] =
      data?.did_find_answer === "false" ? false : true ?? null;
    ctx.request.body.data["answer_status"] = status;
    ctx.request.body.data["session"] = ctx.request.body.data.session ?? "";
    ctx.request.body.data["api_source"] = "dashboard";
    // some logic here
    const response = await super.create(ctx);
    try {
      if (data?.did_find_answer === "false") {
        const message = `We couldn’t find the answer to this question, our team is committed to helping you out! You can request for further assistance here…\n${process.env.MEETING_SCHEDULE_URL}?queryId=${response?.data?.id}&sessionId=${response?.data?.attributes.session}`;
        response.data.attributes.answer = message;
      }
    } catch (e) {}
    // some more logic

    return response;
  },
}));
