import { useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';

/**
 * Custom hook for session management
 * @returns {Object} Session management utilities
 */
const useSession = () => {
  const [sessionId, setSessionId] = useState('');
  const [conversationHistory, setConversationHistory] = useState([]);
  
  // Initialize session ID on mount
  useEffect(() => {
    setSessionId(uuidv4());
  }, []);
  
  /**
   * Updates conversation history with new messages
   * @param {Object} newUserMessage - User message object
   * @param {Object} newAIMessage - AI message object
   * @param {boolean} isFirstMessage - Whether this is the first message
   */
  const updateConversationHistory = (newUserMessage, newAIMessage, isFirstMessage = false) => {
    if (isFirstMessage) {
      setConversationHistory([newAIMessage]);
    } else {
      setConversationHistory(prev => [...prev, newUserMessage, newAIMessage]);
    }
  };
  
  /**
   * Resets the session by generating a new ID and clearing history
   */
  const resetSession = () => {
    setSessionId(uuidv4());
    setConversationHistory([]);
  };
  
  return {
    sessionId,
    conversationHistory,
    updateConversationHistory,
    resetSession
  };
};

export default useSession; 