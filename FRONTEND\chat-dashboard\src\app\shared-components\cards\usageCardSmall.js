// src/components/StatsCard.js
import { useState, useEffect } from "react";
import { Button } from "@mui/material";
import { useSelector } from "react-redux";
import { selectUsage, getUsage } from "app/store/usageSlice";
import { selectUser } from "app/store/userSlice";
import { useDispatch } from "react-redux";
import { humanReadableNumber } from "../text-truncate/human-readable-text";
import Progress from "app/shared-components/indicators/progress_bar";
import history from "@history";

const UsageCardSmall = () => {
  const usage = useSelector(selectUsage);
  const user = useSelector(selectUser);
  const dispatch = useDispatch();
  const [items, setItems] = useState([]);

  useEffect(() => {
    dispatch(getUsage());
  }, []);

  useEffect(() => {
    make();
  }, [usage]);

  const make = () => {
    if (
      usage == null ||
      user.data.organization == null ||
      Object.keys(user.data.organization).length == 0
    )
      return;
    const statItems = [];
    statItems.push({
      image: "assets/images/queries-count.png",
      alt: "Credits Usage",
      text: `${humanReadableNumber(usage.credits_used)} / ${humanReadableNumber(
        user.data.organization?.plan?.allowed_credits || 0
      )} `,
      percent:
        usage.credits_used / user.data.organization?.plan?.allowed_credits || 0,
      onClick: () => {
        history.push("/subscription");
      },
      showProgress: true,
    });
    statItems.push({
      image: "assets/images/words-count.png",
      alt: "Training Usage",
      text: `${humanReadableNumber(
        usage.training_tokens_count
      )} / ${humanReadableNumber(
        user.data.organization?.plan?.allowed_training_tokens || 0
      )}`,
      percent:
        usage.training_tokens_count /
          user.data.organization?.plan?.allowed_training_tokens || 0,
      onClick: () => {
        history.push("/subscription");
      },
      showProgress: true,
    });
    statItems.push({
      image: "assets/images/agent-count.png",
      alt: "Plan Name",
      text: `${usage.plan_name}`,
      onClick: () => {
        history.push("/subscription");
      },
      showProgress: false,
    });
    setItems(statItems);
  };

  return (
    <div className="bg-white p-8 rounded-lg border-1 border-[#D9D9D9]">
      {items.map((item, index) => (
        <div key={index} className="flex flex-col">
          <div
            key={index}
            className="flex items-center cursor-pointer py-8"
            onClick={item.onClick}
          >
            <div className="flex-shrink-0">
              <img src={item.image} alt={item.alt} className="w-20 h-20 ml-8" />
            </div>
            <div className="flex flex-col w-full">
              <div className="ml-16 text-[14px] font-bold text-black my-4">
                {item.alt}
              </div>
              {item.showProgress && (
                <div className="ml-16 my-8 mr-32">
                  <Progress progress={item.percent} className="" />
                </div>
              )}
              <div className="ml-16 text-md font-regular text-black my-4">
                {item.text}
              </div>
            </div>
          </div>
          {index != items.length - 1 && (
            <div className="bg-grey-200 h-[1px] mx-16"></div>
          )}
        </div>
      ))}
      <Button
        onClick={() => {
          history.push("/subscription", "update");
        }}
        variant="contained"
        color="secondary"
        className="press-button shine-button w-full mt-16 rounded-md bg-primary hover:bg-primary text-white"
        aria-label="Sign in"
        // disabled={_.isEmpty(dirtyFields) || !isValid}
        type="submit"
        size="small"
        startIcon={<img src={"assets/images/upgrade-icon.png"} width={24} />}
      >
        Upgrage
      </Button>
    </div>
  );
};

export default UsageCardSmall;
