{"kind": "collectionType", "collectionName": "sessions", "info": {"singularName": "session", "pluralName": "sessions", "displayName": "Session", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"session_id": {"type": "string", "required": true}, "knowledgebase": {"type": "relation", "relation": "oneToOne", "target": "api::knowledgebase.knowledgebase", "required": true}, "machineInfo": {"type": "relation", "relation": "oneToOne", "target": "api::machine-info.machine-info"}, "customerInfo": {"type": "relation", "relation": "oneToOne", "target": "api::customer-info.customer-info"}, "browserInfo": {"type": "relation", "relation": "oneToOne", "target": "api::browser-info.browser-info"}, "answers": {"type": "relation", "relation": "oneToMany", "target": "api::answer.answer", "mappedBy": "sessionModel"}, "scrumboardCard": {"type": "relation", "relation": "oneToOne", "target": "api::scrumboard-card.scrumboard-card"}}}