
import React from 'react';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';

const SettingsCopyTextField = ({
  title = "Organization ID",
  description = "Identifier for this organization sometimes used in API requests",
  organizationId,
  onCopy,
  className = "",
}) => {
  return (
    <div className={`mb-16 ${className}`}>
      <Typography variant="h6" className="text-lg font-semibold text-black tracking-tight leading-8 mr-4 mb-2">
        {title}
      </Typography>
      <Typography  className="text-black text-sm mb-4">
        {description}
      </Typography>
      <div className="bg-gray-100 p-3 rounded-md flex items-center justify-between">
        <Typography variant="body1" className="text-gray-800 font-mono">
          {organizationId}
        </Typography>
        <IconButton
          onClick={() => onCopy(organizationId)}
          size="small"
          className="text-gray-500 hover:text-gray-700"
        >
          <ContentCopyIcon fontSize="small" />
        </IconButton>
      </div>
    </div>
  );
};

export default SettingsCopyTextField;



