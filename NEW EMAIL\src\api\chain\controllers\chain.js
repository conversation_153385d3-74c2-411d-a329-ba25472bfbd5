'use strict';

/**
 * chain controller
 */


const axios = require('axios');
const _ = require('lodash');


module.exports ={
	async chain(ctx, next) { 
		try{
		const kb = await strapi.query('api::knowledgebase.knowledgebase').findOne({
			where:{id: ctx.request.body.kb_id},populate:{shopify_model:true}
		});
		if(_.isEmpty(kb.shopify_model) ){
			ctx.badRequest("Shopify data is not available in this chatbot");
		}
		const body= {
			question: ctx.request.body.question,
			org_id: ctx.state.user.organization.org_id,
			base_url: kb.shopify_model.url,
			headers:{ "X-Shopify-Storefront-Access-Token" : kb.shopify_model.store_front_api_key}
		}
		let { data } = await axios.post(`${process.env.TALKBASE_BASE_URL}/v4/api_task`,body
		).catch(err => {
			console.log(err);
			throw err;
		});

		return data;
	}catch(e){
		throw err;
	}
	},

};

