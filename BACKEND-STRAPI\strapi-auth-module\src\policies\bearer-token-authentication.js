'use strict';

/**
 * Bearer token authentication policy
 */
module.exports = async (policyContext, config, { strapi }) => {
  const bearerToken = process.env.BEARER_TOKEN;

  if (!bearerToken) {
    strapi.log.error('BEARER_TOKEN is not set in the environment variables');
    return false;
  }

  const authHeader = policyContext.request.header.auth;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }

  const token = authHeader.substring(7);

  if (token !== bearerToken) {
    return false;
  }

  // If the token matches, allow the request to proceed
  return true;
};
