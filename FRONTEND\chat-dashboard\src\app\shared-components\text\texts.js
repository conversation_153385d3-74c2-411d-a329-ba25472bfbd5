import React from 'react';

export const H1 = ({ children, className = '' }) => (
  <h1 className={`text-4xl font-bold mb-4 ${className}`}>{children}</h1>
);

export const H2 = ({ children, className = '' }) => (
  <h2 className={`text-3xl font-semibold mb-3 ${className}`}>{children}</h2>
);

export const H3 = ({ children, className = '' }) => (
  <h3 className={`text-2xl font-semibold mb-2 ${className}`}>{children}</h3>
);

export const H4 = ({ children, className = '' }) => (
  <h4 className={`text-xl font-medium mb-2 ${className}`}>{children}</h4>
);

export const H5 = ({ children, className = '' }) => (
  <h5 className={`text-lg font-medium mb-1 ${className}`}>{children}</h5>
);

export const H6 = ({ children, className = '' }) => (
  <h6 className={`text-base font-medium mb-1 ${className}`}>{children}</h6>
);

export const Body = ({ children, className = '' }) => (
  <p className={`text-base mb-2 ${className}`}>{children}</p>
);