// api/whatsapp/controllers/whatsapp.js
"use strict";
const _ = require("lodash");

const axios = require("axios");
const CryptoJS = require("crypto-js");

module.exports = {
  async webhook(ctx, next) {
    console.log("Whatsapp webhook trigger");

    try {
      if (ctx.request.body.entry[0].changes[0].field === "messages") {
        try {
          strapi
            .plugin("measurement-protocol")
            .service("gtag")
            .send({
              name: "AskQuestion",
              params: {
                query: ctx.request.body.question,
                timestamp: Date().toString(),
                user: ctx.request.body.entry[0].changes[0].value.messages[0]
                  .from,
              }, // Event Value (you can change this as needed)
            });
        } catch (e) {
          // console.log(e);
        }
        console.log("********contacts **********");
        console.log(ctx.request.body.entry[0].changes[0].value.contacts);
        console.log("******** meta **********");
        console.log(ctx.request.body.entry[0].changes[0].value.metadata);
        const whatsappIntegrationModel = await strapi
          .query("api::whatsapp-integration-model.whatsapp-integration-model")
          .findOne({
            where: {
              phone_number_id:
                ctx.request.body.entry[0].changes[0].value.metadata
                  .phone_number_id,
            },
            populate: {
              knowledgebase: {
                populate: {
                  organization: {
                    populate: {
                      monthly_usages: true,
                      plan: true,
                    },
                  },
                },
              },
            },
          });
          console.log("Whatsapp model",whatsappIntegrationModel);
        const current_month_usage =
          whatsappIntegrationModel.knowledgebase.organization.monthly_usages.sort(
            (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
          )[0];
        whatsappIntegrationModel.knowledgebase.organization.current_month_usage =
          current_month_usage;
        if (
          parseInt(
            whatsappIntegrationModel.knowledgebase.organization.current_month_usage.credits_used
          ) >= parseInt(whatsappIntegrationModel.knowledgebase.organization.plan.allowed_credits)
        ) {
          ctx.request.body = {
            access_token: CryptoJS.AES.decrypt(
              whatsappIntegrationModel.access_token,
              process.env.ENCRYPTION_KEY
            ).toString(CryptoJS.enc.Utf8),
            phone_number_id: whatsappIntegrationModel.phone_number_id,
            messaging_product: "whatsapp",
            preview_url: true,
            to: ctx.request.body.entry[0].changes[0].value.messages[0].from,
            type: "text",
            text: {
              preview_url: true,
              body: "I'm sorry, I am not able to help you at the moment, please contact us to resolve your issue.",
            },
          };
          await this.sendMessage(ctx);
        }
        var from;
        try{
        from = ctx.request.body.entry[0].changes[0].value.messages[0]?.from;
        } catch(e){
          console.log("Failed to fetch from")
          console.log(e);
        }
        console.log("******* From *******");
        console.log(from);
        ctx.request.body = {
          kb_id: whatsappIntegrationModel.knowledgebase.kb_id,
          org_id: whatsappIntegrationModel.knowledgebase.organization.org_id,
          question:
            ctx.request.body.entry[0].changes[0].value.messages[0].text.body,
          api_source: `whatsapp_${from}`,
          session: from.toString(),
        };
      
        const answer = await strapi.controllers[
          "api::answer.custom-controller"
        ].askquestion(ctx);
        console.log("**** Answer *****");
        console.log(answer.data.attributes);
        if (answer.data.attributes.sources.length === 0) {
          ctx.request.body = {
            access_token: CryptoJS.AES.decrypt(
              whatsappIntegrationModel.access_token,
              process.env.ENCRYPTION_KEY
            ).toString(CryptoJS.enc.Utf8),
            phone_number_id: whatsappIntegrationModel.phone_number_id,
            messaging_product: "whatsapp",
            preview_url: true,
            to: from,
            type: "text",
            text: {
              preview_url: true,
              body:
                answer.data.attributes.answer ??
                "I'm sorry, I am not able to help you at the moment, please contact us to resolve your issue.",
            },
          };
          await this.sendMessage(ctx);
        } else {
          const messages = [
            answer.data.attributes.answer ??
              "I'm sorry, I am not able to help you at the moment, please contact us to resolve your issue.",
          ];
          answer.data.attributes.sources.map((s) => messages.push(s));
          for (let i = 0; i < messages.length; i++) {
            console.log("Source:"+i);
            console.log( messages[i]);
            ctx.request.body = {
              access_token: CryptoJS.AES.decrypt(
                whatsappIntegrationModel.access_token,
                process.env.ENCRYPTION_KEY
              ).toString(CryptoJS.enc.Utf8),
              phone_number_id: whatsappIntegrationModel.phone_number_id,
              preview_url: true,
              messaging_product: "whatsapp",
              to: from,
              type: "text",
              text: {
                preview_url: true,
                body: messages[i],
              },
            };
            await this.sendMessage(ctx);
          }
        }
      }
      return "Whatsapp webhook";
    } catch (e) {
      console.error("Error handling webhook event:", e);
      await this.sendMessage(ctx);
      return "Whatsapp webhook error";
    }
  },
  async getwebhook(ctx, next) {
    console.log("Whatsapp webhook trigger");
    console.log(ctx.request.body);

    try {
      const queryToken = ctx.request.query["hub.verify_token"];
      console.log("Verification request received:", ctx.request.query);
      if (queryToken === process.env.VERIFY_TOKEN) {
        return ctx.request.query["hub.challenge"];
      } else {
        console.error("Invalid verify token:", queryToken);

        return ctx.badRequest("Something went wrong");
      }
    } catch (e) {
      console.error("Error handling webhook event:", e);
      return "Whatsapp webhook error";
    }
  },
  async sendMessage(ctx, next) {
    console.log("Whatsapp send message trigger");
    const token = ctx.request.body.access_token;
    const phone_number_id = ctx.request.body.phone_number_id;
    ctx.request.body = _.omit(ctx.request.body, [
      "access_token",
      "phone_number_id",
    ]);

    try {
      const result = await axios.post(
        `https://graph.facebook.com/${process.env.CLOUD_API_VERSION}/${phone_number_id}/messages`,
        ctx.request.body,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );
      console.log(result);
    } catch (e) {
      console.log("Whatsapp send message event:", e);
      return "Whatsapp send message error";
    }
  },
};
