import React, { useEffect, useState, useCallback } from "react";
import CancelIcon from "@mui/icons-material/Cancel";
import AddButton from "src/app/shared-components/buttons/add_button";
import { H6, H2, H5 } from "src/app/shared-components/text/texts";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import axios from "axios";
import PostBodyParams from "./PostBodyForm";
import ApiRequestComponent from "./APIRequestComponent";
import LoadingSpinner from "app/shared-components/loading-spinner/loading-spinner";
import { ErrorCard } from "app/shared-components/cards/errorCard";

const ApiDocForm = ({
  kbId,
  apiToolData,
  onComplete,
  onFailure,
  editMode = false,
}) => {
  const [loading, setLoading] = useState(false);
  const [formErrors, setFormErrors] = useState([]);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    url: "",
    method: "GET",
    apiDocs: "",
    bodyType: "form-data",
    headers: [],
    approvalRequired: false,
  });

  useEffect(() => {
    setFormData({
      name: apiToolData?.attributes?.name ?? "",
      description: apiToolData?.attributes?.description ?? "",
      url: apiToolData?.attributes?.base_url ?? "",
      method: apiToolData?.attributes?.method ?? "GET",
      apiDocs: apiToolData?.attributes?.api_docs ?? "",
      bodyType: apiToolData?.attributes?.body_type ?? "form-data",
      headers: apiToolData?.attributes?.headers ?? [],
      approvalRequired: false,
    });
  }, [apiToolData]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    setFormData((prevState) => ({
      ...prevState,
      [name]: type === "checkbox" ? checked : value,
      body: [],
    }));
  };

  const validateForm = useCallback(() => {
    const errors = [];

    if (!formData.name.trim()) {
      errors.push("Name is required");
    } else if (formData.name.includes(" ")) {
      errors.push("Name cannot contain spaces");
    }

    if (!formData.description.trim()) {
      errors.push("Description is required");
    }

    if (formData.url) {
      try {
        new URL(formData.url);
      } catch (_) {
        errors.push("Invalid URL");
      }
    } else {
      errors.push("URL is required");
    }

    setFormErrors(errors);
    return errors.length === 0;
  }, [formData]);

  const handleArrayInputChange = (type, index, key, value) => {
    setFormData((prevState) => {
      const newArray = [...prevState[type]];
      newArray[index] = { ...newArray[index], [key]: value };
      return { ...prevState, [type]: newArray };
    });
  };

  const addArrayItem = (type) => {
    setFormData((prevState) => ({
      ...prevState,
      [type]: [...prevState[type], { name: "", value: "" }],
    }));
  };

  const removeArrayItem = (type, index) => {
    setFormData((prevState) => ({
      ...prevState,
      [type]: prevState[type].filter((_, i) => i !== index),
    }));
  };



  const updateApiTool = async (a) => {
    setLoading(true);
    try {
      const datasource = await axios.put(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/api-tools/${apiToolData.id}`,
        {
          data: {
            name: formData.name,
            description: formData.description,
            base_url: formData.url,
            type: "api",
            enabled: true,
            api_docs: formData.apiDocs,
            body: formData.body,
            body_type: formData.bodyType,
            method: formData.method,
            supports_post_request: formData.method === "POST" || formData.method === "PUT",
            auth_type: "bearer",
            path_params: formData.path_params,
            query_params: formData.query_params,
            headers: formData.headers,
            knowledgebases: {
              connect: [{ id: kbId }],
            },
          },
        }
      );
      setLoading(false);
      onComplete();
    } catch (e) {
      setLoading(false);
      onFailure();
      console.log(e);
    }
  };

  const createApiTool = async () => {
    setLoading(true);
    try {
      const datasource = await axios.post(
        `${process.env.REACT_APP_AUTH_BASE_URL}/api/api-tools`,
        {
          data: {
            name: formData.name,
            description: formData.description,
            base_url: formData.url,
            type: "api",
            enabled: true,
            api_docs: formData.apiDocs,
            supports_post_request: formData.method === "POST" || formData.method === "PUT",
            body: formData.body,
            body_type: formData.bodyType,
            method: formData.method,
            auth_type: "bearer",
            path_params: formData.path_params,
            query_params: formData.query_params,
            headers: formData.headers,
            knowledgebases: {
              connect: [{ id: kbId }],
            },
          },
        }
      );
      setLoading(false);
      onComplete();
    } catch (e) {
      setLoading(false);
      onFailure();

      console.log(e);
    }
  };

  return (
    <div className="flex flex-col w-full px-32 h-full mb-16 p-8 bg-white gap-y-16">
      <H2 className="mb-6 text-black">Connect API</H2>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          if(editMode){
            updateApiTool();
          } else {
            createApiTool();
          }
        }}
        className="space-y-16"
      >
        <div>
          <H6 className="text-gray-800 mb-8">Name</H6>
          <body className="text-gray-800 text-sm">
            The name of the API that will be used to identify it in the dashboard.
          </body>
          <input
            type="text"
            name="name"
            placeholder='e.g: "Get Weather"'
            value={formData.name}
            onChange={handleInputChange}
            className="w-full h-36 px-6 py-2 mt-4 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <H6 className="text-gray-800 mb-8">Description</H6>
          <body className="text-gray-800 text-sm">
            A description of the API that will be used to help the agent understand how to use it.
          </body>
          <input
            type="text"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            placeholder="e.g: Useful for getting the current weather in a given city."
            className="w-full h-36 px-6 py-8 mt-4 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <H6 className="text-gray-800 mb-8">URL to call</H6>
          <body className="text-gray-800 text-sm">
            The URL to call the API.
          </body>
          <input
            type="text"
            name="url"
            value={formData.url}
            onChange={handleInputChange}
            className="w-full h-36 px-6 py-2 mt-4 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <H6 className="text-gray-800 mb-8">Request Method</H6>
          <body className="text-gray-800 text-sm">
            The HTTP method to use when calling the API.
          </body>
          <div className="relative">
            <select
              name="method"
              value={formData.method}
              onChange={handleInputChange}
              className="w-full h-36 pl-8 mt-4 py-2 pr-12 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
            >
              <option>GET</option>
              <option>POST</option>
              <option>PUT</option>
              <option>DELETE</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center px-4 pointer-events-none">
              <ArrowDropDownIcon
                className="text-gray-800"
                style={{ fontSize: 24 }}
              />
            </div>
          </div>
        </div>

        <div>
          <H6 className="text-gray-800 mb-8">API Documentation</H6>
          <body className="text-gray-800 text-sm">
            Detailed explanation of the API that will be used to help the agent understand how to use it.
          </body>
          <textarea
            name="apiDocs"
            value={formData.apiDocs}
            onChange={handleInputChange}
            placeholder="e.g: Useful for getting the current weather in a given city. Provide detailed instructions on how to use this API."
            className="w-full h-[300px] px-6 py-4 mt-4 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-y"
          />
        </div>
        {["headers"].map((type) => (
          <div key={type}>
            <H6 className="text-gray-800 mb-16">
              {type.charAt(0).toUpperCase() +
                type.slice(1).replace(/([A-Z])/g, " $1")}
            </H6>
            <div className="space-y-0">
              {formData[type].map((item, index) => (
                <div key={index} className="flex items-center space-x-16">
                  <input
                    type="text"
                    value={item.name}
                    onChange={(e) =>
                      handleArrayInputChange(
                        type,
                        index,
                        "name",
                        e.target.value
                      )
                    }
                    placeholder="Name"
                    className="flex-grow h-36 px-6 py-2 mt-4 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <input
                    type="text"
                    value={item.value}
                    onChange={(e) =>
                      handleArrayInputChange(
                        type,
                        index,
                        "value",
                        e.target.value
                      )
                    }
                    placeholder="Value"
                    className="flex-grow h-36 px-6 mt-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    type="button"
                    onClick={() => removeArrayItem(type, index)}
                    className="p-2 text-red-600 hover:text-red-800 focus:outline-none "
                  >
                    <CancelIcon size={20} />
                  </button>
                </div>
              ))}
            </div>
            <div className="mt-8">
              <AddButton onClick={addArrayItem} metaData={type} />
            </div>
          </div>
        ))}
        {formErrors.length > 0 && (
          <div>
            <ErrorCard errorsMsg={formErrors.join(", ")} />
          </div>
        )}
        <div className="mb-32">
          <button
            type="submit"
            className="w-full py-8 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-base-purple focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {loading && (
              <div className="flex w-full place-content-center">
                <LoadingSpinner size={32} />
              </div>
            )}
            {!loading && <H5>Connect</H5>}
          </button>
        </div>
      </form>
    </div>
  );

};

export default ApiDocForm;
