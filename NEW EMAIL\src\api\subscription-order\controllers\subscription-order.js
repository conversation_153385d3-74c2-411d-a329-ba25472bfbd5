"use strict";
const stripePaymentProvider = require("../../../providers/stripe-payment-provider");
const { max } = require("lodash");
/**
 * subscription-order controller
 */

// const { createCoreController } = require('@strapi/strapi').factories;

// module.exports = createCoreController('api::subscription-order.subscription-order');

const { createCoreController } = require("@strapi/strapi").factories;

module.exports = createCoreController(
  "api::subscription-order.subscription-order",
  ({ strapi }) => ({
    async create(ctx) {
      console.log("***!create subscription order");
      try {
        const code = ctx.request.body?.code || null;
        const plan = await strapi
          .query("api::plan.plan")
          .findOne({
            where: { id: ctx.request.body.plan },
          })
          .catch((err) => {
            console.log(err);
          });
        if (!plan) {
          return ctx.throw(500, "Plan not found.");
        }
        const user = await strapi
          .query("plugin::users-permissions.user")
          .findOne({
            where: { email: ctx.state.user.email },
            populate: {
              organization: {
                populate: {
                  plan: true,
                  monthly_usages: true,
                  subscription_orders: true,
                },
              },
            },
          });

        if (
          user.organization.plan &&
          user.organization?.plan?.id === plan.id &&
          user.organization?.plan?.status === "subscribed"
        ) {
          return ctx.throw(500, "You already have this plan.");
        } else if (
          user.organization.plan === null
        ) {
          const result = await stripePaymentProvider.initiateCheckoutSession(
            ctx,
            plan,
            code
          );
          return result;
        } else if (
          user.organization.plan &&
          user.organization?.plan?.id !== ctx.request.body.plan ||
          user.organization?.subscription === "trial"
        ) {
          ///This logic is wrong, we should check if the current plan is not the same as the new plan and handle free trial logic
          console.log(
            "user.organization.subscription_orders",
            user.organization?.subscription_orders
          );
          const currentSubscriptionOrder = max(
            user.organization?.subscription_orders,
            "createdAt"
          );
          const result = await stripePaymentProvider.initiateSubscriptionUpdate(
            ctx,
            currentSubscriptionOrder.subscriptionId,
            plan.id
          );
          return result;
        }
      } catch (error) {
        console.log(error);
        ctx.response.status = 500;
        return { error };
      }
    },


    async changeSubscription(ctx) {
      try {
        const result = await stripePaymentProvider.initiateSubscriptonChange(
          ctx,
          ctx.request.body.plan
        );
        return result;
      } catch (e) {
        console.log(e);
        return ctx.throw(500, "Something went wrong");
      }
    },
  })
);
