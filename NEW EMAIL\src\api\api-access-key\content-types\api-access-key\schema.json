{"kind": "collectionType", "collectionName": "api_access_keys", "info": {"singularName": "api-access-key", "pluralName": "api-access-keys", "displayName": "API Access Key"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"key": {"type": "string"}, "name": {"type": "string"}, "organization": {"type": "relation", "relation": "manyToOne", "target": "api::organization.organization", "inversedBy": "api_access_keys"}}}