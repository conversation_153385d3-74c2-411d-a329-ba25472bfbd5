import Avatar from '@mui/material/Avatar';
import Checkbox from '@mui/material/Checkbox';
import IconButton from '@mui/material/IconButton';
import ListItemText from '@mui/material/ListItemText';
import MenuItem from '@mui/material/MenuItem';
import { useState } from 'react';
import { useAppSelector } from 'app/store';
import FuseSvgIcon from '@fuse/core/FuseSvgIcon';
import ToolbarMenu from './ToolbarMenu';
import { selectMembers } from '../../../../store/membersSlice';

const MembersMenu = (props) => {
  const { memberIds, onToggleMember } = props;

  const [anchorEl, setAnchorEl] = useState(null);
  // TODO
  const members = memberIds;
  
  // useAppSelector(selectMembers);


  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  return (
    <div>
      <IconButton onClick={handleMenuOpen} size="large">
        <FuseSvgIcon className='text-base-purple'>heroicons-outline:user-circle</FuseSvgIcon>
      </IconButton>
      <ToolbarMenu state={anchorEl} onClose={handleMenuClose}>
        <div>
          {members.map((member) => {
            return (
              <MenuItem
                className="px-8"
                key={member.id}
                onClick={() => {
                  onToggleMember(member);
                }}
              >
                <Checkbox 
                  checked={memberIds.some(m => m.id === member.id)
                    // memberIds.includes(member.id)
                  } 
                  sx={{
                    '& .MuiSvgIcon-root': {
                      color: 'grey', // Change 'grey' to your desired color
                    },
                  }}
                />
                <Avatar className="w-32 h-32" src={member?.avatar??''} />
                <ListItemText className="mx-8">{member?.username}</ListItemText>
              </MenuItem>
            );
          })}
        </div>
      </ToolbarMenu>
    </div>
  );
};

export default MembersMenu;
