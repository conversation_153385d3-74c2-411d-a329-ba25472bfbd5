// api/webhook/controllers/webhook.js
"use strict";

module.exports = {
  async webhook(ctx, next) {
    console.log(" webhook trigger");
    console.log(ctx.request.body.action_type);
    try {
      const { action_type, data } = ctx.request.body;

      switch (action_type) {
        case "add_files_result_model":
          try {
            if (typeof data.knowledgebase == "string") {
              const kb = await strapi
                .query("api::knowledgebase.knowledgebase")
                .findOne({
                  where: { kb_id: data.knowledgebase },
                });
              data.knowledgebase = kb.id;
            }
            data["py_id"] = data.id;
            delete data.id;
            await strapi
              .query("api::file-results-model.file-results-model")
              .create({
                data: data,
              });
          } catch (e) {
            console.log("Webhook create error", e);
            throw e;
          }

          break;

        case "update_files_result_model":
          try {
            console.log("Webhook update Payload", data);
            if (data.knowledgebase) {
              delete data.knowledgebase;
            }
            if (data.source) {
              delete data.source;
            }
            const id = data.id;
            delete data.id;

            await strapi
              .query("api::file-results-model.file-results-model")
              .update({ where: { py_id: id }, data: data });
          } catch (e) {
            console.log("Webhook update error", e);
            throw e;
          }

          break;

        case "create_chunk":
          try {
            const id = data.id;
            delete data.id;
            await strapi.query("api::chunk.chunk").create({
              data: {
                content: data.content,
                embedding_id: data.embedding_id,
                kb_id: data.kb_id,
                document: data.document_id,
                status: data.status,
                chunk_index: data.chunk_index,
              },
            });
          } catch (e) {
            console.log("Webhook create error", e);
            throw e;
          }
          break;

        case "update_document":
          try {
            console.log("Webhook update Payload", data);
            if (data.knowledgebase) {
              delete data.knowledgebase;
            }
            if (data.source) {
              delete data.source;
            }
            const id = data.id;
            delete data.id;
            await strapi.query("api::document.document").update({
              where: { id: data.document_id },
              data: {
                status: data.status,
                tokens_used: data.tokens_used,
                character_count: data.character_count,
                vector_store_name: data.vector_store_name,
                index_name: data.index_name,
                namespace_prefix: data.namespace_prefix,
                embedding_model: data.embedding_model,
                document_store_url : data?.document_store_url ?? null,
              },
            });
            //TODO check this
            const currentUsage = await strapi.query("api::monthly-usage.monthly-usage").findOne({
              where: { id: data.usage_id },
            });

            if(!currentUsage){
              console.error(`Webhook update_document error: No usage found for usage_id: ${data.usage_id}`);
              throw new Error(`No usage found for usage_id: ${data.usage_id}`);
            }
            await strapi.query("api::monthly-usage.monthly-usage").update({
              where: { id: data.usage_id},
              data: {
                training_tokens_count:
                  (Number(currentUsage.training_tokens_count) || 0) + (Number(data.tokens_used) || 0),
              },
            });
          } catch (e) {
            console.log("Webhook update error", e);
            throw e;
          }
          break;

        case "delete_embeddings":
          try {
            const { embedding_ids } = data;
            if (!embedding_ids || !Array.isArray(embedding_ids) || embedding_ids.length === 0) {
              console.log("Webhook delete_embeddings: No embedding IDs provided.");
              // Optionally return an error or specific message
              break; // Exit case if no IDs
            }

            console.log(`Webhook delete_embeddings: Deleting chunks with embedding IDs: ${embedding_ids.join(', ')}`);

            // Use deleteMany to delete multiple chunks based on embedding_id
            const deleteResult = await strapi.query("api::chunk.chunk").deleteMany({
              where: {
                embedding_id: {
                  $in: embedding_ids,
                },
              },
            });

            console.log(`Webhook delete_embeddings: Deleted ${deleteResult.count} chunks.`);

          } catch (e) {
            console.log("Webhook delete_embeddings error", e);
            throw e; // Rethrow the error to be caught by the outer try-catch
          }
          break;

        case "empty_kb":
          try {
            const { kb_id } = data;
            if (!kb_id) {
              console.log("Webhook empty_kb: Missing kb_id in payload.");
              break; // Exit if kb_id is missing
            }

            console.log(`Webhook empty_kb: Received request for kb_id: ${kb_id}`);

            // 1. Find the knowledgebase Strapi ID using the provided kb_id
            const kb = await strapi.query("api::knowledgebase.knowledgebase").findOne({
              where: { kb_id: kb_id },
              select: ['id'] // Only select the ID
            });

            if (!kb) {
              console.log(`Webhook empty_kb: Knowledge base with kb_id ${kb_id} not found.`);
              break; // Exit if KB not found
            }

            const knowledgebaseId = kb.id; // This is the internal Strapi ID
            console.log(`Webhook empty_kb: Found knowledge base with internal ID: ${knowledgebaseId}`);

            // 2. Delete all chunks associated with this knowledge base ID (using the string kb_id)
            const deletedChunks = await strapi.query("api::chunk.chunk").deleteMany({
              where: { kb_id: kb_id }, // Use the original string kb_id from payload
            });
            console.log(`Webhook empty_kb: Deleted ${deletedChunks.count} chunks for knowledge base string ID ${kb_id}.`);

            // 3. Delete all documents associated with this knowledge base ID (using internal Strapi ID)
            // IMPORTANT: Verify the field name in the 'document' model that links to 'knowledgebase'. Assuming 'knowledgebase'.
            const deletedDocuments = await strapi.query("api::document.document").deleteMany({
              where: { knowledgebase: knowledgebaseId }, // Assumes relation field is named 'knowledgebase'
            });
            console.log(`Webhook empty_kb: Deleted ${deletedDocuments.count} documents for knowledge base ID ${knowledgebaseId}.`);

          } catch(e) {
            console.error(`Webhook empty_kb error for kb_id ${data.kb_id}:`, e);
            throw e; // Rethrow the error
          }
          break;

        case "delete_kb":
          try {
            const { kb_id } = data;
            if (!kb_id) {
              console.log("Webhook delete_kb: Missing kb_id in payload.");
              break; // Exit if kb_id is missing
            }

            console.log(`Webhook delete_kb: Received request for kb_id: ${kb_id}`);

            // 1. Find the knowledgebase Strapi ID using the provided kb_id
            const kb = await strapi.query("api::knowledgebase.knowledgebase").findOne({
              where: { kb_id: kb_id },
              select: ['id'] // Only select the ID
            });

            if (!kb) {
              console.log(`Webhook delete_kb: Knowledge base with kb_id ${kb_id} not found.`);
              // If the KB doesn't exist, we can arguably still proceed to ensure related items are gone,
              // or break here if we only want to delete if the KB itself exists.
              // Choosing to break for now, assuming deletion is contingent on the KB existing.
              break;
            }

            const knowledgebaseId = kb.id; // This is the internal Strapi ID
            console.log(`Webhook delete_kb: Found knowledge base with internal ID: ${knowledgebaseId}`);

            // 2. Delete all chunks associated with this knowledge base ID (using the string kb_id)
            const deletedChunks = await strapi.query("api::chunk.chunk").deleteMany({
              where: { kb_id: kb_id }, // Use the original string kb_id from payload
            });
            console.log(`Webhook delete_kb: Deleted ${deletedChunks.count} chunks for knowledge base string ID ${kb_id}.`);

            // 3. Delete all documents associated with this knowledge base ID (using internal Strapi ID)
            const deletedDocuments = await strapi.query("api::document.document").deleteMany({
              where: { knowledgebase: knowledgebaseId }, // Assumes relation field is named 'knowledgebase'
            });
            console.log(`Webhook delete_kb: Deleted ${deletedDocuments.count} documents for knowledge base ID ${knowledgebaseId}.`);

            // 4. Delete the knowledge base itself
            const deletedKb = await strapi.query("api::knowledgebase.knowledgebase").delete({
              where: { id: knowledgebaseId },
            });
            if (deletedKb) {
               console.log(`Webhook delete_kb: Successfully deleted knowledge base with internal ID ${knowledgebaseId}.`);
            } else {
               // This case might not be reachable if findOne succeeded earlier, but good practice
               console.log(`Webhook delete_kb: Knowledge base with internal ID ${knowledgebaseId} could not be deleted (might have been deleted already).`);
            }

          } catch(e) {
            console.error(`Webhook delete_kb error for kb_id ${data.kb_id}:`, e);
            throw e; // Rethrow the error
          }
          break;

        // Add more cases for other events you want to handle
        default:
          console.log(`Unhandled event type: ${type}`);
      }
      return "webhook success";
    } catch (e) {
      console.error("Error handling webhook event:", e);
      return "webhook error";
    }
  },
};
