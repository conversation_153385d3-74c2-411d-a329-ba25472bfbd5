'use strict';

/**
 * user subscription check policy
 */
const { hasActiveSubscriptionPlan } = require('../helpers/has-subscription');
module.exports = async (policyContext, config, { strapi }) => {
	const populatedUser = await strapi.query('plugin::users-permissions.user').findOne({
			where: { id: policyContext.state.user.id },
			populate: { organization:true},})
			.catch(err => {
			console.log(err);
		});
	const isActive = await hasActiveSubscriptionPlan(populatedUser.organization);
	if(isActive){
		return true;
	}else{
		const customError = new Error("You don't have subscription");
		customError.status = 403; // Set the desired status code
		throw customError;
	}
}