import React, { useState, useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import JwtService from 'src/app/auth/services/jwtService'
import history from '@history';
import { showMessage } from 'app/store/fuse/messageSlice';


function GoogleAuthCallback() {
  const location = useLocation()
  useEffect(() => {
    if (!location) {
      return
    }
    const { search } = location
	JwtService.signInWithGoogle(search).then((user) => {
       history.push('/');
      })
      .catch((error) => {
       showMessage({message:"Something went wrong try again"});
	   history.push('/sign-in');
      });;
  }, [location])

  return (
    <div className='flex items-center place-content-center h-full'>
      Signing in with Google... Please wait.
    </div>
  )
}

export default GoogleAuthCallback
