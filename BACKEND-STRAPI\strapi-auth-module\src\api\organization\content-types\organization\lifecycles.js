"use strict";

/**
 * organization lifecycle
 */

module.exports = {
  async beforeCreate(event) {
    const plan = await strapi
      .query("api::plan.plan")
      .findOne({ where: { name: "Trial" } });
    if (plan) {
		event.params.data ={
			...event.params.data,
			subscription: "trial",
      plan: plan.id
		}
    }
  },
  async afterCreate(event) {
	const monthly_usage = await strapi.query("api::monthly-usage.monthly-usage").create({data:{organization: event.result.id}});
  }
};
