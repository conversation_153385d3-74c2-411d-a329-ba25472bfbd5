import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";
import { bool } from "prop-types";

export const getUsage = createAsyncThunk(
  "usage/getUsage",
  async (arg, { getState }) => {
    try {
      // Check if user is authenticated before making the request
      const token = localStorage.getItem('jwt_access_token');
      if (!token) {
        console.log('🔓 No authentication token found - skipping usage fetch');
        return {
          credits_used: 0,
          allowed_credits: 0,
          training_tokens_count: 0,
          allowed_training_tokens_count: 0,
          allowed_kbs: 0,
          kbs_count: 0,
          allowed_scrape_train_count: 0,
          plan_name: 'Free',
          authenticated: false
        };
      }

      const response = await axios
        .get(`${process.env.REACT_APP_AUTH_BASE_URL}/api/org`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        })
        .catch((error) => {
          console.log('❌ Organization API error:', error);
          // Return default values if API fails
          return {
            data: {
              authenticated: false,
              message: 'Authentication required'
            }
          };
        });

      // Handle unauthenticated response
      if (!response.data || response.data.authenticated === false) {
        console.log('🔓 Unauthenticated response from /api/org');
        return {
          credits_used: 0,
          allowed_credits: 0,
          training_tokens_count: 0,
          allowed_training_tokens_count: 0,
          allowed_kbs: 0,
          kbs_count: 0,
          allowed_scrape_train_count: 0,
          plan_name: 'Free',
          authenticated: false
        };
      }
      var credits_used;
      var training_tokens_count;
      try {
        credits_used = parseInt(
          response.data?.current_month_usage?.credits_used ?? 0
        );
        training_tokens_count = parseInt(
          response.data.current_month_usage.training_tokens_count ?? 0
        );
      } catch (e) {
        credits_used = 0;
        training_tokens_count = 0;
      }

      const data = {
        credits_used: credits_used,
        allowed_credits: parseInt(response.data?.plan?.allowed_credits ?? 0),
        training_tokens_count: training_tokens_count,
        allowed_training_tokens_count: response.data?.plan?.allowed_training_tokens ?? 0,
        allowed_kbs: response.data?.plan?.allowed_kbs ?? 0,
        kbs_count: response.data?.current_month_usage?.kb_count ?? 0,
        allowed_scrape_train_count: response.data?.plan?.allowed_scrape_train_count ?? 0,
        plan_name: response.data?.plan?.name ?? 'Free',
        authenticated: true
      };
      return data;
    } catch (error) {
      console.error('❌ Usage fetch error:', error);
      return {
        credits_used: 0,
        allowed_credits: 0,
        training_tokens_count: 0,
        allowed_training_tokens_count: 0,
        allowed_kbs: 0,
        kbs_count: 0,
        allowed_scrape_train_count: 0,
        plan_name: 'Free',
        authenticated: false,
        error: error.message
      };
    }
  }
);
const initialState = {
  credits_used: 0,
  allowed_credits: 0,
  training_tokens_count: 0,
  allowed_training_tokens_count: 0,
  allowed_kbs: 0,
  kbs_count: 0,
  allowed_scrape_train_count: 0,
};
const usageSlice = createSlice({
  name: "usage",
  initialState,
  reducers: {},
  extraReducers: {
    [getUsage.fulfilled]: (state, action) => action.payload,
  },
});

export const selectUsage = ({ usage }) => usage;

export function hasExcededCreditLimit(plan , usage){
 return usage["credits_used"] > parseInt(plan.allowed_credits);
}

export function hasExceededTrainingTokenLimit(plan , usage) {
  return usage["training_tokens_count"] > plan.allowed_training_tokens ;
}

export function hasExceededKBLimit(plan , usage){
  return usage.kbs_count > plan.allowed_kbs
}

export function hasExceededScrapeTrainLimit(linksCount , usage){
  return linksCount >= usage["allowed_scrape_train_count"];
}

export default usageSlice.reducer;
