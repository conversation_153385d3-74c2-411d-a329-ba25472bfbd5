// paymentProviders/paymentProviderInterface.js

class PaymentProviderInterface {
    constructor() {}

    // Customer management
    async createCustomer(email) {
        throw new Error('Method not implemented');
    }


    async initiateCheckoutSession(ctx, planId, isTrial) {
        throw new Error('Method not implemented');
    }

    async handlePaymentSuccess(ctx , invoice_url, product_id, start_date, end_date) {
        throw new Error('Method not implemented');
    }

    async handlePaymentFailed(ctx) {
        throw new Error('Method not implemented');
    }


}

module.exports = PaymentProviderInterface;
