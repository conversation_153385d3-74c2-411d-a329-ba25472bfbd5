import { ThemeProvider } from "@mui/material/styles";
import AppBar from "@mui/material/AppBar";
import Hidden from "@mui/material/Hidden";
import Toolbar from "@mui/material/Toolbar";
import clsx from "clsx";
import { memo } from "react";
import { useSelector } from "react-redux";
import {
  selectFuseCurrentLayoutConfig,
  selectToolbarTheme,
} from "app/store/fuse/settingsSlice";
import { selectFuseNavbar } from "app/store/fuse/navbarSlice";
import NavigationSearch from "../../shared-components/NavigationSearch";
import NavbarToggleButton from "../../shared-components/NavbarToggleButton";
import UserMenu from "../../shared-components/UserMenu";
import { Button } from "@mui/material";
import OfferAnimation from "./OfferAnimation";
import ClearIcon from "@mui/icons-material/Clear";
import Logo from "app/theme-layouts/shared-components/Logo";

function ToolbarLayout1(props) {
  const config = useSelector(selectFuseCurrentLayoutConfig);
  const navbar = useSelector(selectFuseNavbar);
  const toolbarTheme = useSelector(selectToolbarTheme);

  return (
    <ThemeProvider theme={toolbarTheme}>
      <AppBar
        id="fuse-toolbar"
        className={clsx("flex relative z-20 shadow-sm", props.className)}
        style={{
          display: "flex", // Always flex
        }}
        color="default"
        sx={{
          backgroundColor: (theme) =>
            theme.palette.mode === "light"
              ? toolbarTheme.palette.background.paper
              : toolbarTheme.palette.background.default,
          borderBottom: "0.1px solid lightgrey",
        }}
        position="static"
      >
        <Toolbar className="p-0 min-h-48 md:min-h-64">
          <div className="flex flex-1 px-16 items-center">
            {config.navbar.display && config.navbar.position === "left" && (
              <>
                <Hidden lgDown>
                  {(config.navbar.style === "style-3" ||
                    config.navbar.style === "style-3-dense") && (
                    <NavbarToggleButton className="w-40 h-40 p-0 mx-0" />
                  )}

                  {config.navbar.style === "style-1" && !navbar.open && (
                    <NavbarToggleButton className="w-40 h-40 p-0 mx-0" />
                  )}
                </Hidden>

                <Hidden lgUp>
                  <NavbarToggleButton className="w-40 h-40 p-0 mx-0 sm:mx-8" />
                </Hidden>
              </>
            )}
            {!navbar.open && <Logo />}
          </div>

          {config.navbar.display && config.navbar.position === "right" && (
            <>
              <Hidden lgDown>
                {!navbar.open && (
                  <NavbarToggleButton className="w-40 h-40 p-0 mx-0" />
                )}
              </Hidden>

              <Hidden lgUp>
                <NavbarToggleButton className="w-40 h-40 p-0 mx-0 sm:mx-8" />
              </Hidden>
            </>
          )}
        </Toolbar>
      </AppBar>

      {/* <div
        className="bg-[#f6f8fa] border-b-1 min-h-48 shadow-md w-full flex justify-center py-8  relative cursor-pointer hover:shadow-2xl hover:scale-103 transform transition-all duration-500"
        onClick={() => {
          window.location.href = "/subscription"; // Corrected the property for navigation
        }}
      >
        { <div className="flex items-center space-x-2">
          <OfferAnimation />

          <div className="pl-4 pt-2">
            <p className="text-sm text-black">
              Subscribe now and get free support for 1 year. Click here!
            </p>
          </div>
        </div> }
        <div
          className="absolute right-32 top-24"
          onClick={(e) => {
            e.stopPropagation(); // Prevents the click from propagating to the parent div
            e.currentTarget.parentNode.style.display = "none"; // Dismisses the notification bar
          }}
        >
          {<span className="text-black cursor-pointer">
            <ClearIcon />
          </span>}
        </div>
      </div> */}
    </ThemeProvider>
  );
}

export default memo(ToolbarLayout1);
