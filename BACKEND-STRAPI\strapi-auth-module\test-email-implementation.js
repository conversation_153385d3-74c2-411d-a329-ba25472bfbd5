/**
 * Email Implementation Test Script
 * Tests all email flows to ensure Postmark integration works correctly
 */

async function testEmailImplementation() {
  console.log('🧪 Starting Email Implementation Tests...\n');

  try {
    // Check if Strapi is available
    if (typeof strapi === 'undefined') {
      console.error('❌ Strapi is not available. Make sure to run this script within Strapi context.');
      return;
    }

    const testResults = {
      providerTests: [],
      templateTests: [],
      serviceTests: [],
      errors: []
    };

    // Test 1: Provider Factory Initialization
    console.log('📧 Testing Email Provider Factory...');
    try {
      const { emailProviderFactory } = require('../src/providers/email-provider-factory');
      
      // Test provider availability
      const availableProviders = emailProviderFactory.getAvailableProviders();
      console.log(`✅ Available providers: ${availableProviders.join(', ')}`);
      
      // Test provider connections
      const connectionTests = await emailProviderFactory.testProviders();
      console.log('🔗 Provider connection tests:', connectionTests);
      
      testResults.providerTests.push({
        test: 'Provider Factory Initialization',
        status: 'passed',
        providers: availableProviders,
        connections: connectionTests
      });
    } catch (error) {
      console.error('❌ Provider Factory test failed:', error.message);
      testResults.errors.push({ test: 'Provider Factory', error: error.message });
    }

    // Test 2: Email Templates
    console.log('\n📝 Testing Email Templates...');
    try {
      const templates = await strapi.query('api::email-template.email-template').findMany({
        where: { is_active: true }
      });
      
      console.log(`✅ Found ${templates.length} active email templates:`);
      templates.forEach(template => {
        console.log(`  - ${template.name} (${template.provider}, ${template.template_type})`);
      });
      
      testResults.templateTests.push({
        test: 'Template Availability',
        status: 'passed',
        count: templates.length,
        templates: templates.map(t => ({ name: t.name, provider: t.provider, type: t.template_type }))
      });
    } catch (error) {
      console.error('❌ Template test failed:', error.message);
      testResults.errors.push({ test: 'Email Templates', error: error.message });
    }

    // Test 3: Email Connector Service
    console.log('\n🔌 Testing Email Connector Service...');
    try {
      const emailConnectorService = strapi.service('api::email-connector.email-connector');
      
      if (emailConnectorService) {
        console.log('✅ Email Connector Service is available');
        testResults.serviceTests.push({
          test: 'Email Connector Service',
          status: 'passed'
        });
      } else {
        throw new Error('Email Connector Service not found');
      }
    } catch (error) {
      console.error('❌ Email Connector Service test failed:', error.message);
      testResults.errors.push({ test: 'Email Connector Service', error: error.message });
    }

    // Test 4: Email Service Helper
    console.log('\n🛠️ Testing Email Service Helper...');
    try {
      const emailService = require('./src/helpers/email-service');
      
      if (emailService && typeof emailService.sendEmail === 'function') {
        console.log('✅ Email Service Helper is available');
        testResults.serviceTests.push({
          test: 'Email Service Helper',
          status: 'passed'
        });
      } else {
        throw new Error('Email Service Helper not properly configured');
      }
    } catch (error) {
      console.error('❌ Email Service Helper test failed:', error.message);
      testResults.errors.push({ test: 'Email Service Helper', error: error.message });
    }

    // Test 5: Marketing Contacts Service
    console.log('\n📊 Testing Marketing Contacts Service...');
    try {
      const marketingContactsService = require('./src/services/marketing-contacts');
      
      if (marketingContactsService && marketingContactsService.isEnabled()) {
        console.log('✅ Marketing Contacts Service is enabled and available');
        testResults.serviceTests.push({
          test: 'Marketing Contacts Service',
          status: 'passed',
          enabled: true
        });
      } else {
        console.log('⚠️ Marketing Contacts Service is disabled (SendGrid API key not configured)');
        testResults.serviceTests.push({
          test: 'Marketing Contacts Service',
          status: 'warning',
          enabled: false
        });
      }
    } catch (error) {
      console.error('❌ Marketing Contacts Service test failed:', error.message);
      testResults.errors.push({ test: 'Marketing Contacts Service', error: error.message });
    }

    // Test 6: Template Renderer
    console.log('\n🎨 Testing Template Renderer...');
    try {
      const TemplateRenderer = require('./src/services/template-renderer');
      
      // Test basic template rendering
      const testTemplate = 'Hello {{name}}, welcome to {{company}}!';
      const testData = { name: 'John', company: 'Podycy' };
      const rendered = TemplateRenderer.renderTemplate(testTemplate, testData);
      
      if (rendered === 'Hello John, welcome to Podycy!') {
        console.log('✅ Template Renderer working correctly');
        testResults.serviceTests.push({
          test: 'Template Renderer',
          status: 'passed'
        });
      } else {
        throw new Error('Template rendering produced unexpected result');
      }
    } catch (error) {
      console.error('❌ Template Renderer test failed:', error.message);
      testResults.errors.push({ test: 'Template Renderer', error: error.message });
    }

    // Test 7: Environment Variables
    console.log('\n🔧 Testing Environment Variables...');
    const requiredEnvVars = [
      'POSTMARK_API_KEY',
      'POSTMARK_FROM_EMAIL',
      'POSTMARK_FROM_NAME'
    ];
    
    const optionalEnvVars = [
      'SENDGRID_API_KEY',
      'MAILGUN_API_KEY',
      'MAILGUN_DOMAIN'
    ];
    
    console.log('Required environment variables:');
    requiredEnvVars.forEach(envVar => {
      const value = process.env[envVar];
      if (value) {
        console.log(`  ✅ ${envVar}: ${value.substring(0, 10)}...`);
      } else {
        console.log(`  ❌ ${envVar}: Not set`);
        testResults.errors.push({ test: 'Environment Variables', error: `${envVar} not set` });
      }
    });
    
    console.log('Optional environment variables:');
    optionalEnvVars.forEach(envVar => {
      const value = process.env[envVar];
      if (value) {
        console.log(`  ✅ ${envVar}: ${value.substring(0, 10)}...`);
      } else {
        console.log(`  ⚠️ ${envVar}: Not set (optional)`);
      }
    });

    // Summary
    console.log('\n📊 Test Summary:');
    console.log(`✅ Provider Tests: ${testResults.providerTests.length}`);
    console.log(`✅ Template Tests: ${testResults.templateTests.length}`);
    console.log(`✅ Service Tests: ${testResults.serviceTests.length}`);
    console.log(`❌ Errors: ${testResults.errors.length}`);
    
    if (testResults.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      testResults.errors.forEach(error => {
        console.log(`  - ${error.test}: ${error.error}`);
      });
    }
    
    console.log('\n🎉 Email Implementation Test completed!');
    
    if (testResults.errors.length === 0) {
      console.log('✅ All tests passed! Email system is ready for use.');
    } else {
      console.log('⚠️ Some tests failed. Please review the errors above.');
    }
    
    return testResults;

  } catch (error) {
    console.error('❌ Test script failed:', error);
    return { error: error.message };
  }
}

// Export for use in Strapi lifecycle
module.exports = testEmailImplementation;

// If running directly
if (require.main === module) {
  console.log('This script should be run within Strapi context.');
  console.log('Add it to your Strapi bootstrap or run via Strapi console.');
}
