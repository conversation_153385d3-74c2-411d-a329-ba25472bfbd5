const _ = require( 'lodash' );

module.exports = {
	async  updateOrgWithPlan( organization, plan ) {
		try {
			// Update organization with the plan details
			await strapi.query("api::organization.organization").update({
			where: { id: organization?.id },
			data: {
				subscription: "subscribed",
				plan: plan.id,
			},
			});
			await strapi
			          .query("api::monthly-usage.monthly-usage")
			          .create({ data: { organization: organization?.id, period: plan.type==='yearly' ? 'year' : 'month'
					} });
		  
		} catch (e) {
			console.log("Org Update failed", e);
		}
	}
}