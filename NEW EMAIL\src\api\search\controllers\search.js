"use strict";

module.exports = {
  async search(ctx) {
    try {
      const { query, limit = 20, start = 0, mode = "exact" } = ctx.request.body;

      if (!query || query.trim() === "") {
        return ctx.badRequest("Missing or empty search query");
      }

      const searchTerm = query.trim().toLowerCase();
      const isPartial = mode === "partial";

      // Base filters: loose match to reduce DB load (partial OR basic eq)
      const baseFilters = isPartial
        ? {
            $or: [
              { title: { $containsi: searchTerm } },
              { excerpt: { $containsi: searchTerm } },
              { script: { $containsi: searchTerm } },
            ],
          }
        : {
            $or: [
              { title: { $containsi: searchTerm } },
              { excerpt: { $containsi: searchTerm } },
              { script: { $containsi: searchTerm } },
            ],
          }; // fetch broader data, filter later

      const playlistFilters = isPartial
        ? {
            $or: [
              { name: { $containsi: searchTerm } },
              { description: { $containsi: searchTerm } },
            ],
          }
        : {
            $or: [
              { name: { $containsi: searchTerm } },
              { description: { $containsi: searchTerm } },
            ],
          };

      const [tracks, playlists] = await Promise.all([
        strapi.db.query("api::file-results-model.file-results-model").findMany({
          where: baseFilters,
          limit: 100, // fetch extra for filtering
          orderBy: { updatedAt: "desc" },
        }),
        strapi.db.query("api::playlist.playlist").findMany({
          where: playlistFilters,
          limit: 100,
          orderBy: { updatedAt: "desc" },
        }),
      ]);

      // Filter logic for exact mode — match words
      const matchWords = (text = "") =>
        text
          .toLowerCase()
          .split(/\s+/)
          .some((word) => word === searchTerm);

      const filteredTracks = isPartial
        ? tracks
        : tracks.filter((track) =>
            [track.title, track.excerpt, track.script].some(matchWords)
          );

      const filteredPlaylists = isPartial
        ? playlists
        : playlists.filter((playlist) =>
            [playlist.name, playlist.description].some(matchWords)
          );

      const sanitizedTracks = filteredTracks
        .slice(start, start + limit)
        .map((track) => ({
          id: track.id,
          type: "track",
          title: track.title,
          script: track.script,
          url: `/tracks/${track.id}`,
        }));

      const sanitizedPlaylists = filteredPlaylists
        .slice(start, start + limit)
        .map((playlist) => ({
          id: playlist.id,
          type: "playlist",
          title: playlist.name,
          description: playlist.description,
          url: `/playlists/${playlist.id}`,
        }));

      const results = [...sanitizedTracks, ...sanitizedPlaylists];

      return ctx.send(results, 200);
    } catch (error) {
      console.error("Search error:", error);
      return ctx.internalServerError("An error occurred during search");
    }
  },
};
