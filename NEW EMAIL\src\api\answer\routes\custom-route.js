"use strict";

/**
 * answer custom router
 */
module.exports = {
  routes: [
    {
      method: "POST",
      path: "/askquestion",
      handler: "custom-controller.askquestion",
      config: {
        policies: [
          "global::user-details-populate",
          "global::update-org-before-api-call",
          "global::usage-validation",
          "global::subscription-check",
        ],
      },
    },
    {
      method: "POST",
      path: "/runanswer",
      handler: "custom-controller.runanswer",
      config: {
        policies: [
          "global::user-details-populate",
          "global::update-org-before-api-call",
          "global::usage-validation",
        ],
      },
    },
    {
      method: "GET",
      path: "/answer/session",
      handler: "custom-controller.sessionanswer",
      config: {
        policies: [
          "global::user-details-populate",
          "global::update-org-before-api-call",
          "global::usage-validation",
        ],
      },
    },
    {
      method: "POST",
      path: "/multi-agent",
      handler: "custom-controller.multiagent",
      config: {
        policies: [
          "global::user-details-populate",
          "global::update-org-before-api-call",
          "global::usage-validation",
        ],
      },
    },
  ],
};
